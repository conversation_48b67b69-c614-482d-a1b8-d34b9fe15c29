<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.pas.clr.dao.ClrWithdrawRecordMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.clr.domain.ClrWithdrawRecord" >
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="TRANSACTION_NO" property="transactionNo" jdbcType="VARCHAR" />
    <result column="REQ_SERIALNO" property="reqSerialno" jdbcType="VARCHAR" />
    <result column="PAY_AMOUNT" property="payAmount" jdbcType="DECIMAL" />
    <result column="PAY_CURRENCY" property="payCurrency" jdbcType="VARCHAR" />
    <result column="CHANNEL_TYPE" property="channelType" jdbcType="CHAR" />
    <result column="SERVICE_ID" property="serviceId" jdbcType="VARCHAR" />
    <result column="CHANNEL_ID" property="channelId" jdbcType="DECIMAL" />
    <result column="CHANNEL_TRADE_NO" property="channelTradeNo" jdbcType="VARCHAR" />
    <result column="CHANNEL_TRADE_TIME" property="channelTradeTime" jdbcType="VARCHAR" />
    <result column="CHANNEL_CLEAR_DATE" property="channelClearDate" jdbcType="VARCHAR" />
    <result column="CHANNEL_RESP_CODE" property="channelRespCode" jdbcType="VARCHAR" />
    <result column="CHANNEL_RESP_MSG" property="channelRespMsg" jdbcType="VARCHAR" />
    <result column="CHANNEL_EXTEND" property="channelExtend" jdbcType="VARCHAR" />
    <result column="PAYEE_BANKCARD_ID" property="payeeBankcardId" jdbcType="VARCHAR" />
    <result column="PAYEE_BANKCODE" property="payeeBankcode" jdbcType="VARCHAR" />
    <result column="PAYEE_CUSTOMER" property="payeeCustomer" jdbcType="VARCHAR" />
    <result column="CUSTOMER_CODE" property="customerCode" jdbcType="VARCHAR" />
    <result column="STATE" property="state" jdbcType="CHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="PLAN_SEND_TIME" property="planSendTime" jdbcType="TIMESTAMP" />
    <result column="ACTUAL_SEND_TIME" property="actualSendTime" jdbcType="TIMESTAMP" />
    <result column="ASK_EXECUTE_TIME" property="askExecuteTime" jdbcType="TIMESTAMP" />
    <result column="QUERY_COUNT" property="queryCount" jdbcType="DECIMAL" />
    <result column="INSTITUTION_ID" property="institutionId" jdbcType="DECIMAL" />
    <result column="PAY_METHOD" property="payMethod" jdbcType="VARCHAR" />
    <result column="INSTITUTION_NAME" property="institutionName" jdbcType="VARCHAR" />
    <result column="CHANNEL_MCHT_NO" property="channelMchtNo" jdbcType="VARCHAR" />
    <result column="CHANNEL_INST_CODE" property="channelInstCode" jdbcType="VARCHAR" />
    <result column="ROUTE_TYPE" property="routeType" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, TRANSACTION_NO, REQ_SERIALNO, PAY_AMOUNT, PAY_CURRENCY, CHANNEL_TYPE, SERVICE_ID, 
    CHANNEL_ID, CHANNEL_TRADE_NO, CHANNEL_TRADE_TIME, CHANNEL_CLEAR_DATE, CHANNEL_RESP_CODE, 
    CHANNEL_RESP_MSG, CHANNEL_EXTEND, PAYEE_BANKCARD_ID, PAYEE_BANKCODE, PAYEE_CUSTOMER, 
    CUSTOMER_CODE, STATE, CREATE_TIME, UPDATE_TIME, PLAN_SEND_TIME, ACTUAL_SEND_TIME, 
    ASK_EXECUTE_TIME, QUERY_COUNT, INSTITUTION_ID, PAY_METHOD, INSTITUTION_NAME, CHANNEL_MCHT_NO, 
    CHANNEL_INST_CODE, ROUTE_TYPE
  </sql>
  <select id="queryPreDayList" resultMap="BaseResultMap" >
    select 
    <include refid="Base_Column_List" />
    from CLR_WITHDRAW_RECORD
    <where>
      <if test="beginCountTime != null ">
        CREATE_TIME  &gt;=#{beginCountTime,jdbcType=TIMESTAMP}
      </if>
      <if test="endCountTime != null ">
        AND CREATE_TIME &lt;=#{endCountTime,jdbcType=TIMESTAMP}
      </if>
      <if test="institutionId != null ">
        AND INSTITUTION_ID = #{institutionId,jdbcType=DECIMAL}
      </if>
      <if test="state != null ">
        AND STATE = #{state,jdbcType=CHAR}
      </if>
        AND CHANNEL_ID IN (37,38)
    </where>
  </select>
</mapper>