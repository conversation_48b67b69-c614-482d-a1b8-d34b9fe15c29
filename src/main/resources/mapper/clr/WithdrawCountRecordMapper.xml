<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.pas.clr.dao.WithdrawCountRecordMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.clr.domain.WithdrawCountRecord">
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="INSTITUTION_ID" jdbcType="DECIMAL" property="institutionId" />
    <result column="CHANNEL_ID" jdbcType="DECIMAL" property="channelId" />
    <result column="COUNT_AMOUNT" jdbcType="DECIMAL" property="countAmount" />
    <result column="COUNT_TYPE" jdbcType="VARCHAR" property="countType" />
    <result column="COUNT_TIME" jdbcType="TIMESTAMP" property="countTime" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="BILL_NO" jdbcType="VARCHAR" property="billNo" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, INSTITUTION_ID, CHANNEL_ID, COUNT_AMOUNT, COUNT_TYPE, COUNT_TIME, CREATE_TIME, 
    BILL_NO
  </sql>
  <!-- 代付交易汇总记录查询 -->
  <select id="queryList" resultMap="BaseResultMap">
    select
    ID, INSTITUTION_ID, CHANNEL_ID, COUNT_AMOUNT, COUNT_TYPE, COUNT_TIME, CREATE_TIME,
    BILL_NO
    from (
    select A.*, rownum RN
    from (
    select ID, INSTITUTION_ID, CHANNEL_ID, COUNT_AMOUNT, COUNT_TYPE, COUNT_TIME, CREATE_TIME,
    BILL_NO
    from CUM_WITHDRAW_COUNT_RECORD
    <where>
      <if test="beginCountTime != null ">
        COUNT_TIME  &gt;=#{beginCountTime,jdbcType=TIMESTAMP}
      </if>
      <if test="endCountTime != null ">
        AND COUNT_TIME  &lt;=#{endCountTime,jdbcType=TIMESTAMP}
      </if>
    </where>
    order by CREATE_TIME desc
    ) A
    where rownum &lt;= #{endRowNo,jdbcType=DECIMAL}
    )
    where RN &gt;= #{beginRowNo,jdbcType=DECIMAL}
  </select>
  <!--查询代付交易总记录数-->
  <select id="countWithdrawList" resultType="java.lang.Integer">
    select count(*)
    from CUM_WITHDRAW_COUNT_RECORD
    <where>
      <if test="beginCountTime != null">
        COUNT_TIME  &gt;=#{beginCountTime,jdbcType=TIMESTAMP}
      </if>
      <if test="endCountTime != null">
        AND COUNT_TIME  &lt;=#{endCountTime,jdbcType=TIMESTAMP}
      </if>
    </where>
  </select>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.clr.domain.WithdrawCountRecord">
    insert into CUM_WITHDRAW_COUNT_RECORD (ID, INSTITUTION_ID, CHANNEL_ID, 
      COUNT_AMOUNT, COUNT_TYPE, COUNT_TIME, 
      CREATE_TIME, BILL_NO
      )
    values (#{id,jdbcType=DECIMAL}, #{institutionId,jdbcType=DECIMAL}, #{channelId,jdbcType=DECIMAL}, 
      #{countAmount,jdbcType=DECIMAL}, #{countType,jdbcType=VARCHAR}, #{countTime,jdbcType=TIMESTAMP}, 
      #{createTime,jdbcType=TIMESTAMP}, #{billNo,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.clr.domain.WithdrawCountRecord">
    insert into CUM_WITHDRAW_COUNT_RECORD
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="institutionId != null">
        INSTITUTION_ID,
      </if>
      <if test="channelId != null">
        CHANNEL_ID,
      </if>
      <if test="countAmount != null">
        COUNT_AMOUNT,
      </if>
      <if test="countType != null">
        COUNT_TYPE,
      </if>
      <if test="countTime != null">
        COUNT_TIME,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="billNo != null">
        BILL_NO,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="institutionId != null">
        #{institutionId,jdbcType=DECIMAL},
      </if>
      <if test="channelId != null">
        #{channelId,jdbcType=DECIMAL},
      </if>
      <if test="countAmount != null">
        #{countAmount,jdbcType=DECIMAL},
      </if>
      <if test="countType != null">
        #{countType,jdbcType=VARCHAR},
      </if>
      <if test="countTime != null">
        #{countTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="billNo != null">
        #{billNo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>