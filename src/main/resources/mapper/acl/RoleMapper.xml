<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.pas.acl.dao.RoleMapper">
	<resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.acl.po.Role">
		<id column="ROLE_ID" jdbcType="DECIMAL" property="uid" />
		<result column="CODE" jdbcType="VARCHAR" property="code" />
		<result column="NAME" jdbcType="VARCHAR" property="name" />
		<result column="REMARK" jdbcType="VARCHAR" property="remark" />
		<result column="USER_COUNT" jdbcType="INTEGER" property="userCount" />
		<result column="DATA_AUTH" jdbcType="VARCHAR" property="dataAuth" />
		<result column="OPERATE_TIME" jdbcType="TIMESTAMP" property="operateTime" />
		<result column="OPERATER" jdbcType="VARCHAR" property="operater" />
	</resultMap>
	<sql id="Base_Column_List">
		ROLE_ID, NAME, REMARK,CODE,DATA_AUTH,OPERATE_TIME,OPERATER
	</sql>
	<sql id="Base_Column_List1">
		ROLE_ID, NAME, REMARK,USER_COUNT,CODE,DATA_AUTH,OPERATE_TIME,OPERATER
	</sql>
	<select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from PAS_ROLE
		where ROLE_ID = #{uid,jdbcType=DECIMAL}
	</select>

	<select id="selectAllRoles" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from PAS_ROLE where ROLE_ID != 0
		<if test="dataAuth != null and dataAuth != ''">
			and DATA_AUTH = #{dataAuth,jdbcType=VARCHAR}
		</if>
	</select>

	<delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
		delete from PAS_ROLE
		where ROLE_ID = #{uid,jdbcType=DECIMAL} and ROLE_ID != 0
	</delete>

	<select id="selectByPage" resultMap="BaseResultMap"
			parameterType="java.util.Map">
		select
		<include refid="Base_Column_List" />
		from (
		select A.*, rownum RN
		from (
		select * from PAS_ROLE
		<where>
			<if test="name != null and name !=''">
				AND NAME like '%'||#{name,jdbcType=VARCHAR}||'%'
			</if>
			and ROLE_ID != 0
		</where>
		order by ROLE_ID desc
		) A
		where rownum &lt;= #{endRowNo,jdbcType=DECIMAL}
		)
		where RN &gt;= #{beginRowNo,jdbcType=DECIMAL}

	</select>

	<insert id="insert" parameterType="com.epaylinks.efps.pas.acl.po.Role">
		insert into PAS_ROLE (
			ROLE_ID, NAME, REMARK ,CODE,DATA_AUTH,OPERATE_TIME,OPERATER
		)
		values (
			#{uid,jdbcType=DECIMAL}, #{name,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR} ,
		    #{code,jdbcType=VARCHAR},#{dataAuth,jdbcType=VARCHAR},#{operateTime,jdbcType=TIMESTAMP},
		        #{operater,jdbcType=VARCHAR}
		)
	</insert>
	<insert id="insertSelective" parameterType="com.epaylinks.efps.pas.acl.po.Role">
		insert into PAS_ROLE
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="uid != null">
				ROLE_ID,
			</if>
			<if test="name != null">
				NAME,
			</if>
			<if test="remark != null">
				REMARK,
			</if>
			<if test="code != null">
				CODE,
			</if>
			<if test="dataAuth != null">
				DATA_AUTH,
			</if>
			<if test="operateTime != null">
				OPERATE_TIME,
			</if>
			<if test="operater != null">
				OPERATER,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="uid != null">
				#{uid,jdbcType=DECIMAL},
			</if>
			<if test="name != null">
				#{name,jdbcType=VARCHAR},
			</if>
			<if test="remark != null">
				#{remark,jdbcType=VARCHAR},
			</if>
			<if test="code != null">
				#{code,jdbcType=VARCHAR},
			</if>
			<if test="dataAuth != null">
				#{dataAuth,jdbcType=VARCHAR},
			</if>
			<if test="operateTime != null">
				#{operateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="operater != null">
				#{operater,jdbcType=VARCHAR},
			</if>
		</trim>
	</insert>
	<update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.acl.po.Role">
		update PAS_ROLE
		<set>
			<if test="name != null">
				NAME = #{name,jdbcType=VARCHAR},
			</if>
			<if test="remark != null">
				REMARK = #{remark,jdbcType=VARCHAR},
			</if>
			<if test="dataAuth != null">
				DATA_AUTH = #{dataAuth,jdbcType=VARCHAR},
			</if>
			<if test="operateTime != null">
				OPERATE_TIME = #{operateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="operater != null">
				OPERATER = #{operater,jdbcType=VARCHAR},
			</if>
		</set>
		where ROLE_ID = #{uid,jdbcType=DECIMAL}
	</update>
	<update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.acl.po.Role">
		update PAS_ROLE
		set NAME = #{name,jdbcType=VARCHAR},
		REMARK = #{remark,jdbcType=VARCHAR},
		DATA_AUTH = #{dataAuth,jdbcType=VARCHAR},
		OPERATE_TIME = #{operateTime,jdbcType=TIMESTAMP},
		OPERATER = #{operater,jdbcType=VARCHAR}
		where ROLE_ID = #{uid,jdbcType=DECIMAL}
	</update>
	<!-- query list -->
	<select id="queryList" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from PAS_ROLE
	</select>
	<!-- add permissons for role: use batchInsert method -->
	<insert id="addPermissons" parameterType="java.util.Map">
		BEGIN
		<foreach collection="permissionIds" item="permId" index="index" separator="">
			insert into PAS_ROLE_PERM
			(ROLE_ID, PERM_ID)
			VALUES
			(
			#{roleId,jdbcType=DECIMAL},
			#{permId,jdbcType=DECIMAL} );
		</foreach>
		COMMIT;
		END;
	</insert>

	<select id="selectBySelective" resultMap="BaseResultMap"
			parameterType="java.util.Map">
		select
		<include refid="Base_Column_List" />
		from PAS_ROLE
		<where>
			<if test="name != null and name !=''">
				AND NAME like '%'||#{name,jdbcType=VARCHAR}||'%'
			</if>
		</where>
	</select>

	<select id="selectRolesBySelective" resultMap="BaseResultMap"
			parameterType="java.util.Map">
		select count(u.user_id) as user_count,r.name,r.remark,r.role_id from pas_role r left join pas_role_user u
		on r.role_id = u.role_id
		<where>
			<if test="name != null and name !=''">
				AND r.NAME like '%'||#{name,jdbcType=VARCHAR}||'%'
			</if>
			<choose>
				<when test='dataAuth != null and dataAuth =="0"'>
					AND (r.DATA_AUTH = '0' or r.DATA_AUTH is null)
				</when>
				<when test='dataAuth != null and dataAuth =="1"'>
					AND r.DATA_AUTH = '1'
				</when>
			</choose>
			<if test="beginTime != null and  beginTime != ''">
				<![CDATA[ AND OPERATE_TIME >= TO_DATE(#{beginTime,jdbcType=VARCHAR},'yyyyMMddhh24miss')]]>
			</if>
			<if test="endTime != null and  endTime != ''">
				<![CDATA[ AND OPERATE_TIME <= TO_DATE(#{endTime,jdbcType=VARCHAR},'yyyyMMddhh24miss')]]>
			</if>
			and r.ROLE_ID != 0
		</where>
		group by r.name,r.remark,r.role_id
	</select>

	<select id="selectRolesByPage" resultMap="BaseResultMap"
				parameterType="java.util.Map">
			select
			<include refid="Base_Column_List1" />
			from (
			select A.*, rownum RN
			from (
		select count(u.user_id) as user_count,r.name,r.remark,r.role_id,r.code,r.DATA_AUTH,r.OPERATE_TIME,r.OPERATER
		from pas_role r left join pas_role_user u
		on r.role_id = u.role_id
		<where>
				<if test="name != null and name !=''">
					AND r.NAME like '%'||#{name,jdbcType=VARCHAR}||'%'
				</if>
				<choose>
					<when test='dataAuth != null and dataAuth =="0"'>
						AND (r.DATA_AUTH = '0' or r.DATA_AUTH is null)
					</when>
					<when test='dataAuth != null and dataAuth =="1"'>
						AND r.DATA_AUTH = '1'
					</when>
				</choose>
				<if test="beginTime != null and  beginTime != ''">
					<![CDATA[ AND OPERATE_TIME >= TO_DATE(#{beginTime,jdbcType=VARCHAR},'yyyyMMddhh24miss')]]>
				</if>
				<if test="endTime != null and  endTime != ''">
					<![CDATA[ AND OPERATE_TIME <= TO_DATE(#{endTime,jdbcType=VARCHAR},'yyyyMMddhh24miss')]]>
				</if>
				and r.role_id != 0
		</where>
		group by r.name,r.remark,r.role_id,r.code,r.DATA_AUTH,r.OPERATE_TIME,r.OPERATER
			order by r.role_id desc
			) A
			where rownum &lt;= #{endRowNo,jdbcType=DECIMAL}
			)
			where RN &gt;= #{beginRowNo,jdbcType=DECIMAL}

	</select>

	<select id="checkExistOfRoleName" resultType="Boolean">
		select count(*)
		from PAS_ROLE
		where NAME = #{name,jdbcType=VARCHAR}
	</select>

	<select id="checkExistOfRoleNameWhenModify" resultType="Boolean">
		select count(*)
		from PAS_ROLE
		where NAME = #{name,jdbcType=VARCHAR} and ROLE_ID != #{roleId,jdbcType=DECIMAL}
	</select>

	<select id="selectByRoleCode" parameterType="java.lang.String" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from PAS_ROLE
		where CODE = #{roleCode,jdbcType=VARCHAR}
	</select>

	<select id="selectRoleDataAuthByUserId" resultType="com.epaylinks.efps.pas.acl.dto.UserRoleDataAuthDTO">
		select ru.user_id userId,
		       u.name userName,
		       u.real_name userRealName,
		       ru.role_id roleId,
		       r.name roleName,
		       r.data_auth dataAuth
		from pas_role_user ru
		left join pas_role r on ru.role_id = r.role_id
		left join pas_user u on ru.user_id = u.user_id
		where ru.user_id = #{userId,jdbcType=DECIMAL}
		  <if test="dataAuth != null and dataAuth != ''">
			  and r.data_auth != #{dataAuth,jdbcType=VARCHAR}
		  </if>
	</select>

	<select id="judgeRoleDataByRoleIds" resultType="java.lang.Boolean">
		select count(1)
		from pas_role r
		where r.role_id in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
		and r.data_auth != #{dataAuth,jdbcType=VARCHAR}
</select>
</mapper>