<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.pas.acl.dao.UserMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.acl.po.User">
    <id column="USER_ID" jdbcType="DECIMAL" property="uid" />
    <result column="DEPT_ID" jdbcType="DECIMAL" property="deptId" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="REAL_NAME" jdbcType="VARCHAR" property="realName" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="PASSWORD" jdbcType="VARCHAR" property="password" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATOR" jdbcType="VARCHAR" property="updator" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="EMAIL" jdbcType="VARCHAR" property="email" />
    <result column="MOBILE" jdbcType="VARCHAR" property="mobile" />
    <result column="STATUS" jdbcType="CHAR" property="status" />
    <result column="COMPANY_ID" jdbcType="DECIMAL" property="companyId" />
    <result column="SALES_CODE" jdbcType="DECIMAL" property="salesCode" />
    <result column="USER_TYPE" jdbcType="DECIMAL" property="userType" />
    <result column="PASSWORD_EXPIRED_TIME" jdbcType="TIMESTAMP" property="passwordExpiredTime" />
    <result column="PASSWORD_UPDATE_TIME" jdbcType="TIMESTAMP" property="passwordUpdateTime" />
    <result column="CAN_DECRYPT" jdbcType="DECIMAL" property="canDecrypt" />
    <result column="EN_MOBILE" jdbcType="VARCHAR" property="enMobile" />
    <result column="EN_EMAIL" jdbcType="VARCHAR" property="enEmail" />
    <result column="PIECE_LINK" jdbcType="VARCHAR" property="pieceLink" />
    <result column="PICEC_LINK_STATUS" jdbcType="DECIMAL" property="picecLinkStatus" />
      <result column="HASH_MOBILE" property="hashMobile" jdbcType="VARCHAR" />
      <result column="HASH_EMAIL" property="hashEmail" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List">
    USER_ID, DEPT_ID, NAME, REAL_NAME, REMARK, PASSWORD, CREATOR, CREATE_TIME, UPDATOR, 
    UPDATE_TIME, EMAIL, MOBILE, STATUS, COMPANY_ID, SALES_CODE, USER_TYPE, PASSWORD_EXPIRED_TIME, 
    PASSWORD_UPDATE_TIME, CAN_DECRYPT, EN_MOBILE, EN_EMAIL, PIECE_LINK, PICEC_LINK_STATUS,HASH_MOBILE, HASH_EMAIL
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from PAS_USER
    where USER_ID = #{userId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from PAS_USER
    where USER_ID = #{userId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.acl.po.User">
    insert into PAS_USER (USER_ID, DEPT_ID, NAME, 
      REAL_NAME, REMARK, PASSWORD, 
      CREATOR, CREATE_TIME, UPDATOR, 
      UPDATE_TIME, EMAIL, MOBILE, 
      STATUS, COMPANY_ID, SALES_CODE, 
      USER_TYPE, PASSWORD_EXPIRED_TIME, PASSWORD_UPDATE_TIME, 
      CAN_DECRYPT, EN_MOBILE, EN_EMAIL, 
      PIECE_LINK, PICEC_LINK_STATUS,HASH_MOBILE, HASH_EMAIL
      )
    values (#{uid,jdbcType=DECIMAL}, #{deptId,jdbcType=DECIMAL}, #{name,jdbcType=VARCHAR}, 
      #{realName,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{password,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updator,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{email,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR}, 
      #{status,jdbcType=CHAR}, #{companyId,jdbcType=DECIMAL}, #{salesCode,jdbcType=DECIMAL}, 
      #{userType,jdbcType=DECIMAL}, #{passwordExpiredTime,jdbcType=TIMESTAMP}, #{passwordUpdateTime,jdbcType=TIMESTAMP}, 
      #{canDecrypt,jdbcType=DECIMAL}, #{enMobile,jdbcType=VARCHAR}, #{enEmail,jdbcType=VARCHAR}, 
      #{pieceLink,jdbcType=VARCHAR}, #{picecLinkStatus,jdbcType=DECIMAL},
      #{hashMobile,jdbcType=VARCHAR}, #{hashEmail,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.acl.po.User">
    insert into PAS_USER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="uid != null">
        USER_ID,
      </if>
      <if test="deptId != null">
        DEPT_ID,
      </if>
      <if test="name != null">
        NAME,
      </if>
      <if test="realName != null">
        REAL_NAME,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="password != null">
        PASSWORD,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updator != null">
        UPDATOR,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="email != null">
        EMAIL,
      </if>
      <if test="mobile != null">
        MOBILE,
      </if>
      <if test="status != null">
        STATUS,
      </if>
      <if test="companyId != null">
        COMPANY_ID,
      </if>
      <if test="salesCode != null">
        SALES_CODE,
      </if>
      <if test="userType != null">
        USER_TYPE,
      </if>
      <if test="passwordExpiredTime != null">
        PASSWORD_EXPIRED_TIME,
      </if>
      <if test="passwordUpdateTime != null">
        PASSWORD_UPDATE_TIME,
      </if>
      <if test="canDecrypt != null">
        CAN_DECRYPT,
      </if>
      <if test="enMobile != null">
        EN_MOBILE,
      </if>
      <if test="enEmail != null">
        EN_EMAIL,
      </if>
      <if test="pieceLink != null">
        PIECE_LINK,
      </if>
      <if test="picecLinkStatus != null">
        PICEC_LINK_STATUS,
      </if>
        <if test="hashMobile != null" >
            HASH_MOBILE,
        </if>
        <if test="hashEmail != null" >
            HASH_EMAIL,
        </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="uid != null">
        #{uid,jdbcType=DECIMAL},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=DECIMAL},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="realName != null">
        #{realName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        #{password,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null">
        #{updator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=CHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=DECIMAL},
      </if>
      <if test="salesCode != null">
        #{salesCode,jdbcType=DECIMAL},
      </if>
      <if test="userType != null">
        #{userType,jdbcType=DECIMAL},
      </if>
      <if test="passwordExpiredTime != null">
        #{passwordExpiredTime,jdbcType=TIMESTAMP},
      </if>
      <if test="passwordUpdateTime != null">
        #{passwordUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="canDecrypt != null">
        #{canDecrypt,jdbcType=DECIMAL},
      </if>
      <if test="enMobile != null">
        #{enMobile,jdbcType=VARCHAR},
      </if>
      <if test="enEmail != null">
        #{enEmail,jdbcType=VARCHAR},
      </if>
      <if test="pieceLink != null">
        #{pieceLink,jdbcType=VARCHAR},
      </if>
      <if test="picecLinkStatus != null">
        #{picecLinkStatus,jdbcType=DECIMAL},
      </if>
        <if test="hashMobile != null" >
            #{hashMobile,jdbcType=VARCHAR},
        </if>
        <if test="hashEmail != null" >
            #{hashEmail,jdbcType=VARCHAR},
        </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.acl.po.User">
    update PAS_USER
    <set>
      <if test="deptId != null">
        DEPT_ID = #{deptId,jdbcType=DECIMAL},
      </if>
      <if test="name != null">
        NAME = #{name,jdbcType=VARCHAR},
      </if>
      <if test="realName != null">
        REAL_NAME = #{realName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        PASSWORD = #{password,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null">
        UPDATOR = #{updator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="email != null">
        EMAIL = #{email,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        MOBILE = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        STATUS = #{status,jdbcType=CHAR},
      </if>
      <if test="companyId != null">
        COMPANY_ID = #{companyId,jdbcType=DECIMAL},
      </if>
      <if test="salesCode != null">
        SALES_CODE = #{salesCode,jdbcType=DECIMAL},
      </if>
      <if test="userType != null">
        USER_TYPE = #{userType,jdbcType=DECIMAL},
      </if>
      <if test="passwordExpiredTime != null">
        PASSWORD_EXPIRED_TIME = #{passwordExpiredTime,jdbcType=TIMESTAMP},
      </if>
      <if test="passwordUpdateTime != null">
        PASSWORD_UPDATE_TIME = #{passwordUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="canDecrypt != null">
        CAN_DECRYPT = #{canDecrypt,jdbcType=DECIMAL},
      </if>
      <if test="enMobile != null">
        EN_MOBILE = #{enMobile,jdbcType=VARCHAR},
      </if>
      <if test="enEmail != null">
        EN_EMAIL = #{enEmail,jdbcType=VARCHAR},
      </if>
      <if test="pieceLink != null">
        PIECE_LINK = #{pieceLink,jdbcType=VARCHAR},
      </if>
      <if test="picecLinkStatus != null">
        PICEC_LINK_STATUS = #{picecLinkStatus,jdbcType=DECIMAL},
      </if>
        <if test="hashMobile != null" >
            HASH_MOBILE = #{hashMobile,jdbcType=VARCHAR},
        </if>
        <if test="hashEmail != null" >
            HASH_EMAIL = #{hashEmail,jdbcType=VARCHAR},
        </if>
    </set>
    where USER_ID = #{uid,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.acl.po.User">
    update PAS_USER
    set DEPT_ID = #{deptId,jdbcType=DECIMAL},
      NAME = #{name,jdbcType=VARCHAR},
      REAL_NAME = #{realName,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      PASSWORD = #{password,jdbcType=VARCHAR},
      CREATOR = #{creator,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATOR = #{updator,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      EMAIL = #{email,jdbcType=VARCHAR},
      MOBILE = #{mobile,jdbcType=VARCHAR},
      STATUS = #{status,jdbcType=CHAR},
      COMPANY_ID = #{companyId,jdbcType=DECIMAL},
      SALES_CODE = #{salesCode,jdbcType=DECIMAL},
      USER_TYPE = #{userType,jdbcType=DECIMAL},
      PASSWORD_EXPIRED_TIME = #{passwordExpiredTime,jdbcType=TIMESTAMP},
      PASSWORD_UPDATE_TIME = #{passwordUpdateTime,jdbcType=TIMESTAMP},
      CAN_DECRYPT = #{canDecrypt,jdbcType=DECIMAL},
      EN_MOBILE = #{enMobile,jdbcType=VARCHAR},
      EN_EMAIL = #{enEmail,jdbcType=VARCHAR},
      PIECE_LINK = #{pieceLink,jdbcType=VARCHAR},
      PICEC_LINK_STATUS = #{picecLinkStatus,jdbcType=DECIMAL},
      HASH_MOBILE = #{hashMobile,jdbcType=VARCHAR},
      HASH_EMAIL = #{hashEmail,jdbcType=VARCHAR}
    where USER_ID = #{uid,jdbcType=DECIMAL}
  </update>
  
    <resultMap id="ResultMapWithName" type="com.epaylinks.efps.pas.acl.vo.UserVo" extends="BaseResultMap" >
        <result column="DEPT_NAME" jdbcType="VARCHAR" property="deptName" />
    </resultMap>
    <sql id="Name_Column_List">
        DEPT_NAME
    </sql>
    
    <resultMap id="ResultMapWithRoleInfo" type="com.epaylinks.efps.pas.acl.vo.UserVo" extends="BaseResultMap" >
        <result column="ROLE_ID" jdbcType="VARCHAR" property="roleId" />
        <result column="ROLE_NAME" jdbcType="VARCHAR" property="roleName" />
    </resultMap>

	<!-- 查找用户 -->
	<select id="selectByName" parameterType="java.lang.String"
		resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from PAS_USER
		where NAME = #{name,jdbcType=VARCHAR}
	</select>
	

	<select id="selectBySelective" resultMap="BaseResultMap"
			parameterType="java.util.Map">
		select
		<include refid="Base_Column_List" />
		from PAS_USER
		<where>
			<if test="name != null and name !=''">
				AND NAME like '%'||#{name,jdbcType=VARCHAR}||'%'
			</if>
			<if test="realName != null and realName !=''">
				AND REAL_NAME like '%'||#{realName,jdbcType=VARCHAR}||'%'
			</if>
			<if test="status != null and status !=''">
				AND STATUS = #{status,jdbcType=VARCHAR}
			</if>
		</where>
	</select>

	<select id="getUserByIds" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from PAS_USER where USER_ID IN
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>
	
	<select id="selectEmail" resultType="String">
		select EMAIL
		from PAS_USER
		where USER_ID = #{userId,jdbcType=DECIMAL}
	</select>
	
	<select id="selectNamesByIds" resultMap="BaseResultMap">
		select
		USER_ID, NAME
		from PAS_USER where USER_ID IN
		<foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<select id="selectByParam" resultType="java.lang.Integer" parameterType="java.util.Map">
		select  count(*)
		from pas_user u
	    left join PAS_COMPANY c on u.COMPANY_ID = c.COMPANY_ID
        left join (
          select
            distinct pru.user_id userId,pr.data_auth
          from pas_role_user pru
          left join pas_role pr on pru.role_id = pr.role_id
          left join pas_user pu on pru.user_id = pu.user_id
        )rru on u.user_id = rru.userId
        <where>
            <include refid="queryUserListCondition" />
        </where>
	</select>

	<select id="selectByParamByPage" resultMap="ResultMapWithName"  parameterType="java.util.Map">
		select
		<include refid="Base_Column_List"/>,
        <include refid="Name_Column_List"/>,
        dataAuth
		from (
		select A.*, rownum RN
		from (
    		select
    		u.USER_ID, u.company_id DEPT_ID, u.NAME, u.REAL_NAME, u.REMARK, u.PASSWORD, u.CREATOR,
    		u.CREATE_TIME,
    		u.UPDATOR, u.UPDATE_TIME, u.EMAIL , u.MOBILE , u.STATUS ,u.COMPANY_ID, u.SALES_CODE,u.USER_TYPE,
            u.PASSWORD_EXPIRED_TIME, u.PASSWORD_UPDATE_TIME,u.CAN_DECRYPT,u.EN_MOBILE,u.EN_EMAIL,u.HASH_EMAIL,u.HASH_MOBILE,
            u.PIECE_LINK, u.PICEC_LINK_STATUS,
            c.name as dept_name,
		    rru.data_auth dataAuth
		  from pas_user u
	    left join PAS_COMPANY c on u.COMPANY_ID = c.COMPANY_ID
	    left join (
                select
                    distinct pu.user_id userId,pr.data_auth
                from pas_role_user pru
                left join pas_role pr on pru.role_id = pr.role_id
                left join pas_user pu on pru.user_id = pu.user_id
            )rru on u.user_id = rru.userId
		<where>
            <include refid="queryUserListCondition" />
        </where>
		  order by u.user_id desc
		) A
		where rownum &lt;= #{endRowNo,jdbcType=DECIMAL}
		)
		where RN &gt;= #{beginRowNo,jdbcType=DECIMAL}
	</select>

    <sql id="queryUserListCondition">
        u.USER_ID > 0
        <if test="startTime != null and startTime != ''">
            AND <![CDATA[ u.CREATE_TIME >= TO_DATE(#{startTime,jdbcType=VARCHAR},'yyyyMMdd')]]>
        </if>
        <if test="endTime != null and endTime != ''">
            AND <![CDATA[ u.CREATE_TIME <= TO_DATE(#{endTime,jdbcType=VARCHAR},'yyyyMMdd')]]>
        </if>
        <if test="uid != null">
            AND u.user_id = #{uid,jdbcType=DECIMAL}
        </if>
        <if test="name != null and name !=''">
            AND u.NAME like '%'||#{name,jdbcType=VARCHAR}||'%'
        </if>
        <if test="realName != null and realName !=''">
            AND u.REAL_NAME like '%'||#{realName,jdbcType=VARCHAR}||'%'
        </if>
        <if test="status != null and status !=''">
            AND u.STATUS = #{status,jdbcType=VARCHAR}
        </if>
        <if test="roleName != null and roleName !=''">
            AND u.USER_ID in (
                select ru.user_id from pas_role_user ru , pas_role r
                where ru.role_id = r.role_id and r.name like '%'||#{roleName,jdbcType=VARCHAR}||'%'
            )
        </if>
        <if test="deptName != null and deptName !=''">
            AND c.name like '%'||#{deptName,jdbcType=VARCHAR}||'%'
        </if>
        <if test="companyId != null">
            AND u.COMPANY_ID = #{companyId,jdbcType=DECIMAL}
        </if>
        <if test="userType != null">
            AND u.USER_TYPE = #{userType,jdbcType=DECIMAL}
        </if>
        <if test="parentCompanyId != null">
            AND u.company_id in (
                select company_id from pas_company
                start with company_id = #{parentCompanyId,jdbcType=DECIMAL}
                connect by  parent_id = prior company_id
            )
        </if>
        <if test="companyStatus != null">
            AND c.STATUS = #{companyStatus,jdbcType=DECIMAL}
        </if>
        <if test="canDecrypt != null">
            AND u.CAN_DECRYPT = #{canDecrypt,jdbcType=DECIMAL}
        </if>
        <if test="salesCode != null and salesCode != ''">
            AND u.SALES_CODE = #{salesCode}
        </if>
        <if test="dataAuth != null and dataAuth != ''">
            AND EXISTS (
                select
                    distinct pu.user_id,pr.data_auth
                from pas_role_user pru
                left join pas_role pr on pru.role_id = pr.role_id
                left join pas_user pu on pru.user_id = pu.user_id
                WHERE pr.data_auth = #{dataAuth,jdbcType=VARCHAR} AND pru.user_id = u.user_id
            )
        </if>
    </sql>

	<select id="selectRoleBySelective" resultMap="ResultMapWithRoleInfo"
			parameterType="com.epaylinks.efps.pas.acl.po.User">
		select r.role_id,r.name as role_name,u.user_id from pas_role r, pas_role_user u where r.role_id =u.role_id
		<if test="uid != null">
			AND u.user_id = #{uid,jdbcType=DECIMAL}
		</if>
		order by u.user_id desc
	</select>

	<select id="checkExistOfName" resultType="Boolean">
		select count(*)
		from PAS_USER
		where NAME = #{name,jdbcType=VARCHAR}
	 </select>
	 
	 <select id="checkExistOfRealName" resultType="Boolean">
		select count(*)
		from PAS_USER
		where REAL_NAME = #{realName,jdbcType=VARCHAR}
		<if test="selfUserId != null">
			AND user_id != #{selfUserId,jdbcType=DECIMAL}
		</if>
	 </select>
	 
	<select id="checkExistOfPhone" resultType="Boolean">
		select count(*)
		from PAS_USER
		where HASH_MOBILE = #{phone,jdbcType=VARCHAR}
	 </select>

	<select id="checkExistOfPhoneWhenModify" resultType="Boolean">
		select count(*)
		from PAS_USER
		where HASH_MOBILE = #{phone,jdbcType=VARCHAR} and USER_ID != #{userId,jdbcType=DECIMAL}
	</select>

	<select id="selectUserByParam" resultMap="BaseResultMap" parameterType="java.util.Map">
			select
			<include refid="Base_Column_List" />
			from PAS_USER
			<where>
				<if test="userId != null ">
					AND user_id = #{userId,jdbcType=DECIMAL}
				</if>
				<if test="name != null and name !=''">
					AND name = #{name,jdbcType=VARCHAR}
				</if>
				<if test="mobile != null and mobile !=''">
					AND HASH_MOBILE = #{mobile,jdbcType=VARCHAR}
				</if>
				<if test="realName != null and realName !=''">
					AND REAL_NAME like '%'||#{realName,jdbcType=VARCHAR}||'%'
				</if>
				<if test="status != null and status !=''">
					AND STATUS = #{status,jdbcType=VARCHAR}
				</if>
				<if test="companyId != null">
				  	AND COMPANY_ID = #{companyId,jdbcType=DECIMAL}
				</if>
				<if test="userType != null">
				  	AND USER_TYPE = #{userType,jdbcType=DECIMAL}
				</if>
			</where>
		</select>

	<select id="queryByUserIdList" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from PAS_USER
		where user_id in
		<foreach item="item" collection="userIdList" open="(" separator="," close=")">#{item}</foreach>
	</select>
	<select id="selectListByName" resultMap="BaseResultMap"	>
		select  *
		from PAS_USER
		where (name like  '%'||#{name,jdbcType=VARCHAR}||'%' or REAL_NAME like '%'||#{name,jdbcType=VARCHAR}||'%')
	</select>
	
	<select id="searchUser" resultMap="BaseResultMap"	parameterType="java.util.Map">
		select
		<include refid="Base_Column_List"/>
		from (
		select A.*, rownum RN from (
			select *
			from pas_user u
			<where>
				<if test="key != null and key !=''">
			 		(name like  '%'||#{key,jdbcType=VARCHAR}||'%' or REAL_NAME like '%'||#{key,jdbcType=VARCHAR}||'%')
		 		</if>		
		 		<if test="companyId != null">
				  	AND company_id in (
				  		select company_id from pas_company
						start with company_id = #{companyId,jdbcType=DECIMAL}
						connect by  parent_id = prior company_id
				  	)
				</if>
				<if test="userId != null ">
					AND user_id = #{userId,jdbcType=DECIMAL}
				</if>
				<if test="userType != null">
				  	AND USER_TYPE = #{userType,jdbcType=DECIMAL}
				</if>
				<if test="status != null and status !=''">
					AND STATUS = #{status,jdbcType=VARCHAR}
				</if>
			</where>
		) A
		where rownum &lt;= #{endRowNo,jdbcType=DECIMAL}
		)
		where RN &gt;= #{beginRowNo,jdbcType=DECIMAL}
	</select>	
	
	<select id="listCompanyUser" resultMap="BaseResultMap"	parameterType="java.lang.Long">
		select
		<include refid="Base_Column_List"/>
		from pas_user 
		where user_type = 2
         and company_id in (
	  		select company_id from pas_company
			start with company_id = #{companyId,jdbcType=DECIMAL}
			connect by  parent_id = prior company_id
	  	)
	</select>
	
   <select id="selectUserByRealParam" resultMap="BaseResultMap" >
        select      
        <include refid="Base_Column_List"/>
        from PAS_USER
        where REAL_NAME = #{realName,jdbcType=VARCHAR}
   </select>

   <select id="randomOneByCompany" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from
        ( select t.* from pas_user t where t.company_id = #{companyId,jdbcType=DECIMAL} and t.status = 'Y' and t.user_type = 2 order by dbms_random.random() )
        where rownum = 1
    </select>
   
    <select id="queryCustLoginName" resultType="java.lang.String">
       select t.username from cust_user t where t.user_id = #{companyId,jdbcType=DECIMAL} 
     </select>
     
    <select id="queryLockedUserList" resultMap="BaseResultMap" >
        select 
        <include refid="Base_Column_List" />
        from (
            select t.*, rownum rn  from pas_user t where t.status = 'L' and t.update_time > sysdate -1/24
        )
        where rn &lt;= #{count,jdbcType=DECIMAL}
    </select>
    
    <select id="queryUserIdsByPermId" resultType="java.lang.Long" >
        select distinct (t1.user_id)
          from pas_user u, pas_role_user t1, pas_role_perm t2
         where t1.role_id = t2.role_id
           and t1.user_id = u.user_id
           and t2.perm_id = #{permId,jdbcType=DECIMAL}
           <if test="userId != null ">
               and t1.role_id not in (
                   select t3.role_id from pas_role_user t3 where t3.user_id = #{userId,jdbcType=DECIMAL}
               )
           </if>
    </select>

    <select id="countAllCustUser" resultType="java.lang.Integer">
        select count(1) from pas_user
    </select>
    <select id="queryAllCustUserByPage" resultMap="BaseResultMap" parameterType="java.util.Map">
        select *
        from (
        select A.*, rownum RN
        from (
        SELECT
        <include refid="Base_Column_List" />
        FROM pas_user t
        ORDER BY user_id
        ) A
        where rownum &lt;= #{endRowNo,jdbcType=DECIMAL}
        )
        where RN &gt;= #{beginRowNo,jdbcType=DECIMAL}
    </select>

    <select id="queryUserByName" parameterType="java.lang.String" resultType="java.util.Map">
        select * from (
            select USER_ID, NAME, REAL_NAME
            FROM pas_user
            where 1=1
            and STATUS = 'Y'
            <if test="name != null and name != ''">
                and (name like '%' || #{name} || '%' or REAL_NAME like '%' || #{name} || '%')
            </if>
            order by user_id desc
        ) where rownum &lt;= 10

    </select>

    <select id="selectUserBySalesCode" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List"/>
        from PAS_USER
        where SALES_CODE = #{salesCode,jdbcType=DECIMAL}
    </select>

    <select id="queryCompanyUrlByUserId" parameterType="java.lang.Long" resultType="java.lang.String">
        select c.remark
        from pas_user u
        left join pas_company c on u.company_id = c.company_id
        where u.user_id = #{userId,jdbcType=DECIMAL}
    </select>
</mapper>