<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.pas.acl.dao.RolePermMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.acl.po.RolePerm">
    <result column="ID" jdbcType="DECIMAL" property="id" />
    <result column="ROLE_ID" jdbcType="DECIMAL" property="roleId" />
    <result column="PERM_ID" jdbcType="DECIMAL" property="permId" />
  </resultMap>
  <select id="existByPermId" parameterType="java.lang.Long" resultType="java.lang.Integer">
    select COUNT(*)
    from PAS_ROLE_PERM
    where PERM_ID = #{uid,jdbcType=DECIMAL}
  </select>
  <select id="selectByRoleId" parameterType="java.lang.Long" resultType="java.lang.Long">
    select PERM_ID  from PAS_ROLE_PERM
    where ROLE_ID = #{uid,jdbcType=DECIMAL}
  </select>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.acl.po.RolePerm">
    insert into PAS_ROLE_PERM (ID,ROLE_ID, PERM_ID)
    values (#{id,jdbcType=DECIMAL},#{roleId,jdbcType=DECIMAL}, #{permId,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.acl.po.RolePerm">
    insert into PAS_ROLE_PERM
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="roleId != null">
        ROLE_ID,
      </if>
      <if test="permId != null">
        PERM_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="roleId != null">
        #{roleId,jdbcType=DECIMAL},
      </if>
      <if test="permId != null">
        #{permId,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <delete id="deleteByRoleId">
    DELETE FROM PAS_ROLE_PERM WHERE ROLE_ID = #{roleId,jdbcType=DECIMAL}
  </delete>
  <delete id="deleteByPermIds">
    delete from PAS_ROLE_PERM
    where PERM_ID in
    <foreach item="item" collection="permIds" open="(" separator="," close=")">#{item}</foreach>
  </delete>
</mapper>