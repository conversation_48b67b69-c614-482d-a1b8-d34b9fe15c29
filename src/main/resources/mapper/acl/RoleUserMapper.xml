<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.pas.acl.dao.RoleUserMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.acl.po.RoleUser">
    <result column="USER_ID" jdbcType="DECIMAL" property="userId" />
    <result column="ROLE_ID" jdbcType="DECIMAL" property="roleId" />
  </resultMap>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.acl.po.RoleUser">
    insert into PAS_ROLE_USER (USER_ID, ROLE_ID)
    values (#{userId,jdbcType=DECIMAL}, #{roleId,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.acl.po.RoleUser">
    insert into PAS_ROLE_USER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="roleId != null">
        ROLE_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=DECIMAL},
      </if>
      <if test="roleId != null">
        #{roleId,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>

  <update id="update" parameterType="com.epaylinks.efps.pas.acl.po.RoleUser">
    update PAS_ROLE_USER
    set ROLE_ID = #{roleId,jdbcType=DECIMAL}
    where USER_ID = #{userId,jdbcType=DECIMAL}
  </update>

  <select id="selectRoleIdByUserId" parameterType="java.lang.Long" resultType="java.lang.Long">
  	select ROLE_ID
    from PAS_ROLE_USER
    where USER_ID = #{userId,jdbcType=DECIMAL}
  </select>

  <delete id="deleteByUserId"  parameterType="java.lang.Long">
    DELETE FROM PAS_ROLE_USER WHERE USER_ID = #{userId,jdbcType=DECIMAL}
  </delete>

  <select id="selectRoleCodeByUserId" parameterType="java.lang.Long" resultType="java.lang.String">
    select wm_concat(r.code) from pas_role r where r.role_id in (
      select ru.role_id from pas_role_user ru where ru.user_id = #{userId,jdbcType=DECIMAL}
    )
  </select>
  
  <select id="selectRoleNameByUserId" parameterType="java.lang.Long" resultType="java.lang.String">
    select wm_concat(r.name) from pas_role r where r.role_id in (
      select ru.role_id from pas_role_user ru where ru.user_id = #{userId,jdbcType=DECIMAL}
    )
  </select>

  <select id="selectRoleIdsByUser" parameterType="java.lang.Long" resultMap="BaseResultMap">
     select ROLE_ID
     from PAS_ROLE_USER
     where USER_ID = #{userId,jdbcType=DECIMAL}
  </select>

  <select id="selectUerIdsByRoleId" parameterType="java.lang.Long" resultType="java.lang.Long">
    select USER_ID
    from PAS_ROLE_USER
    where ROLE_ID = #{roleId,jdbcType=DECIMAL}
  </select>

  <select id="checkIsSuperAdmin" resultType="Boolean">
      select count(*)
      from PAS_ROLE_USER
      where USER_ID = #{userId,jdbcType=DECIMAL} and ROLE_ID = 0
  </select>

  <delete id="deleteByRoleId">
    DELETE FROM PAS_ROLE_USER WHERE ROLE_ID = #{roleId,jdbcType=DECIMAL}
  </delete>

  <select id="selectCheckRole" resultType="java.lang.Integer">
    select count(*) from pas_role_user
    where user_id = #{userId,jdbcType=DECIMAL}
      and role_id = #{roleId,jdbcType=DECIMAL}
  </select>
</mapper>