<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.pas.acl.dao.PermMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.acl.po.Perm">
    <id column="PERM_ID" jdbcType="DECIMAL" property="permId" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="PARENT_ID" jdbcType="DECIMAL" property="parentId" />
    <result column="ALIAS" jdbcType="VARCHAR" property="alias" />
      <result column="URL" jdbcType="VARCHAR" property="url" />
      <result column="PERM_SORT" jdbcType="DECIMAL" property="permSort" />
  </resultMap>
  <sql id="Base_Column_List">
    PERM_ID, NAME, PARENT_ID, ALIAS, URL  ,PERM_SORT
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from PAS_PERM
    where PERM_ID = #{permId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from PAS_PERM
    where PERM_ID = #{permId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.acl.po.Perm">
      insert into PAS_PERM (PERM_ID, PARENT_ID,NAME, ALIAS, URL,PERM_SORT)
    values (#{permId,jdbcType=DECIMAL},#{parentId,jdbcType=DECIMAL}, #{name,jdbcType=VARCHAR},
      #{alias,jdbcType=VARCHAR}, #{url,jdbcType=VARCHAR},#{permSort,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.acl.po.Perm">
    insert into PAS_PERM
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="permId != null">
        PERM_ID,
      </if>
      <if test="parentId != null">
        PARENT_ID,
      </if>
      <if test="name != null">
        NAME,
      </if>
      <if test="alias != null">
        ALIAS,
      </if>
      <if test="url != null">
        URL,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="permId != null">
        #{permId,jdbcType=DECIMAL},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="alias != null">
        #{alias,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        #{url,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.acl.po.Perm">
    update PAS_PERM
    <set>
      <if test="name != null">
        NAME = #{name,jdbcType=VARCHAR},
      </if>
      <if test="alias != null">
        ALIAS = #{alias,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        URL = #{url,jdbcType=VARCHAR},
      </if>
    </set>
    where PERM_ID = #{permId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.acl.po.Perm">
    update PAS_PERM
    set NAME = #{name,jdbcType=VARCHAR},
      ALIAS = #{alias,jdbcType=VARCHAR},
      URL = #{url,jdbcType=VARCHAR}
    where PERM_ID = #{permId,jdbcType=DECIMAL}
  </update>
  <select id="selectAll" resultMap="BaseResultMap" parameterType="com.epaylinks.efps.pas.acl.po.Perm">
    select * from PAS_PERM   order by perm_sort
  </select>
  <select id="existByName" resultType="java.lang.Integer" parameterType="java.lang.String">
  	select count(*)
    from PAS_PERM where NAME=#{name,jdbcType=VARCHAR}
  </select>
  <select id="selectPermIdsByUserId" parameterType="java.lang.Long" resultType="java.lang.Long">
    select distinct p.perm_id from PAS_PERM p
     start with p.perm_id in (
       select distinct rp.perm_id from pas_role_perm rp where rp.role_id in (
          select ru.role_id from pas_role_user ru where ru.user_id = #{userId,jdbcType=DECIMAL}
        )
    ) connect by prior p.parent_id=p.perm_id
  </select>
  <select id="selectPermIdsByUserIdAndRoleId" resultType="java.lang.Long">
    select distinct p.perm_id from PAS_PERM p
    start with p.perm_id in (
    select distinct rp.perm_id from pas_role_perm rp where rp.role_id in (
    select ru.role_id from pas_role_user ru where ru.user_id = #{userId,jdbcType=DECIMAL}
    <if test="roleId != null and roleId != 0">
      and ru.role_id = #{roleId,jdbcType=DECIMAL}
    </if>
    )
    ) connect by prior p.parent_id=p.perm_id
  </select>
  <select id="selectAllPermIds" resultType="java.lang.Long">
    select perm_id from PAS_PERM    order by perm_sort
  </select>
  <select id="selectPermIdsByRoleId" parameterType="java.lang.Long" resultType="java.lang.Long">
    select perm_id from pas_role_perm where role_id = #{roleId,jdbcType=DECIMAL}
  </select>
  <select id="selectSubPermIds" resultType="java.lang.Long">
    select perm_id from pas_perm start with perm_id=#{permId,jdbcType=DECIMAL} connect by prior perm_id=parent_id
  </select>
  <delete id="deleteByPermIds">
    delete from PAS_PERM
    where PERM_ID in
    <foreach item="item" collection="permIds" open="(" separator="," close=")">#{item}</foreach>
  </delete>
  <select id="selectMaxSubPermId" resultType="java.lang.Long">
    select max(perm_id) from pas_perm where parent_id=#{parentId,jdbcType=DECIMAL}
  </select>
   <select id="getMaxPermId" parameterType="java.lang.Long" resultType="java.lang.Long">
      select max(perm_ID) +1 from PAS_PERM
    </select>

   <select id="selectFullPermNamesById" resultType="String">
        select to_char(wm_concat(alias))
            from (select t.alias
              from pas_perm t
             where t.perm_id != 0 and t.parent_id != 0
             START WITH t.perm_id = #{permId,jdbcType=DECIMAL}
            CONNECT BY t.perm_id = PRIOR t.parent_id
             order by t.perm_id)
   </select>


  <select id="selectCountPasPerm" resultType="java.lang.Integer"
             parameterType="java.util.Map">
         select count(sum(1))
         from PAS_PERM
         <where>
           <if test="permId != null ">
                  AND PERM_ID = #{permId,jdbcType=DECIMAL}
                </if>
                <if test="name != null and name != '' ">
                  AND NAME = #{name,jdbcType=VARCHAR}
                </if>
                <if test="parentId != null  ">
                  AND PARENT_ID = #{parentId,jdbcType=DECIMAL}
                </if>
                <if test="alias != null and alias != '' ">
                  AND ALIAS = #{alias,jdbcType=VARCHAR}
                </if>
                <if test="url != null and url != '' ">
                  AND URL = #{url,jdbcType=VARCHAR}
                </if>
                <if test="permSort != null and permSort != '' ">
                  AND PERM_SORT = #{name,jdbcType=VARCHAR}
                </if>
         </where>
     </select>
  <select id="selectByPage" resultMap="BaseResultMap"
  			parameterType="java.util.Map">
  		select
  		<include refid="Base_Column_List" />
  		from (
  		select A.*, rownum RN
  		from (
  		select * from PAS_PERM
    <where>
      <if test="permId != null ">
        AND PERM_ID = #{permId,jdbcType=DECIMAL}
      </if>
      <if test="name != null and name != '' ">
        AND NAME = #{name,jdbcType=VARCHAR}
      </if>
      <if test="parentId != null  ">
        AND PARENT_ID = #{parentId,jdbcType=DECIMAL}
      </if>
      <if test="alias != null and alias != '' ">
        AND ALIAS = #{alias,jdbcType=VARCHAR}
      </if>
      <if test="url != null and url != '' ">
        AND URL = #{url,jdbcType=VARCHAR}
      </if>
      <if test="permSort != null and permSort != '' ">
        AND PERM_SORT = #{name,jdbcType=VARCHAR}
      </if>

    </where>
  		order by PERM_ID asc
  		) A
  		where rownum &lt;= #{endRowNo,jdbcType=DECIMAL}
  		)
  		where RN &gt;= #{beginRowNo,jdbcType=DECIMAL}

  	</select>

  <select id="selectDataPermList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from pas_perm p
    start with p.perm_id in
    <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
      #{item}
    </foreach>
    connect by prior p.perm_id = p.parent_id
    union
    select
    <include refid="Base_Column_List" />
    from pas_perm p
    start with p.perm_id in
    <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
      #{item}
    </foreach>
    connect by prior p.parent_id = p.perm_id
  </select>
</mapper>