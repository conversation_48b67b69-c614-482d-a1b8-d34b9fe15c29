<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.pas.acl.dao.PasUserAuditMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.acl.po.PasUserAudit" >
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="USER_ID" property="userId" jdbcType="DECIMAL" />
    <result column="USER_NAME" property="userName" jdbcType="VARCHAR" />
    <result column="ROLE_ID_BEFORE" property="roleIdBefore" jdbcType="VARCHAR" />
    <result column="ROLE_NAME_BEFORE" property="roleNameBefore" jdbcType="VARCHAR" />
    <result column="ROLE_ID_AFTER" property="roleIdAfter" jdbcType="VARCHAR" />
    <result column="ROLE_NAME_AFTER" property="roleNameAfter" jdbcType="VARCHAR" />
    <result column="STATUS" property="status" jdbcType="VARCHAR" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
    <result column="AUDIT_PERSON" property="auditPerson" jdbcType="VARCHAR" />
    <result column="AUDIT_TIME" property="auditTime" jdbcType="TIMESTAMP" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="CREATE_PERSON" property="createPerson" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, USER_ID, USER_NAME, ROLE_ID_BEFORE, ROLE_NAME_BEFORE, ROLE_ID_AFTER, ROLE_NAME_AFTER, STATUS,
    REMARK, AUDIT_PERSON, AUDIT_TIME, CREATE_TIME, CREATE_PERSON
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from PAS_USER_AUDIT
    where ID = #{id,jdbcType=DECIMAL}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from PAS_USER_AUDIT
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.acl.po.PasUserAudit" >
    insert into PAS_USER_AUDIT (ID, USER_ID, USER_NAME, ROLE_ID_BEFORE,
      ROLE_NAME_BEFORE, ROLE_ID_AFTER, ROLE_NAME_AFTER, 
      STATUS, REMARK, AUDIT_PERSON, 
      AUDIT_TIME, CREATE_TIME, CREATE_PERSON)
    values (#{id,jdbcType=DECIMAL}, #{userId,jdbcType=DECIMAL}, #{userName,jdbcType=VARCHAR}, #{roleIdBefore,jdbcType=VARCHAR},
      #{roleNameBefore,jdbcType=VARCHAR}, #{roleIdAfter,jdbcType=VARCHAR}, #{roleNameAfter,jdbcType=VARCHAR}, 
      #{status,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{auditPerson,jdbcType=VARCHAR}, 
      #{auditTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, #{createPerson,jdbcType=VARCHAR})
  </insert>

  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.acl.po.PasUserAudit" >
    update PAS_USER_AUDIT
    set USER_ID = #{userId,jdbcType=DECIMAL},
        USER_NAME = #{userName,jdbcType=VARCHAR},
      ROLE_ID_BEFORE = #{roleIdBefore,jdbcType=VARCHAR},
      ROLE_NAME_BEFORE = #{roleNameBefore,jdbcType=VARCHAR},
      ROLE_ID_AFTER = #{roleIdAfter,jdbcType=VARCHAR},
      ROLE_NAME_AFTER = #{roleNameAfter,jdbcType=VARCHAR},
      STATUS = #{status,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      AUDIT_PERSON = #{auditPerson,jdbcType=VARCHAR},
      AUDIT_TIME = #{auditTime,jdbcType=TIMESTAMP},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=DECIMAL}
  </update>

  <select id="countByPage" resultType="java.lang.Integer">
    select  count(1)
    from PAS_USER_AUDIT u
    <where>
    <if test="name != null and name !=''">
      AND u.USER_NAME like '%'||#{name,jdbcType=VARCHAR}||'%'
    </if>
    <if test="status != null and status !=''">
      AND u.STATUS = #{status,jdbcType=VARCHAR}
    </if>
    </where>
  </select>
  <select id="selectByPage" resultMap="BaseResultMap">
    select *
    from (
    select A.*, rownum RN
    from (
    select  <include refid="Base_Column_List"/>
    from PAS_USER_AUDIT u

    <where>
      <if test="uid != null">
        AND u.user_id = #{uid,jdbcType=DECIMAL}
      </if>
      <if test="name != null and name !=''">
        AND u.USER_NAME like '%'||#{name,jdbcType=VARCHAR}||'%'
      </if>
      <if test="status != null and status !=''">
        AND u.STATUS = #{status,jdbcType=VARCHAR}
      </if>
    </where>
    order by u.id desc
    ) A
    where rownum &lt;= #{endRowNo,jdbcType=DECIMAL}
    )
    where RN &gt;= #{beginRowNo,jdbcType=DECIMAL}
  </select>

</mapper>