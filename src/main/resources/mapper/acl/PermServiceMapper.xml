<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.pas.acl.dao.PermServiceMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.acl.po.PermService">
    <id column="PERM_ID" jdbcType="DECIMAL" property="permId" />
    <result column="ID" jdbcType="DECIMAL" property="id" />
    <result column="SERVICE_CODE" jdbcType="VARCHAR" property="serviceCode" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, PERM_ID, SERVICE_CODE
  </sql>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.acl.po.PermService">
    insert into PAS_PERM_SERVICE (ID, PERM_ID, SERVICE_CODE)
    values (#{id,jdbcType=DECIMAL}, #{permId,jdbcType=DECIMAL}, #{serviceCode,jdbcType=VARCHAR})
  </insert>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from PAS_PERM_SERVICE
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <select id="selectPermServiceByParam" resultType="java.lang.String" >
    select distinct SERVICE_CODE from PAS_PERM_SERVICE where PERM_ID in
    <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
      <if test="(index % 999) == 998">
       NULL ) OR PERM_ID IN (
      </if>
    #{item}
    </foreach>
  </select>
  <delete id="deleteByPermIds">
    delete from PAS_PERM_SERVICE
    where PERM_ID in
    <foreach item="item" collection="permIds" open="(" separator="," close=")">#{item}</foreach>
  </delete>
  <select id="queryPermService" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from PAS_PERM_SERVICE
    where PERM_ID=#{permId,jdbcType=DECIMAL}
  </select>
  <select id="selectMaxId" resultType="java.lang.Long">
    select max(id) from PAS_PERM_SERVICE where PERM_ID=#{permId,jdbcType=DECIMAL}
  </select>
  <update id="updateServiceCode">
    update PAS_PERM_SERVICE
    set SERVICE_CODE = #{newServiceCode,jdbcType=VARCHAR}
    where SERVICE_CODE = #{origServiceCode,jdbcType=VARCHAR}
  </update>
  <delete id="deleteByServiceCode">
    delete from PAS_PERM_SERVICE
    where SERVICE_CODE = #{serviceCode,jdbcType=VARCHAR}
  </delete>

   <select id="getMaxPermId" parameterType="java.lang.Long" resultType="java.lang.Long">
       select max(ID) +1 from PAS_PERM_SERVICE
     </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from PAS_PERM_SERVICE
      where ID = #{id,jdbcType=DECIMAL}
    </select>
</mapper>