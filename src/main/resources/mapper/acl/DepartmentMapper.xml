<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.pas.acl.dao.DepartmentMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.acl.po.Department" >
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="PARENT_ID" property="parentId" jdbcType="DECIMAL" />
    <result column="NAME" property="name" jdbcType="VARCHAR" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
  </resultMap>
  <resultMap id="UserAmountResultMap" type="com.epaylinks.efps.pas.acl.dto.DepartmentUserAmount" >
    <result column="DEPT_ID" property="deptId" jdbcType="DECIMAL" />
    <result column="USER_AMOUNT" property="userAmount" jdbcType="DECIMAL" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, PARENT_ID, NAME, REMARK
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from PAS_DEPARTMENT
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from PAS_DEPARTMENT
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.acl.po.Department" >
    insert into PAS_DEPARTMENT (ID, PARENT_ID, NAME, 
      REMARK)
    values (#{id,jdbcType=DECIMAL}, #{parentId,jdbcType=DECIMAL}, #{name,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.acl.po.Department" >
    insert into PAS_DEPARTMENT
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="parentId != null" >
        PARENT_ID,
      </if>
      <if test="name != null" >
        NAME,
      </if>
      <if test="remark != null" >
        REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="parentId != null" >
        #{parentId,jdbcType=DECIMAL},
      </if>
      <if test="name != null" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.acl.po.Department" >
    update PAS_DEPARTMENT
    <set >
      <if test="parentId != null" >
        PARENT_ID = #{parentId,jdbcType=DECIMAL},
      </if>
      <if test="name != null" >
        NAME = #{name,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.acl.po.Department" >
    update PAS_DEPARTMENT
    set PARENT_ID = #{parentId,jdbcType=DECIMAL},
      NAME = #{name,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>

  <select id="selectUserAmountByDeptIds" resultMap="UserAmountResultMap" >
    select dept_id, count(1) as user_amount from pas_user where dept_id in
    <foreach item="item" index="index" collection="deptIdList" open="(" separator="," close=")">
      #{item}
    </foreach>
    group by dept_id
  </select>

  <select id="queryUserAmountByDeptIds" resultMap="UserAmountResultMap" >
    select dept_id, count(1) as user_amount from pas_user where dept_id in
    <foreach item="item" index="index" collection="deptIdList" open="(" separator="," close=")">
      #{item}
    </foreach>
    group by dept_id
  </select>

  <select id="checkExistOfName" resultType="Boolean">
    select count(*)
    from PAS_DEPARTMENT
    where NAME = #{name,jdbcType=VARCHAR}
  </select>
  <select id="checkExistOfNameWhenModify" resultType="Boolean">
    select count(*)
    from PAS_DEPARTMENT
    where NAME = #{name,jdbcType=VARCHAR} and ID != #{id,jdbcType=DECIMAL}
  </select>
  <update id="deleteDeptFromUser">
    update PAS_USER
    set DEPT_ID = null
    where DEPT_ID = #{deptId,jdbcType=DECIMAL}
  </update>
</mapper>