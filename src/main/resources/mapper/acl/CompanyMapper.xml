<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.pas.acl.dao.CompanyMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.acl.po.Company" >
    <id column="COMPANY_ID" property="companyId" jdbcType="DECIMAL" />
    <result column="NAME" property="name" jdbcType="VARCHAR" />
    <result column="PARENT_ID" property="parentId" jdbcType="DECIMAL" />
    <result column="CREATOR" property="creator" jdbcType="DECIMAL" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UPDATOR" property="updator" jdbcType="DECIMAL" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="COMPANY_LEVEL" property="companyLevel" jdbcType="DECIMAL" />
    <result column="SORT" property="sort" jdbcType="DECIMAL" />
    <result column="STATUS" property="status" jdbcType="DECIMAL" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    COMPANY_ID, NAME, PARENT_ID, CREATOR, CREATE_TIME, UPDATOR, UPDATE_TIME, COMPANY_LEVEL, 
    SORT, STATUS, REMARK
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from PAS_COMPANY
    where COMPANY_ID = #{companyId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from PAS_COMPANY
    where COMPANY_ID = #{companyId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.acl.po.Company" >
    insert into PAS_COMPANY (COMPANY_ID, NAME, PARENT_ID, 
      CREATOR, CREATE_TIME, UPDATOR, 
      UPDATE_TIME, COMPANY_LEVEL, SORT, 
      STATUS, REMARK)
    values (#{companyId,jdbcType=DECIMAL}, #{name,jdbcType=VARCHAR}, #{parentId,jdbcType=DECIMAL}, 
      #{creator,jdbcType=DECIMAL}, #{createTime,jdbcType=TIMESTAMP}, #{updator,jdbcType=DECIMAL}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{companyLevel,jdbcType=DECIMAL}, #{sort,jdbcType=DECIMAL}, 
      #{status,jdbcType=DECIMAL}, #{remark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.acl.po.Company" >
    insert into PAS_COMPANY
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="companyId != null" >
        COMPANY_ID,
      </if>
      <if test="name != null" >
        NAME,
      </if>
      <if test="parentId != null" >
        PARENT_ID,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="updator != null" >
        UPDATOR,
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME,
      </if>
      <if test="companyLevel != null" >
        COMPANY_LEVEL,
      </if>
      <if test="sort != null" >
        SORT,
      </if>
      <if test="status != null" >
        STATUS,
      </if>
      <if test="remark != null" >
        REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="companyId != null" >
        #{companyId,jdbcType=DECIMAL},
      </if>
      <if test="name != null" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null" >
        #{parentId,jdbcType=DECIMAL},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null" >
        #{updator,jdbcType=DECIMAL},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyLevel != null" >
        #{companyLevel,jdbcType=DECIMAL},
      </if>
      <if test="sort != null" >
        #{sort,jdbcType=DECIMAL},
      </if>
      <if test="status != null" >
        #{status,jdbcType=DECIMAL},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.acl.po.Company" >
    update PAS_COMPANY
    <set >
      <if test="name != null" >
        NAME = #{name,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null" >
        PARENT_ID = #{parentId,jdbcType=DECIMAL},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null" >
        UPDATOR = #{updator,jdbcType=DECIMAL},
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyLevel != null" >
        COMPANY_LEVEL = #{companyLevel,jdbcType=DECIMAL},
      </if>
      <if test="sort != null" >
        SORT = #{sort,jdbcType=DECIMAL},
      </if>
      <if test="status != null" >
        STATUS = #{status,jdbcType=DECIMAL},
      </if>
      <if test="remark != null" >
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
    </set>
    where COMPANY_ID = #{companyId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.acl.po.Company" >
    update PAS_COMPANY
    set NAME = #{name,jdbcType=VARCHAR},
      PARENT_ID = #{parentId,jdbcType=DECIMAL},
      CREATOR = #{creator,jdbcType=DECIMAL},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATOR = #{updator,jdbcType=DECIMAL},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      COMPANY_LEVEL = #{companyLevel,jdbcType=DECIMAL},
      SORT = #{sort,jdbcType=DECIMAL},
      STATUS = #{status,jdbcType=DECIMAL},
      REMARK = #{remark,jdbcType=VARCHAR}
    where COMPANY_ID = #{companyId,jdbcType=DECIMAL}
  </update>
  
  <select id="selectAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from PAS_COMPANY
    <where>
    	<if test="companyId != null">
		    company_id in (
		       select company_id from pas_company
		       start with company_id = #{companyId,jdbcType=DECIMAL}
		       connect by  parent_id = prior company_id
		    ) 
		</if>
    </where>
  </select>
  
  <select id="queryByNameAndParentId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from PAS_COMPANY
    where  PARENT_ID = #{parentId,jdbcType=DECIMAL}
    and  NAME = #{name,jdbcType=VARCHAR}
  </select>
  
  
  <resultMap id="UserAmountResultMap" type="com.epaylinks.efps.pas.acl.dto.CompanyUserAmount" >
    <result column="COMPANY_ID" property="companyId" jdbcType="DECIMAL" />
    <result column="USER_AMOUNT" property="userAmount" jdbcType="DECIMAL" />
  </resultMap>
  <select id="selectUserAmountByCompanyIds" resultMap="UserAmountResultMap" >
    select company_id, count(1) as user_amount from pas_user where company_id in
    <foreach item="item" index="index" collection="companyIdList" open="(" separator="," close=")">
      #{item}
    </foreach>
    group by company_id
  </select>
  
  <select id="searchCompany" resultMap="BaseResultMap"	parameterType="java.util.Map">
		select
		<include refid="Base_Column_List"/>
		from (
		select A.*, rownum RN from (
			select *
			from pas_company u
			<where>
				<if test="key != null and key !=''">
			 		name like  '%'||#{key,jdbcType=VARCHAR}||'%' 
		 		</if>		
		 		<if test="companyId != null">
				  	AND company_id in (
				  		select company_id from pas_company
						start with company_id = #{companyId,jdbcType=DECIMAL}
						connect by  parent_id = prior company_id
				  	)
				</if>
			</where>
		) A
		where rownum &lt;= #{endRowNo,jdbcType=DECIMAL}
		)
		where RN &gt;= #{beginRowNo,jdbcType=DECIMAL}
	</select>	
  
  <select id="queryLeafCompany" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from PAS_COMPANY t 
    where t.PARENT_ID in (select COMPANY_ID from PAS_COMPANY)
        and t.COMPANY_ID not in (select PARENT_ID from PAS_COMPANY where status = 1)
        and t.COMPANY_ID != 0 
        and t.STATUS = 1 order by t.NAME
  </select>

  <select id="queryByName" resultType="Boolean">
    select
    count(*)
    from PAS_COMPANY
    where NAME = #{name,jdbcType=VARCHAR}
  </select>

  <select id="checkExistOfNameWhenModify" resultType="java.lang.Boolean">
    select count(*)
    from PAS_COMPANY
    where NAME = #{name,jdbcType=VARCHAR} and COMPANY_ID != #{id,jdbcType=DECIMAL}
  </select>

  <update id="deleteCompanyFromUser">
    update PAS_USER
    set COMPANY_ID = null
    where COMPANY_ID = #{companyId,jdbcType=DECIMAL}
  </update>
  
  <select id="queryJuniorCompanyId" resultType="java.lang.Long">
    select c.company_id from pas_company c
    start with c.company_id = #{companyId,jdbcType=DECIMAL}
    connect by prior c.company_id = c.parent_id
  </select>
  
  <select id="queryCompanysByUserId" parameterType="java.lang.Long" resultType="java.lang.Long">
    select c.company_id from pas_company c
    start with c.company_id = (select u.company_id from pas_user u where u.user_id = #{userId,jdbcType=DECIMAL})
    connect by prior c.company_id = c.parent_id
  </select>

</mapper>