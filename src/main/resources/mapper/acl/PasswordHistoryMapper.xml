<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.pas.acl.dao.PasswordHistoryMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.acl.po.PasswordHistory" >
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="USER_NAME" property="userName" jdbcType="VARCHAR" />
    <result column="USER_TYPE" property="userType" jdbcType="CHAR" />
    <result column="PASSWORD" property="password" jdbcType="VARCHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, USER_NAME, USER_TYPE, PASSWORD, CREATE_TIME
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from PAS_PASSWORD_HISTORY
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from PAS_PASSWORD_HISTORY
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.acl.po.PasswordHistory" >
    insert into PAS_PASSWORD_HISTORY (ID, USER_NAME, USER_TYPE, 
      PASSWORD, CREATE_TIME)
    values (#{id,jdbcType=DECIMAL}, #{userName,jdbcType=VARCHAR}, #{userType,jdbcType=CHAR}, 
      #{password,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.acl.po.PasswordHistory" >
    insert into PAS_PASSWORD_HISTORY
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="userName != null" >
        USER_NAME,
      </if>
      <if test="userType != null" >
        USER_TYPE,
      </if>
      <if test="password != null" >
        PASSWORD,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="userName != null" >
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="userType != null" >
        #{userType,jdbcType=CHAR},
      </if>
      <if test="password != null" >
        #{password,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.acl.po.PasswordHistory" >
    update PAS_PASSWORD_HISTORY
    <set >
      <if test="userName != null" >
        USER_NAME = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="userType != null" >
        USER_TYPE = #{userType,jdbcType=CHAR},
      </if>
      <if test="password != null" >
        PASSWORD = #{password,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.acl.po.PasswordHistory" >
    update PAS_PASSWORD_HISTORY
    set USER_NAME = #{userName,jdbcType=VARCHAR},
      USER_TYPE = #{userType,jdbcType=CHAR},
      PASSWORD = #{password,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  
    <select id="countHistoryPassword" resultType="java.lang.Integer" >
       select count(*) from pas_password_history t
        where t.user_name = #{userName,jdbcType=VARCHAR}
        and t.user_type =  #{userType,jdbcType=CHAR}
        and t.password = #{password,jdbcType=VARCHAR}
    </select>
    
  <select id="queryPwdHistoryByUser" resultMap="BaseResultMap" >
    select * from (
        select t.* from pas_password_history t
        where t.user_name = #{userName,jdbcType=VARCHAR} and t.user_type = #{userType,jdbcType=CHAR}
        order by id desc
    ) where rownum  &lt;= #{pwdHistoryCount,jdbcType=DECIMAL}
  </select>
  
    
</mapper>