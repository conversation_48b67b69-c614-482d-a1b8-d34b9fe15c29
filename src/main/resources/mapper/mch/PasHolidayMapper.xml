<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.pas.mch.dao.PasHolidayMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.mch.domain.PasHoliday" >
    <id column="DATE_STR" property="dateStr" jdbcType="CHAR" />
    <result column="TYPE" property="type" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    DATE_STR, TYPE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from PAS_HOLIDAY
    where DATE_STR = #{dateStr,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from PAS_HOLIDAY
    where DATE_STR = #{dateStr,jdbcType=CHAR}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.mch.domain.PasHoliday" >
    insert into PAS_HOLIDAY (DATE_STR, TYPE)
    values (#{dateStr,jdbcType=CHAR}, #{type,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.mch.domain.PasHoliday" >
    insert into PAS_HOLIDAY
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="dateStr != null" >
        DATE_STR,
      </if>
      <if test="type != null" >
        TYPE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="dateStr != null" >
        #{dateStr,jdbcType=CHAR},
      </if>
      <if test="type != null" >
        #{type,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.mch.domain.PasHoliday" >
    update PAS_HOLIDAY
    <set >
      <if test="type != null" >
        TYPE = #{type,jdbcType=VARCHAR},
      </if>
    </set>
    where DATE_STR = #{dateStr,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.mch.domain.PasHoliday" >
    update PAS_HOLIDAY
    set TYPE = #{type,jdbcType=VARCHAR}
    where DATE_STR = #{dateStr,jdbcType=CHAR}
  </update>
</mapper>