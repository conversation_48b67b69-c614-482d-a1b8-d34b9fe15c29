<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.pas.mch.dao.CustomerSettleInfoMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.mch.domain.CustomerSettleInfo">
    <id column="SETTLE_ID" jdbcType="DECIMAL" property="settleId" />
    <result column="CUSTOMER_INFO_ID" jdbcType="DECIMAL" property="customerInfoId" />
    <result column="CUSTOMER_ID" jdbcType="DECIMAL" property="customerId" />
    <result column="CUSTOMER_CODE" jdbcType="VARCHAR" property="customerCode" />
    <result column="CIRCLE" jdbcType="DECIMAL" property="circle" />
    <result column="TRANSFER_MONEY_DAY" jdbcType="DECIMAL" property="transferMoneyDay" />
    <result column="RISK_PREMATURITY" jdbcType="DECIMAL" property="riskPrematurity" />
    <result column="TARGET" jdbcType="DECIMAL" property="target" />
    <result column="CURRENCY" jdbcType="VARCHAR" property="currency" />
    <result column="PAUSE_SETT" jdbcType="DECIMAL" property="pauseSett" />
    <result column="BANK_ACCOUNT_TYPE" jdbcType="DECIMAL" property="bankAccountType" />
    <result column="BANK_CODE" jdbcType="VARCHAR" property="bankCode" />
    <result column="BANK_LINE_NUMBER" jdbcType="VARCHAR" property="bankLineNumber" />
    <result column="OPEN_ACCOUNT_BANK_NAME" jdbcType="VARCHAR" property="openAccountBankName" />
    <result column="BANK_ACCOUNT_NO" jdbcType="VARCHAR" property="bankAccountNo" />
    <result column="CUSTOMER_NAME_IN_BANK" jdbcType="VARCHAR" property="customerNameInBank" />
    <result column="SETTLE_MODE" jdbcType="CHAR" property="settleMode" />
    <result column="START_AMOUNT" jdbcType="DECIMAL" property="startAmount" />
    <result column="BANK_RESERVED_MOBILE" jdbcType="VARCHAR" property="bankReservedMobile" />
    <result column="BANK_RESERVED_LPID_NO" jdbcType="VARCHAR" property="bankReservedLpidNo" />
    <result column="AREA_CODE" jdbcType="CHAR" property="areaCode" />
  </resultMap>
  <sql id="Base_Column_List">
    SETTLE_ID, CUSTOMER_INFO_ID, CUSTOMER_ID, CUSTOMER_CODE, CIRCLE, TRANSFER_MONEY_DAY, 
    RISK_PREMATURITY, TARGET, CURRENCY, PAUSE_SETT, BANK_ACCOUNT_TYPE, BANK_CODE, BANK_LINE_NUMBER, 
    OPEN_ACCOUNT_BANK_NAME, BANK_ACCOUNT_NO, CUSTOMER_NAME_IN_BANK, SETTLE_MODE, START_AMOUNT, 
    BANK_RESERVED_MOBILE, BANK_RESERVED_LPID_NO, AREA_CODE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from PAS_CUSTOMER_SETTLE_INFO
    where SETTLE_ID = #{settleId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from PAS_CUSTOMER_SETTLE_INFO
    where SETTLE_ID = #{settleId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.mch.domain.CustomerSettleInfo">
    insert into PAS_CUSTOMER_SETTLE_INFO (SETTLE_ID, CUSTOMER_INFO_ID, CUSTOMER_ID, 
      CUSTOMER_CODE, CIRCLE, TRANSFER_MONEY_DAY, 
      RISK_PREMATURITY, TARGET, CURRENCY, 
      PAUSE_SETT, BANK_ACCOUNT_TYPE, BANK_CODE, 
      BANK_LINE_NUMBER, OPEN_ACCOUNT_BANK_NAME, BANK_ACCOUNT_NO, 
      CUSTOMER_NAME_IN_BANK, SETTLE_MODE, START_AMOUNT, 
      BANK_RESERVED_MOBILE, BANK_RESERVED_LPID_NO, 
      AREA_CODE)
    values (#{settleId,jdbcType=DECIMAL}, #{customerInfoId,jdbcType=DECIMAL}, #{customerId,jdbcType=DECIMAL}, 
      #{customerCode,jdbcType=VARCHAR}, #{circle,jdbcType=DECIMAL}, #{transferMoneyDay,jdbcType=DECIMAL}, 
      #{riskPrematurity,jdbcType=DECIMAL}, #{target,jdbcType=DECIMAL}, #{currency,jdbcType=VARCHAR}, 
      #{pauseSett,jdbcType=DECIMAL}, #{bankAccountType,jdbcType=DECIMAL}, #{bankCode,jdbcType=VARCHAR}, 
      #{bankLineNumber,jdbcType=VARCHAR}, #{openAccountBankName,jdbcType=VARCHAR}, #{bankAccountNo,jdbcType=VARCHAR}, 
      #{customerNameInBank,jdbcType=VARCHAR}, #{settleMode,jdbcType=CHAR}, #{startAmount,jdbcType=DECIMAL}, 
      #{bankReservedMobile,jdbcType=VARCHAR}, #{bankReservedLpidNo,jdbcType=VARCHAR}, 
      #{areaCode,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.mch.domain.CustomerSettleInfo">
    insert into PAS_CUSTOMER_SETTLE_INFO
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="settleId != null">
        SETTLE_ID,
      </if>
      <if test="customerInfoId != null">
        CUSTOMER_INFO_ID,
      </if>
      <if test="customerId != null">
        CUSTOMER_ID,
      </if>
      <if test="customerCode != null">
        CUSTOMER_CODE,
      </if>
      <if test="circle != null">
        CIRCLE,
      </if>
      <if test="transferMoneyDay != null">
        TRANSFER_MONEY_DAY,
      </if>
      <if test="riskPrematurity != null">
        RISK_PREMATURITY,
      </if>
      <if test="target != null">
        TARGET,
      </if>
      <if test="currency != null">
        CURRENCY,
      </if>
      <if test="pauseSett != null">
        PAUSE_SETT,
      </if>
      <if test="bankAccountType != null">
        BANK_ACCOUNT_TYPE,
      </if>
      <if test="bankCode != null">
        BANK_CODE,
      </if>
      <if test="bankLineNumber != null">
        BANK_LINE_NUMBER,
      </if>
      <if test="openAccountBankName != null">
        OPEN_ACCOUNT_BANK_NAME,
      </if>
      <if test="bankAccountNo != null">
        BANK_ACCOUNT_NO,
      </if>
      <if test="customerNameInBank != null">
        CUSTOMER_NAME_IN_BANK,
      </if>
      <if test="settleMode != null">
        SETTLE_MODE,
      </if>
      <if test="startAmount != null">
        START_AMOUNT,
      </if>
      <if test="bankReservedMobile != null">
        BANK_RESERVED_MOBILE,
      </if>
      <if test="bankReservedLpidNo != null">
        BANK_RESERVED_LPID_NO,
      </if>
      <if test="areaCode != null">
        AREA_CODE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="settleId != null">
        #{settleId,jdbcType=DECIMAL},
      </if>
      <if test="customerInfoId != null">
        #{customerInfoId,jdbcType=DECIMAL},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=DECIMAL},
      </if>
      <if test="customerCode != null">
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="circle != null">
        #{circle,jdbcType=DECIMAL},
      </if>
      <if test="transferMoneyDay != null">
        #{transferMoneyDay,jdbcType=DECIMAL},
      </if>
      <if test="riskPrematurity != null">
        #{riskPrematurity,jdbcType=DECIMAL},
      </if>
      <if test="target != null">
        #{target,jdbcType=DECIMAL},
      </if>
      <if test="currency != null">
        #{currency,jdbcType=VARCHAR},
      </if>
      <if test="pauseSett != null">
        #{pauseSett,jdbcType=DECIMAL},
      </if>
      <if test="bankAccountType != null">
        #{bankAccountType,jdbcType=DECIMAL},
      </if>
      <if test="bankCode != null">
        #{bankCode,jdbcType=VARCHAR},
      </if>
      <if test="bankLineNumber != null">
        #{bankLineNumber,jdbcType=VARCHAR},
      </if>
      <if test="openAccountBankName != null">
        #{openAccountBankName,jdbcType=VARCHAR},
      </if>
      <if test="bankAccountNo != null">
        #{bankAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="customerNameInBank != null">
        #{customerNameInBank,jdbcType=VARCHAR},
      </if>
      <if test="settleMode != null">
        #{settleMode,jdbcType=CHAR},
      </if>
      <if test="startAmount != null">
        #{startAmount,jdbcType=DECIMAL},
      </if>
      <if test="bankReservedMobile != null">
        #{bankReservedMobile,jdbcType=VARCHAR},
      </if>
      <if test="bankReservedLpidNo != null">
        #{bankReservedLpidNo,jdbcType=VARCHAR},
      </if>
      <if test="areaCode != null">
        #{areaCode,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.mch.domain.CustomerSettleInfo">
    update PAS_CUSTOMER_SETTLE_INFO
    <set>
      <if test="customerInfoId != null">
        CUSTOMER_INFO_ID = #{customerInfoId,jdbcType=DECIMAL},
      </if>
      <if test="customerId != null">
        CUSTOMER_ID = #{customerId,jdbcType=DECIMAL},
      </if>
      <if test="customerCode != null">
        CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="circle != null">
        CIRCLE = #{circle,jdbcType=DECIMAL},
      </if>
      <if test="transferMoneyDay != null">
        TRANSFER_MONEY_DAY = #{transferMoneyDay,jdbcType=DECIMAL},
      </if>
      <if test="riskPrematurity != null">
        RISK_PREMATURITY = #{riskPrematurity,jdbcType=DECIMAL},
      </if>
      <if test="target != null">
        TARGET = #{target,jdbcType=DECIMAL},
      </if>
      <if test="currency != null">
        CURRENCY = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="pauseSett != null">
        PAUSE_SETT = #{pauseSett,jdbcType=DECIMAL},
      </if>
      <if test="bankAccountType != null">
        BANK_ACCOUNT_TYPE = #{bankAccountType,jdbcType=DECIMAL},
      </if>
      <if test="bankCode != null">
        BANK_CODE = #{bankCode,jdbcType=VARCHAR},
      </if>
      <if test="bankLineNumber != null">
        BANK_LINE_NUMBER = #{bankLineNumber,jdbcType=VARCHAR},
      </if>
      <if test="openAccountBankName != null">
        OPEN_ACCOUNT_BANK_NAME = #{openAccountBankName,jdbcType=VARCHAR},
      </if>
      <if test="bankAccountNo != null">
        BANK_ACCOUNT_NO = #{bankAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="customerNameInBank != null">
        CUSTOMER_NAME_IN_BANK = #{customerNameInBank,jdbcType=VARCHAR},
      </if>
      <if test="settleMode != null">
        SETTLE_MODE = #{settleMode,jdbcType=CHAR},
      </if>
      <if test="startAmount != null">
        START_AMOUNT = #{startAmount,jdbcType=DECIMAL},
      </if>
      <if test="bankReservedMobile != null">
        BANK_RESERVED_MOBILE = #{bankReservedMobile,jdbcType=VARCHAR},
      </if>
      <if test="bankReservedLpidNo != null">
        BANK_RESERVED_LPID_NO = #{bankReservedLpidNo,jdbcType=VARCHAR},
      </if>
      <if test="areaCode != null">
        AREA_CODE = #{areaCode,jdbcType=CHAR},
      </if>
    </set>
    where SETTLE_ID = #{settleId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.mch.domain.CustomerSettleInfo">
    update PAS_CUSTOMER_SETTLE_INFO
    set CUSTOMER_INFO_ID = #{customerInfoId,jdbcType=DECIMAL},
      CUSTOMER_ID = #{customerId,jdbcType=DECIMAL},
      CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      CIRCLE = #{circle,jdbcType=DECIMAL},
      TRANSFER_MONEY_DAY = #{transferMoneyDay,jdbcType=DECIMAL},
      RISK_PREMATURITY = #{riskPrematurity,jdbcType=DECIMAL},
      TARGET = #{target,jdbcType=DECIMAL},
      CURRENCY = #{currency,jdbcType=VARCHAR},
      PAUSE_SETT = #{pauseSett,jdbcType=DECIMAL},
      BANK_ACCOUNT_TYPE = #{bankAccountType,jdbcType=DECIMAL},
      BANK_CODE = #{bankCode,jdbcType=VARCHAR},
      BANK_LINE_NUMBER = #{bankLineNumber,jdbcType=VARCHAR},
      OPEN_ACCOUNT_BANK_NAME = #{openAccountBankName,jdbcType=VARCHAR},
      BANK_ACCOUNT_NO = #{bankAccountNo,jdbcType=VARCHAR},
      CUSTOMER_NAME_IN_BANK = #{customerNameInBank,jdbcType=VARCHAR},
      SETTLE_MODE = #{settleMode,jdbcType=CHAR},
      START_AMOUNT = #{startAmount,jdbcType=DECIMAL},
      BANK_RESERVED_MOBILE = #{bankReservedMobile,jdbcType=VARCHAR},
      BANK_RESERVED_LPID_NO = #{bankReservedLpidNo,jdbcType=VARCHAR},
      AREA_CODE = #{areaCode,jdbcType=CHAR}
    where SETTLE_ID = #{settleId,jdbcType=DECIMAL}
  </update>
  
  <select id="getByCustomerInfoId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from PAS_CUSTOMER_SETTLE_INFO
    where CUSTOMER_INFO_ID = #{customerInfoId,jdbcType=DECIMAL}
  </select>
  
  <select id="selectBankAccountNoByInfoId" resultType="String">
    select BANK_ACCOUNT_NO
    from PAS_CUSTOMER_SETTLE_INFO
    where CUSTOMER_INFO_ID = #{customerInfoId,jdbcType=DECIMAL}
  </select>
  
</mapper>