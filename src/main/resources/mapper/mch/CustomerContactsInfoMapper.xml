<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.pas.mch.dao.CustomerContactsInfoMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.mch.domain.CustomerContactsInfo">
    <id column="CONTACT_ID" jdbcType="DECIMAL" property="contactId" />
    <result column="CUSTOMER_INFO_ID" jdbcType="DECIMAL" property="customerInfoId" />
    <result column="CUSTOMER_ID" jdbcType="DECIMAL" property="customerId" />
    <result column="CUSTOMER_CODE" jdbcType="VARCHAR" property="customerCode" />
    <result column="TYPE" jdbcType="DECIMAL" property="type" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="MOBILE" jdbcType="VARCHAR" property="mobile" />
    <result column="TELEPHONE" jdbcType="VARCHAR" property="telephone" />
    <result column="EMAIL" jdbcType="VARCHAR" property="email" />
    <result column="QQ" jdbcType="VARCHAR" property="qq" />
  </resultMap>
  <sql id="Base_Column_List">
    CONTACT_ID, CUSTOMER_INFO_ID, CUSTOMER_ID, CUSTOMER_CODE, TYPE, NAME, MOBILE, TELEPHONE, 
    EMAIL, QQ
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from PAS_CUSTOMER_CONTACTS_INFO
    where CONTACT_ID = #{contactId,jdbcType=DECIMAL}
  </select>
  <select id="selectBySelective" parameterType="com.epaylinks.efps.pas.mch.domain.CustomerContactsInfo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from PAS_CUSTOMER_CONTACTS_INFO
    where 1 = 1
    <if test="customerInfoId != null">
      and CUSTOMER_INFO_ID = #{customerInfoId,jdbcType=DECIMAL}
    </if>
    <if test="customerId != null">
      and CUSTOMER_ID = #{customerId,jdbcType=DECIMAL}
    </if>
    <if test="customerCode != null">
      and CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
    </if>
    <if test="type != null">
      and TYPE = #{type,jdbcType=DECIMAL}
    </if>
    <if test="name != null">
      and NAME = #{name,jdbcType=VARCHAR}
    </if>
    <if test="mobile != null">
      and MOBILE = #{mobile,jdbcType=VARCHAR}
    </if>
    <if test="telephone != null">
      and TELEPHONE = #{telephone,jdbcType=VARCHAR}
    </if>
    <if test="email != null">
      and EMAIL = #{email,jdbcType=VARCHAR}
    </if>
    <if test="qq != null">
      and QQ = #{qq,jdbcType=VARCHAR}
    </if>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from PAS_CUSTOMER_CONTACTS_INFO
    where CONTACT_ID = #{contactId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.mch.domain.CustomerContactsInfo">
    insert into PAS_CUSTOMER_CONTACTS_INFO (CONTACT_ID, CUSTOMER_INFO_ID, CUSTOMER_ID, 
      CUSTOMER_CODE, TYPE, NAME, 
      MOBILE, TELEPHONE, EMAIL, 
      QQ)
    values (#{contactId,jdbcType=DECIMAL}, #{customerInfoId,jdbcType=DECIMAL}, #{customerId,jdbcType=DECIMAL}, 
      #{customerCode,jdbcType=VARCHAR}, #{type,jdbcType=DECIMAL}, #{name,jdbcType=VARCHAR}, 
      #{mobile,jdbcType=VARCHAR}, #{telephone,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR}, 
      #{qq,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.mch.domain.CustomerContactsInfo">
    insert into PAS_CUSTOMER_CONTACTS_INFO
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="contactId != null">
        CONTACT_ID,
      </if>
      <if test="customerInfoId != null">
        CUSTOMER_INFO_ID,
      </if>
      <if test="customerId != null">
        CUSTOMER_ID,
      </if>
      <if test="customerCode != null">
        CUSTOMER_CODE,
      </if>
      <if test="type != null">
        TYPE,
      </if>
      <if test="name != null">
        NAME,
      </if>
      <if test="mobile != null">
        MOBILE,
      </if>
      <if test="telephone != null">
        TELEPHONE,
      </if>
      <if test="email != null">
        EMAIL,
      </if>
      <if test="qq != null">
        QQ,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="contactId != null">
        #{contactId,jdbcType=DECIMAL},
      </if>
      <if test="customerInfoId != null">
        #{customerInfoId,jdbcType=DECIMAL},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=DECIMAL},
      </if>
      <if test="customerCode != null">
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=DECIMAL},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null">
        #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="qq != null">
        #{qq,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.mch.domain.CustomerContactsInfo">
    update PAS_CUSTOMER_CONTACTS_INFO
    <set>
      <if test="customerInfoId != null">
        CUSTOMER_INFO_ID = #{customerInfoId,jdbcType=DECIMAL},
      </if>
      <if test="customerId != null">
        CUSTOMER_ID = #{customerId,jdbcType=DECIMAL},
      </if>
      <if test="customerCode != null">
        CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        TYPE = #{type,jdbcType=DECIMAL},
      </if>
      <if test="name != null">
        NAME = #{name,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        MOBILE = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null">
        TELEPHONE = #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        EMAIL = #{email,jdbcType=VARCHAR},
      </if>
      <if test="qq != null">
        QQ = #{qq,jdbcType=VARCHAR},
      </if>
    </set>
    where CONTACT_ID = #{contactId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.mch.domain.CustomerContactsInfo">
    update PAS_CUSTOMER_CONTACTS_INFO
    set CUSTOMER_INFO_ID = #{customerInfoId,jdbcType=DECIMAL},
      CUSTOMER_ID = #{customerId,jdbcType=DECIMAL},
      CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      TYPE = #{type,jdbcType=DECIMAL},
      NAME = #{name,jdbcType=VARCHAR},
      MOBILE = #{mobile,jdbcType=VARCHAR},
      TELEPHONE = #{telephone,jdbcType=VARCHAR},
      EMAIL = #{email,jdbcType=VARCHAR},
      QQ = #{qq,jdbcType=VARCHAR}
    where CONTACT_ID = #{contactId,jdbcType=DECIMAL}
  </update>
  
  <select id="getByCustomerInfoId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from PAS_CUSTOMER_CONTACTS_INFO
    where CUSTOMER_INFO_ID = #{customerInfoId,jdbcType=DECIMAL}
  </select>
  
  <select id="selectByCustomerInfoId" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from PAS_CUSTOMER_CONTACTS_INFO
    where CUSTOMER_INFO_ID = #{customerInfoId,jdbcType=DECIMAL}
  </select>
  
</mapper>