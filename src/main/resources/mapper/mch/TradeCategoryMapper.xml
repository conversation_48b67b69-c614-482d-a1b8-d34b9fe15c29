<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.pas.mch.dao.TradeCategoryMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.mch.domain.TradeCategory" >
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="PARENT_ID" property="parentId" jdbcType="DECIMAL" />
    <result column="NAME" property="name" jdbcType="VARCHAR" />
    <result column="CODE" property="code" jdbcType="VARCHAR" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
    <result column="MCC" property="mcc" jdbcType="CHAR" />
    <result column="XMPF" property="xmpf" jdbcType="VARCHAR" />
    <result column="SZPA" property="szpa" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, PARENT_ID, NAME, CODE, REMARK, MCC , XMPF ,SZPA
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from PAS_TRADE_CATEGORY
    where ID = #{id,jdbcType=DECIMAL}
  </select>

  <select id="selectBySelective" resultMap="BaseResultMap" parameterType="com.epaylinks.efps.pas.mch.domain.TradeCategory" >
    select
    <include refid="Base_Column_List" />
    from PAS_TRADE_CATEGORY
    <where>
      <if test="id != null and id !=''" >
        ID = #{id,jdbcType=DECIMAL}
      </if>
      <if test="parentId != null and parentId !=''" >
        and PARENT_ID = #{parentId,jdbcType=DECIMAL}
      </if>
      <if test="name != null and name !=''" >
        and NAME = #{name,jdbcType=VARCHAR}
      </if>
      <if test="code != null and code !=''" >
        and CODE = #{code,jdbcType=VARCHAR}
      </if>
      <if test="remark != null and remark !=''" >
        and REMARK = #{remark,jdbcType=VARCHAR}
      </if>
      <if test="mcc != null and mcc !=''" >
        and MCC = #{mcc,jdbcType=CHAR}
      </if>
      <if test="xmpf != null and xmpf !=''" >
        and XMPF = #{xmpf,jdbcType=VARCHAR}
      </if>
      <if test="szpa != null and szpa !=''" >
        and SZPA = #{szpa,jdbcType=VARCHAR}
      </if>
    </where>
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from PAS_TRADE_CATEGORY
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.mch.domain.TradeCategory" >
    insert into PAS_TRADE_CATEGORY (ID, PARENT_ID, NAME,
      CODE, REMARK, MCC, XMPF , SZPA)
    values (#{id,jdbcType=DECIMAL}, #{parentId,jdbcType=DECIMAL}, #{name,jdbcType=VARCHAR},
      #{code,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{mcc,jdbcType=CHAR}, #{xmpf,jdbcType=VARCHAR}, #{szpa,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.mch.domain.TradeCategory" >
    insert into PAS_TRADE_CATEGORY
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null and id !=''" >
        ID,
      </if>
      <if test="parentId != null and parentId !=''" >
        PARENT_ID,
      </if>
      <if test="name != null and name !=''" >
        NAME,
      </if>
      <if test="code != null and code !=''" >
        CODE,
      </if>
      <if test="remark != null and remark !=''" >
        REMARK,
      </if>
      <if test="mcc != null and mcc !=''" >
        MCC,
      </if>
      <if test="xmpf != null and xmpf !=''" >
        XMPF,
      </if>
      <if test="szpa != null and szpa !=''" >
        SZPA,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null and id !=''" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="parentId != null and parentId !=''" >
        #{parentId,jdbcType=DECIMAL},
      </if>
      <if test="name != null and name !=''" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="code != null and code !=''" >
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="remark != null and remark !=''" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="mcc != null and mcc !=''" >
        #{mcc,jdbcType=CHAR},
      </if>
      <if test="xmpf != null and xmpf !=''" >
        #{xmpf,jdbcType=VARCHAR},
      </if>
      <if test="szpa != null and szpa !=''" >
        #{szpa,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.mch.domain.TradeCategory" >
    update PAS_TRADE_CATEGORY
    <set >
      <if test="parentId != null and parentId !=''" >
        PARENT_ID = #{parentId,jdbcType=DECIMAL},
      </if>
      <if test="name != null and name !=''" >
        NAME = #{name,jdbcType=VARCHAR},
      </if>
      <if test="code != null and code !=''" >
        CODE = #{code,jdbcType=VARCHAR},
      </if>
      <if test="remark != null and remark !=''" >
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="mcc != null and mcc !=''" >
        MCC = #{mcc,jdbcType=CHAR},
      </if>
      <if test="xmpf != null and xmpf !=''" >
        XMPF = #{xmpf,jdbcType=VARCHAR},
      </if>
      <if test="szpa != null and szpa !=''" >
        SZPA = #{szpa,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.mch.domain.TradeCategory" >
    update PAS_TRADE_CATEGORY
    set PARENT_ID = #{parentId,jdbcType=DECIMAL},
      NAME = #{name,jdbcType=VARCHAR},
      CODE = #{code,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      MCC = #{mcc,jdbcType=CHAR},
      XMPF = #{xmpf,jdbcType=VARCHAR},
      SZPA = #{szpa,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>
</mapper>