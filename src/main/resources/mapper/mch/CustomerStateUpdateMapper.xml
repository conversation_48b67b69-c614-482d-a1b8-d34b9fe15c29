<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.pas.mch.dao.CustomerStateUpdateMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.mch.domain.CustomerStateUpdate">
    <id column="UPDATE_ID" jdbcType="DECIMAL" property="updateId" />
    <result column="CUSTOMER_ID" jdbcType="DECIMAL" property="customerId" />
    <result column="NEW_STATE" jdbcType="DECIMAL" property="newState" />
    <result column="OLD_STATE" jdbcType="DECIMAL" property="oldState" />
    <result column="OPERATOR_ID" jdbcType="DECIMAL" property="operatorId" />
    <result column="OPERATE_TIME" jdbcType="TIMESTAMP" property="operateTime" />
    <result column="UPDATE_COMMENT" jdbcType="VARCHAR" property="updateComment" />
  </resultMap>
  <sql id="Base_Column_List">
    UPDATE_ID, CUSTOMER_ID, NEW_STATE, OLD_STATE, OPERATOR_ID, OPERATE_TIME, UPDATE_COMMENT
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from PAS_CUSTOMER_STATE_UPDATE
    where UPDATE_ID = #{updateId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from PAS_CUSTOMER_STATE_UPDATE
    where UPDATE_ID = #{updateId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.mch.domain.CustomerStateUpdate">
    insert into PAS_CUSTOMER_STATE_UPDATE (UPDATE_ID, CUSTOMER_ID, NEW_STATE, 
      OLD_STATE, OPERATOR_ID, OPERATE_TIME, 
      UPDATE_COMMENT)
    values (#{updateId,jdbcType=DECIMAL}, #{customerId,jdbcType=DECIMAL}, #{newState,jdbcType=DECIMAL}, 
      #{oldState,jdbcType=DECIMAL}, #{operatorId,jdbcType=DECIMAL}, #{operateTime,jdbcType=TIMESTAMP}, 
      #{updateComment,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.mch.domain.CustomerStateUpdate">
    insert into PAS_CUSTOMER_STATE_UPDATE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="updateId != null">
        UPDATE_ID,
      </if>
      <if test="customerId != null">
        CUSTOMER_ID,
      </if>
      <if test="newState != null">
        NEW_STATE,
      </if>
      <if test="oldState != null">
        OLD_STATE,
      </if>
      <if test="operatorId != null">
        OPERATOR_ID,
      </if>
      <if test="operateTime != null">
        OPERATE_TIME,
      </if>
      <if test="updateComment != null">
        UPDATE_COMMENT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="updateId != null">
        #{updateId,jdbcType=DECIMAL},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=DECIMAL},
      </if>
      <if test="newState != null">
        #{newState,jdbcType=DECIMAL},
      </if>
      <if test="oldState != null">
        #{oldState,jdbcType=DECIMAL},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=DECIMAL},
      </if>
      <if test="operateTime != null">
        #{operateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateComment != null">
        #{updateComment,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.mch.domain.CustomerStateUpdate">
    update PAS_CUSTOMER_STATE_UPDATE
    <set>
      <if test="customerId != null">
        CUSTOMER_ID = #{customerId,jdbcType=DECIMAL},
      </if>
      <if test="newState != null">
        NEW_STATE = #{newState,jdbcType=DECIMAL},
      </if>
      <if test="oldState != null">
        OLD_STATE = #{oldState,jdbcType=DECIMAL},
      </if>
      <if test="operatorId != null">
        OPERATOR_ID = #{operatorId,jdbcType=DECIMAL},
      </if>
      <if test="operateTime != null">
        OPERATE_TIME = #{operateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateComment != null">
        UPDATE_COMMENT = #{updateComment,jdbcType=VARCHAR},
      </if>
    </set>
    where UPDATE_ID = #{updateId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.mch.domain.CustomerStateUpdate">
    update PAS_CUSTOMER_STATE_UPDATE
    set CUSTOMER_ID = #{customerId,jdbcType=DECIMAL},
      NEW_STATE = #{newState,jdbcType=DECIMAL},
      OLD_STATE = #{oldState,jdbcType=DECIMAL},
      OPERATOR_ID = #{operatorId,jdbcType=DECIMAL},
      OPERATE_TIME = #{operateTime,jdbcType=TIMESTAMP},
      UPDATE_COMMENT = #{updateComment,jdbcType=VARCHAR}
    where UPDATE_ID = #{updateId,jdbcType=DECIMAL}
  </update>
  
  <select id="selectStateCommentsByIds" resultMap="BaseResultMap">
	select
	UPDATE_ID, UPDATE_COMMENT, OPERATOR_ID
	from PAS_CUSTOMER_STATE_UPDATE where UPDATE_ID IN
	<foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
		#{item}
	</foreach>
  </select>
  
</mapper>