<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.pas.mch.dao.CustomerAttachmentInfoMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.mch.domain.CustomerAttachmentInfo">
    <id column="ATTACHMENT_ID" jdbcType="DECIMAL" property="attachmentId" />
    <result column="CUSTOMER_INFO_ID" jdbcType="DECIMAL" property="customerInfoId" />
    <result column="ATTACHMENT_CODE" jdbcType="VARCHAR" property="attachmentCode" />
    <result column="ATTACHMENT_NAME" jdbcType="VARCHAR" property="attachmentName" />
    <result column="ATTACHMENT_URL" jdbcType="VARCHAR" property="attachmentUrl" />
    <result column="ATTACHMEN_SIZE" jdbcType="DECIMAL" property="attachmenSize" />
  </resultMap>
  <sql id="Base_Column_List">
    ATTACHMENT_ID, CUSTOMER_INFO_ID, ATTACHMENT_CODE, ATTACHMENT_NAME, ATTACHMENT_URL, 
    ATTACHMEN_SIZE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from PAS_CUSTOMER_ATTACHMENT_INFO
    where ATTACHMENT_ID = #{attachmentId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from PAS_CUSTOMER_ATTACHMENT_INFO
    where ATTACHMENT_ID = #{attachmentId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.mch.domain.CustomerAttachmentInfo">
    insert into PAS_CUSTOMER_ATTACHMENT_INFO (ATTACHMENT_ID, CUSTOMER_INFO_ID, ATTACHMENT_CODE, 
      ATTACHMENT_NAME, ATTACHMENT_URL, ATTACHMEN_SIZE
      )
    values (#{attachmentId,jdbcType=DECIMAL}, #{customerInfoId,jdbcType=DECIMAL}, #{attachmentCode,jdbcType=VARCHAR}, 
      #{attachmentName,jdbcType=VARCHAR}, #{attachmentUrl,jdbcType=VARCHAR}, #{attachmenSize,jdbcType=DECIMAL}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.mch.domain.CustomerAttachmentInfo">
    insert into PAS_CUSTOMER_ATTACHMENT_INFO
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="attachmentId != null">
        ATTACHMENT_ID,
      </if>
      <if test="customerInfoId != null">
        CUSTOMER_INFO_ID,
      </if>
      <if test="attachmentCode != null">
        ATTACHMENT_CODE,
      </if>
      <if test="attachmentName != null">
        ATTACHMENT_NAME,
      </if>
      <if test="attachmentUrl != null">
        ATTACHMENT_URL,
      </if>
      <if test="attachmenSize != null">
        ATTACHMEN_SIZE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="attachmentId != null">
        #{attachmentId,jdbcType=DECIMAL},
      </if>
      <if test="customerInfoId != null">
        #{customerInfoId,jdbcType=DECIMAL},
      </if>
      <if test="attachmentCode != null">
        #{attachmentCode,jdbcType=VARCHAR},
      </if>
      <if test="attachmentName != null">
        #{attachmentName,jdbcType=VARCHAR},
      </if>
      <if test="attachmentUrl != null">
        #{attachmentUrl,jdbcType=VARCHAR},
      </if>
      <if test="attachmenSize != null">
        #{attachmenSize,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.mch.domain.CustomerAttachmentInfo">
    update PAS_CUSTOMER_ATTACHMENT_INFO
    <set>
      <if test="customerInfoId != null">
        CUSTOMER_INFO_ID = #{customerInfoId,jdbcType=DECIMAL},
      </if>
      <if test="attachmentCode != null">
        ATTACHMENT_CODE = #{attachmentCode,jdbcType=VARCHAR},
      </if>
      <if test="attachmentName != null">
        ATTACHMENT_NAME = #{attachmentName,jdbcType=VARCHAR},
      </if>
      <if test="attachmentUrl != null">
        ATTACHMENT_URL = #{attachmentUrl,jdbcType=VARCHAR},
      </if>
      <if test="attachmenSize != null">
        ATTACHMEN_SIZE = #{attachmenSize,jdbcType=DECIMAL},
      </if>
    </set>
    where ATTACHMENT_ID = #{attachmentId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.mch.domain.CustomerAttachmentInfo">
    update PAS_CUSTOMER_ATTACHMENT_INFO
    set CUSTOMER_INFO_ID = #{customerInfoId,jdbcType=DECIMAL},
      ATTACHMENT_CODE = #{attachmentCode,jdbcType=VARCHAR},
      ATTACHMENT_NAME = #{attachmentName,jdbcType=VARCHAR},
      ATTACHMENT_URL = #{attachmentUrl,jdbcType=VARCHAR},
      ATTACHMEN_SIZE = #{attachmenSize,jdbcType=DECIMAL}
    where ATTACHMENT_ID = #{attachmentId,jdbcType=DECIMAL}
  </update>
  
  <select id="getAttachmentByCustomerInfoId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from PAS_CUSTOMER_ATTACHMENT_INFO
    where CUSTOMER_INFO_ID = #{customerInfoId,jdbcType=DECIMAL}
  </select>
  
</mapper>