<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.pas.mch.dao.PasSettCycleRuleMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.mch.domain.PasSettCycleRule">
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="CODE" jdbcType="VARCHAR" property="code" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="TYPE" jdbcType="VARCHAR" property="type" />
    <result column="PARAMS" jdbcType="VARCHAR" property="params" />
    <result column="STATUS" jdbcType="VARCHAR" property="status" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="CREATOR" jdbcType="DECIMAL" property="creator" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="UPDATOR" jdbcType="DECIMAL" property="updator" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, CODE, NAME, TYPE, PARAMS, STATUS, CREATE_TIME, CREATOR, UPDATE_TIME, UPDATOR
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from PAS_SETT_CYCLE_RULE
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from PAS_SETT_CYCLE_RULE
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.mch.domain.PasSettCycleRule">
    insert into PAS_SETT_CYCLE_RULE (ID, CODE, NAME, 
      TYPE, PARAMS, STATUS, 
      CREATE_TIME, CREATOR, UPDATE_TIME, 
      UPDATOR)
    values (#{id,jdbcType=DECIMAL}, #{code,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{type,jdbcType=VARCHAR}, #{params,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=DECIMAL}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{updator,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.mch.domain.PasSettCycleRule">
    insert into PAS_SETT_CYCLE_RULE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="code != null">
        CODE,
      </if>
      <if test="name != null">
        NAME,
      </if>
      <if test="type != null">
        TYPE,
      </if>
      <if test="params != null">
        PARAMS,
      </if>
      <if test="status != null">
        STATUS,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="updator != null">
        UPDATOR,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="params != null">
        #{params,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=DECIMAL},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null">
        #{updator,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.mch.domain.PasSettCycleRule">
    update PAS_SETT_CYCLE_RULE
    <set>
      <if test="code != null">
        CODE = #{code,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        NAME = #{name,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        TYPE = #{type,jdbcType=VARCHAR},
      </if>
      <if test="params != null">
        PARAMS = #{params,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        STATUS = #{status,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=DECIMAL},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null">
        UPDATOR = #{updator,jdbcType=DECIMAL},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.mch.domain.PasSettCycleRule">
    update PAS_SETT_CYCLE_RULE
    set CODE = #{code,jdbcType=VARCHAR},
      NAME = #{name,jdbcType=VARCHAR},
      TYPE = #{type,jdbcType=VARCHAR},
      PARAMS = #{params,jdbcType=VARCHAR},
      STATUS = #{status,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=DECIMAL},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      UPDATOR = #{updator,jdbcType=DECIMAL}
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  
  <select id="selectByTypeAndParamsAndStatus" resultMap="BaseResultMap">
  	select 
    <include refid="Base_Column_List" />
    from PAS_SETT_CYCLE_RULE
    where TYPE = #{type,jdbcType=VARCHAR} and PARAMS = #{params , jdbcType=VARCHAR} 
    and STATUS = #{status , jdbcType = VARCHAR}
  </select>
  
  <select id="selectByTypeAndStatus" resultMap="BaseResultMap">
  	select 
    <include refid="Base_Column_List" />
    from PAS_SETT_CYCLE_RULE
    where TYPE = #{type,jdbcType=VARCHAR}
    and STATUS = #{status , jdbcType = VARCHAR}
  </select>
  
  <select id="selectAll" resultMap="BaseResultMap">
  	select 
    <include refid="Base_Column_List" />
    from PAS_SETT_CYCLE_RULE
  </select>
  
  <select id="selectRuleByStatus" resultMap="BaseResultMap">
  	select 
    <include refid="Base_Column_List" />
    from PAS_SETT_CYCLE_RULE 
    where STATUS = #{status , jdbcType=VARCHAR}
  </select>
  
  <select id="selectByCode" resultMap="BaseResultMap">
  	select 
    <include refid="Base_Column_List" />
    from PAS_SETT_CYCLE_RULE 
    where CODE = #{code , jdbcType=VARCHAR}
  </select>
  
</mapper>