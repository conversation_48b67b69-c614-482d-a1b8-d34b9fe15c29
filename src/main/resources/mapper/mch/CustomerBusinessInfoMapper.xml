<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.pas.mch.dao.CustomerBusinessInfoMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.mch.domain.CustomerBusinessInfo">
    <id column="BUSINESS_ID" jdbcType="DECIMAL" property="businessId" />
    <result column="CUSTOMER_INFO_ID" jdbcType="DECIMAL" property="customerInfoId" />
    <result column="CUSTOMER_ID" jdbcType="DECIMAL" property="customerId" />
    <result column="CUSTOMER_CODE" jdbcType="VARCHAR" property="customerCode" />
    <result column="BUSINESS_CODE" jdbcType="VARCHAR" property="businessCode" />
    <result column="RATE_NAME" jdbcType="VARCHAR" property="rateName" />
    <result column="RATE_MODE" jdbcType="DECIMAL" property="rateMode" />
    <result column="RATE_PARAM" jdbcType="VARCHAR" property="rateParam" />
    <result column="STATUS" jdbcType="DECIMAL" property="status" />
    <result column="FOLLOW_WECHAT_ACCOUNT" jdbcType="CHAR" property="followWechatAccount" />
    <result column="REFUND_PRODUCURE_FEE" jdbcType="CHAR" property="refundProducureFee" />
    <result column="REFUND_RATE_MODE" jdbcType="CHAR" property="refundRateMode" />
    <result column="REFUND_RATE_PARAM" jdbcType="CHAR" property="refundRateParam" />
    <result column="BUSSINESS_EXAM_ID" jdbcType="VARCHAR" property="businessExamId"/>
    <result column="BEGIN_TIME" jdbcType="TIMESTAMP" property="beginTime"/>
    <result column="END_TIME" jdbcType="TIMESTAMP" property="endTime" />
    <result column="FEE_PER" jdbcType="DECIMAL" property="feePer" />
    <result column="MIN_FEE" jdbcType="DECIMAL" property="minFee" />
    <result column="MAX_FEE" jdbcType="DECIMAL" property="maxFee" />
    <result column="NO_CREDITCARDS" jdbcType="VARCHAR" property="noCreditcards"/>
    <result column="CAN_REFUND" property="canRefund" jdbcType = "VARCHAR"/>
    <result column="SETT_CYCLE" property="settCycle" jdbcType="VARCHAR"/>
    <result column="BUSINESS_CATEGORY_CODE" property="businessCategoryCode" jdbcType = "VARCHAR"/>
    <result column="CAN_ADVANCE" property="canAdvance" jdbcType = "VARCHAR"/>
    <result column="BUSINESS_CATEGORY_NAME" property="businessCategoryName" jdbcType = "VARCHAR"/>
  </resultMap>
  <sql id="Base_Column_List">
    BUSINESS_ID, CUSTOMER_INFO_ID, CUSTOMER_ID, CUSTOMER_CODE, BUSINESS_CODE, RATE_NAME,
    RATE_MODE, RATE_PARAM, STATUS, FOLLOW_WECHAT_ACCOUNT, REFUND_PRODUCURE_FEE, REFUND_RATE_MODE,
    REFUND_RATE_PARAM , BUSSINESS_EXAM_ID , BEGIN_TIME , END_TIME ,FEE_PER    ,MIN_FEE  ,MAX_FEE,
    NO_CREDITCARDS,CAN_REFUND,SETT_CYCLE,BUSINESS_CATEGORY_CODE,CAN_ADVANCE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from PAS_CUSTOMER_BUSINESS_INFO
    where BUSINESS_ID = #{businessId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from PAS_CUSTOMER_BUSINESS_INFO
    where BUSINESS_ID = #{businessId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.mch.domain.CustomerBusinessInfo">
    insert into PAS_CUSTOMER_BUSINESS_INFO (BUSINESS_ID, CUSTOMER_INFO_ID, CUSTOMER_ID,
      CUSTOMER_CODE, BUSINESS_CODE, RATE_NAME,
      RATE_MODE, RATE_PARAM, STATUS,
      FOLLOW_WECHAT_ACCOUNT, REFUND_PRODUCURE_FEE, REFUND_RATE_MODE,
      REFUND_RATE_PARAM, BUSSINESS_EXAM_ID , BEGIN_TIME , END_TIME,FEE_PER    ,MIN_FEE  ,MAX_FEE ,
      NO_CREDITCARDS,CAN_REFUND,SETT_CYCLE,BUSINESS_CATEGORY_CODE,CAN_ADVANCE)
    values (#{businessId,jdbcType=DECIMAL}, #{customerInfoId,jdbcType=DECIMAL}, #{customerId,jdbcType=DECIMAL},
      #{customerCode,jdbcType=VARCHAR}, #{businessCode,jdbcType=VARCHAR}, #{rateName,jdbcType=VARCHAR},
      #{rateMode,jdbcType=DECIMAL}, #{rateParam,jdbcType=VARCHAR}, #{status,jdbcType=DECIMAL},
      #{followWechatAccount,jdbcType=CHAR}, #{refundProducureFee,jdbcType=CHAR}, #{refundRateMode,jdbcType=CHAR},
      #{refundRateParam,jdbcType=CHAR} , #{businessExamId , jdbcType=VARCHAR} , #{beginTime , jdbcType=TIMESTAMP} ,
      #{endTime , jdbcType=TIMESTAMP},
      #{feePer,jdbcType=DECIMAL} , #{minFee , jdbcType=DECIMAL} , #{maxFee , jdbcType=DECIMAL} ,
      #{noCreditcards , jdbcType = VARCHAR} ,#{canRefund , jdbcType = VARCHAR}, #{settCycle,jdbcType=VARCHAR},
       #{businessCategoryCode , jdbcType = VARCHAR},#{canAdvance , jdbcType = VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.mch.domain.CustomerBusinessInfo">
    insert into PAS_CUSTOMER_BUSINESS_INFO
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        BUSINESS_ID,
      </if>
      <if test="customerInfoId != null">
        CUSTOMER_INFO_ID,
      </if>
      <if test="customerId != null">
        CUSTOMER_ID,
      </if>
      <if test="customerCode != null">
        CUSTOMER_CODE,
      </if>
      <if test="businessCode != null">
        BUSINESS_CODE,
      </if>
      <if test="rateName != null">
        RATE_NAME,
      </if>
      <if test="rateMode != null">
        RATE_MODE,
      </if>
      <if test="rateParam != null">
        RATE_PARAM,
      </if>
      <if test="status != null">
        STATUS,
      </if>
      <if test="followWechatAccount != null">
        FOLLOW_WECHAT_ACCOUNT,
      </if>
      <if test="refundProducureFee != null">
        REFUND_PRODUCURE_FEE,
      </if>
      <if test="refundRateMode != null">
        REFUND_RATE_MODE,
      </if>
      <if test="refundRateParam != null">
        REFUND_RATE_PARAM,
      </if>
      <if test="businessExamId != null">
        BUSSINESS_EXAM_ID,
      </if>
      <if test="beginTime != null">
        BEGIN_TIME,
      </if>
      <if test="endTime != null">
        END_TIME,
      </if>
      <if test="feePer != null">
        FEE_PER,
      </if>
      <if test="minFee != null">
        MIN_FEE,
      </if>
      <if test="maxFee != null">
        MAX_FEE,
      </if>
      <if test="noCreditcards != null">
        NO_CREDITCARDS,
      </if>
      <if test="canRefund != null">
        CAN_REFUND,
      </if>
      <if test="settCycle != null">
        SETT_CYCLE,
      </if>
      <if test="businessCategoryCode != null">
        BUSINESS_CATEGORY_CODE,
      </if>
      <if test="canAdvance != null">
        CAN_ADVANCE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=DECIMAL},
      </if>
      <if test="customerInfoId != null">
        #{customerInfoId,jdbcType=DECIMAL},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=DECIMAL},
      </if>
      <if test="customerCode != null">
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="businessCode != null">
        #{businessCode,jdbcType=VARCHAR},
      </if>
      <if test="rateName != null">
        #{rateName,jdbcType=VARCHAR},
      </if>
      <if test="rateMode != null">
        #{rateMode,jdbcType=DECIMAL},
      </if>
      <if test="rateParam != null">
        #{rateParam,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=DECIMAL},
      </if>
      <if test="followWechatAccount != null">
        #{followWechatAccount,jdbcType=CHAR},
      </if>
      <if test="refundProducureFee != null">
        #{refundProducureFee,jdbcType=CHAR},
      </if>
      <if test="refundRateMode != null">
        #{refundRateMode,jdbcType=CHAR},
      </if>
      <if test="refundRateParam != null">
        #{refundRateParam,jdbcType=CHAR},
      </if>
      <if test="businessExamId != null">
        #{businessExamId,jdbcType=VARCHAR},
      </if>
      <if test="beginTime != null">
        #{beginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="feePer != null">
        #{feePer,jdbcType=DECIMAL},
      </if>
      <if test="minFee != null">
        #{minFee,jdbcType=DECIMAL},
      </if>
      <if test="maxFee != null">
        #{maxFee,jdbcType=DECIMAL},
      </if>
      <if test="noCreditcards != null">
        #{noCreditcards,jdbcType=VARCHAR},
      </if>
      <if test="canRefund != null">
        #{canRefund,jdbcType=VARCHAR},
      </if>
      <if test="settCycle != null">
        #{SETT_CYCLE,jdbcType=VARCHAR},
      </if>
      <if test="businessCategoryCode != null">
        #{businessCategoryCode , jdbcType = VARCHAR},
      </if>
      <if test="canAdvance != null">
        #{canAdvance , jdbcType = VARCHAR} ,
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.mch.domain.CustomerBusinessInfo">
    update PAS_CUSTOMER_BUSINESS_INFO
    <set>
      <if test="customerInfoId != null">
        CUSTOMER_INFO_ID = #{customerInfoId,jdbcType=DECIMAL},
      </if>
      <if test="customerId != null">
        CUSTOMER_ID = #{customerId,jdbcType=DECIMAL},
      </if>
      <if test="customerCode != null">
        CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="businessCode != null">
        BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR},
      </if>
      <if test="rateName != null">
        RATE_NAME = #{rateName,jdbcType=VARCHAR},
      </if>
      <if test="rateMode != null">
        RATE_MODE = #{rateMode,jdbcType=DECIMAL},
      </if>
     <!-- <if test="rateParam != null">-->
        RATE_PARAM = #{rateParam,jdbcType=VARCHAR},
     <!-- </if>-->
      <if test="status != null">
        STATUS = #{status,jdbcType=DECIMAL},
      </if>
      <if test="followWechatAccount != null">
        FOLLOW_WECHAT_ACCOUNT = #{followWechatAccount,jdbcType=CHAR},
      </if>
      <if test="refundProducureFee != null">
        REFUND_PRODUCURE_FEE = #{refundProducureFee,jdbcType=CHAR},
      </if>
      <if test="refundRateMode != null">
        REFUND_RATE_MODE = #{refundRateMode,jdbcType=CHAR},
      </if>
      <if test="refundRateParam != null">
        REFUND_RATE_PARAM = #{refundRateParam,jdbcType=CHAR},
      </if>
      <if test="businessExamId != null">
        BUSSINESS_EXAM_ID = #{businessExamId,jdbcType=VARCHAR},
      </if>
      <if test="beginTime != null">
        BEGIN_TIME = #{beginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        END_TIME = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <!--<if test="feePer != null">-->
        FEE_PER = #{feePer,jdbcType=DECIMAL},
     <!-- </if>-->
      <if test="minFee != null">
        MIN_FEE = #{minFee,jdbcType=DECIMAL},
      </if>
      <if test="maxFee != null">
        MAX_FEE = #{maxFee,jdbcType=DECIMAL},
      </if>
      <if test="noCreditcards != null">
        NO_CREDITCARDS = #{noCreditcards,jdbcType=VARCHAR},
      </if>
      <if test="canRefund != null">
        CAN_REFUND =  #{canRefund,jdbcType=VARCHAR},
      </if>
      <if test="settCycle != null">
        SETT_CYCLE =  #{settCycle,jdbcType=VARCHAR},
      </if>
      <if test="businessCategoryCode != null">
        BUSINESS_CATEGORY_CODE = #{businessCategoryCode,jdbcType=VARCHAR},
      </if>
      <if test="canAdvance != null">
        CAN_ADVANCE = #{canAdvance,jdbcType=VARCHAR},
      </if>
    </set>
    where BUSINESS_ID = #{businessId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.mch.domain.CustomerBusinessInfo">
    update PAS_CUSTOMER_BUSINESS_INFO
    set CUSTOMER_INFO_ID = #{customerInfoId,jdbcType=DECIMAL},
      CUSTOMER_ID = #{customerId,jdbcType=DECIMAL},
      CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR},
      RATE_NAME = #{rateName,jdbcType=VARCHAR},
      RATE_MODE = #{rateMode,jdbcType=DECIMAL},
      RATE_PARAM = #{rateParam,jdbcType=VARCHAR},
      STATUS = #{status,jdbcType=DECIMAL},
      FOLLOW_WECHAT_ACCOUNT = #{followWechatAccount,jdbcType=CHAR},
      REFUND_PRODUCURE_FEE = #{refundProducureFee,jdbcType=CHAR},
      REFUND_RATE_MODE = #{refundRateMode,jdbcType=CHAR},
      REFUND_RATE_PARAM = #{refundRateParam,jdbcType=CHAR},
      BUSSINESS_EXAM_ID = #{businessExamId,jdbcType=VARCHAR},
      BEGIN_TIME = #{beginTime,jdbcType=TIMESTAMP},
      END_TIME = #{endTime,jdbcType=TIMESTAMP} ,
      FEE_PER =  #{feePer,jdbcType=DECIMAL} ,
      MIN_FEE =   #{minFee , jdbcType=DECIMAL} ,
      MAX_FEE =  #{maxFee , jdbcType=DECIMAL},
      NO_CREDITCARDS =  #{noCreditcards , jdbcType=VARCHAR} ,
      CAN_REFUND =  #{canRefund,jdbcType=VARCHAR},
      SETT_CYCLE = #{settCycle,jdbcType=VARCHAR},
      BUSINESS_CATEGORY_CODE =  #{businessCategoryCode,jdbcType=VARCHAR},
      CAN_ADVANCE =  #{canAdvance,jdbcType=VARCHAR}
    where BUSINESS_ID = #{businessId,jdbcType=DECIMAL}
  </update>

  <select id="getByCustomerInfoId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from PAS_CUSTOMER_BUSINESS_INFO
    where CUSTOMER_INFO_ID = #{customerInfoId,jdbcType=DECIMAL}
  </select>

  <select id="selectByBusinessCode" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from PAS_CUSTOMER_BUSINESS_INFO
    where BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR}
  </select>

  <!-- page query list -->
  <select id="pageQueryCustomerBusinessInfos" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from (
    select A.*, rownum RN
    from (
    select *
    from PAS_CUSTOMER_BUSINESS_INFO
    where CUSTOMER_INFO_ID = #{customerInfoId,jdbcType=DECIMAL}
    ) A
    where rownum &lt;= #{endRowNo,jdbcType=DECIMAL}
    )
    where RN &gt;= #{beginRowNo,jdbcType=DECIMAL}
  </select>
  <select id="countCustomerBusinessInfos" parameterType="java.lang.Long" resultType="java.lang.Integer">
		select count(*)
		from PAS_CUSTOMER_BUSINESS_INFO
		where CUSTOMER_INFO_ID = #{customerInfoId,jdbcType=DECIMAL}
	</select>

  <select id="queryListByCustomerInfoId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from PAS_CUSTOMER_BUSINESS_INFO
    where CUSTOMER_INFO_ID = #{customerInfoId,jdbcType=DECIMAL}
  </select>

  <select id="countCustomerBusinessInfo" resultType="int">
		select count(*)
		from PAS_CUSTOMER_BUSINESS_INFO
		where CUSTOMER_INFO_ID = #{customerInfoId,jdbcType=DECIMAL}
	</select>

  <select id="isBusinessInfoExist" resultType="boolean">
		select count(*)
		from PAS_CUSTOMER_BUSINESS_INFO
		where CUSTOMER_INFO_ID = #{customerInfoId,jdbcType=DECIMAL} and BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR}
	</select>

  <select id="selectAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from PAS_CUSTOMER_BUSINESS_INFO
  </select>

  <select id="selectByCustomerCode" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from PAS_CUSTOMER_BUSINESS_INFO
    where CUSTOMER_CODE = #{customerCode , jdbcType = VARCHAR}
  </select>
  <select id="selectByBusinessCodeAndInfoId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from PAS_CUSTOMER_BUSINESS_INFO
    where BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR}
    and CUSTOMER_INFO_ID = #{customerInfoId , jdbcType = INTEGER}
  </select>

  <select id="selectByInfoId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from PAS_CUSTOMER_BUSINESS_INFO
    where CUSTOMER_INFO_ID = #{customerInfoId , jdbcType = INTEGER}
  </select>

  <select id="selectByBusinessCodes" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from PAS_CUSTOMER_BUSINESS_INFO
    where CUSTOMER_INFO_ID = #{customerInfoId , jdbcType = INTEGER}
    and BUSINESS_CODE in
    <foreach collection="businessCodes" open="(" close=")" separator="," item="businessCode">
      #{businessCode , jdbcType = VARCHAR}
    </foreach>
    and sysdate between BEGIN_TIME and END_TIME
  </select>

  <select id="selectByInfoIdAndBusinessCodeAndDate" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from PAS_CUSTOMER_BUSINESS_INFO
    where CUSTOMER_INFO_ID = #{infoId , jdbcType = DECIMAL}
    and BUSINESS_CODE = #{businessCode , jdbcType = VARCHAR}
    and BEGIN_TIME &lt;= #{date , jdbcType = TIMESTAMP}
    and END_TIME &gt; #{date , jdbcType = TIMESTAMP}
  </select>

  <select id="countCustomerBusinessInfosById" parameterType="java.util.Map" resultType="java.lang.Integer">
    select count(*)
    from PAS_CUSTOMER_BUSINESS_INFO  t left join pas_business_category c2 on t.business_category_code =c2.code
    where t.CUSTOMER_INFO_ID = #{customerInfoId,jdbcType=DECIMAL}
    <if test="businessId != null ">
      and t.business_id = #{businessId,jdbcType=DECIMAL}
    </if>
  </select>

  <select id="pageQueryCustomerBusinessInfosById" resultMap="BaseResultMap">
    select
    BUSINESS_ID, CUSTOMER_INFO_ID, CUSTOMER_ID, CUSTOMER_CODE, BUSINESS_CODE, RATE_NAME,
            RATE_MODE, RATE_PARAM, STATUS, FOLLOW_WECHAT_ACCOUNT, REFUND_PRODUCURE_FEE, REFUND_RATE_MODE,
            REFUND_RATE_PARAM , BUSSINESS_EXAM_ID , BEGIN_TIME , END_TIME ,FEE_PER    ,MIN_FEE  ,MAX_FEE,
            NO_CREDITCARDS,CAN_REFUND,SETT_CYCLE,BUSINESS_CATEGORY_CODE,CAN_ADVANCE,BUSINESS_CATEGORY_NAME
    from (
    select A.*, rownum RN
    from (
    select  t.BUSINESS_ID, t.CUSTOMER_INFO_ID, t.CUSTOMER_ID, t.CUSTOMER_CODE, t.BUSINESS_CODE, t.RATE_NAME,
    t.RATE_MODE, t.RATE_PARAM, t.STATUS, t.FOLLOW_WECHAT_ACCOUNT, t.REFUND_PRODUCURE_FEE, t.REFUND_RATE_MODE,
    t.REFUND_RATE_PARAM , t.BUSSINESS_EXAM_ID , t.BEGIN_TIME , t.END_TIME ,t.FEE_PER    ,t.MIN_FEE  ,t.MAX_FEE,
    t.NO_CREDITCARDS,t.CAN_REFUND,t.SETT_CYCLE,t.BUSINESS_CATEGORY_CODE,t.CAN_ADVANCE,c2.NAME as BUSINESS_CATEGORY_NAME
    from PAS_CUSTOMER_BUSINESS_INFO  t left join pas_business_category c2 on t.business_category_code =c2.code
    where t.CUSTOMER_INFO_ID = #{customerInfoId,jdbcType=DECIMAL}
    <if test="businessId != null ">
      and t.business_id = #{businessId,jdbcType=DECIMAL}
    </if>
    ) A
    where rownum &lt;= #{endRowNo,jdbcType=DECIMAL}
    )
    where RN &gt;= #{beginRowNo,jdbcType=DECIMAL}
  </select>

  <resultMap id="BaseResultMap1" type="com.epaylinks.efps.pas.mch.domain.CustomerBusinessInfoVO">
     <id column="BUSINESS_ID" jdbcType="DECIMAL" property="businessId" />
     <result column="CUSTOMER_INFO_ID" jdbcType="DECIMAL" property="customerInfoId" />
     <!--<result column="CUSTOMER_CODE" jdbcType="VARCHAR" property="customerCode" />-->
     <result column="PARENT_CUSTOMER_CODE" jdbcType="VARCHAR" property="parentCustomerCode" />
     <result column="BUSINESS_CODE" jdbcType="VARCHAR" property="businessCode" />
     <result column="business_id" jdbcType="DECIMAL" property="businessId" />
  </resultMap>
  <select id="selectAllEmptyCategory" resultMap="BaseResultMap1">
      select t.business_id,t.business_code,t.customer_info_id,c.parent_customer_code
          from PAS_CUSTOMER_BUSINESS_INFO t,pas_customer_info c where t.customer_info_id = c.info_id and  t.business_category_code is null
    </select>
  <update id="updateByPkBusiness" parameterType="com.epaylinks.efps.pas.mch.domain.CustomerBusinessInfo">
      update PAS_CUSTOMER_BUSINESS_INFOtmp
      set
        BUSINESS_CATEGORY_CODE =  #{businessCategoryCode,jdbcType=VARCHAR}

      where BUSINESS_ID = #{businessId,jdbcType=DECIMAL}   and BUSINESS_CODE =  #{businessCode,jdbcType=VARCHAR}
    </update>

</mapper>