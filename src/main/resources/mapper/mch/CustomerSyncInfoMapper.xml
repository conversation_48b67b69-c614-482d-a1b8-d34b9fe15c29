<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.pas.mch.dao.CustomerSyncInfoMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.mch.domain.CustomerSyncInfo">
    <id column="SYNC_ID" jdbcType="DECIMAL" property="syncId" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="SYNC_TYPE" jdbcType="VARCHAR" property="syncType" />
    <result column="RECORD_ID" jdbcType="DECIMAL" property="recordId" />
    <result column="CUSTOMER_CODE" jdbcType="VARCHAR" property="customerCode" />
    <result column="RETRY_COUNT" jdbcType="DECIMAL" property="retryCount" />
    <result column="LAST_RETRY_TIME" jdbcType="TIMESTAMP" property="lastRetryTime" />
    <result column="ERROR_CODE" jdbcType="VARCHAR" property="errorCode" />
  </resultMap>
  <sql id="Base_Column_List">
    SYNC_ID, CREATE_TIME, SYNC_TYPE, RECORD_ID, CUSTOMER_CODE, RETRY_COUNT, LAST_RETRY_TIME, 
    ERROR_CODE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from PAS_CUSTOMER_SYNC_INFO
    where SYNC_ID = #{syncId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from PAS_CUSTOMER_SYNC_INFO
    where SYNC_ID = #{syncId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.mch.domain.CustomerSyncInfo">
    insert into PAS_CUSTOMER_SYNC_INFO (SYNC_ID, CREATE_TIME, SYNC_TYPE, 
      RECORD_ID, CUSTOMER_CODE, RETRY_COUNT, 
      LAST_RETRY_TIME, ERROR_CODE)
    values (#{syncId,jdbcType=DECIMAL}, #{createTime,jdbcType=TIMESTAMP}, #{syncType,jdbcType=VARCHAR}, 
      #{recordId,jdbcType=DECIMAL}, #{customerCode,jdbcType=VARCHAR}, #{retryCount,jdbcType=DECIMAL}, 
      #{lastRetryTime,jdbcType=TIMESTAMP}, #{errorCode,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.mch.domain.CustomerSyncInfo">
    insert into PAS_CUSTOMER_SYNC_INFO
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="syncId != null">
        SYNC_ID,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="syncType != null">
        SYNC_TYPE,
      </if>
      <if test="recordId != null">
        RECORD_ID,
      </if>
      <if test="customerCode != null">
        CUSTOMER_CODE,
      </if>
      <if test="retryCount != null">
        RETRY_COUNT,
      </if>
      <if test="lastRetryTime != null">
        LAST_RETRY_TIME,
      </if>
      <if test="errorCode != null">
        ERROR_CODE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="syncId != null">
        #{syncId,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="syncType != null">
        #{syncType,jdbcType=VARCHAR},
      </if>
      <if test="recordId != null">
        #{recordId,jdbcType=DECIMAL},
      </if>
      <if test="customerCode != null">
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="retryCount != null">
        #{retryCount,jdbcType=DECIMAL},
      </if>
      <if test="lastRetryTime != null">
        #{lastRetryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="errorCode != null">
        #{errorCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.mch.domain.CustomerSyncInfo">
    update PAS_CUSTOMER_SYNC_INFO
    <set>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="syncType != null">
        SYNC_TYPE = #{syncType,jdbcType=VARCHAR},
      </if>
      <if test="recordId != null">
        RECORD_ID = #{recordId,jdbcType=DECIMAL},
      </if>
      <if test="customerCode != null">
        CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="retryCount != null">
        RETRY_COUNT = #{retryCount,jdbcType=DECIMAL},
      </if>
      <if test="lastRetryTime != null">
        LAST_RETRY_TIME = #{lastRetryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="errorCode != null">
        ERROR_CODE = #{errorCode,jdbcType=VARCHAR},
      </if>
    </set>
    where SYNC_ID = #{syncId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.mch.domain.CustomerSyncInfo">
    update PAS_CUSTOMER_SYNC_INFO
    set CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      SYNC_TYPE = #{syncType,jdbcType=VARCHAR},
      RECORD_ID = #{recordId,jdbcType=DECIMAL},
      CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      RETRY_COUNT = #{retryCount,jdbcType=DECIMAL},
      LAST_RETRY_TIME = #{lastRetryTime,jdbcType=TIMESTAMP},
      ERROR_CODE = #{errorCode,jdbcType=VARCHAR}
    where SYNC_ID = #{syncId,jdbcType=DECIMAL}
  </update>
  
  <!-- query all -->
  <select id="queryAllSyncInfo" resultMap="BaseResultMap">
    select
	<include refid="Base_Column_List" />
	from PAS_CUSTOMER_SYNC_INFO
	order by CREATE_TIME
  </select>
  
  <select id="countSyncInfoByCustomerInfoId" parameterType="Long" resultType="int">
    select count(*)
    from PAS_CUSTOMER_SYNC_INFO
    where RECORD_ID = #{customerInfoId,jdbcType=DECIMAL}
  </select>
  
</mapper>