<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.pas.mch.dao.CustomerAuditInfoMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.mch.domain.CustomerAuditInfo">
    <id column="AUDIT_INFO_ID" jdbcType="DECIMAL" property="auditInfoId" />
    <result column="CUSTOMER_INFO_ID" jdbcType="DECIMAL" property="customerInfoId" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="OPERATION_TYPE" jdbcType="VARCHAR" property="operationType" />
    <result column="CREATOR_ID" jdbcType="DECIMAL" property="creatorId" />
    <result column="AUDIT_STATUS" jdbcType="VARCHAR" property="auditStatus" />
    <result column="FIRST_AUDITOR" jdbcType="DECIMAL" property="firstAuditor" />
    <result column="FIRST_AUDIT_TIME" jdbcType="TIMESTAMP" property="firstAuditTime" />
    <result column="FIRST_AUDIT_COMMENT" jdbcType="VARCHAR" property="firstAuditComment" />
    <result column="SECOND_AUDITOR" jdbcType="DECIMAL" property="secondAuditor" />
    <result column="SECOND_AUDIT_TIME" jdbcType="TIMESTAMP" property="secondAuditTime" />
    <result column="SECOND_AUDIT_COMMENT" jdbcType="VARCHAR" property="secondAuditComment" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    AUDIT_INFO_ID, CUSTOMER_INFO_ID, CREATE_TIME, OPERATION_TYPE, CREATOR_ID, AUDIT_STATUS, 
    FIRST_AUDITOR, FIRST_AUDIT_TIME, FIRST_AUDIT_COMMENT, SECOND_AUDITOR, SECOND_AUDIT_TIME, 
    SECOND_AUDIT_COMMENT, UPDATE_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from PAS_CUSTOMER_AUDIT_INFO
    where AUDIT_INFO_ID = #{auditInfoId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from PAS_CUSTOMER_AUDIT_INFO
    where AUDIT_INFO_ID = #{auditInfoId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.mch.domain.CustomerAuditInfo">
    insert into PAS_CUSTOMER_AUDIT_INFO (AUDIT_INFO_ID, CUSTOMER_INFO_ID, CREATE_TIME, 
      OPERATION_TYPE, CREATOR_ID, AUDIT_STATUS, 
      FIRST_AUDITOR, FIRST_AUDIT_TIME, FIRST_AUDIT_COMMENT, 
      SECOND_AUDITOR, SECOND_AUDIT_TIME, SECOND_AUDIT_COMMENT, 
      UPDATE_TIME)
    values (#{auditInfoId,jdbcType=DECIMAL}, #{customerInfoId,jdbcType=DECIMAL}, #{createTime,jdbcType=TIMESTAMP}, 
      #{operationType,jdbcType=VARCHAR}, #{creatorId,jdbcType=DECIMAL}, #{auditStatus,jdbcType=VARCHAR}, 
      #{firstAuditor,jdbcType=DECIMAL}, #{firstAuditTime,jdbcType=TIMESTAMP}, #{firstAuditComment,jdbcType=VARCHAR}, 
      #{secondAuditor,jdbcType=DECIMAL}, #{secondAuditTime,jdbcType=TIMESTAMP}, #{secondAuditComment,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.mch.domain.CustomerAuditInfo">
    insert into PAS_CUSTOMER_AUDIT_INFO
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="auditInfoId != null">
        AUDIT_INFO_ID,
      </if>
      <if test="customerInfoId != null">
        CUSTOMER_INFO_ID,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="operationType != null">
        OPERATION_TYPE,
      </if>
      <if test="creatorId != null">
        CREATOR_ID,
      </if>
      <if test="auditStatus != null">
        AUDIT_STATUS,
      </if>
      <if test="firstAuditor != null">
        FIRST_AUDITOR,
      </if>
      <if test="firstAuditTime != null">
        FIRST_AUDIT_TIME,
      </if>
      <if test="firstAuditComment != null">
        FIRST_AUDIT_COMMENT,
      </if>
      <if test="secondAuditor != null">
        SECOND_AUDITOR,
      </if>
      <if test="secondAuditTime != null">
        SECOND_AUDIT_TIME,
      </if>
      <if test="secondAuditComment != null">
        SECOND_AUDIT_COMMENT,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="auditInfoId != null">
        #{auditInfoId,jdbcType=DECIMAL},
      </if>
      <if test="customerInfoId != null">
        #{customerInfoId,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operationType != null">
        #{operationType,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=DECIMAL},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=VARCHAR},
      </if>
      <if test="firstAuditor != null">
        #{firstAuditor,jdbcType=DECIMAL},
      </if>
      <if test="firstAuditTime != null">
        #{firstAuditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="firstAuditComment != null">
        #{firstAuditComment,jdbcType=VARCHAR},
      </if>
      <if test="secondAuditor != null">
        #{secondAuditor,jdbcType=DECIMAL},
      </if>
      <if test="secondAuditTime != null">
        #{secondAuditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="secondAuditComment != null">
        #{secondAuditComment,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.mch.domain.CustomerAuditInfo">
    update PAS_CUSTOMER_AUDIT_INFO
    <set>
      <if test="customerInfoId != null">
        CUSTOMER_INFO_ID = #{customerInfoId,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operationType != null">
        OPERATION_TYPE = #{operationType,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        CREATOR_ID = #{creatorId,jdbcType=DECIMAL},
      </if>
      <if test="auditStatus != null">
        AUDIT_STATUS = #{auditStatus,jdbcType=VARCHAR},
      </if>
      <if test="firstAuditor != null">
        FIRST_AUDITOR = #{firstAuditor,jdbcType=DECIMAL},
      </if>
      <if test="firstAuditTime != null">
        FIRST_AUDIT_TIME = #{firstAuditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="firstAuditComment != null">
        FIRST_AUDIT_COMMENT = #{firstAuditComment,jdbcType=VARCHAR},
      </if>
      <if test="secondAuditor != null">
        SECOND_AUDITOR = #{secondAuditor,jdbcType=DECIMAL},
      </if>
      <if test="secondAuditTime != null">
        SECOND_AUDIT_TIME = #{secondAuditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="secondAuditComment != null">
        SECOND_AUDIT_COMMENT = #{secondAuditComment,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where AUDIT_INFO_ID = #{auditInfoId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.mch.domain.CustomerAuditInfo">
    update PAS_CUSTOMER_AUDIT_INFO
    set CUSTOMER_INFO_ID = #{customerInfoId,jdbcType=DECIMAL},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      OPERATION_TYPE = #{operationType,jdbcType=VARCHAR},
      CREATOR_ID = #{creatorId,jdbcType=DECIMAL},
      AUDIT_STATUS = #{auditStatus,jdbcType=VARCHAR},
      FIRST_AUDITOR = #{firstAuditor,jdbcType=DECIMAL},
      FIRST_AUDIT_TIME = #{firstAuditTime,jdbcType=TIMESTAMP},
      FIRST_AUDIT_COMMENT = #{firstAuditComment,jdbcType=VARCHAR},
      SECOND_AUDITOR = #{secondAuditor,jdbcType=DECIMAL},
      SECOND_AUDIT_TIME = #{secondAuditTime,jdbcType=TIMESTAMP},
      SECOND_AUDIT_COMMENT = #{secondAuditComment,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
    where AUDIT_INFO_ID = #{auditInfoId,jdbcType=DECIMAL}
  </update>
  
  <select id="getByCustomerInfoId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from PAS_CUSTOMER_AUDIT_INFO
    where CUSTOMER_INFO_ID = #{customerInfoId,jdbcType=DECIMAL}
  </select>
  
    <!-- 审核信息查询列表返回类型 -->
	<resultMap id="customerAuditResultMap" type="com.epaylinks.efps.pas.mch.model.CustomerAuditResult">
	    <result column="CUSTOMER_AUDIT_INFO_ID" jdbcType="DECIMAL" property="customerAuditInfoId" />
	    <result column="CUSTOMER_INFO_ID" jdbcType="DECIMAL" property="customerInfoId" />
	    <result column="CUSTOMER_NAME" jdbcType="VARCHAR" property="customerName" />
	    <result column="OPERATION_TYPE" jdbcType="VARCHAR" property="operationType" />
	    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
	    <result column="CREATOR_ID" jdbcType="DECIMAL" property="creatorId" />
	    <result column="AUDIT_STATUS" jdbcType="VARCHAR" property="auditStatus" />
	    <result column="SOURCE_CHANNEL" jdbcType="VARCHAR" property="sourceChannel" />
	</resultMap>
    
	<select id="pageQueryCustomerAuditInfosOfWaitAudit" resultMap="customerAuditResultMap">
		select
			CUSTOMER_AUDIT_INFO_ID, CUSTOMER_INFO_ID, CUSTOMER_NAME, OPERATION_TYPE,
			UPDATE_TIME, CREATOR_ID, AUDIT_STATUS,SOURCE_CHANNEL
		from (
			select A.*, rownum RN
			from (
				select t1.AUDIT_INFO_ID CUSTOMER_AUDIT_INFO_ID, t2.info_id CUSTOMER_INFO_ID, t2.name customer_name, t1.OPERATION_TYPE OPERATION_TYPE,
				 t1.UPDATE_TIME UPDATE_TIME, t3.CREATOR_ID CREATOR_ID, t1.AUDIT_STATUS AUDIT_STATUS,t3.source_channel SOURCE_CHANNEL
				from PAS_CUSTOMER_AUDIT_INFO t1 left join PAS_CUSTOMER_INFO t2 on t1.CUSTOMER_INFO_ID=t2.INFO_ID
				 		left join PAS_CUSTOMER t3 on t2.CUSTOMER_ID=t3.CUSTOMER_ID
				where 1=1
				<if test="auditStatus != null and auditStatus != ''">
					<!-- 如果不为null，那么按照入参查询条件来查询 -->
					and t1.AUDIT_STATUS = #{auditStatus , jdbcType = VARCHAR}
				</if>
				<if test="auditStatus == null || auditStatus == ''">
					<!-- 如果为空，那么查询待初审和待复审的数据 -->
					and t1.AUDIT_STATUS in ('00','02')
				</if>
				<if test="submitTimeBegin != null">
					and t1.CREATE_TIME &gt;= #{submitTimeBegin , jdbcType = TIMESTAMP}
				</if>
				<if test="submitTimeEnd != null">
					and t1.CREATE_TIME &lt;= #{submitTimeEnd , jdbcType = TIMESTAMP}
				</if>
				<if test="customerName != null and customerName != ''">
					and t2.name = #{customerName , jdbcType = VARCHAR}
				</if>
				<if test="operationType != null and operationType != ''">
					and t1.OPERATION_TYPE = #{operationType , jdbcType = VARCHAR}
				</if>
            <if test="sourceChannel != null and sourceChannel != ''">
                and t3.source_channel = #{sourceChannel,jdbcType=VARCHAR}
            </if>
				order by t1.UPDATE_TIME desc
			) A
			where rownum &lt;= #{endRowNo,jdbcType=DECIMAL}
		)
		where RN &gt;= #{beginRowNo,jdbcType=DECIMAL}
	</select>
	<select id="countCustomerAuditInfosOfWaitAudit" resultType="java.lang.Integer">
		select count(*) 
		from PAS_CUSTOMER_AUDIT_INFO t1 left join PAS_CUSTOMER_INFO t2 on t1.CUSTOMER_INFO_ID=t2.INFO_ID
		left join PAS_CUSTOMER t3 on t2.CUSTOMER_ID=t3.CUSTOMER_ID
		where 1=1
		<if test="auditStatus != null and auditStatus != ''">
			<!-- 如果不为null，那么按照入参查询条件来查询 -->
			and t1.AUDIT_STATUS = #{auditStatus , jdbcType = VARCHAR}
		</if>
		<if test="auditStatus == null || auditStatus == ''">
			<!-- 如果为空，那么查询待初审和待复审的数据 -->
			and t1.AUDIT_STATUS in ('00','02')
		</if>
		<if test="submitTimeBegin != null">
			and t1.CREATE_TIME &gt;= #{submitTimeBegin , jdbcType = TIMESTAMP}
		</if>
		<if test="submitTimeEnd != null">
			and t1.CREATE_TIME &lt;= #{submitTimeEnd , jdbcType = TIMESTAMP}
		</if>
		<if test="customerName != null and customerName != ''">
			and t2.name = #{customerName , jdbcType = VARCHAR}
		</if>
		<if test="operationType != null and operationType != ''">
			and t1.OPERATION_TYPE = #{operationType , jdbcType = VARCHAR}
		</if>
        <if test="sourceChannel != null and sourceChannel != ''">
            and t3.source_channel = #{sourceChannel,jdbcType=VARCHAR}
        </if>
	</select>

	<select id="pageQueryCustomerAuditInfosOfRefuse" resultMap="customerAuditResultMap">
		select
	         CUSTOMER_AUDIT_INFO_ID, CUSTOMER_INFO_ID, CUSTOMER_NAME, OPERATION_TYPE,
	         UPDATE_TIME, CREATOR_ID, AUDIT_STATUS,SOURCE_CHANNEL
		from (
			select A.*, rownum RN
			from (
				select t1.AUDIT_INFO_ID CUSTOMER_AUDIT_INFO_ID, t2.INFO_ID CUSTOMER_INFO_ID, t2.name customer_name, t1.OPERATION_TYPE OPERATION_TYPE,
					t1.UPDATE_TIME UPDATE_TIME, t3.CREATOR_ID CREATOR_ID, t1.AUDIT_STATUS AUDIT_STATUS,t3.source_channel SOURCE_CHANNEL
				from PAS_CUSTOMER_AUDIT_INFO t1 left join PAS_CUSTOMER_INFO t2 on t1.CUSTOMER_INFO_ID=t2.INFO_ID
				 		left join PAS_CUSTOMER t3 on t2.CUSTOMER_ID=t3.CUSTOMER_ID
				where t1.AUDIT_STATUS in ('01','03')
				<if test="submitTimeBegin != null and submitTimeBegin != ''">
			        and t1.UPDATE_TIME &gt;= to_date(#{submitTimeBegin},'yyyy-MM-dd')
			    </if>
			    <if test="submitTimeEnd != null and submitTimeEnd != ''">
			        and t1.UPDATE_TIME &lt; to_date(#{submitTimeEnd},'yyyy-MM-dd')+1
			    </if>
			    <if test="operationType != null and operationType != ''">
			        and t1.OPERATION_TYPE = #{operationType,jdbcType=VARCHAR}
			    </if>
			    <if test="customerName != null and customerName != ''">
			        and t2.NAME like '%'||#{customerName}||'%'
			    </if>
			    <if test="auditStatus != null and auditStatus != ''">
			        and t1.AUDIT_STATUS = #{auditStatus,jdbcType=VARCHAR}
			    </if>
                <if test="sourceChannel != null and sourceChannel != ''">
                    and t3.source_channel = #{sourceChannel,jdbcType=VARCHAR}
                </if>
			    order by t1.UPDATE_TIME desc
			) A
			where rownum &lt;= #{endRowNo,jdbcType=DECIMAL}
		)
		where RN &gt;= #{beginRowNo,jdbcType=DECIMAL}
	</select>
	<select id="countCustomerAuditInfosOfRefuse" resultType="java.lang.Integer">
		select count(*)
		from PAS_CUSTOMER_AUDIT_INFO t1 left outer join PAS_CUSTOMER_INFO t2 on t1.CUSTOMER_INFO_ID=t2.INFO_ID
				 left join PAS_CUSTOMER t3 on t2.CUSTOMER_ID=t3.CUSTOMER_ID
		where t1.AUDIT_STATUS in ('01','03')
			<if test="submitTimeBegin != null and submitTimeBegin != ''">
		        and t1.UPDATE_TIME &gt;= to_date(#{submitTimeBegin},'yyyy-MM-dd')
		    </if>
		    <if test="submitTimeEnd != null and submitTimeEnd != ''">
		        and t1.UPDATE_TIME &lt; to_date(#{submitTimeEnd},'yyyy-MM-dd')+1
		    </if>
		    <if test="operationType != null and operationType != ''">
		        and t1.OPERATION_TYPE = #{operationType,jdbcType=VARCHAR}
		    </if>
		    <if test="customerName != null and customerName != ''">
		        and t2.NAME like '%'||#{customerName}||'%'
		    </if>
		    <if test="auditStatus != null and auditStatus != ''">
		        and t1.AUDIT_STATUS = #{auditStatus,jdbcType=VARCHAR}
		    </if>
            <if test="sourceChannel != null and sourceChannel != ''">
                and t3.source_channel = #{sourceChannel,jdbcType=VARCHAR}
            </if>
	</select>

    
    <select id="checkExistOfMchNameInWaitAuditeds" resultType="Boolean">
		select count(1)
		from pas_customer_audit_info t
			 left join pas_customer_info t2 on t.customer_info_id = t2.info_id
		where t.audit_status in ('00','02')
		   and t2.name = #{mchName,jdbcType=VARCHAR}
		   and t.customer_info_id != #{customerInfoId,jdbcType=DECIMAL}
	</select>
	
	<select id="checkExistOfLicenseNoInWaitAuditeds" resultType="Boolean">
		select count(1)
		from pas_customer_audit_info t
			 left join pas_customer_info t2 on t.customer_info_id = t2.info_id
		where t.audit_status in ('00','02')
		   and t2.business_license_no = #{businessLicenseNo,jdbcType=VARCHAR}
		   and t.customer_info_id != #{customerInfoId,jdbcType=DECIMAL}
	</select>
	
	<select id="checkExistOfLeaPersonIDNoInWaitAuditeds" resultType="Boolean">
		select count(1)
		from pas_customer_audit_info t
			 left join pas_customer_info t2 on t.customer_info_id = t2.info_id
		where t.audit_status in ('00','02')
		   and t2.LEA_PERSONI_DENTIFICATION_NO = #{leaPersoniDentificationNo,jdbcType=VARCHAR}
		   and t.customer_info_id != #{customerInfoId,jdbcType=DECIMAL}
		   <choose>
			  	<when test="parentCustomerCode != null and parentCustomerCode!=''">
			  		and t2.parent_customer_code = #{parentCustomerCode,jdbcType=VARCHAR}
			  	</when>
			  	<otherwise>
			  		and t2.parent_customer_code is null
			  	</otherwise>
		    </choose>
	</select>
	
	<delete id="deleteWaitAuditInfosByCustomerId" parameterType="java.lang.Long">
	    delete from pas_customer_audit_info t where t.audit_status in ('00','02')
		and customer_info_id in (
		    select info_id from pas_customer_info where customer_id = #{customerId,jdbcType=DECIMAL}
		)
 	</delete>
	
  
</mapper>