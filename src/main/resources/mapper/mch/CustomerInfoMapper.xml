<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.pas.mch.dao.CustomerInfoMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.mch.domain.CustomerInfo">
    <id column="INFO_ID" jdbcType="DECIMAL" property="infoId" />
    <result column="CUSTOMER_ID" jdbcType="DECIMAL" property="customerId" />
    <result column="CUSTOMER_CODE" jdbcType="VARCHAR" property="customerCode" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="SHORT_NAME" jdbcType="VARCHAR" property="shortName" />
    <result column="MOBILE" jdbcType="VARCHAR" property="mobile" />
    <result column="TELEPHONE" jdbcType="VARCHAR" property="telephone" />
    <result column="BUSINESS_ADDRESS" jdbcType="VARCHAR" property="businessAddress" />
    <result column="USE_USCC" jdbcType="DECIMAL" property="useUscc" />
    <result column="CUSTOMER_TYPE" jdbcType="DECIMAL" property="customerType" />
    <result column="BUSINESS_LICENSE_NO" jdbcType="VARCHAR" property="businessLicenseNo" />
    <result column="BUSINESS_LICENSE_EXP_DATE" jdbcType="TIMESTAMP" property="businessLicenseExpDate" />
    <result column="REGISTERED_ADDRESS" jdbcType="VARCHAR" property="registeredAddress" />
    <result column="NAT_TAX_REG_CER_NO" jdbcType="VARCHAR" property="natTaxRegCerNo" />
    <result column="NAT_TAX_REG_EXP_DATE" jdbcType="TIMESTAMP" property="natTaxRegExpDate" />
    <result column="LOC_TAX_REG_CER_NO" jdbcType="VARCHAR" property="locTaxRegCerNo" />
    <result column="LOC_TAX_REG_CER_EXP_DATE" jdbcType="TIMESTAMP" property="locTaxRegCerExpDate" />
    <result column="ORG_STRUCTURE_CODE" jdbcType="VARCHAR" property="orgStructureCode" />
    <result column="LEA_PERSON_NAME" jdbcType="VARCHAR" property="leaPersonName" />
    <result column="LEA_PERSONI_DENTIFICATION_TYPE" jdbcType="DECIMAL" property="leaPersoniDentificationType" />
    <result column="LEA_PERSONI_DENTIFICATION_NO" jdbcType="VARCHAR" property="leaPersoniDentificationNo" />
    <result column="LEA_PER_DEN_EXP_DATE" jdbcType="TIMESTAMP" property="leaPerDenExpDate" />
    <result column="PARENT_CUSTOMER_CODE" jdbcType="VARCHAR" property="parentCustomerCode" />
    <result column="WEB_ADDRESS" jdbcType="VARCHAR" property="webAddress" />
    <result column="ICP_LICENSE_NO" jdbcType="VARCHAR" property="icpLicenseNo" />
    <result column="BUSINESS_SCOPE" jdbcType="VARCHAR" property="businessScope" />
    <result column="INDUSTRY_DESCRIPTION" jdbcType="VARCHAR" property="industryDescription" />
    <result column="EMPLOYEE_COUNT" jdbcType="DECIMAL" property="employeeCount" />
    <result column="BUSINESS_TYPE" jdbcType="CHAR" property="businessType" />
    <result column="AREA_CODE" jdbcType="VARCHAR" property="areaCode" />
    <result column="REGISTERED_CAPITAL" jdbcType="DECIMAL" property="registeredCapital" />
    <result column="TRADE_CATEGORY" jdbcType="CHAR" property="tradeCategory" />
    <result column="NOTIFY_URL" jdbcType="VARCHAR" property="notifyUrl"/>
    <result column="CUSTOMER_CATEGORY" jdbcType="VARCHAR" property="customerCategory"/>
    <result column="sign_model" jdbcType="VARCHAR" property="signModel"/>
    <result column="BILL_MODE" jdbcType="VARCHAR" property="billMode"/>
  </resultMap>
  <sql id="Base_Column_List">
    INFO_ID, CUSTOMER_ID, CUSTOMER_CODE, NAME, SHORT_NAME, MOBILE, TELEPHONE, BUSINESS_ADDRESS, 
    USE_USCC, CUSTOMER_TYPE, BUSINESS_LICENSE_NO, BUSINESS_LICENSE_EXP_DATE, REGISTERED_ADDRESS, 
    NAT_TAX_REG_CER_NO, NAT_TAX_REG_EXP_DATE, LOC_TAX_REG_CER_NO, LOC_TAX_REG_CER_EXP_DATE, 
    ORG_STRUCTURE_CODE, LEA_PERSON_NAME, LEA_PERSONI_DENTIFICATION_TYPE, LEA_PERSONI_DENTIFICATION_NO, 
    LEA_PER_DEN_EXP_DATE, PARENT_CUSTOMER_CODE, WEB_ADDRESS, ICP_LICENSE_NO, BUSINESS_SCOPE, 
    INDUSTRY_DESCRIPTION, EMPLOYEE_COUNT, BUSINESS_TYPE, AREA_CODE, REGISTERED_CAPITAL, 
    TRADE_CATEGORY, NOTIFY_URL , CUSTOMER_CATEGORY,sign_model,BILL_MODE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from PAS_CUSTOMER_INFO
    where INFO_ID = #{infoId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from PAS_CUSTOMER_INFO
    where INFO_ID = #{infoId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.mch.domain.CustomerInfo">
    insert into PAS_CUSTOMER_INFO (INFO_ID, CUSTOMER_ID, CUSTOMER_CODE, 
      NAME, SHORT_NAME, MOBILE, 
      TELEPHONE, BUSINESS_ADDRESS, USE_USCC, 
      CUSTOMER_TYPE, BUSINESS_LICENSE_NO, BUSINESS_LICENSE_EXP_DATE, 
      REGISTERED_ADDRESS, NAT_TAX_REG_CER_NO, NAT_TAX_REG_EXP_DATE, 
      LOC_TAX_REG_CER_NO, LOC_TAX_REG_CER_EXP_DATE, 
      ORG_STRUCTURE_CODE, LEA_PERSON_NAME, LEA_PERSONI_DENTIFICATION_TYPE, 
      LEA_PERSONI_DENTIFICATION_NO, LEA_PER_DEN_EXP_DATE, 
      PARENT_CUSTOMER_CODE, WEB_ADDRESS, ICP_LICENSE_NO, 
      BUSINESS_SCOPE, INDUSTRY_DESCRIPTION, EMPLOYEE_COUNT, 
      BUSINESS_TYPE, AREA_CODE, REGISTERED_CAPITAL, 
      TRADE_CATEGORY, NOTIFY_URL , CUSTOMER_CATEGORY,sign_model, BILL_MODE)
    values (#{infoId,jdbcType=DECIMAL}, #{customerId,jdbcType=DECIMAL}, #{customerCode,jdbcType=VARCHAR}, 
      #{name,jdbcType=VARCHAR}, #{shortName,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR}, 
      #{telephone,jdbcType=VARCHAR}, #{businessAddress,jdbcType=VARCHAR}, #{useUscc,jdbcType=DECIMAL}, 
      #{customerType,jdbcType=DECIMAL}, #{businessLicenseNo,jdbcType=VARCHAR}, #{businessLicenseExpDate,jdbcType=TIMESTAMP}, 
      #{registeredAddress,jdbcType=VARCHAR}, #{natTaxRegCerNo,jdbcType=VARCHAR}, #{natTaxRegExpDate,jdbcType=TIMESTAMP}, 
      #{locTaxRegCerNo,jdbcType=VARCHAR}, #{locTaxRegCerExpDate,jdbcType=TIMESTAMP}, 
      #{orgStructureCode,jdbcType=VARCHAR}, #{leaPersonName,jdbcType=VARCHAR}, #{leaPersoniDentificationType,jdbcType=DECIMAL}, 
      #{leaPersoniDentificationNo,jdbcType=VARCHAR}, #{leaPerDenExpDate,jdbcType=TIMESTAMP}, 
      #{parentCustomerCode,jdbcType=VARCHAR}, #{webAddress,jdbcType=VARCHAR}, #{icpLicenseNo,jdbcType=VARCHAR}, 
      #{businessScope,jdbcType=VARCHAR}, #{industryDescription,jdbcType=VARCHAR}, #{employeeCount,jdbcType=DECIMAL}, 
      #{businessType,jdbcType=CHAR}, #{areaCode,jdbcType=VARCHAR}, #{registeredCapital,jdbcType=DECIMAL}, 
      #{tradeCategory,jdbcType=CHAR} , #{notifyUrl , jdbcType=VARCHAR} , #{customerCategory , jdbcType = VARCHAR},
      #{signModel , jdbcType = VARCHAR}, #{billMode,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.mch.domain.CustomerInfo">
    insert into PAS_CUSTOMER_INFO
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="infoId != null">
        INFO_ID,
      </if>
      <if test="customerId != null">
        CUSTOMER_ID,
      </if>
      <if test="customerCode != null">
        CUSTOMER_CODE,
      </if>
      <if test="name != null">
        NAME,
      </if>
      <if test="shortName != null">
        SHORT_NAME,
      </if>
      <if test="mobile != null">
        MOBILE,
      </if>
      <if test="telephone != null">
        TELEPHONE,
      </if>
      <if test="businessAddress != null">
        BUSINESS_ADDRESS,
      </if>
      <if test="useUscc != null">
        USE_USCC,
      </if>
      <if test="customerType != null">
        CUSTOMER_TYPE,
      </if>
      <if test="businessLicenseNo != null">
        BUSINESS_LICENSE_NO,
      </if>
      <if test="businessLicenseExpDate != null">
        BUSINESS_LICENSE_EXP_DATE,
      </if>
      <if test="registeredAddress != null">
        REGISTERED_ADDRESS,
      </if>
      <if test="natTaxRegCerNo != null">
        NAT_TAX_REG_CER_NO,
      </if>
      <if test="natTaxRegExpDate != null">
        NAT_TAX_REG_EXP_DATE,
      </if>
      <if test="locTaxRegCerNo != null">
        LOC_TAX_REG_CER_NO,
      </if>
      <if test="locTaxRegCerExpDate != null">
        LOC_TAX_REG_CER_EXP_DATE,
      </if>
      <if test="orgStructureCode != null">
        ORG_STRUCTURE_CODE,
      </if>
      <if test="leaPersonName != null">
        LEA_PERSON_NAME,
      </if>
      <if test="leaPersoniDentificationType != null">
        LEA_PERSONI_DENTIFICATION_TYPE,
      </if>
      <if test="leaPersoniDentificationNo != null">
        LEA_PERSONI_DENTIFICATION_NO,
      </if>
      <if test="leaPerDenExpDate != null">
        LEA_PER_DEN_EXP_DATE,
      </if>
      <if test="parentCustomerCode != null">
        PARENT_CUSTOMER_CODE,
      </if>
      <if test="webAddress != null">
        WEB_ADDRESS,
      </if>
      <if test="icpLicenseNo != null">
        ICP_LICENSE_NO,
      </if>
      <if test="businessScope != null">
        BUSINESS_SCOPE,
      </if>
      <if test="industryDescription != null">
        INDUSTRY_DESCRIPTION,
      </if>
      <if test="employeeCount != null">
        EMPLOYEE_COUNT,
      </if>
      <if test="businessType != null">
        BUSINESS_TYPE,
      </if>
      <if test="areaCode != null">
        AREA_CODE,
      </if>
      <if test="registeredCapital != null">
        REGISTERED_CAPITAL,
      </if>
      <if test="tradeCategory != null">
        TRADE_CATEGORY,
      </if>
      <if test="notifyUrl != null">
        NOTIFY_URL,
      </if>
      <if test="customerCategory != null">
        CUSTOMER_CATEGORY,
      </if>
      <if test="signModel != null">
        sign_model,
      </if>
      <if test="billMode != null" >
        BILL_MODE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="infoId != null">
        #{infoId,jdbcType=DECIMAL},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=DECIMAL},
      </if>
      <if test="customerCode != null">
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="shortName != null">
        #{shortName,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null">
        #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="businessAddress != null">
        #{businessAddress,jdbcType=VARCHAR},
      </if>
      <if test="useUscc != null">
        #{useUscc,jdbcType=DECIMAL},
      </if>
      <if test="customerType != null">
        #{customerType,jdbcType=DECIMAL},
      </if>
      <if test="businessLicenseNo != null">
        #{businessLicenseNo,jdbcType=VARCHAR},
      </if>
      <if test="businessLicenseExpDate != null">
        #{businessLicenseExpDate,jdbcType=TIMESTAMP},
      </if>
      <if test="registeredAddress != null">
        #{registeredAddress,jdbcType=VARCHAR},
      </if>
      <if test="natTaxRegCerNo != null">
        #{natTaxRegCerNo,jdbcType=VARCHAR},
      </if>
      <if test="natTaxRegExpDate != null">
        #{natTaxRegExpDate,jdbcType=TIMESTAMP},
      </if>
      <if test="locTaxRegCerNo != null">
        #{locTaxRegCerNo,jdbcType=VARCHAR},
      </if>
      <if test="locTaxRegCerExpDate != null">
        #{locTaxRegCerExpDate,jdbcType=TIMESTAMP},
      </if>
      <if test="orgStructureCode != null">
        #{orgStructureCode,jdbcType=VARCHAR},
      </if>
      <if test="leaPersonName != null">
        #{leaPersonName,jdbcType=VARCHAR},
      </if>
      <if test="leaPersoniDentificationType != null">
        #{leaPersoniDentificationType,jdbcType=DECIMAL},
      </if>
      <if test="leaPersoniDentificationNo != null">
        #{leaPersoniDentificationNo,jdbcType=VARCHAR},
      </if>
      <if test="leaPerDenExpDate != null">
        #{leaPerDenExpDate,jdbcType=TIMESTAMP},
      </if>
      <if test="parentCustomerCode != null">
        #{parentCustomerCode,jdbcType=VARCHAR},
      </if>
      <if test="webAddress != null">
        #{webAddress,jdbcType=VARCHAR},
      </if>
      <if test="icpLicenseNo != null">
        #{icpLicenseNo,jdbcType=VARCHAR},
      </if>
      <if test="businessScope != null">
        #{businessScope,jdbcType=VARCHAR},
      </if>
      <if test="industryDescription != null">
        #{industryDescription,jdbcType=VARCHAR},
      </if>
      <if test="employeeCount != null">
        #{employeeCount,jdbcType=DECIMAL},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=CHAR},
      </if>
      <if test="areaCode != null">
        #{areaCode,jdbcType=VARCHAR},
      </if>
      <if test="registeredCapital != null">
        #{registeredCapital,jdbcType=DECIMAL},
      </if>
      <if test="tradeCategory != null">
        #{tradeCategory,jdbcType=CHAR},
      </if>
      <if test="notifyUrl != null">
        #{notifyUrl,jdbcType=VARCHAR},
      </if>
      <if test="customerCategory != null">
        #{customerCategory,jdbcType=VARCHAR},
      </if>
      <if test="signModel != null">
        #{signModel , jdbcType = VARCHAR},
      </if>
      <if test="billMode != null" >
        #{billMode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.mch.domain.CustomerInfo">
    update PAS_CUSTOMER_INFO
    <set>
      <if test="customerId != null">
        CUSTOMER_ID = #{customerId,jdbcType=DECIMAL},
      </if>
      <if test="customerCode != null">
        CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        NAME = #{name,jdbcType=VARCHAR},
      </if>
      <if test="shortName != null">
        SHORT_NAME = #{shortName,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        MOBILE = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null">
        TELEPHONE = #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="businessAddress != null">
        BUSINESS_ADDRESS = #{businessAddress,jdbcType=VARCHAR},
      </if>
      <if test="useUscc != null">
        USE_USCC = #{useUscc,jdbcType=DECIMAL},
      </if>
      <if test="customerType != null">
        CUSTOMER_TYPE = #{customerType,jdbcType=DECIMAL},
      </if>
      <if test="businessLicenseNo != null">
        BUSINESS_LICENSE_NO = #{businessLicenseNo,jdbcType=VARCHAR},
      </if>
      <if test="businessLicenseExpDate != null">
        BUSINESS_LICENSE_EXP_DATE = #{businessLicenseExpDate,jdbcType=TIMESTAMP},
      </if>
      <if test="registeredAddress != null">
        REGISTERED_ADDRESS = #{registeredAddress,jdbcType=VARCHAR},
      </if>
      <if test="natTaxRegCerNo != null">
        NAT_TAX_REG_CER_NO = #{natTaxRegCerNo,jdbcType=VARCHAR},
      </if>
      <if test="natTaxRegExpDate != null">
        NAT_TAX_REG_EXP_DATE = #{natTaxRegExpDate,jdbcType=TIMESTAMP},
      </if>
      <if test="locTaxRegCerNo != null">
        LOC_TAX_REG_CER_NO = #{locTaxRegCerNo,jdbcType=VARCHAR},
      </if>
      <if test="locTaxRegCerExpDate != null">
        LOC_TAX_REG_CER_EXP_DATE = #{locTaxRegCerExpDate,jdbcType=TIMESTAMP},
      </if>
      <if test="orgStructureCode != null">
        ORG_STRUCTURE_CODE = #{orgStructureCode,jdbcType=VARCHAR},
      </if>
      <if test="leaPersonName != null">
        LEA_PERSON_NAME = #{leaPersonName,jdbcType=VARCHAR},
      </if>
      <if test="leaPersoniDentificationType != null">
        LEA_PERSONI_DENTIFICATION_TYPE = #{leaPersoniDentificationType,jdbcType=DECIMAL},
      </if>
      <if test="leaPersoniDentificationNo != null">
        LEA_PERSONI_DENTIFICATION_NO = #{leaPersoniDentificationNo,jdbcType=VARCHAR},
      </if>
      <if test="leaPerDenExpDate != null">
        LEA_PER_DEN_EXP_DATE = #{leaPerDenExpDate,jdbcType=TIMESTAMP},
      </if>
      <if test="parentCustomerCode != null">
        PARENT_CUSTOMER_CODE = #{parentCustomerCode,jdbcType=VARCHAR},
      </if>
      <if test="webAddress != null">
        WEB_ADDRESS = #{webAddress,jdbcType=VARCHAR},
      </if>
      <if test="icpLicenseNo != null">
        ICP_LICENSE_NO = #{icpLicenseNo,jdbcType=VARCHAR},
      </if>
      <if test="businessScope != null">
        BUSINESS_SCOPE = #{businessScope,jdbcType=VARCHAR},
      </if>
      <if test="industryDescription != null">
        INDUSTRY_DESCRIPTION = #{industryDescription,jdbcType=VARCHAR},
      </if>
      <if test="employeeCount != null">
        EMPLOYEE_COUNT = #{employeeCount,jdbcType=DECIMAL},
      </if>
      <if test="businessType != null">
        BUSINESS_TYPE = #{businessType,jdbcType=CHAR},
      </if>
      <if test="areaCode != null">
        AREA_CODE = #{areaCode,jdbcType=VARCHAR},
      </if>
      <if test="registeredCapital != null">
        REGISTERED_CAPITAL = #{registeredCapital,jdbcType=DECIMAL},
      </if>
      <if test="tradeCategory != null">
        TRADE_CATEGORY = #{tradeCategory,jdbcType=CHAR},
      </if>
      <if test="notifyUrl != null">
        NOTIFY_URL = #{notifyUrl,jdbcType=VARCHAR},
      </if>
      <if test="customerCategory != null">
        CUSTOMER_CATEGORY = #{customerCategory,jdbcType=VARCHAR},
      </if>
      <if test="signModel != null">
        sign_model = #{signModel , jdbcType = VARCHAR},
      </if>
      <if test="billMode != null" >
        BILL_MODE = #{billMode,jdbcType=VARCHAR},
      </if>
    </set>
    where INFO_ID = #{infoId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.mch.domain.CustomerInfo">
    update PAS_CUSTOMER_INFO
    set CUSTOMER_ID = #{customerId,jdbcType=DECIMAL},
      CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      NAME = #{name,jdbcType=VARCHAR},
      SHORT_NAME = #{shortName,jdbcType=VARCHAR},
      MOBILE = #{mobile,jdbcType=VARCHAR},
      TELEPHONE = #{telephone,jdbcType=VARCHAR},
      BUSINESS_ADDRESS = #{businessAddress,jdbcType=VARCHAR},
      USE_USCC = #{useUscc,jdbcType=DECIMAL},
      CUSTOMER_TYPE = #{customerType,jdbcType=DECIMAL},
      BUSINESS_LICENSE_NO = #{businessLicenseNo,jdbcType=VARCHAR},
      BUSINESS_LICENSE_EXP_DATE = #{businessLicenseExpDate,jdbcType=TIMESTAMP},
      REGISTERED_ADDRESS = #{registeredAddress,jdbcType=VARCHAR},
      NAT_TAX_REG_CER_NO = #{natTaxRegCerNo,jdbcType=VARCHAR},
      NAT_TAX_REG_EXP_DATE = #{natTaxRegExpDate,jdbcType=TIMESTAMP},
      LOC_TAX_REG_CER_NO = #{locTaxRegCerNo,jdbcType=VARCHAR},
      LOC_TAX_REG_CER_EXP_DATE = #{locTaxRegCerExpDate,jdbcType=TIMESTAMP},
      ORG_STRUCTURE_CODE = #{orgStructureCode,jdbcType=VARCHAR},
      LEA_PERSON_NAME = #{leaPersonName,jdbcType=VARCHAR},
      LEA_PERSONI_DENTIFICATION_TYPE = #{leaPersoniDentificationType,jdbcType=DECIMAL},
      LEA_PERSONI_DENTIFICATION_NO = #{leaPersoniDentificationNo,jdbcType=VARCHAR},
      LEA_PER_DEN_EXP_DATE = #{leaPerDenExpDate,jdbcType=TIMESTAMP},
      PARENT_CUSTOMER_CODE = #{parentCustomerCode,jdbcType=VARCHAR},
      WEB_ADDRESS = #{webAddress,jdbcType=VARCHAR},
      ICP_LICENSE_NO = #{icpLicenseNo,jdbcType=VARCHAR},
      BUSINESS_SCOPE = #{businessScope,jdbcType=VARCHAR},
      INDUSTRY_DESCRIPTION = #{industryDescription,jdbcType=VARCHAR},
      EMPLOYEE_COUNT = #{employeeCount,jdbcType=DECIMAL},
      BUSINESS_TYPE = #{businessType,jdbcType=CHAR},
      AREA_CODE = #{areaCode,jdbcType=VARCHAR},
      REGISTERED_CAPITAL = #{registeredCapital,jdbcType=DECIMAL},
      TRADE_CATEGORY = #{tradeCategory,jdbcType=CHAR},
      NOTIFY_URL = #{notifyUrl,jdbcType=VARCHAR},
      CUSTOMER_CATEGORY = #{customerCategory,jdbcType=VARCHAR},
      sign_model = #{signModel , jdbcType = VARCHAR},
      BILL_MODE = #{billMode,jdbcType=VARCHAR}
    where INFO_ID = #{infoId,jdbcType=DECIMAL}
  </update>
  
  <select id="getCustomerCode" parameterType="java.util.Map" resultType="com.epaylinks.efps.pas.mch.domain.CustomerInfo">
    select 
	    CUSTOMER_CODE as customerCode,
	    BUSINESS_LICENSE_NO as businessLicenseNo,
	    NAME as name
    from PAS_CUSTOMER_INFO
    where CUSTOMER_TYPE = #{customerType,jdbcType=DECIMAL}
    <if test="businessLicenseNo != null">
        and BUSINESS_LICENSE_NO = #{businessLicenseNo,jdbcType=VARCHAR}
    </if>
    <if test="name != null">
        and NAME = #{name,jdbcType=VARCHAR}
    </if>
  </select>
  
  <select id="getCustomerInfo" parameterType="java.util.Map" resultType="com.epaylinks.efps.pas.mch.domain.CustomerInfo">
    select 
    	<include refid="Base_Column_List" />
    from PAS_CUSTOMER_INFO
    where CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
  </select>
  
  <select id="getCustomerInfoByByCustomerCode" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    	<include refid="Base_Column_List" />
    from PAS_CUSTOMER_INFO
    where INFO_ID = (select max(NEWEST_CUSTOMER_INFO_ID) from PAS_CUSTOMER where CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR})
  </select>
  
  <select id="selectMerchantNameByInfoId" resultType="String">
	    select NAME
	    from PAS_CUSTOMER_INFO
	    where INFO_ID = #{customerInfoId,jdbcType=DECIMAL}
  </select>

  <select id="getCustomerInfoByParam" parameterType="java.util.Map" resultMap="BaseResultMap">
    select
    	 i.INFO_ID,i.CUSTOMER_ID,i.CUSTOMER_CODE,i.NAME,i.SHORT_NAME,i.MOBILE,i.TELEPHONE,i.BUSINESS_ADDRESS,i.
    	     USE_USCC,i.CUSTOMER_TYPE,i.BUSINESS_LICENSE_NO,i.BUSINESS_LICENSE_EXP_DATE,i.REGISTERED_ADDRESS,i.
    	     NAT_TAX_REG_CER_NO,i.NAT_TAX_REG_EXP_DATE,i.LOC_TAX_REG_CER_NO,i.LOC_TAX_REG_CER_EXP_DATE,i.
    	     ORG_STRUCTURE_CODE,i.LEA_PERSON_NAME,i.LEA_PERSONI_DENTIFICATION_TYPE,i.LEA_PERSONI_DENTIFICATION_NO,i.
    	     LEA_PER_DEN_EXP_DATE,i.PARENT_CUSTOMER_CODE,i.WEB_ADDRESS,i.ICP_LICENSE_NO,i.BUSINESS_SCOPE,i.
    	     INDUSTRY_DESCRIPTION,i.EMPLOYEE_COUNT,i.BUSINESS_TYPE,i.AREA_CODE,i.REGISTERED_CAPITAL,i.
    	     TRADE_CATEGORY
    from pas_customer c,PAS_CUSTOMER_INFO  i
    where  c.newest_customer_info_id = i.info_id
    and  ( i.name = #{name,jdbcType=VARCHAR} or i.BUSINESS_LICENSE_NO =   #{businessLicenseNo,jdbcType=VARCHAR}
    or i.MOBILE   = #{mobile,jdbcType=VARCHAR}   )
    

  </select>
  <select id="getCustomerInfoByMobileOrName" resultMap="BaseResultMap">
    select
    	 i.INFO_ID,i.CUSTOMER_ID,i.CUSTOMER_CODE,i.NAME,i.SHORT_NAME,i.MOBILE,i.TELEPHONE,i.BUSINESS_ADDRESS,i.
    	     USE_USCC,i.CUSTOMER_TYPE,i.BUSINESS_LICENSE_NO,i.BUSINESS_LICENSE_EXP_DATE,i.REGISTERED_ADDRESS,i.
    	     NAT_TAX_REG_CER_NO,i.NAT_TAX_REG_EXP_DATE,i.LOC_TAX_REG_CER_NO,i.LOC_TAX_REG_CER_EXP_DATE,i.
    	     ORG_STRUCTURE_CODE,i.LEA_PERSON_NAME,i.LEA_PERSONI_DENTIFICATION_TYPE,i.LEA_PERSONI_DENTIFICATION_NO,i.
    	     LEA_PER_DEN_EXP_DATE,i.PARENT_CUSTOMER_CODE,i.WEB_ADDRESS,i.ICP_LICENSE_NO,i.BUSINESS_SCOPE,i.
    	     INDUSTRY_DESCRIPTION,i.EMPLOYEE_COUNT,i.BUSINESS_TYPE,i.AREA_CODE,i.REGISTERED_CAPITAL,i.
    	     TRADE_CATEGORY
    from pas_customer c,PAS_CUSTOMER_INFO  i
    where  c.newest_customer_info_id = i.info_id
    and  ( i.name = #{name,jdbcType=VARCHAR}
    or i.MOBILE   = #{mobile,jdbcType=VARCHAR}   )
    

  </select>
  <resultMap id="BaseBusinessResultMap" type="com.epaylinks.efps.pas.pas.domain.BusinessParam">
      <id column="ID" jdbcType="DECIMAL" property="id" />
      <result column="NAME" jdbcType="VARCHAR" property="name" />
      <result column="CODE" jdbcType="VARCHAR" property="code" />
      <result column="MUST" jdbcType="VARCHAR" property="must" />
      <result column="TYPE" jdbcType="VARCHAR" property="type" />
      <result column="BUSINESS_ID" jdbcType="VARCHAR" property="businessId" />
      <result column="CREATE_TIME" jdbcType="VARCHAR" property="createTime" />
      <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
      <result column="UPDATE_TIME" jdbcType="VARCHAR" property="updateTime" />
      <result column="UPDATOR" jdbcType="VARCHAR" property="updator" />
      <result column="RATIO_MODE" jdbcType="VARCHAR" property="ratioMode" />
  </resultMap>
  <select id="getBusinessParamByParam" parameterType="java.util.Map" resultMap="BaseBusinessResultMap">
   select p.id,p.name,p.code,p.must,p.type,p.business_id,p.create_time,p.creator,p.update_time,p.updator,
   v.value,b.ratio_mode
    from pas_business_param p,pas_business_param_value v,pas_business b
    where p.id =v.business_param_id
    and p.business_id =v.business_id
    and  p.business_id = b.id
    <if test="businessId != null and businessId != ''" >
        and  p.business_id = #{businessId,jdbcType=VARCHAR}
    </if>

  </select>

  <select id="selectByCustomerId" resultMap="BaseResultMap">
  	select 
  	<include refid="Base_Column_List" />
    from PAS_CUSTOMER_INFO 
    where CUSTOMER_ID = #{customerId , jdbcType = INTEGER}
  </select>
  
  <select id="selectByLeaPersoniDentificationNo" resultMap="BaseResultMap">
  	select 
  	<include refid="Base_Column_List" />
    from PAS_CUSTOMER_INFO 
    where LEA_PERSONI_DENTIFICATION_NO = #{leaPersoniDentificationNo , jdbcType = VARCHAR}
  </select>
  
  <select id="selectByBusinessLicenseNo" resultMap="BaseResultMap">
  	select 
  	<include refid="Base_Column_List" />
    from PAS_CUSTOMER_INFO 
    where BUSINESS_LICENSE_NO = #{businessLicenseNo , jdbcType = VARCHAR}
  </select>
  
  <select id="selectByCustomerCodeAndBusinessCodeAndDate">
  	select 
  	<include refid="Base_Column_List" />
    from PAS_CUSTOMER_INFO 
  </select>

  <select id="selectListByCustomerId" resultMap="BaseResultMap">
    	select
    	<include refid="Base_Column_List" />
      from PAS_CUSTOMER_INFO
      where CUSTOMER_ID = #{customerId , jdbcType = INTEGER}
    order by  INFO_ID desc
    </select>
</mapper>