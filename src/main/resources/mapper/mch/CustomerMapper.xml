<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.pas.mch.dao.CustomerMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.mch.domain.Customer">
    <id column="CUSTOMER_ID" jdbcType="DECIMAL" property="customerId" />
    <result column="CUSTOMER_CODE" jdbcType="VARCHAR" property="customerCode" />
    <result column="CREATOR_ID" jdbcType="DECIMAL" property="creatorId" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="NEWEST_CUSTOMER_INFO_ID" jdbcType="DECIMAL" property="newestCustomerInfoId" />
    <result column="NEWEST_STATE_ID" jdbcType="DECIMAL" property="newestStateId" />
    <result column="STATE" jdbcType="DECIMAL" property="state" />
    <result column="INIT_LOGIN_PSW" jdbcType="VARCHAR" property="initLoginPsw" />
    <result column="INIT_PAY_PSW" jdbcType="VARCHAR" property="initPayPsw" />
    <result column="SOURCE_CHANNEL" jdbcType="VARCHAR" property="sourceChannel" />
  </resultMap>
  <sql id="Base_Column_List">
    CUSTOMER_ID, CUSTOMER_CODE, CREATOR_ID, CREATE_TIME, NEWEST_CUSTOMER_INFO_ID, NEWEST_STATE_ID, 
    STATE, INIT_LOGIN_PSW, INIT_PAY_PSW,SOURCE_CHANNEL
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from PAS_CUSTOMER
    where CUSTOMER_ID = #{customerId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from PAS_CUSTOMER
    where CUSTOMER_ID = #{customerId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.mch.domain.Customer">
    insert into PAS_CUSTOMER (CUSTOMER_ID, CUSTOMER_CODE, CREATOR_ID, 
      CREATE_TIME, NEWEST_CUSTOMER_INFO_ID, NEWEST_STATE_ID, 
      STATE, INIT_LOGIN_PSW, INIT_PAY_PSW,SOURCE_CHANNEL
      )
    values (#{customerId,jdbcType=DECIMAL}, #{customerCode,jdbcType=VARCHAR}, #{creatorId,jdbcType=DECIMAL}, 
      #{createTime,jdbcType=TIMESTAMP}, #{newestCustomerInfoId,jdbcType=DECIMAL}, #{newestStateId,jdbcType=DECIMAL}, 
      #{state,jdbcType=DECIMAL}, #{initLoginPsw,jdbcType=VARCHAR}, #{initPayPsw,jdbcType=VARCHAR}, #{sourceChannel,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.mch.domain.Customer">
    insert into PAS_CUSTOMER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="customerId != null">
        CUSTOMER_ID,
      </if>
      <if test="customerCode != null">
        CUSTOMER_CODE,
      </if>
      <if test="creatorId != null">
        CREATOR_ID,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="newestCustomerInfoId != null">
        NEWEST_CUSTOMER_INFO_ID,
      </if>
      <if test="newestStateId != null">
        NEWEST_STATE_ID,
      </if>
      <if test="state != null">
        STATE,
      </if>
      <if test="initLoginPsw != null">
        INIT_LOGIN_PSW,
      </if>
      <if test="initPayPsw != null">
        INIT_PAY_PSW,
      </if>
        <if test="sourceChannel != null">
        SOURCE_CHANNEL,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="customerId != null">
        #{customerId,jdbcType=DECIMAL},
      </if>
      <if test="customerCode != null">
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="newestCustomerInfoId != null">
        #{newestCustomerInfoId,jdbcType=DECIMAL},
      </if>
      <if test="newestStateId != null">
        #{newestStateId,jdbcType=DECIMAL},
      </if>
      <if test="state != null">
        #{state,jdbcType=DECIMAL},
      </if>
      <if test="initLoginPsw != null">
        #{initLoginPsw,jdbcType=VARCHAR},
      </if>
      <if test="initPayPsw != null">
        #{initPayPsw,jdbcType=VARCHAR},
      </if>
        <if test="sourceChannel != null">
            #{sourceChannel,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.mch.domain.Customer">
    update PAS_CUSTOMER
    <set>
      <if test="customerCode != null">
        CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        CREATOR_ID = #{creatorId,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="newestCustomerInfoId != null">
        NEWEST_CUSTOMER_INFO_ID = #{newestCustomerInfoId,jdbcType=DECIMAL},
      </if>
      <if test="newestStateId != null">
        NEWEST_STATE_ID = #{newestStateId,jdbcType=DECIMAL},
      </if>
      <if test="state != null">
        STATE = #{state,jdbcType=DECIMAL},
      </if>
      <if test="initLoginPsw != null">
        INIT_LOGIN_PSW = #{initLoginPsw,jdbcType=VARCHAR},
      </if>
      <if test="initPayPsw != null">
        INIT_PAY_PSW = #{initPayPsw,jdbcType=VARCHAR},
      </if>
        <if test="sourceChannel != null">
            SOURCE_CHANNEL = #{sourceChannel,jdbcType=VARCHAR},
        </if>
    </set>
    where CUSTOMER_ID = #{customerId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.mch.domain.Customer">
    update PAS_CUSTOMER
    set CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      CREATOR_ID = #{creatorId,jdbcType=DECIMAL},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      NEWEST_CUSTOMER_INFO_ID = #{newestCustomerInfoId,jdbcType=DECIMAL},
      NEWEST_STATE_ID = #{newestStateId,jdbcType=DECIMAL},
      STATE = #{state,jdbcType=DECIMAL},
      INIT_LOGIN_PSW = #{initLoginPsw,jdbcType=VARCHAR},
      INIT_PAY_PSW = #{initPayPsw,jdbcType=VARCHAR},
      SOURCE_CHANNEL = #{sourceChannel,jdbcType=VARCHAR}
    where CUSTOMER_ID = #{customerId,jdbcType=DECIMAL}
  </update>
  
   
  <select id="findInputingCustomerInfoId" resultType="java.lang.Long">
    select max(info_id) from pas_customer_info where customer_id = (
       select max(customer_id) from pas_customer
       where creator_id = #{curUserId,jdbcType=DECIMAL}
    	 and customer_code is null
    	  and state=0
	)
  </select>
  
  <select id="updateAfterAddCustomerInfo">
    update PAS_CUSTOMER
    set NEWEST_CUSTOMER_INFO_ID = #{newestCustomerInfoId,jdbcType=DECIMAL}
    where CUSTOMER_ID = #{customerId,jdbcType=DECIMAL}
  </select>
  
  <select id="getByCustomerInfoId" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from PAS_CUSTOMER
    where CUSTOMER_ID in (
    	select CUSTOMER_ID from PAS_CUSTOMER_INFO where INFO_ID = #{customerInfoId, jdbcType=DECIMAL}
    )
  </select>
  
    <!-- page query list -->
	<resultMap id="CustomerResultMap" type="com.epaylinks.efps.pas.mch.model.CustomerResult">
	    <result column="customer_id" jdbcType="DECIMAL" property="customerId" />
	    <result column="customer_info_id" jdbcType="DECIMAL" property="customerInfoId" />
	    <result column="customer_code" jdbcType="VARCHAR" property="customerCode" />
	    <result column="name" jdbcType="VARCHAR" property="customerName" />
	    <result column="business_type" jdbcType="VARCHAR" property="businessType" />
	    <result column="pause_sett" jdbcType="DECIMAL" property="settleState" />
	    <result column="area_code" jdbcType="VARCHAR" property="areaCode" />
	    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
	    <result column="newest_state_id" jdbcType="DECIMAL" property="newestStateId" />
	    <result column="state_operator" jdbcType="VARCHAR" property="lastUpdator" />
	    <result column="state_update_comment" jdbcType="VARCHAR" property="stateComment" />
	    <result column="state" jdbcType="VARCHAR" property="customerState" />
	    <result column="source_channel" jdbcType="VARCHAR" property="sourceChannel" />
	</resultMap>
	<select id="pageQueryCustomerInfos" resultMap="CustomerResultMap">
		select *
		from (
			select A.*, rownum RN
			from (
				select t1.customer_id CUSTOMER_ID, t1.newest_customer_info_id customer_info_id, t1.customer_code customer_code, t1.state state, t1.newest_state_id newestStateId, t1.create_time CREATE_TIME,
				 t2.name name, t2.business_type business_type, t2.area_code area_code, t3.pause_sett PAUSE_SETT,
				 null STATE_OPERATOR, null STATE_UPDATE_COMMENT, t1.source_channel
				from PAS_CUSTOMER t1
					 left join PAS_CUSTOMER_INFO t2 on t1.NEWEST_CUSTOMER_INFO_ID=t2.INFO_ID
					 left join PAS_CUSTOMER_SETTLE_INFO t3 on t2.INFO_ID=t3.CUSTOMER_INFO_ID
				where t1.STATE in (1,2)
				    <if test="beginCreateTime != null">
				        and t1.CREATE_TIME &gt;= #{beginCreateTime , jdbcType = TIMESTAMP}
				    </if>
				    <if test="endCreateTime != null">
				        and t1.CREATE_TIME &lt; #{endCreateTime , jdbcType = TIMESTAMP}
				    </if>
				    <if test="customerCode != null and customerCode != ''">
				        and t1.CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
				    </if>
				    <if test="customerName != null and customerName != ''">
                        and t2.NAME LIKE '%'||#{customerName}||'%'
				    </if>
				    <if test="customerState != null and customerState != ''">
				        and t1.STATE = #{customerState,jdbcType=DECIMAL}
				    </if>
				    <if test="businessType != null and businessType != ''">
				        and t2.BUSINESS_TYPE = #{businessType,jdbcType=VARCHAR}
				    </if>
				    <if test="settleState != null and settleState != ''">
				        and t3.PAUSE_SETT = #{settleState,jdbcType=DECIMAL}
				    </if>
				    <if test="areaCodeQueryStr != null and areaCodeQueryStr != ''">
				        and t2.area_code like #{areaCodeQueryStr}||'%'
				    </if>
                    <if test="sourceChannel != null and sourceChannel != ''">
				        and t1.source_channel =  #{sourceChannel,jdbcType=VARCHAR}
				    </if>
			    order by t1.CREATE_TIME desc
			) A
			where rownum &lt;= #{endRowNo,jdbcType=DECIMAL}
		)
		where RN &gt;= #{beginRowNo,jdbcType=DECIMAL}
	</select>
	<select id="countCustomerInfos" resultType="java.lang.Integer">
		select count(*)
		from PAS_CUSTOMER t1
			 left join PAS_CUSTOMER_INFO t2 on t1.NEWEST_CUSTOMER_INFO_ID=t2.INFO_ID
			 left join PAS_CUSTOMER_SETTLE_INFO t3 on t2.INFO_ID=t3.CUSTOMER_INFO_ID
		where t1.STATE in (1,2)
		    <if test="beginCreateTime != null">
		        and t1.CREATE_TIME &gt;= #{beginCreateTime , jdbcType = TIMESTAMP}
		    </if>
		    <if test="endCreateTime != null">
		        and t1.CREATE_TIME &lt; #{endCreateTime , jdbcType = TIMESTAMP}
		    </if>
		    <if test="customerCode != null and customerCode != ''">
		        and t1.CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
		    </if>
		    <if test="customerName != null and customerName != ''">
                and t2.NAME LIKE '%'||#{customerName}||'%'
		    </if>
		    <if test="customerState != null and customerState != ''">
		        and t1.STATE = #{customerState,jdbcType=DECIMAL}
		    </if>
		    <if test="businessType != null and businessType != ''">
		        and t2.BUSINESS_TYPE = #{businessType,jdbcType=VARCHAR}
		    </if>
		    <if test="settleState != null and settleState != ''">
		        and t3.PAUSE_SETT = #{settleState,jdbcType=DECIMAL}
		    </if>
		    <if test="areaCodeQueryStr != null and areaCodeQueryStr != ''">
		        and t2.area_code like #{areaCodeQueryStr}||'%'
		    </if>
        <if test="sourceChannel != null and sourceChannel != ''">
            and t1.source_channel = #{sourceChannel,jdbcType=VARCHAR}
        </if>
	</select>
	
	<update id="updateCustomerState">
		update PAS_CUSTOMER
		set STATE = #{state, jdbcType=VARCHAR}
		where CUSTOMER_ID = #{customerId, jdbcType=DECIMAL}
	</update>
	
	<select id="selectCustomerCodeByCustomerInfoId" resultType="String">
	    select CUSTOMER_CODE
	    from PAS_CUSTOMER
	    where CUSTOMER_ID in (
	    	select CUSTOMER_ID from PAS_CUSTOMER_INFO where INFO_ID = #{customerInfoId, jdbcType=DECIMAL}
	    )
	</select>
	
	<select id="checkExistOfMchNameInAuditeds" resultType="Boolean">
		select count(*)
		from pas_customer t
			 left join pas_customer_info t2 on t.newest_customer_info_id = t2.info_id
		where t.newest_customer_info_id is not null
			 and t.customer_code is not null
			 and t2.name= #{mchName,jdbcType=VARCHAR}
			 and t2.customer_id != #{customerId,jdbcType=DECIMAL}
	</select>
	
	<select id="checkExistOfLicenseNoInAuditeds" resultType="Boolean">
		select count(*)
		from pas_customer t
			 left join pas_customer_info t2 on t.newest_customer_info_id=t2.info_id
		where t.newest_customer_info_id is not null
			 and t.customer_code is not null
			 and t2.business_license_no= #{businessLicenseNo,jdbcType=VARCHAR}
			  and t2.customer_id != #{customerId,jdbcType=DECIMAL}
	</select>
	
	<select id="checkExistOfLeaPersonIDNoInAuditeds" resultType="Boolean">
		select count(*)
		from pas_customer t
			 left join pas_customer_info t2 on t.newest_customer_info_id=t2.info_id
		where t.newest_customer_info_id is not null
			 and t.customer_code is not null
			 and t2.LEA_PERSONI_DENTIFICATION_NO= #{leaPersoniDentificationNo,jdbcType=VARCHAR}
			  and t2.customer_id != #{customerId,jdbcType=DECIMAL}
			  <choose>
			  	<when test="parentCustomerCode != null and parentCustomerCode!=''">
			  		and t2.parent_customer_code = #{parentCustomerCode,jdbcType=VARCHAR}
			  	</when>
			  	<otherwise>
			  		and t2.parent_customer_code is null
			  	</otherwise>
			  </choose>
	</select>
  
  	<select id="selectAll" resultMap="BaseResultMap">
  		select 
	    <include refid="Base_Column_List" />
	    from PAS_CUSTOMER
  	</select>
  
  	<select id="selectByCustomerCode" resultMap="BaseResultMap">
  		select 
	    <include refid="Base_Column_List" />
	    from PAS_CUSTOMER 
	    where CUSTOMER_CODE = #{customerCode , jdbcType = VARCHAR}
  	</select>
</mapper>