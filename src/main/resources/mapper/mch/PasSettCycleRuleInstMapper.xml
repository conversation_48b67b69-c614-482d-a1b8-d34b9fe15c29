<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.pas.mch.dao.PasSettCycleRuleInstMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.mch.domain.PasSettCycleRuleInst">
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="CUSTOMER_BUSINESS_INFO_ID" jdbcType="DECIMAL" property="customerBusinessInfoId" />
    <result column="BUSINESS_INST_DB_ID" jdbcType="DECIMAL" property="businessInstDBId" />
    <result column="SETT_CYCLE_RULE_CODE" jdbcType="VARCHAR" property="settCycleRuleCode" />
    <result column="VALID_START_TIME" jdbcType="TIMESTAMP" property="validStartTime" />
    <result column="VALID_END_TIME" jdbcType="TIMESTAMP" property="validEndTime" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="CREATOR" jdbcType="DECIMAL" property="creator" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="UPDATOR" jdbcType="DECIMAL" property="updator" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, CUSTOMER_BUSINESS_INFO_ID, SETT_CYCLE_RULE_CODE, VALID_START_TIME, VALID_END_TIME, 
    CREATE_TIME, CREATOR, UPDATE_TIME, UPDATOR,BUSINESS_INST_DB_ID
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from PAS_SETT_CYCLE_RULE_INST
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from PAS_SETT_CYCLE_RULE_INST
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.mch.domain.PasSettCycleRuleInst">
    insert into PAS_SETT_CYCLE_RULE_INST (ID, CUSTOMER_BUSINESS_INFO_ID, SETT_CYCLE_RULE_CODE, 
      VALID_START_TIME, VALID_END_TIME, CREATE_TIME, 
      CREATOR, UPDATE_TIME, UPDATOR,BUSINESS_INST_DB_ID
      )
    values (#{id,jdbcType=DECIMAL}, #{customerBusinessInfoId,jdbcType=DECIMAL}, #{settCycleRuleCode,jdbcType=VARCHAR}, 
      #{validStartTime,jdbcType=TIMESTAMP}, #{validEndTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, 
      #{creator,jdbcType=DECIMAL}, #{updateTime,jdbcType=TIMESTAMP}, #{updator,jdbcType=DECIMAL},#{businessInstDBId,jdbcType=DECIMAL}
      )
  </insert>
  
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.mch.domain.PasSettCycleRuleInst">
    update PAS_SETT_CYCLE_RULE_INST
    <set>
      <if test="customerBusinessInfoId != null">
        CUSTOMER_BUSINESS_INFO_ID = #{customerBusinessInfoId,jdbcType=DECIMAL},
      </if>
      <if test="settCycleRuleCode != null">
        SETT_CYCLE_RULE_CODE = #{settCycleRuleCode,jdbcType=VARCHAR},
      </if>
      <if test="validStartTime != null">
        VALID_START_TIME = #{validStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="validEndTime != null">
        VALID_END_TIME = #{validEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=DECIMAL},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null">
        UPDATOR = #{updator,jdbcType=DECIMAL},
      </if>
      <if test="businessInstDBId != null">
        BUSINESS_INST_DB_ID = #{businessInstDBId,jdbcType=DECIMAL},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.mch.domain.PasSettCycleRuleInst">
    update PAS_SETT_CYCLE_RULE_INST
    set CUSTOMER_BUSINESS_INFO_ID = #{customerBusinessInfoId,jdbcType=DECIMAL},
      SETT_CYCLE_RULE_CODE = #{settCycleRuleCode,jdbcType=VARCHAR},
      VALID_START_TIME = #{validStartTime,jdbcType=TIMESTAMP},
      VALID_END_TIME = #{validEndTime,jdbcType=TIMESTAMP},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=DECIMAL},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      UPDATOR = #{updator,jdbcType=DECIMAL},
      BUSINESS_INST_DB_ID = #{businessInstDBId,jdbcType=DECIMAL}
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  
  
  <select id="selectByBusinessInstDBId" resultMap="BaseResultMap">
  	select 
  	<include refid="Base_Column_List" />
    from PAS_SETT_CYCLE_RULE_INST 
    where BUSINESS_INST_DB_ID = #{businessInstDBId , jdbcType = DECIMAL}
  </select>
  
  <select id="selectByBusinessInstDBIdAndDate" resultMap="BaseResultMap">
  	select 
  	<include refid="Base_Column_List" />
    from PAS_SETT_CYCLE_RULE_INST 
    where BUSINESS_INST_DB_ID = #{businessInstDBId , jdbcType = DECIMAL} 
    and VALID_START_TIME &lt;= #{date , jdbcType = TIMESTAMP} 
    and VALID_END_TIME &gt; #{date , jdbcType = TIMESTAMP}
  </select>

  <select id="selectSettCycleCodeInSameBizCategory" resultType="String">
    select max(t2.sett_cycle_rule_code)
	from pas_sett_cycle_rule_inst t2
	where t2.business_inst_db_id in (
	  select t.business_id from pas_customer_business_info t
	  where t.customer_info_id = #{customerInfoId,jdbcType=DECIMAL} and t.business_code in (
	     select t1.code from pas_business t1
	     where t1.business_category=(select max(business_category)
								     from pas_business
								     where code=#{businessCode,jdbcType=VARCHAR})
	  )
	)
  </select>

  <delete id="deleteByBusinessExamId">
    delete from PAS_SETT_CYCLE_RULE_INST
    where CUSTOMER_BUSINESS_INFO_ID = #{businessExamId,jdbcType=VARCHAR}
  </delete>

</mapper>