<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.pas.pas.dao.BusinessMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.pas.domain.Business" >
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="NAME" property="name" jdbcType="VARCHAR" />
    <result column="CODE" property="code" jdbcType="VARCHAR" />
    <result column="TYPE" property="type" jdbcType="CHAR" />
    <result column="RATIO_MODE" property="ratioMode" jdbcType="CHAR" />
    <result column="RATIO" property="ratio" jdbcType="VARCHAR" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
    <result column="STATE" property="state" jdbcType="CHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="CREATOR" property="creator" jdbcType="VARCHAR" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="UPDATOR" property="updator" jdbcType="VARCHAR" />
    <result column="BUSINESS_CATEGORY" property="businessCategory" jdbcType="VARCHAR" />
    <result column="SHOW_PAY_CONTROL" property="showPayControl" jdbcType="VARCHAR" />
    <result column="BUSINESS_LABEL" property="businessLabel" jdbcType="VARCHAR" />
    <result column="CAN_REPEAT" property="canRepeat" jdbcType="VARCHAR" />
    <result column="TEMPLATE_CODE" property="templateCode" jdbcType="VARCHAR" />
    <result column="BUSINESS_GROUP" property="businessGroup" jdbcType="VARCHAR" />
    <result column="DISPLAY_CODE" property="displayCode" jdbcType="VARCHAR" />
    <result column="SCOPE" property="scope" jdbcType="VARCHAR" />
    <result column="CARD_TYPE" property="cardType" jdbcType="DECIMAL" />
    <result column="CHARGE_GROUP" property="chargeGroup" jdbcType="VARCHAR" />
  </resultMap>
  <resultMap id="ApplyBusinessResultMap" type="com.epaylinks.efps.pas.pas.domain.ApplyBusiness" >
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="NAME" property="name" jdbcType="VARCHAR" />
    <result column="CODE" property="code" jdbcType="VARCHAR" />
    <result column="TYPE" property="type" jdbcType="CHAR" />
    <result column="RATIO_MODE" property="ratioMode" jdbcType="CHAR" />
    <result column="RATIO" property="ratio" jdbcType="VARCHAR" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
    <result column="STATE" property="state" jdbcType="CHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="CREATOR" property="creator" jdbcType="VARCHAR" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="UPDATOR" property="updator" jdbcType="VARCHAR" />
    <result column="BUSINESS_CATEGORY" property="businessCategory" jdbcType="VARCHAR" />
    <result column="SHOW_PAY_CONTROL" property="showPayControl" jdbcType="VARCHAR" />
    <result column="BUSINESS_LABEL" property="businessLabel" jdbcType="VARCHAR" />
    <result column="CAN_REPEAT" property="canRepeat" jdbcType="VARCHAR" />
    <result column="TEMPLATE_CODE" property="templateCode" jdbcType="VARCHAR" />
    <result column="BUSINESS_GROUP" property="businessGroup" jdbcType="VARCHAR" />
    <result column="BUSINESS_PARENT_GROUP" property="businessParentGroup" jdbcType="VARCHAR" />
    <result column="SCOPE" property="scope" jdbcType="VARCHAR" />
    <result column="DISPLAY_CODE" property="displayCode" jdbcType="VARCHAR" />
    <result column="CARD_TYPE" property="cardType" jdbcType="DECIMAL" />
    <result column="CHARGE_GROUP" property="chargeGroup" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, NAME, CODE, TYPE, RATIO_MODE, RATIO, REMARK, STATE, CREATE_TIME, CREATOR, UPDATE_TIME, 
    UPDATOR, BUSINESS_CATEGORY,SHOW_PAY_CONTROL,BUSINESS_LABEL,CAN_REPEAT ,TEMPLATE_CODE ,BUSINESS_GROUP,DISPLAY_CODE ,SCOPE,CARD_TYPE,CHARGE_GROUP
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from PAS_BUSINESS
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <select id="selectBySelective" resultMap="BaseResultMap" parameterType="com.epaylinks.efps.pas.pas.domain.Business" >
    select
      	b.ID, b.NAME, b.CODE, b.TYPE, b.RATIO_MODE, b.RATIO, b.REMARK, b.STATE, b.CREATE_TIME, b.CREATOR, b.UPDATE_TIME,
      	    b.UPDATOR, b.BUSINESS_CATEGORY,b.SHOW_PAY_CONTROL,BUSINESS_LABEL,CAN_REPEAT,b.TEMPLATE_CODE ,b.BUSINESS_GROUP,b.DISPLAY_CODE ,b.SCOPE
      	from PAS_BUSINESS  b ,pas_business_category_relation r where b.id = r.business_id   and b.state ='1'

      <if test="id != null  and id != ''" >
        AND b.ID = #{id,jdbcType=DECIMAL}
      </if>
      <if test="name != null and name != ''" >
        AND b.NAME = #{name,jdbcType=VARCHAR}
      </if>
      <if test="code != null and code != ''" >
        AND b.CODE = #{code,jdbcType=VARCHAR}
      </if>
      <if test="type != null and type != ''" >
        AND b.TYPE = #{type,jdbcType=CHAR}
      </if>
      <if test="ratioMode != null and ratioMode != ''" >
        AND b.RATIO_MODE = #{ratioMode,jdbcType=CHAR}
      </if>
      <if test="ratio != null and ratio != ''" >
        AND b.RATIO = #{ratio,jdbcType=VARCHAR}
      </if>
      <if test="remark != null and remark != ''" >
        AND b.REMARK = #{remark,jdbcType=VARCHAR}
      </if>
      <if test="state != null and state != ''" >
        AND b.STATE = #{state,jdbcType=CHAR}
      </if>
      <if test="createTime != null" >
        AND b.CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="creator != null and creator != ''" >
        AND b.CREATOR = #{creator,jdbcType=VARCHAR}
      </if>
      <if test="updateTime != null" >
        AND b.UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updator != null and updator != ''" >
        AND b.UPDATOR = #{updator,jdbcType=VARCHAR}
      </if>
      <if test="businessCategory != null and businessCategory != ''" >
        AND r.BUSINESS_CATEGORY_code = #{businessCategory,jdbcType=VARCHAR}
      </if>
      <if test="showPayControl != null and showPayControl != ''" >
        AND b.SHOW_PAY_CONTROL = #{showPayControl,jdbcType=VARCHAR}
      </if>
    <if test="canRepeat != null and canRepeat != ''" >
        AND b.CAN_REPEAT = #{canRepeat,jdbcType=VARCHAR}
      </if>
    <if test="templateCode != null and templateCode != ''" >
        AND b.TEMPLATE_CODE = #{templateCode,jdbcType=VARCHAR}
      </if>
    <if test="businessGroup != null and businessGroup != ''" >
        AND b.BUSINESS_GROUP = #{businessGroup,jdbcType=VARCHAR}
      </if>
    <if test="displayCode != null and displayCode != ''" >
        AND b.DISPLAY_CODE  = #{displayCode,jdbcType=VARCHAR}
      </if>
    <if test="scope != null and scope != ''" >
        AND b.SCOPE = #{scope,jdbcType=VARCHAR}
      </if>

  </select>
  <select id="selectByPage" resultMap="BaseResultMap"
          parameterType="java.util.Map">
    select
    <include refid="Base_Column_List" />
    from (
    select A.*, rownum RN
    from (
    select * from PAS_BUSINESS
    <where>
      <if test="id != null and id != ''" >
        AND ID = #{id,jdbcType=DECIMAL}
      </if>
      <if test="name != null and name != ''" >
        AND NAME = #{name,jdbcType=VARCHAR}
      </if>
      <if test="code != null and code != ''" >
        AND CODE = #{code,jdbcType=VARCHAR}
      </if>
      <if test="type != null and type != ''" >
        AND TYPE = #{type,jdbcType=CHAR}
      </if>
      <if test="ratioMode != null and ratioMode != ''" >
        AND RATIO_MODE = #{ratioMode,jdbcType=CHAR}
      </if>
      <if test="ratio != null and ratio != ''" >
        AND RATIO = #{ratio,jdbcType=VARCHAR}
      </if>
      <if test="remark != null and remark != ''" >
        AND REMARK = #{remark,jdbcType=VARCHAR}
      </if>
      <if test="state != null and state != ''" >
        AND STATE = #{state,jdbcType=CHAR}
      </if>
      <if test="createTime != null" >
        AND CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="creator != null and creator != ''" >
        AND CREATOR = #{creator,jdbcType=VARCHAR}
      </if>
      <if test="updateTime != null" >
        AND UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updator != null and updator != ''" >
        AND UPDATOR = #{updator,jdbcType=VARCHAR}
      </if>
      <if test="businessCategory != null and businessCategory != ''" >
        AND BUSINESS_CATEGORY = #{businessCategory,jdbcType=VARCHAR}
      </if>
      <if test="showPayControl != null and showPayControl != ''">
        AND SHOW_PAY_CONTROL = #{showPayControl,jdbcType=VARCHAR}
      </if>
      <if test="canRepeat != null and canRepeat != ''">
        AND CAN_REPEAT = #{canRepeat,jdbcType=VARCHAR}
      </if>
      <if test="templateCode != null and templateCode != ''">
        AND TEMPLATE_CODE = #{templateCode,jdbcType=VARCHAR}
      </if>
      <if test="businessGroup != null and businessGroup != ''">
        AND BUSINESS_GROUP = #{businessGroup,jdbcType=VARCHAR}
      </if>
      <if test="displayCode != null and displayCode != ''">
        AND DISPLAY_CODE = #{displayCode,jdbcType=VARCHAR}
      </if>
      <if test="scope != null and scope != ''">
        AND SCOPE = #{scope,jdbcType=VARCHAR}
      </if>
    </where>
    order by CREATE_TIME desc
    ) A
    where rownum &lt;= #{endRowNo,jdbcType=DECIMAL}
    )
    where RN &gt;= #{beginRowNo,jdbcType=DECIMAL}

  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from PAS_BUSINESS
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.pas.domain.Business" >
    insert into PAS_BUSINESS (ID, NAME, CODE, 
      TYPE, RATIO_MODE, RATIO, REMARK, 
      STATE, CREATE_TIME, CREATOR, 
      UPDATE_TIME, UPDATOR, BUSINESS_CATEGORY,SHOW_PAY_CONTROL,BUSINESS_LABEL,CAN_REPEAT,TEMPLATE_CODE ,BUSINESS_GROUP,DISPLAY_CODE,SCOPE )
    values (#{id,jdbcType=DECIMAL}, #{name,jdbcType=VARCHAR}, #{code,jdbcType=VARCHAR}, 
      #{type,jdbcType=CHAR}, #{ratioMode,jdbcType=CHAR}, #{ratio,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{state,jdbcType=CHAR}, #{createTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{updator,jdbcType=VARCHAR}, #{businessCategory,jdbcType=VARCHAR}, #{showPayControl,jdbcType=VARCHAR}, 
      #{businessLabel,jdbcType=VARCHAR},#{canRepeat,jdbcType=VARCHAR},
      #{templateCode,jdbcType=VARCHAR},#{businessGroup,jdbcType=VARCHAR},
      #{displayCode,jdbcType=VARCHAR},#{scope,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.pas.domain.Business" >
    insert into PAS_BUSINESS
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null and id != ''" >
        ID,
      </if>
      <if test="name != null and name != ''" >
        NAME,
      </if>
      <if test="code != null and code != ''" >
        CODE,
      </if>
      <if test="type != null and type != ''" >
        TYPE,
      </if>
      <if test="ratioMode != null and ratioMode != ''" >
        RATIO_MODE,
      </if>
      <if test="ratio != null and ratio != ''" >
        RATIO,
      </if>
      <if test="remark != null and remark != ''" >
        REMARK,
      </if>
      <if test="state != null and state != ''" >
        STATE,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="creator != null and creator != ''" >
        CREATOR,
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME,
      </if>
      <if test="updator != null and updator != ''" >
        UPDATOR,
      </if>
      <if test="businessCategory != null and businessCategory != ''" >
        BUSINESS_CATEGORY,
      </if>
      <if test="showPayControl != null and showPayControl != ''">
        SHOW_PAY_CONTROL,
      </if>
      <if test="businessLabel != null and businessLabel != ''">
        BUSINESS_LABEL,
      </if>
      <if test="businessLabel != null and businessLabel != ''">
        CAN_REPEAT,
      </if>
      <if test="templateCode != null and templateCode != ''">
        TEMPLATE_CODE ,
      </if>
      <if test="businessGroup != null and businessGroup != ''">
        BUSINESS_GROUP ,
      </if>
      <if test="displayCode != null and displayCode != ''">
        DISPLAY_CODE ,
      </if>
      <if test="scope != null and scope != ''">
        SCOPE ,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null and id != ''" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="name != null and name != ''" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="code != null and code != ''" >
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="type != null and type != ''" >
        #{type,jdbcType=CHAR},
      </if>
      <if test="ratioMode != null and ratioMode != ''" >
        #{ratioMode,jdbcType=CHAR},
      </if>
      <if test="ratio != null and ratio != ''" >
        #{ratio,jdbcType=VARCHAR},
      </if>
      <if test="remark != null and remark != ''" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="state != null and state != ''" >
        #{state,jdbcType=CHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null and creator != ''" >
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null and updator != ''" >
        #{updator,jdbcType=VARCHAR},
      </if>
      <if test="businessCategory != null and businessCategory != ''" >
        #{businessCategory,jdbcType=VARCHAR},
      </if>
      <if test="showPayControl != null and showPayControl != ''">
        #{showPayControl,jdbcType=VARCHAR} ,
      </if>
      <if test="businessLabel != null and businessLabel != ''">
        #{businessLabel,jdbcType=VARCHAR} ,
      </if>
      <if test="canRepeat != null and canRepeat != ''">
        #{canRepeat,jdbcType=VARCHAR} ,
      </if>
      <if test="templateCode != null and templateCode != ''">
        #{templateCode,jdbcType=VARCHAR},
      </if>
      <if test="businessGroup != null and businessGroup != ''">
        #{businessGroup,jdbcType=VARCHAR} ,
      </if>
      <if test="displayCode != null and displayCode != ''">
         #{displayCode,jdbcType=VARCHAR} ,
      </if>
      <if test="scope != null and scope != ''">
        #{scope,jdbcType=VARCHAR} ,
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.pas.domain.Business" >
    update PAS_BUSINESS
    <set >
      <if test="name != null and name != ''" >
        NAME = #{name,jdbcType=VARCHAR},
      </if>
      <if test="code != null and code != ''" >
        CODE = #{code,jdbcType=VARCHAR},
      </if>
      <if test="type != null and type != ''" >
        TYPE = #{type,jdbcType=CHAR},
      </if>
      <if test="ratioMode != null and ratioMode != ''" >
        RATIO_MODE = #{ratioMode,jdbcType=CHAR},
      </if>
      <if test="ratio != null and ratio != ''" >
        RATIO = #{ratio,jdbcType=VARCHAR},
      </if>
      <if test="remark != null and remark != ''" >
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="state != null and state != ''" >
        STATE = #{state,jdbcType=CHAR},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null and creator != ''" >
        CREATOR = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null and updator != ''" >
        UPDATOR = #{updator,jdbcType=VARCHAR},
      </if>
      <if test="businessCategory != null and businessCategory != ''" >
        BUSINESS_CATEGORY = #{businessCategory,jdbcType=VARCHAR},
      </if>
      <if test="showPayControl != null and showPayControl != ''">
        AND SHOW_PAY_CONTROL = #{showPayControl,jdbcType=VARCHAR}
      </if>
      <if test="businessLabel != null and businessLabel != ''">
        AND BUSINESS_LABEL = #{businessLabel,jdbcType=VARCHAR}
      </if> <if test="canRepeat != null and canRepeat != ''">
        AND CAN_REPEAT = #{canRepeat,jdbcType=VARCHAR}
      </if>
      <if test="templateCode != null and templateCode != ''">
        AND TEMPLATE_CODE = #{templateCode,jdbcType=VARCHAR}
      </if>
      <if test="businessGroup != null and businessGroup != ''">
        AND BUSINESS_GROUP = #{businessGroup,jdbcType=VARCHAR}
      </if>
      <if test="displayCode != null and displayCode != ''">
        and DISPLAY_CODE = #{displayCode,jdbcType=VARCHAR}
      </if>
      <if test="scope != null and scope != ''">
        and scope = #{scope,jdbcType=VARCHAR}
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.pas.domain.Business" >
    update PAS_BUSINESS
    set NAME = #{name,jdbcType=VARCHAR},
      CODE = #{code,jdbcType=VARCHAR},
      TYPE = #{type,jdbcType=CHAR},
      RATIO_MODE = #{ratioMode,jdbcType=CHAR},
      RATIO = #{ratio,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      STATE = #{state,jdbcType=CHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      UPDATOR = #{updator,jdbcType=VARCHAR},
      BUSINESS_CATEGORY = #{businessCategory,jdbcType=VARCHAR},
      SHOW_PAY_CONTROL = #{showPayControl,jdbcType=VARCHAR},
      BUSINESS_LABEL = #{businessLabel,jdbcType=VARCHAR},
      CAN_REPEAT = #{canRepeat,jdbcType=VARCHAR},
     TEMPLATE_CODE = #{templateCode,jdbcType=VARCHAR} ,
     BUSINESS_GROUP = #{businessGroup,jdbcType=VARCHAR},
     DISPLAY_CODE = #{displayCode,jdbcType=VARCHAR},
     scope = #{scope,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <select id="getBusinessByCodes" resultMap="BaseResultMap"  >
    select
    <include refid="Base_Column_List" />
    from PAS_BUSINESS where CODE IN
    <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>
  <select id="selectMaxCode" resultType="java.lang.String"  >
    <![CDATA[ SELECT * FROM (select CODE from PAS_BUSINESS WHERE CODE LIKE 'BC%' order by CODE desc) WHERE rownum <=1 ]]>
  </select>
  <select id="selectByState" resultMap="BaseResultMap">
    select
  	<include refid="Base_Column_List" />
    from PAS_BUSINESS where STATE = #{state , jdbcType = VARCHAR}
  </select>
  <select id="selectByCode" resultMap="BaseResultMap">
  	select
  	<include refid="Base_Column_List" />
  	from PAS_BUSINESS where CODE = #{businessCode , jdbcType = VARCHAR}
  </select>
  <select id="getBusinessesByCatetory" resultMap="BaseResultMap">
  	select distinct
  	b.ID, b.NAME, b.CODE, b.TYPE, b.RATIO_MODE, b.RATIO, b.REMARK, b.STATE, b.CREATE_TIME, b.CREATOR, b.UPDATE_TIME,
  	    b.UPDATOR, b.BUSINESS_CATEGORY,b.SHOW_PAY_CONTROL,b.CAN_REPEAT,b.TEMPLATE_CODE ,b.BUSINESS_GROUP ,b.DISPLAY_CODE,b.SCOPE
  	from PAS_BUSINESS  b ,pas_business_category_relation r
  	where b.id = r.business_id and r.business_category_code in
    <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
      #{item}
    </foreach>
    order by b.NAME
  </select>
  <select id="selectBusinessCategoryByCode" resultType="String">
    select
    BUSINESS_CATEGORY
    from PAS_BUSINESS where CODE = #{businessCode,jdbcType=VARCHAR}
  </select>
  <select id="selectByBusinessLabel"  resultMap="BaseResultMap">
    select 
   	<include refid="Base_Column_List" />
    from PAS_BUSINESS
    where ','||business_label||',' like  '%,'|| #{businessLabel,jdbcType=VARCHAR} ||',%'
  </select>
  <select id="selectByCodeOrName" resultMap="BaseResultMap" parameterType="java.util.Map" >
        select
        <include refid="Base_Column_List" />
        from PAS_BUSINESS
        <where>
          <if test="businessCode != null and businessCode != ''" >
            AND code = #{businessCode,jdbcType=DECIMAL}
          </if>
          <if test="businessName != null and businessName != ''" >
            AND name = #{businessName,jdbcType=VARCHAR}
          </if>

        </where>
      </select>
  <select id="getBusinessCatetoryByBusiness" resultMap="BaseResultMap">
  	select
  	b.ID, b.NAME, b.CODE, b.TYPE, b.RATIO_MODE, b.RATIO, b.REMARK, b.STATE, b.CREATE_TIME, b.CREATOR, b.UPDATE_TIME,
  	    b.UPDATOR,  r.BUSINESS_CATEGORY_code as BUSINESS_CATEGORY,b.SHOW_PAY_CONTROL,b.CAN_REPEAT
  	from PAS_BUSINESS  b ,pas_business_category_relation r where b.id = r.business_id and b.CODE = #{businessCode , jdbcType = VARCHAR}
  </select>
  <select id="selectBusinessByGroupOrTemplate" resultMap="BaseResultMap" parameterType="java.util.Map" >
         select
         <include refid="Base_Column_List" />
         from PAS_BUSINESS
         <where>

           <if test="businessGroup != null and businessGroup != ''" >
             AND BUSINESS_GROUP IN
             <foreach item="item" index="index" collection="groupCodeList" open="(" separator="," close=")">
               #{item}
             </foreach>
           </if>
           <if test="templateCode != null and templateCode != ''" >
             AND TEMPLATE_CODE     IN
             <foreach item="item" index="index" collection="templateCodeList" open="(" separator="," close=")">
                                #{item}
                              </foreach>
           </if>
           <if test="businessCode != null and businessCode != ''">
             AND code = #{businessCode,jdbcType=DECIMAL}
           </if>
           <if test="businessName != null and businessName != ''">
             AND name = #{businessName,jdbcType=VARCHAR}
           </if>

         </where>
       </select>
  <resultMap id="BaseResultMap1" type="com.epaylinks.efps.pas.pas.domain.Business" >
      <id column="ID" property="id" jdbcType="DECIMAL" />
      <result column="NAME" property="name" jdbcType="VARCHAR" />
      <result column="CODE" property="code" jdbcType="VARCHAR" />
      <result column="TYPE" property="type" jdbcType="CHAR" />
      <result column="RATIO_MODE" property="ratioMode" jdbcType="CHAR" />
      <result column="RATIO" property="ratio" jdbcType="VARCHAR" />
      <result column="REMARK" property="remark" jdbcType="VARCHAR" />
      <result column="STATE" property="state" jdbcType="CHAR" />
      <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
      <result column="CREATOR" property="creator" jdbcType="VARCHAR" />
      <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
      <result column="UPDATOR" property="updator" jdbcType="VARCHAR" />
      <result column="BUSINESS_CATEGORY" property="businessCategory" jdbcType="VARCHAR" />
      <result column="SHOW_PAY_CONTROL" property="showPayControl" jdbcType="VARCHAR" />
      <result column="BUSINESS_LABEL" property="businessLabel" jdbcType="VARCHAR" />
      <result column="CAN_REPEAT" property="canRepeat" jdbcType="VARCHAR" />
      <result column="TEMPLATE_CODE" property="templateCode" jdbcType="VARCHAR" />
      <result column="BUSINESS_GROUP" property="businessGroup" jdbcType="VARCHAR" />
      <result column="SETT_CYCLE" property="settCycle" jdbcType="VARCHAR" />
      <result column="MAX_PROFIT_PROPORTION" property="maxProfitProportion" jdbcType="VARCHAR" />
      <result column="template_name" property="templateName" jdbcType="VARCHAR" />
      <result column="business_group_name" property="businessGroupName" jdbcType="VARCHAR" />

    </resultMap>
  <select id="selectApplyBusinessByDisplayCode" resultMap="ApplyBusinessResultMap">
   SELECT * FROM PAS_BUSINESS WHERE DISPLAY_CODE = #{displayCode,jdbcType=VARCHAR}
  </select>
  <select id="selectApplyBusinessByCode" resultMap="ApplyBusinessResultMap">
   SELECT * FROM PAS_BUSINESS WHERE CODE = #{code,jdbcType=VARCHAR}
  </select>
  <select id="selectBusinessesByScope" resultMap="BaseResultMap">
    SELECT * FROM PAS_BUSINESS WHERE SCOPE = #{scope,jdbcType=VARCHAR}
  </select>
  <select id="queryKeywords"  resultMap="BaseResultMap" parameterType="java.lang.String" >
    SELECT * FROM PAS_BUSINESS WHERE  NAME LIKE  CONCAT(CONCAT('%',#{keywords}),'%')  OR CODE LIKE  CONCAT(CONCAT('%',#{keywords}),'%')
  </select>

  <select id="selectByCodeList" resultMap="ApplyBusinessResultMap">
   SELECT * FROM PAS_BUSINESS WHERE CODE IN
    <foreach item="item" index="index" collection="codes" open="(" separator="," close=")">
      #{item}
    </foreach>
    AND TERM_TAG='TERM'
  </select>

  <!-- 查询商户费率信息 -->
  <select id="selectCustBusinessPrice" parameterType="java.lang.Long" resultType="com.epaylinks.efps.pas.pas.controller.dto.CustBusinessPriceDTO">
    select p.CUST_BUSINESS_PRICE_ID custBusinessPriceId,
           p.CUSTOMER_ID customerId,
           p.CUST_BUSINESS_ID custBusinessId,
           p.FEE_FROM feeFrom,
           p.FEE_TO feeTo,
           p.FEE_MODE feeMode,
           p.FEE_RATE feeRate,
           p.FEE_PER feePer,
           p.ORDERNO orderno,
           p.FEE_FROM_MODE feeFromMode,
           p.FEE_TO_MODE feeToMode,
           b.BUSINESS_CODE businessCode
    from cust_business b ,cust_business_price p
    where b.cust_business_id = p.cust_business_id
    and b.customer_id = #{customerId,jdbcType=DECIMAL}
  </select>

  <select id="selectAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from PAS_BUSINESS
  </select>
</mapper>