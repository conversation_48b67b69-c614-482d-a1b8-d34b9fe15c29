<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.pas.pas.dao.BusinessGroupMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.pas.domain.BusinessGroup" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon May 06 13:18:13 CST 2019.
    -->
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="CODE" property="code" jdbcType="VARCHAR" />
    <result column="NAME" property="name" jdbcType="VARCHAR" />
    <result column="IS_TEMPLATE" property="isTemplate" jdbcType="VARCHAR" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
    <result column="SORT_NO" property="sortNo" jdbcType="DECIMAL" />
    <result column="SETT_CYCLE" property="settCycle" jdbcType="VARCHAR" />
    <result column="MAX_PROFIT_PROPORTION" property="maxProfitProportion" jdbcType="VARCHAR" />
    <result column="PARENT_CODE" property="parentCode" jdbcType="VARCHAR" />
    <result column="MODEL_CODE" property="modelCode" jdbcType="VARCHAR" />
    <result column="BUSINESS_CATEGORY_CODE" property="businessCategoryCode" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon May 06 13:18:13 CST 2019.
    -->
    ID, CODE, NAME, IS_TEMPLATE, REMARK, SORT_NO ,SETT_CYCLE ,MAX_PROFIT_PROPORTION,PARENT_CODE ,MODEL_CODE,BUSINESS_CATEGORY_CODE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon May 06 13:18:13 CST 2019.
    -->
    select 
    <include refid="Base_Column_List" />
    from PAS_BUSINESS_GROUP
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon May 06 13:18:13 CST 2019.
    -->
    delete from PAS_BUSINESS_GROUP
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.pas.domain.BusinessGroup" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon May 06 13:18:13 CST 2019.
    -->
    insert into PAS_BUSINESS_GROUP (ID, CODE, NAME, 
      IS_TEMPLATE, REMARK, SORT_NO,SETT_CYCLE ,MAX_PROFIT_PROPORTION,PARENT_CODE,MODEL_CODE
      )
    values (#{id,jdbcType=DECIMAL}, #{code,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{isTemplate,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{sortNo,jdbcType=DECIMAL}
      ,#{settCycle,jdbcType=VARCHAR},#{maxProfitProportion,jdbcType=VARCHAR},#{parentCode,jdbcType=VARCHAR},
    #{modelCode,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.pas.domain.BusinessGroup" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon May 06 13:18:13 CST 2019.
    -->
    insert into PAS_BUSINESS_GROUP
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="code != null" >
        CODE,
      </if>
      <if test="name != null" >
        NAME,
      </if>
      <if test="isTemplate != null" >
        IS_TEMPLATE,
      </if>
      <if test="remark != null" >
        REMARK,
      </if>
      <if test="sortNo != null" >
        SORT_NO,
      </if>
      <if test="settCycle != null">
        SETT_CYCLE ,
      </if>
      <if test="maxProfitProportion != null">
        MAX_PROFIT_PROPORTION,
      </if>
      <if test="parentCode != null">
        PARENT_CODE,
      </if> <if test="modelCode != null">
      MODEL_CODE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="code != null" >
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="name != null" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="isTemplate != null" >
        #{isTemplate,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="sortNo != null" >
        #{sortNo,jdbcType=DECIMAL},
      </if>
      <if test="settCycle != null" >
        #{settCycle,jdbcType=VARCHAR},
      </if>
      <if test="maxProfitProportion != null" >
      #{maxProfitProportion,jdbcType=VARCHAR},
      </if>
      <if test="parentCode != null" >
      #{parentCode,jdbcType=VARCHAR},
      </if>
      <if test="modelCode != null" >
      #{modelCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.pas.domain.BusinessGroup" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon May 06 13:18:13 CST 2019.
    -->
    update PAS_BUSINESS_GROUP
    <set >
      <if test="code != null" >
        CODE = #{code,jdbcType=VARCHAR},
      </if>
      <if test="name != null" >
        NAME = #{name,jdbcType=VARCHAR},
      </if>
      <if test="isTemplate != null" >
        IS_TEMPLATE = #{isTemplate,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="sortNo != null" >
        SORT_NO = #{sortNo,jdbcType=DECIMAL},
      </if>
      <if test="settCycle != null">
        SETT_CYCLE= #{settCycle,jdbcType=VARCHAR},
      </if>
      <if test="maxProfitProportion != null">
        MAX_PROFIT_PROPORTION = #{maxProfitProportion,jdbcType=VARCHAR},
      </if>
      <if test="parentCode != null">
        PARENT_CODE = #{parentCode,jdbcType=VARCHAR},
      </if>
      <if test="modelCode != null">
        MODEL_CODE = #{modelCode,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.pas.domain.BusinessGroup" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon May 06 13:18:13 CST 2019.
    -->
    update PAS_BUSINESS_GROUP
    set CODE = #{code,jdbcType=VARCHAR},
      NAME = #{name,jdbcType=VARCHAR},
      IS_TEMPLATE = #{isTemplate,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      SORT_NO = #{sortNo,jdbcType=DECIMAL},
    SETT_CYCLE= #{settCycle,jdbcType=VARCHAR},
    MAX_PROFIT_PROPORTION = #{maxProfitProportion,jdbcType=VARCHAR} ,
    PARENT_CODE = #{parentCode,jdbcType=VARCHAR} ,
    MODEL_CODE = #{modelCode,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>


  <select id="selectAllBusinssGroup" resultMap="BaseResultMap" parameterType="java.lang.String">
      select
      <include refid="Base_Column_List" />
      from PAS_BUSINESS_GROUP
    where IS_TEMPLATE = #{isTemplate,jdbcType=VARCHAR}
    AND   parent_code is null order by sort_no
    </select>
<select id="selectAllTemplateBusinssGroup" resultMap="BaseResultMap" parameterType="com.epaylinks.efps.pas.pas.domain.BusinessGroup">
      select
      <include refid="Base_Column_List" />
      from PAS_BUSINESS_GROUP
    where IS_TEMPLATE = #{isTemplate,jdbcType=VARCHAR}
    AND   parent_code  =  #{parentCode,jdbcType=VARCHAR}
  order by   SORT_NO
    </select>

  <select id="selectByCode" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from PAS_BUSINESS_GROUP
    where CODE = #{code,jdbcType=VARCHAR}
  </select>
</mapper>