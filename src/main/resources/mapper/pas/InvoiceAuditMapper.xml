<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.pas.pas.dao.InvoiceAuditMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.pas.domain.InvoiceAudit" >
    <id column="INVOICE_ID" property="invoiceId" jdbcType="DECIMAL" />
    <result column="EPL_SERIAL_NO" property="eplSerialNo" jdbcType="VARCHAR" />
    <result column="CUSTOMER_NO" property="customerNo" jdbcType="VARCHAR" />
    <result column="CUSTOMER_NAME" property="customerName" jdbcType="VARCHAR" />
    <result column="INVOICE_TYPE" property="invoiceType" jdbcType="VARCHAR" />
    <result column="INVOICE_MEDIUM" property="invoiceMedium" jdbcType="VARCHAR" />
    <result column="INVOICE_NO" property="invoiceNo" jdbcType="VARCHAR" />
    <result column="INVOICE_TIME" property="invoiceTime" jdbcType="TIMESTAMP" />
    <result column="TAX_RATIO" property="taxRatio" jdbcType="DECIMAL" />
    <result column="INVOICE_AMOUNT" property="invoiceAmount" jdbcType="DECIMAL" />
    <result column="UNIQUEID" property="uniqueid" jdbcType="VARCHAR" />
    <result column="AUDIT_STATUS" property="auditStatus" jdbcType="VARCHAR" />
    <result column="AUDIT_COMMENT" property="auditComment" jdbcType="VARCHAR" />
    <result column="REMARKS" property="remarks" jdbcType="VARCHAR" />
    <result column="SOURCE" property="source" jdbcType="VARCHAR" />
    <result column="CREATOR" property="creator" jdbcType="DECIMAL" />
    <result column="REVIEWER" property="reviewer" jdbcType="DECIMAL" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="AUDIT_TIME" property="auditTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    INVOICE_ID, EPL_SERIAL_NO, CUSTOMER_NO, CUSTOMER_NAME, INVOICE_TYPE, INVOICE_MEDIUM, 
    INVOICE_NO, INVOICE_TIME, TAX_RATIO, INVOICE_AMOUNT, UNIQUEID, AUDIT_STATUS, AUDIT_COMMENT, 
    REMARKS, SOURCE, CREATOR, REVIEWER, CREATE_TIME, UPDATE_TIME, AUDIT_TIME
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from PAS_INVOICE_AUDIT
    where INVOICE_ID = #{invoiceId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from PAS_INVOICE_AUDIT
    where INVOICE_ID = #{invoiceId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.pas.domain.InvoiceAudit" >
    insert into PAS_INVOICE_AUDIT (INVOICE_ID, EPL_SERIAL_NO, CUSTOMER_NO, 
      CUSTOMER_NAME, INVOICE_TYPE, INVOICE_MEDIUM, 
      INVOICE_NO, INVOICE_TIME, TAX_RATIO, 
      INVOICE_AMOUNT, UNIQUEID, AUDIT_STATUS, 
      AUDIT_COMMENT, REMARKS, SOURCE, 
      CREATOR, REVIEWER, CREATE_TIME, 
      UPDATE_TIME, AUDIT_TIME)
    values (#{invoiceId,jdbcType=DECIMAL}, #{eplSerialNo,jdbcType=VARCHAR}, #{customerNo,jdbcType=VARCHAR}, 
      #{customerName,jdbcType=VARCHAR}, #{invoiceType,jdbcType=VARCHAR}, #{invoiceMedium,jdbcType=VARCHAR}, 
      #{invoiceNo,jdbcType=VARCHAR}, #{invoiceTime,jdbcType=TIMESTAMP}, #{taxRatio,jdbcType=DECIMAL}, 
      #{invoiceAmount,jdbcType=DECIMAL}, #{uniqueid,jdbcType=VARCHAR}, #{auditStatus,jdbcType=VARCHAR}, 
      #{auditComment,jdbcType=VARCHAR}, #{remarks,jdbcType=VARCHAR}, #{source,jdbcType=VARCHAR}, 
      #{creator,jdbcType=DECIMAL}, #{reviewer,jdbcType=DECIMAL}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{auditTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.pas.domain.InvoiceAudit" >
    insert into PAS_INVOICE_AUDIT
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="invoiceId != null" >
        INVOICE_ID,
      </if>
      <if test="eplSerialNo != null" >
        EPL_SERIAL_NO,
      </if>
      <if test="customerNo != null" >
        CUSTOMER_NO,
      </if>
      <if test="customerName != null" >
        CUSTOMER_NAME,
      </if>
      <if test="invoiceType != null" >
        INVOICE_TYPE,
      </if>
      <if test="invoiceMedium != null" >
        INVOICE_MEDIUM,
      </if>
      <if test="invoiceNo != null" >
        INVOICE_NO,
      </if>
      <if test="invoiceTime != null" >
        INVOICE_TIME,
      </if>
      <if test="taxRatio != null" >
        TAX_RATIO,
      </if>
      <if test="invoiceAmount != null" >
        INVOICE_AMOUNT,
      </if>
      <if test="uniqueid != null" >
        UNIQUEID,
      </if>
      <if test="auditStatus != null" >
        AUDIT_STATUS,
      </if>
      <if test="auditComment != null" >
        AUDIT_COMMENT,
      </if>
      <if test="remarks != null" >
        REMARKS,
      </if>
      <if test="source != null" >
        SOURCE,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="reviewer != null" >
        REVIEWER,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME,
      </if>
      <if test="auditTime != null" >
        AUDIT_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="invoiceId != null" >
        #{invoiceId,jdbcType=DECIMAL},
      </if>
      <if test="eplSerialNo != null" >
        #{eplSerialNo,jdbcType=VARCHAR},
      </if>
      <if test="customerNo != null" >
        #{customerNo,jdbcType=VARCHAR},
      </if>
      <if test="customerName != null" >
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="invoiceType != null" >
        #{invoiceType,jdbcType=VARCHAR},
      </if>
      <if test="invoiceMedium != null" >
        #{invoiceMedium,jdbcType=VARCHAR},
      </if>
      <if test="invoiceNo != null" >
        #{invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTime != null" >
        #{invoiceTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taxRatio != null" >
        #{taxRatio,jdbcType=DECIMAL},
      </if>
      <if test="invoiceAmount != null" >
        #{invoiceAmount,jdbcType=DECIMAL},
      </if>
      <if test="uniqueid != null" >
        #{uniqueid,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null" >
        #{auditStatus,jdbcType=VARCHAR},
      </if>
      <if test="auditComment != null" >
        #{auditComment,jdbcType=VARCHAR},
      </if>
      <if test="remarks != null" >
        #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="source != null" >
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=DECIMAL},
      </if>
      <if test="reviewer != null" >
        #{reviewer,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditTime != null" >
        #{auditTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.pas.domain.InvoiceAudit" >
    update PAS_INVOICE_AUDIT
    <set >
      <if test="eplSerialNo != null" >
        EPL_SERIAL_NO = #{eplSerialNo,jdbcType=VARCHAR},
      </if>
      <if test="customerNo != null" >
        CUSTOMER_NO = #{customerNo,jdbcType=VARCHAR},
      </if>
      <if test="customerName != null" >
        CUSTOMER_NAME = #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="invoiceType != null" >
        INVOICE_TYPE = #{invoiceType,jdbcType=VARCHAR},
      </if>
      <if test="invoiceMedium != null" >
        INVOICE_MEDIUM = #{invoiceMedium,jdbcType=VARCHAR},
      </if>
      <if test="invoiceNo != null" >
        INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTime != null" >
        INVOICE_TIME = #{invoiceTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taxRatio != null" >
        TAX_RATIO = #{taxRatio,jdbcType=DECIMAL},
      </if>
      <if test="invoiceAmount != null" >
        INVOICE_AMOUNT = #{invoiceAmount,jdbcType=DECIMAL},
      </if>
      <if test="uniqueid != null" >
        UNIQUEID = #{uniqueid,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null" >
        AUDIT_STATUS = #{auditStatus,jdbcType=VARCHAR},
      </if>
      <if test="auditComment != null" >
        AUDIT_COMMENT = #{auditComment,jdbcType=VARCHAR},
      </if>
      <if test="remarks != null" >
        REMARKS = #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="source != null" >
        SOURCE = #{source,jdbcType=VARCHAR},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=DECIMAL},
      </if>
      <if test="reviewer != null" >
        REVIEWER = #{reviewer,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditTime != null" >
        AUDIT_TIME = #{auditTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where INVOICE_ID = #{invoiceId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.pas.domain.InvoiceAudit" >
    update PAS_INVOICE_AUDIT
    set EPL_SERIAL_NO = #{eplSerialNo,jdbcType=VARCHAR},
      CUSTOMER_NO = #{customerNo,jdbcType=VARCHAR},
      CUSTOMER_NAME = #{customerName,jdbcType=VARCHAR},
      INVOICE_TYPE = #{invoiceType,jdbcType=VARCHAR},
      INVOICE_MEDIUM = #{invoiceMedium,jdbcType=VARCHAR},
      INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR},
      INVOICE_TIME = #{invoiceTime,jdbcType=TIMESTAMP},
      TAX_RATIO = #{taxRatio,jdbcType=DECIMAL},
      INVOICE_AMOUNT = #{invoiceAmount,jdbcType=DECIMAL},
      UNIQUEID = #{uniqueid,jdbcType=VARCHAR},
      AUDIT_STATUS = #{auditStatus,jdbcType=VARCHAR},
      AUDIT_COMMENT = #{auditComment,jdbcType=VARCHAR},
      REMARKS = #{remarks,jdbcType=VARCHAR},
      SOURCE = #{source,jdbcType=VARCHAR},
      CREATOR = #{creator,jdbcType=DECIMAL},
      REVIEWER = #{reviewer,jdbcType=DECIMAL},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      AUDIT_TIME = #{auditTime,jdbcType=TIMESTAMP}
    where INVOICE_ID = #{invoiceId,jdbcType=DECIMAL}
  </update>

  <select id="selectSeqInvoiceAudit" resultType="java.lang.Long">
    select SEQ_INVOICE_AUDIT.nextval from dual
  </select>

  <select id="pageQuery" parameterType="java.util.Map" resultMap="BaseResultMap">
    select *
    from (
      select A.*,rownum RN
      from (
        select
        <include refid="Base_Column_List" />
        from PAS_INVOICE_AUDIT
        where 1=1
        <if test="customerNo != null and customerNo !=''">
          and CUSTOMER_NO = #{customerNo,jdbcType=VARCHAR}
        </if>
        <if test="invoiceNo != null and invoiceNo != ''">
          and INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR}
        </if>
        <if test="invoiceMedium != null and invoiceMedium != ''">
          and INVOICE_MEDIUM = #{invoiceMedium,jdbcType=VARCHAR}
        </if>
        <if test="auditStatus != null and auditStatus != ''">
          and AUDIT_STATUS = #{auditStatus,jdbcType=VARCHAR}
        </if>
        <if test="startCreateTime != null and startCreateTime != ''">
          <![CDATA[ and CREATE_TIME >= TO_DATE(#{startCreateTime,jdbcType=VARCHAR},'yyyyMMddhh24miss')]]>
        </if>
        <if test="endCreateTime != null and endCreateTime != ''">
          <![CDATA[ and CREATE_TIME <= TO_DATE(#{endCreateTime,jdbcType=VARCHAR},'yyyyMMddhh24miss')]]>
        </if>
        <if test="headCustomerNo != null and headCustomerNo != ''">
          and CUSTOMER_NO = #{headCustomerNo,jdbcType=VARCHAR}
        </if>
        order by INVOICE_ID desc
      )A where rownum &lt;= #{endRowNo}
    ) where RN &gt;= #{beginRowNo}
  </select>

  <select id="count" parameterType="java.util.Map" resultType="java.lang.Integer" >
    select count(*)
    from (
      select
      <include refid="Base_Column_List" />
      from PAS_INVOICE_AUDIT
      where 1=1
      <if test="customerNo != null and customerNo !=''">
        and CUSTOMER_NO = #{customerNo,jdbcType=VARCHAR}
      </if>
      <if test="invoiceNo != null and invoiceNo != ''">
        and INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR}
      </if>
      <if test="invoiceMedium != null and invoiceMedium != ''">
        and INVOICE_MEDIUM = #{invoiceMedium,jdbcType=VARCHAR}
      </if>
      <if test="auditStatus != null and auditStatus != ''">
        and AUDIT_STATUS = #{auditStatus,jdbcType=VARCHAR}
      </if>
      <if test="startCreateTime != null and startCreateTime != ''">
        <![CDATA[ and CREATE_TIME >= TO_DATE(#{startCreateTime,jdbcType=VARCHAR},'yyyyMMddhh24miss')]]>
      </if>
      <if test="endCreateTime != null and endCreateTime != ''">
        <![CDATA[ and CREATE_TIME <= TO_DATE(#{endCreateTime,jdbcType=VARCHAR},'yyyyMMddhh24miss')]]>
      </if>
      <if test="headCustomerNo != null and headCustomerNo != ''">
        and CUSTOMER_NO = #{headCustomerNo,jdbcType=VARCHAR}
      </if>
      order by INVOICE_ID desc
    )
  </select>

  <select id="pageQueryInvoice" parameterType="java.util.Map" resultType="com.epaylinks.efps.pas.pas.domain.InvoiceAudit">
    select *
    from (
      select A.*,rownum RN
      from (
        select
    t.INVOICE_ID invoiceId, t.EPL_SERIAL_NO eplSerialNo, t.CUSTOMER_NO customerNo,
    t.CUSTOMER_NAME customerName, t.INVOICE_TYPE invoiceType, t.INVOICE_MEDIUM invoiceMedium,
    t.INVOICE_NO invoiceNo, t.INVOICE_TIME invoiceTime, t.TAX_RATIO taxRatio,
    t.INVOICE_AMOUNT invoiceAmount, t.UNIQUEID uniqueid, t.AUDIT_STATUS auditStatus,
    t.AUDIT_COMMENT auditComment, t.REMARKS remarks, t.SOURCE source,
    t.CREATOR creator, t.REVIEWER reviewer, t.CREATE_TIME createTime,
    t.UPDATE_TIME updateTime, t.AUDIT_TIME auditTime,
    case when t.SOURCE = 'PAS' then nvl(u.REAL_NAME,u.NAME)
        else nvl(c.REALNAME,c.USERNAME)
    end as creatorName,
    nvl(r.REAL_NAME,r.NAME) reviewerName
        from PAS_INVOICE_AUDIT t
        left join pas_user u on t.CREATOR = u.user_id
        left join cust_user c on t.CREATOR = c.user_id
        left join pas_user r on t.REVIEWER = r.user_id
        where 1=1
        <if test="customerNo != null and customerNo !=''">
          and t.CUSTOMER_NO = #{customerNo,jdbcType=VARCHAR}
        </if>
        <if test="invoiceNo != null and invoiceNo != ''">
          and t.INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR}
        </if>
        <if test="invoiceMedium != null and invoiceMedium != ''">
          and t.INVOICE_MEDIUM = #{invoiceMedium,jdbcType=VARCHAR}
        </if>
        <if test="auditStatus != null and auditStatus != ''">
          and t.AUDIT_STATUS = #{auditStatus,jdbcType=VARCHAR}
        </if>
        <if test="startCreateTime != null and startCreateTime != ''">
          <![CDATA[ and t.CREATE_TIME >= TO_DATE(#{startCreateTime,jdbcType=VARCHAR},'yyyyMMddhh24miss')]]>
        </if>
        <if test="endCreateTime != null and endCreateTime != ''">
          <![CDATA[ and t.CREATE_TIME <= TO_DATE(#{endCreateTime,jdbcType=VARCHAR},'yyyyMMddhh24miss')]]>
        </if>
        <if test="headCustomerNo != null and headCustomerNo != ''">
          and t.CUSTOMER_NO = #{headCustomerNo,jdbcType=VARCHAR}
        </if>
        order by t.INVOICE_ID desc
      )A where rownum &lt;= #{endRowNo}
    ) where RN &gt;= #{beginRowNo}
  </select>

  <select id="selectCustUser" parameterType="java.lang.Long" resultType="java.lang.String">
    select nvl(REALNAME,USERNAME) from cust_user
    where user_id = #{userId,jdbcType=DECIMAL}
    and type != '2'
  </select>

</mapper>