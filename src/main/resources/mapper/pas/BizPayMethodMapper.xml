<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.pas.pas.dao.BizPayMethodMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.pas.domain.BizPayMethod" >
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="BUSINESS_CODE" property="businessCode" jdbcType="VARCHAR" />
    <result column="PAY_METHOD_CODE" property="payMethodCode" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, BUSINESS_CODE, PAY_METHOD_CODE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from PAS_BIZ_PAY_METHOD
    where ID = #{id,jdbcType=DECIMAL}
  </select>

  <select id="selectBySelective" resultMap="BaseResultMap" parameterType="com.epaylinks.efps.pas.pas.domain.BizPayMethod" >
    select
    <include refid="Base_Column_List" />
    from PAS_BIZ_PAY_METHOD
    <where>
      <if test="id != null and id != ''" >
        AND ID = #{id,jdbcType=DECIMAL}
      </if>
      <if test="businessCode != null and businessCode != ''" >
        AND BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR}
      </if>
      <if test="payMethodCode != null and payMethodCode != ''" >
        AND PAY_METHOD_CODE = #{payMethodCode,jdbcType=VARCHAR}
      </if>
    </where>
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from PAS_BIZ_PAY_METHOD
    where ID = #{id,jdbcType=DECIMAL}
  </delete>

  <delete id="deleteBySelective" parameterType="java.lang.Long" >
    delete from PAS_BIZ_PAY_METHOD
    <where>
      <if test="id != null and id != ''" >
        AND ID = #{id,jdbcType=DECIMAL}
      </if>
      <if test="businessCode != null and businessCode != ''" >
        AND BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR}
      </if>
      <if test="payMethodCode != null and payMethodCode != ''" >
        AND PAY_METHOD_CODE = #{payMethodCode,jdbcType=VARCHAR}
      </if>
    </where>
  </delete>

  <insert id="insert" parameterType="com.epaylinks.efps.pas.pas.domain.BizPayMethod" >
    insert into PAS_BIZ_PAY_METHOD (ID, BUSINESS_CODE, PAY_METHOD_CODE
      )
    values (#{id,jdbcType=DECIMAL}, #{businessCode,jdbcType=VARCHAR}, #{payMethodCode,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.pas.domain.BizPayMethod" >
    insert into PAS_BIZ_PAY_METHOD
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null and id != ''" >
        ID,
      </if>
      <if test="businessCode != null and businessCode != ''" >
        BUSINESS_CODE,
      </if>
      <if test="payMethodCode != null and payMethodCode != ''" >
        PAY_METHOD_CODE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null and id != ''" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="businessCode != null and businessCode != ''" >
        #{businessCode,jdbcType=VARCHAR},
      </if>
      <if test="payMethodCode != null and payMethodCode != ''" >
        #{payMethodCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.pas.domain.BizPayMethod" >
    update PAS_BIZ_PAY_METHOD
    <set >
      <if test="businessCode != null and businessCode != ''" >
        BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR},
      </if>
      <if test="payMethodCode != null and payMethodCode != ''" >
        PAY_METHOD_CODE = #{payMethodCode,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.pas.domain.BizPayMethod" >
    update PAS_BIZ_PAY_METHOD
    set BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR},
      PAY_METHOD_CODE = #{payMethodCode,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>

  <insert id="addBizPayMethods" parameterType="java.util.List">
    BEGIN
    <foreach collection="list" item="item" index="index" separator="">
      insert into PAS_BIZ_PAY_METHOD
      (ID, BUSINESS_CODE, PAY_METHOD_CODE)
      VALUES
      (
      #{item.id,jdbcType=DECIMAL},
      #{item.businessCode,jdbcType=VARCHAR},
      #{item.payMethodCode,jdbcType=VARCHAR} );
    </foreach>
    COMMIT;
    END;
  </insert>
  
  <select id="selectPayMethods" resultType="java.lang.String">
    select PAY_METHOD_CODE
    from PAS_BIZ_PAY_METHOD
    where BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR}
  </select>

  <select id="selectAll" resultMap="BaseResultMap">
  	select
    <include refid="Base_Column_List" />
    from PAS_BIZ_PAY_METHOD
  </select>

</mapper>