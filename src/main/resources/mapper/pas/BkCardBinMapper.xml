<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.pas.pas.dao.BkCardBinMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.pas.model.BkCardBin">
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="RECORD_ID" jdbcType="DECIMAL" property="recordId" />
    <result column="CARD_NO_RANGE" jdbcType="VARCHAR" property="cardNoRange" />
    <result column="CARD_NO_RANGE_LEN" jdbcType="DECIMAL" property="cardNoRangeLen" />
    <result column="ISSUE_BANK_NO" jdbcType="VARCHAR" property="issueBankNo" />
    <result column="BANK_ICON" jdbcType="VARCHAR" property="bankIcon" />
    <result column="ISSUE_BANK_NAME" jdbcType="VARCHAR" property="issueBankName" />
    <result column="CARD_NAME" jdbcType="VARCHAR" property="cardName" />
    <result column="APPLY_RANGE" jdbcType="DECIMAL" property="applyRange" />
    <result column="CARD_NO_LEN" jdbcType="DECIMAL" property="cardNoLen" />
    <result column="CARD_TYPE" jdbcType="VARCHAR" property="cardType" />
    <result column="ISSUE_BANK_ACCOUNT" jdbcType="VARCHAR" property="issueBankAccount" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="OPERATOR_ID" jdbcType="VARCHAR" property="operatorId" />
    <result column="FLAG" jdbcType="VARCHAR" property="flag" />
    <result column="UNION_BRAND" jdbcType="VARCHAR" property="unionBrand" />
    <result column="PARENT_TYPE" jdbcType="VARCHAR" property="parentType" />
    <result column="SON_TYPE" jdbcType="VARCHAR" property="sonType" />
    <result column="LOAD_FROM" jdbcType="VARCHAR" property="loadFrom" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, RECORD_ID, CARD_NO_RANGE, CARD_NO_RANGE_LEN, ISSUE_BANK_NO, BANK_ICON, ISSUE_BANK_NAME, 
    CARD_NAME, APPLY_RANGE, CARD_NO_LEN, CARD_TYPE, ISSUE_BANK_ACCOUNT, CREATE_TIME, 
    UPDATE_TIME, OPERATOR_ID, FLAG, UNION_BRAND, PARENT_TYPE, SON_TYPE, LOAD_FROM
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from BK_CARD_BIN
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from BK_CARD_BIN
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.pas.model.BkCardBin">
    insert into BK_CARD_BIN (ID, RECORD_ID, CARD_NO_RANGE, 
      CARD_NO_RANGE_LEN, ISSUE_BANK_NO, BANK_ICON, 
      ISSUE_BANK_NAME, CARD_NAME, APPLY_RANGE, 
      CARD_NO_LEN, CARD_TYPE, ISSUE_BANK_ACCOUNT, 
      CREATE_TIME, UPDATE_TIME, OPERATOR_ID, UNION_BRAND, PARENT_TYPE, SON_TYPE, LOAD_FROM
      )
    values (#{id,jdbcType=DECIMAL}, #{recordId,jdbcType=DECIMAL}, #{cardNoRange,jdbcType=VARCHAR}, 
      #{cardNoRangeLen,jdbcType=DECIMAL}, #{issueBankNo,jdbcType=VARCHAR}, #{bankIcon,jdbcType=VARCHAR}, 
      #{issueBankName,jdbcType=VARCHAR}, #{cardName,jdbcType=VARCHAR}, #{applyRange,jdbcType=DECIMAL}, 
      #{cardNoLen,jdbcType=DECIMAL}, #{cardType,jdbcType=VARCHAR}, #{issueBankAccount,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{operatorId,jdbcType=VARCHAR},
      #{unionBrand,jdbcType=VARCHAR}, #{parentType,jdbcType=VARCHAR}, #{sonType,jdbcType=VARCHAR}, #{loadFrom,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.pas.model.BkCardBin">
    insert into BK_CARD_BIN
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="recordId != null">
        RECORD_ID,
      </if>
      <if test="cardNoRange != null">
        CARD_NO_RANGE,
      </if>
      <if test="cardNoRangeLen != null">
        CARD_NO_RANGE_LEN,
      </if>
      <if test="issueBankNo != null">
        ISSUE_BANK_NO,
      </if>
      <if test="bankIcon != null">
        BANK_ICON,
      </if>
      <if test="issueBankName != null">
        ISSUE_BANK_NAME,
      </if>
      <if test="cardName != null">
        CARD_NAME,
      </if>
      <if test="applyRange != null">
        APPLY_RANGE,
      </if>
      <if test="cardNoLen != null">
        CARD_NO_LEN,
      </if>
      <if test="cardType != null">
        CARD_TYPE,
      </if>
      <if test="issueBankAccount != null">
        ISSUE_BANK_ACCOUNT,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="operatorId != null">
        OPERATOR_ID,
      </if>
      <if test="unionBrand != null">
        UNION_BRAND,
      </if>
      <if test="parentType != null">
        PARENT_TYPE,
      </if>
      <if test="sonType != null">
        SON_TYPE,
      </if>
      <if test="loadFrom != null">
        LOAD_FROM,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="recordId != null">
        #{recordId,jdbcType=DECIMAL},
      </if>
      <if test="cardNoRange != null">
        #{cardNoRange,jdbcType=VARCHAR},
      </if>
      <if test="cardNoRangeLen != null">
        #{cardNoRangeLen,jdbcType=DECIMAL},
      </if>
      <if test="issueBankNo != null">
        #{issueBankNo,jdbcType=VARCHAR},
      </if>
      <if test="bankIcon != null">
        #{bankIcon,jdbcType=VARCHAR},
      </if>
      <if test="issueBankName != null">
        #{issueBankName,jdbcType=VARCHAR},
      </if>
      <if test="cardName != null">
        #{cardName,jdbcType=VARCHAR},
      </if>
      <if test="applyRange != null">
        #{applyRange,jdbcType=DECIMAL},
      </if>
      <if test="cardNoLen != null">
        #{cardNoLen,jdbcType=DECIMAL},
      </if>
      <if test="cardType != null">
        #{cardType,jdbcType=VARCHAR},
      </if>
      <if test="issueBankAccount != null">
        #{issueBankAccount,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=VARCHAR},
      </if>
      <if test="unionBrand != null">
        #{unionBrand,jdbcType=VARCHAR},
      </if>
      <if test="parentType != null">
        #{parentType,jdbcType=VARCHAR},
      </if>
      <if test="sonType != null">
        #{sonType,jdbcType=VARCHAR},
      </if>
      <if test="loadFrom != null">
        #{loadFrom,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.pas.model.BkCardBin">
    update BK_CARD_BIN
    <set>
      <if test="recordId != null">
        RECORD_ID = #{recordId,jdbcType=DECIMAL},
      </if>
      <if test="cardNoRange != null">
        CARD_NO_RANGE = #{cardNoRange,jdbcType=VARCHAR},
      </if>
      <if test="cardNoRangeLen != null">
        CARD_NO_RANGE_LEN = #{cardNoRangeLen,jdbcType=DECIMAL},
      </if>
      <if test="issueBankNo != null">
        ISSUE_BANK_NO = #{issueBankNo,jdbcType=VARCHAR},
      </if>
      <if test="bankIcon != null">
        BANK_ICON = #{bankIcon,jdbcType=VARCHAR},
      </if>
      <if test="issueBankName != null">
        ISSUE_BANK_NAME = #{issueBankName,jdbcType=VARCHAR},
      </if>
      <if test="cardName != null">
        CARD_NAME = #{cardName,jdbcType=VARCHAR},
      </if>
      <if test="applyRange != null">
        APPLY_RANGE = #{applyRange,jdbcType=DECIMAL},
      </if>
      <if test="cardNoLen != null">
        CARD_NO_LEN = #{cardNoLen,jdbcType=DECIMAL},
      </if>
      <if test="cardType != null">
        CARD_TYPE = #{cardType,jdbcType=VARCHAR},
      </if>
      <if test="issueBankAccount != null">
        ISSUE_BANK_ACCOUNT = #{issueBankAccount,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operatorId != null">
        OPERATOR_ID = #{operatorId,jdbcType=VARCHAR},
      </if>
      <if test="flag != null">
          FLAG = #{flag,jdbcType=VARCHAR},
      </if>
      <if test="unionBrand != null">
        UNION_BRAND = #{unionBrand,jdbcType=VARCHAR},
      </if>
      <if test="parentType != null">
        PARENT_TYPE = #{parentType,jdbcType=VARCHAR},
      </if>
      <if test="sonType != null">
        SON_TYPE = #{sonType,jdbcType=VARCHAR},
      </if>
      <if test="loadFrom != null">
        LOAD_FROM = #{loadFrom,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.pas.model.BkCardBin">
    update BK_CARD_BIN
    set RECORD_ID = #{recordId,jdbcType=DECIMAL},
      CARD_NO_RANGE = #{cardNoRange,jdbcType=VARCHAR},
      CARD_NO_RANGE_LEN = #{cardNoRangeLen,jdbcType=DECIMAL},
      ISSUE_BANK_NO = #{issueBankNo,jdbcType=VARCHAR},
      BANK_ICON = #{bankIcon,jdbcType=VARCHAR},
      ISSUE_BANK_NAME = #{issueBankName,jdbcType=VARCHAR},
      CARD_NAME = #{cardName,jdbcType=VARCHAR},
      APPLY_RANGE = #{applyRange,jdbcType=DECIMAL},
      CARD_NO_LEN = #{cardNoLen,jdbcType=DECIMAL},
      CARD_TYPE = #{cardType,jdbcType=VARCHAR},
      ISSUE_BANK_ACCOUNT = #{issueBankAccount,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      OPERATOR_ID = #{operatorId,jdbcType=VARCHAR},
      UNION_BRAND = #{unionBrand,jdbcType=VARCHAR},
      PARENT_TYPE = #{parentType,jdbcType=VARCHAR},
      SON_TYPE = #{sonType,jdbcType=VARCHAR},
      LOAD_FROM = #{loadFrom,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>

  <select id="selectCountByMap" resultType="java.lang.Integer"
          parameterType="java.util.Map">
    select count(*)
    from BK_CARD_BIN
    <where>
      <include refid="queryCondition"/>
    </where>
  </select>

  <select id="selectBkCardBinPage" resultMap="BaseResultMap"
          parameterType="java.util.Map">
    select
    <include refid="Base_Column_List" />
    from (
    select A.*, rownum RN
    from (
    select
    <include refid="Base_Column_List" />
     from BK_CARD_BIN
    <where>
      <include refid="queryCondition"/>
    </where>
    order by CREATE_TIME desc
    ) A
    where rownum &lt;= #{endRowNo,jdbcType=DECIMAL}
    )
    where RN &gt;=
    #{beginRowNo,jdbcType=DECIMAL}
  </select>

  <select id="selectBkCardBinNotPage" resultMap="BaseResultMap"
          parameterType="java.util.Map">
    select
    <include refid="Base_Column_List" />
    from BK_CARD_BIN
    <where>
      <include refid="queryCondition"/>
    </where>
    order by CREATE_TIME desc
  </select>

  <select id="selectBkCardBinByCardNoRang" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from BK_CARD_BIN
    where CARD_NO_RANGE = #{cardNoRange,jdbcType=VARCHAR}
    AND PARENT_TYPE = #{parentType,jdbcType=VARCHAR}
    AND SON_TYPE = #{sonType,jdbcType=VARCHAR}
    AND FLAG = '1'
    order by LOAD_FROM asc, CREATE_TIME desc
  </select>

  <select id="selectByCardNoAndCardNoLen" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from BK_CARD_BIN
    where CARD_NO_RANGE = substr(#{cardNo,jdbcType=VARCHAR}, 0, CARD_NO_RANGE_LEN)
    and CARD_NO_LEN = #{cardNoLen,jdbcType=DECIMAL}
    AND PARENT_TYPE = #{parentType,jdbcType=VARCHAR}
    AND SON_TYPE = #{sonType,jdbcType=VARCHAR}
    AND FLAG = '1'
    order by LOAD_FROM asc, CARD_NO_RANGE_LEN desc
  </select>

  <sql id = "queryCondition">
    <if test="beginTime != null and beginTime != ''">
      AND CREATE_TIME <![CDATA[ >= ]]> to_date(#{beginTime},'yyyyMMddhh24miss')
    </if>
    <if test="endTime != null and endTime != ''">
      AND CREATE_TIME <![CDATA[ < ]]> to_date(#{endTime},'yyyyMMddhh24miss')
    </if>
    <if test="issueBankNo != null and issueBankNo != ''" >
      AND ISSUE_BANK_NO =  #{issueBankNo,jdbcType=VARCHAR}
    </if>
    <if test="issueBankName != null and issueBankName != ''" >
      AND ISSUE_BANK_NAME like concat('%',concat(#{issueBankName},'%'))
    </if>
    <if test="cardNoRange != null and cardNoRange != ''" >
      AND CARD_NO_RANGE = #{cardNoRange,jdbcType=VARCHAR}
    </if>
    <if test="cardNoLen != null and cardNoLen != ''" >
      AND CARD_NO_LEN = #{cardNoLen,jdbcType=VARCHAR}
    </if>
    <if test="flag != null and flag != ''" >
      AND FLAG =  #{flag,jdbcType=VARCHAR}
    </if>
    <if test="cardType != null and cardType != ''" >
      AND CARD_TYPE = #{cardType,jdbcType=VARCHAR}
    </if>
    <if test="cardName != null and cardName != ''">
      AND CARD_NAME = #{cardName,jdbcType=VARCHAR}
    </if>
    <if test="issueBankAccount != null and issueBankAccount != ''">
      AND ISSUE_BANK_ACCOUNT = #{issueBankAccount,jdbcType=VARCHAR}
    </if>
    <if test="parentType != null and parentType != ''">
      AND PARENT_TYPE = #{parentType,jdbcType=VARCHAR}
    </if>
    <if test="sonType != null and sonType != ''">
      AND SON_TYPE = #{sonType,jdbcType=VARCHAR}
    </if>
    <if test="loadFrom != null and loadFrom != ''">
      AND LOAD_FROM = #{loadFrom,jdbcType=VARCHAR}
    </if>
  </sql>

  <!-- 检查卡bin类型是否存在 -->
  <select id="checkCardBinTypeExist" resultType="java.lang.Boolean" >
    select count(*) from BK_CARD_BIN
    where PARENT_TYPE = #{parentType,jdbcType=VARCHAR} and SON_TYPE = #{sonType,jdbcType=VARCHAR} and OPERATOR_ID is NULL
  </select>

  <!-- 检查业务小类卡bin类型是否存在 -->
  <select id="checkYWCardBinTypeExist" resultType="java.lang.Boolean" >
    select count(*) from BK_CARD_BIN
    where PARENT_TYPE = #{parentType,jdbcType=VARCHAR} and SON_TYPE = #{sonType,jdbcType=VARCHAR} and RECORD_ID is NULL
  </select>

  <!-- 检查卡bin是否存在 -->
  <select id="checkCardBinExist" resultType="java.lang.Boolean" >
    select count(*) from BK_CARD_BIN
    where PARENT_TYPE = #{parentType,jdbcType=VARCHAR} and SON_TYPE = #{sonType,jdbcType=VARCHAR}
    and CARD_NO_RANGE = #{cardNoRange,jdbcType=VARCHAR} and CARD_NO_LEN = #{cardNoLen,jdbcType=DECIMAL}
    and LOAD_FROM = #{loadFrom,jdbcType=VARCHAR}
  </select>

  <!-- 查询卡bin -->
  <select id="selectCardBinByType" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from BK_CARD_BIN
    where PARENT_TYPE = #{parentType,jdbcType=VARCHAR} and SON_TYPE = #{sonType,jdbcType=VARCHAR}
    and CARD_NO_RANGE = #{cardNoRange,jdbcType=VARCHAR} and CARD_NO_LEN = #{cardNoLen,jdbcType=DECIMAL}
    and LOAD_FROM = #{loadFrom,jdbcType=VARCHAR}
  </select>
</mapper>