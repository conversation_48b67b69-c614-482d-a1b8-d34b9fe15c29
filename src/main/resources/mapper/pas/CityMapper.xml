<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.pas.pas.dao.CityMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.pas.domain.City" >
    <id column="CITY_CD" property="cityCd" jdbcType="VARCHAR" />
    <result column="PROV_CD" property="provCd" jdbcType="VARCHAR" />
    <result column="CITY_LNM" property="cityLnm" jdbcType="VARCHAR" />
    <result column="CITY_NM" property="cityNm" jdbcType="VARCHAR" />
    <result column="BOSS_CITY" property="bossCity" jdbcType="VARCHAR" />
    <result column="VGOP_CITY" property="vgopCity" jdbcType="VARCHAR" />
    <result column="UPD_DT" property="updDt" jdbcType="VARCHAR" />
    <result column="TM_SMP" property="tmSmp" jdbcType="VARCHAR" />
    <result column="NOD_ID" property="nodId" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    CITY_CD, PROV_CD, CITY_LNM, CITY_NM, BOSS_CITY, VGOP_CITY, UPD_DT, TM_SMP, NOD_ID
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from PAS_CCOP
    where CITY_CD = #{cityCd,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from PAS_CCOP
    where CITY_CD = #{cityCd,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.pas.domain.City" >
    insert into PAS_CCOP (CITY_CD, PROV_CD, CITY_LNM, 
      CITY_NM, BOSS_CITY, VGOP_CITY, 
      UPD_DT, TM_SMP, NOD_ID
      )
    values (#{cityCd,jdbcType=VARCHAR}, #{provCd,jdbcType=VARCHAR}, #{cityLnm,jdbcType=VARCHAR}, 
      #{cityNm,jdbcType=VARCHAR}, #{bossCity,jdbcType=VARCHAR}, #{vgopCity,jdbcType=VARCHAR}, 
      #{updDt,jdbcType=VARCHAR}, #{tmSmp,jdbcType=VARCHAR}, #{nodId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.pas.domain.City" >
    insert into PAS_CCOP
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="cityCd != null" >
        CITY_CD,
      </if>
      <if test="provCd != null" >
        PROV_CD,
      </if>
      <if test="cityLnm != null" >
        CITY_LNM,
      </if>
      <if test="cityNm != null" >
        CITY_NM,
      </if>
      <if test="bossCity != null" >
        BOSS_CITY,
      </if>
      <if test="vgopCity != null" >
        VGOP_CITY,
      </if>
      <if test="updDt != null" >
        UPD_DT,
      </if>
      <if test="tmSmp != null" >
        TM_SMP,
      </if>
      <if test="nodId != null" >
        NOD_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="cityCd != null" >
        #{cityCd,jdbcType=VARCHAR},
      </if>
      <if test="provCd != null" >
        #{provCd,jdbcType=VARCHAR},
      </if>
      <if test="cityLnm != null" >
        #{cityLnm,jdbcType=VARCHAR},
      </if>
      <if test="cityNm != null" >
        #{cityNm,jdbcType=VARCHAR},
      </if>
      <if test="bossCity != null" >
        #{bossCity,jdbcType=VARCHAR},
      </if>
      <if test="vgopCity != null" >
        #{vgopCity,jdbcType=VARCHAR},
      </if>
      <if test="updDt != null" >
        #{updDt,jdbcType=VARCHAR},
      </if>
      <if test="tmSmp != null" >
        #{tmSmp,jdbcType=VARCHAR},
      </if>
      <if test="nodId != null" >
        #{nodId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.pas.domain.City" >
    update PAS_CCOP
    <set >
      <if test="provCd != null" >
        PROV_CD = #{provCd,jdbcType=VARCHAR},
      </if>
      <if test="cityLnm != null" >
        CITY_LNM = #{cityLnm,jdbcType=VARCHAR},
      </if>
      <if test="cityNm != null" >
        CITY_NM = #{cityNm,jdbcType=VARCHAR},
      </if>
      <if test="bossCity != null" >
        BOSS_CITY = #{bossCity,jdbcType=VARCHAR},
      </if>
      <if test="vgopCity != null" >
        VGOP_CITY = #{vgopCity,jdbcType=VARCHAR},
      </if>
      <if test="updDt != null" >
        UPD_DT = #{updDt,jdbcType=VARCHAR},
      </if>
      <if test="tmSmp != null" >
        TM_SMP = #{tmSmp,jdbcType=VARCHAR},
      </if>
      <if test="nodId != null" >
        NOD_ID = #{nodId,jdbcType=VARCHAR},
      </if>
    </set>
    where CITY_CD = #{cityCd,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.pas.domain.City" >
    update PAS_CCOP
    set PROV_CD = #{provCd,jdbcType=VARCHAR},
      CITY_LNM = #{cityLnm,jdbcType=VARCHAR},
      CITY_NM = #{cityNm,jdbcType=VARCHAR},
      BOSS_CITY = #{bossCity,jdbcType=VARCHAR},
      VGOP_CITY = #{vgopCity,jdbcType=VARCHAR},
      UPD_DT = #{updDt,jdbcType=VARCHAR},
      TM_SMP = #{tmSmp,jdbcType=VARCHAR},
      NOD_ID = #{nodId,jdbcType=VARCHAR}
    where CITY_CD = #{cityCd,jdbcType=VARCHAR}
  </update>

  <select id="selectByProvinceCode" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from PAS_CCOP
    where PROV_CD = #{provinceCode,jdbcType=VARCHAR}
  </select>
  
  <select id="queryCityCodeByNameAndProvinceCode" resultType="java.lang.String" >
    SELECT CITY_CD FROM PAS_CCOP WHERE 
   	 CITY_NM = #{cityName,jdbcType=VARCHAR} 
   	 and PROV_CD = #{provinceCode,jdbcType=VARCHAR}
  </select>	
  

</mapper>