<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.pas.pas.dao.ManagementDataMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.pas.domain.ManagementData" >
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="UNIQUE_ID1" property="uniqueId1" jdbcType="VARCHAR" />
    <result column="UNIQUE_ID2" property="uniqueId2" jdbcType="VARCHAR" />
    <result column="SERIAL_NUM" property="serialNum" jdbcType="VARCHAR" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
    <result column="OPERATOR" property="operator" jdbcType="DECIMAL" />
    <result column="SEND_STATUS" property="sendStatus" jdbcType="VARCHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="SEND_TIME" property="sendTime" jdbcType="TIMESTAMP" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, UNIQUE_ID1, UNIQUE_ID2, SERIAL_NUM, REMARK, OPERATOR, SEND_STATUS, CREATE_TIME, 
    SEND_TIME, UPDATE_TIME
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from PAS_MANAGEMENT_DATA
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from PAS_MANAGEMENT_DATA
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.pas.domain.ManagementData" >
    insert into PAS_MANAGEMENT_DATA (ID, UNIQUE_ID1, UNIQUE_ID2, 
      SERIAL_NUM, REMARK, OPERATOR, 
      SEND_STATUS, CREATE_TIME, SEND_TIME, 
      UPDATE_TIME)
    values (#{id,jdbcType=DECIMAL}, #{uniqueId1,jdbcType=VARCHAR}, #{uniqueId2,jdbcType=VARCHAR}, 
      #{serialNum,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{operator,jdbcType=DECIMAL}, 
      #{sendStatus,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{sendTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.pas.domain.ManagementData" >
    insert into PAS_MANAGEMENT_DATA
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="uniqueId1 != null" >
        UNIQUE_ID1,
      </if>
      <if test="uniqueId2 != null" >
        UNIQUE_ID2,
      </if>
      <if test="serialNum != null" >
        SERIAL_NUM,
      </if>
      <if test="remark != null" >
        REMARK,
      </if>
      <if test="operator != null" >
        OPERATOR,
      </if>
      <if test="sendStatus != null" >
        SEND_STATUS,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="sendTime != null" >
        SEND_TIME,
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="uniqueId1 != null" >
        #{uniqueId1,jdbcType=VARCHAR},
      </if>
      <if test="uniqueId2 != null" >
        #{uniqueId2,jdbcType=VARCHAR},
      </if>
      <if test="serialNum != null" >
        #{serialNum,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="operator != null" >
        #{operator,jdbcType=DECIMAL},
      </if>
      <if test="sendStatus != null" >
        #{sendStatus,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sendTime != null" >
        #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.pas.domain.ManagementData" >
    update PAS_MANAGEMENT_DATA
    <set >
      <if test="uniqueId1 != null" >
        UNIQUE_ID1 = #{uniqueId1,jdbcType=VARCHAR},
      </if>
      <if test="uniqueId2 != null" >
        UNIQUE_ID2 = #{uniqueId2,jdbcType=VARCHAR},
      </if>
      <if test="serialNum != null" >
        SERIAL_NUM = #{serialNum,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="operator != null" >
        OPERATOR = #{operator,jdbcType=DECIMAL},
      </if>
      <if test="sendStatus != null" >
        SEND_STATUS = #{sendStatus,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sendTime != null" >
        SEND_TIME = #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.pas.domain.ManagementData" >
    update PAS_MANAGEMENT_DATA
    set UNIQUE_ID1 = #{uniqueId1,jdbcType=VARCHAR},
      UNIQUE_ID2 = #{uniqueId2,jdbcType=VARCHAR},
      SERIAL_NUM = #{serialNum,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      OPERATOR = #{operator,jdbcType=DECIMAL},
      SEND_STATUS = #{sendStatus,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      SEND_TIME = #{sendTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=DECIMAL}
  </update>

  <select id="queryMDNextSeq" resultType="java.lang.Long">
    select seq_PAS_MANAGEMENT_DATA.nextval from dual
  </select>

  <select id="pageQueryManageData" parameterType="java.util.Map" resultType="com.epaylinks.efps.pas.pas.controller.response.ManagementDataResponse">
    select *
    from (
      select A.*,rownum RN
      from (
        select
        t.id,
        case
          when t.unique_id1 is null and t.unique_id2 is null then '2'
          when t.unique_id1 is not null and nvl(ur1.deleted,'0') != '1' then '1'
          when t.unique_id2 is not null and nvl(ur2.deleted,'0') != '1' then '1'
        else '2'
        end as detail,
        t.SERIAL_NUM serialNum,
        t.REMARK remark,
        t.OPERATOR operator,
        u.real_name operatorName,
        t.SEND_STATUS sendStatus,
        to_char(t.CREATE_TIME,'yyyy-MM-dd') createTime
        from PAS_MANAGEMENT_DATA t
        left join pas_user u on t.OPERATOR = u.user_id
        left join fs_upload_record ur1 on ur1.unique_id = t.unique_id1
        left join fs_upload_record ur2 on ur2.unique_id = t.unique_id2
        where 1=1
        <if test="startCreateTime != null and startCreateTime != ''">
          <![CDATA[ and to_char(t.CREATE_TIME,'yyyyMMdd') >= #{startCreateTime,jdbcType=VARCHAR}]]>
        </if>
        <if test="endCreateTime != null and endCreateTime != ''">
          <![CDATA[ and to_char(t.CREATE_TIME,'yyyyMMdd') <= #{endCreateTime,jdbcType=VARCHAR}]]>
        </if>
        order by t.id desc
      )A where rownum &lt;= #{endRowNo}
    ) where RN &gt;= #{beginRowNo}
  </select>

  <select id="countManageData" parameterType="java.util.Map" resultType="java.lang.Integer">
    select count(*)
    from (
      select
      <include refid="Base_Column_List" />
      from PAS_MANAGEMENT_DATA t
      where 1=1
      <if test="startCreateTime != null and startCreateTime != ''">
        <![CDATA[ and to_char(t.CREATE_TIME,'yyyyMMdd') >= #{startCreateTime,jdbcType=VARCHAR}]]>
      </if>
      <if test="endCreateTime != null and endCreateTime != ''">
        <![CDATA[ and to_char(t.CREATE_TIME,'yyyyMMdd') <= #{endCreateTime,jdbcType=VARCHAR}]]>
      </if>
    )
  </select>

  <select id="selectByManagementData" resultType="com.epaylinks.efps.pas.pas.domain.ManagementData" parameterType="java.lang.Long" >
    select
      t.id,
      case when u.DELETED = '1' then ''
           else t.unique_id1
        end as uniqueId1,
      case when r.DELETED = '1' then ''
           else t.unique_id2
        end as uniqueId2,
      t.serial_num serialNum,
      t.remark remark,
      t.operator operator,
      t.send_status sendStatus,
      t.create_time createTime,
      t.update_time updateTime,
      t.send_time sendTime
    from PAS_MANAGEMENT_DATA t
     left join fs_upload_record u on u.UNIQUE_ID = t.UNIQUE_ID1
     left join fs_upload_record r on r.UNIQUE_ID = t.UNIQUE_ID2
    where t.ID = #{id,jdbcType=DECIMAL}
  </select>
</mapper>