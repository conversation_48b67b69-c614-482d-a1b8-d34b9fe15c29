<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.pas.pas.dao.UnionQuotaPayMentsMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.pas.domain.UnionQuotaPayMents" >
    <id column="TRANSACTION_NO" property="transactionNo" jdbcType="VARCHAR" />
    <result column="INSTITUTION_ID" property="institutionId" jdbcType="DECIMAL" />
    <result column="ACQ_INS_CODE" property="acqInsCode" jdbcType="VARCHAR" />
    <result column="INS_SEQ" property="insSeq" jdbcType="VARCHAR" />
    <result column="PAYER_ACCT_NO" property="payerAcctNo" jdbcType="VARCHAR" />
    <result column="PAYER_ACCT_NAME" property="payerAcctName" jdbcType="VARCHAR" />
    <result column="CURRENCY_CODE" property="currencyCode" jdbcType="VARCHAR" />
    <result column="AMOUNT" property="amount" jdbcType="DECIMAL" />
    <result column="STATE" property="state" jdbcType="VARCHAR" />
    <result column="CHANNEL_RESP_CODE" property="channelRespCode" jdbcType="VARCHAR" />
    <result column="CHANNEL_RESP_MSG" property="channelRespMsg" jdbcType="VARCHAR" />
    <result column="CHANNEL_EXTEND" property="channelExtend" jdbcType="VARCHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="SRC_TYPE" property="srcType" jdbcType="VARCHAR" />
    <result column="HUIKUAN_DATE" property="huikuanDate" jdbcType="VARCHAR" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
    <result column="USER_ID" property="userId" jdbcType="VARCHAR" />
    <result column="CFD_PURPOSE" property="cfdPurpose" jdbcType="CHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    TRANSACTION_NO, INSTITUTION_ID, ACQ_INS_CODE, INS_SEQ, PAYER_ACCT_NO, PAYER_ACCT_NAME, 
    CURRENCY_CODE, AMOUNT, STATE, CHANNEL_RESP_CODE, CHANNEL_RESP_MSG, CHANNEL_EXTEND, 
    CREATE_TIME, UPDATE_TIME, SRC_TYPE, HUIKUAN_DATE, REMARK, USER_ID, CFD_PURPOSE
  </sql>

  <select id="queryList" resultMap="BaseResultMap" parameterType="java.util.Map">
    select
    <include refid="Base_Column_List" />
    from (
    select A.*, rownum RN
    from (
    select *
    from CLR_HUIKUAN_RECORD
    <where>
      <if test="beginDate != null">
        CREATE_TIME <![CDATA[ >= ]]>  #{beginDate,jdbcType=TIMESTAMP}
      </if>
      <if test="endDate != null">
        AND CREATE_TIME <![CDATA[ <= ]]>  #{endDate,jdbcType=TIMESTAMP}
      </if>
      <if test="payerAcctNo != null and payerAcctNo != '' ">
        AND PAYER_ACCT_NO = #{payerAcctNo,jdbcType=VARCHAR}
      </if>
      <if test="transactionNo != null and transactionNo != ''">
        AND TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
      </if>
      <if test="state != null ">
        AND STATE = #{state,jdbcType=DECIMAL}
      </if>
    </where>
    order by CREATE_TIME desc
    ) A
    where rownum &lt;= #{endRowNo,jdbcType=DECIMAL}
    )
    where RN &gt;= #{beginRowNo,jdbcType=DECIMAL}
  </select>
  <!--查询总数量-->
  <select id="countQueryList" resultType="java.lang.Integer" parameterType="java.util.Map">
    select count(*)
    from CLR_HUIKUAN_RECORD
    <where>
      <if test="beginDate != null">
        CREATE_TIME <![CDATA[ >= ]]>  #{beginDate,jdbcType=TIMESTAMP}
      </if>
      <if test="endDate != null">
        AND CREATE_TIME <![CDATA[ <= ]]>  #{endDate,jdbcType=TIMESTAMP}
      </if>
      <if test="payerAcctNo != null and payerAcctNo != '' ">
        AND PAYER_ACCT_NO = #{payerAcctNo,jdbcType=VARCHAR}
      </if>
      <if test="transactionNo != null and transactionNo != ''">
        AND TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
      </if>
      <if test="state != null ">
        AND STATE = #{state,jdbcType=DECIMAL}
      </if>
    </where>
  </select>

  <select id="notPageQueryList" resultMap="BaseResultMap" parameterType="java.util.Map">
    select
    <include refid="Base_Column_List" />
    from CLR_HUIKUAN_RECORD
    <where>
      <if test="beginDate != null">
        CREATE_TIME <![CDATA[ >= ]]> #{beginDate,jdbcType=TIMESTAMP}
      </if>
      <if test="endDate != null">
        AND CREATE_TIME <![CDATA[ <= ]]> #{endDate,jdbcType=TIMESTAMP}
      </if>
      <if test="payerAcctNo != null and payerAcctNo != '' ">
        AND PAYER_ACCT_NO = #{payerAcctNo,jdbcType=VARCHAR}
      </if>
      <if test="transactionNo != null and transactionNo != ''">
        AND TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
      </if>
      <if test="state != null ">
        AND STATE = #{state,jdbcType=DECIMAL}
      </if>
    </where>
    order by CREATE_TIME desc
  </select>

</mapper>