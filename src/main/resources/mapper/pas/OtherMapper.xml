<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.pas.pas.dao.OtherMapper" >
    <select id="judgeCheckRole" parameterType="java.lang.Long" resultType="java.lang.Integer">
        select count(*) from cust_role_user
        where user_id = #{userId,jdbcType=DECIMAL}
          and role_id = 100008
    </select>
</mapper>