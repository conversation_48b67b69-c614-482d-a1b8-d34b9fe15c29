<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.pas.pas.dao.OpLogReportMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.pas.domain.OpLogReport" >
    <id column="REPORT_ID" property="reportId" jdbcType="DECIMAL" />
    <result column="USER_NAME" property="userName" jdbcType="VARCHAR" />
    <result column="REAL_NAME" property="realName" jdbcType="VARCHAR" />
    <result column="OP_MODULE" property="opModule" jdbcType="VARCHAR" />
    <result column="OP_NUM" property="opNum" jdbcType="DECIMAL" />
    <result column="OP_TIME" property="opTime" jdbcType="VARCHAR" />
    <result column="CHECK_STATE" property="checkState" jdbcType="VARCHAR" />
    <result column="CHECK_MSG" property="checkMsg" jdbcType="VARCHAR" />
    <result column="AUDIT_MSG" property="auditMsg" jdbcType="VARCHAR" />
    <result column="AUDIT_NAME" property="auditName" jdbcType="VARCHAR" />
    <result column="AUDIT_TIME" property="auditTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    REPORT_ID, USER_NAME, REAL_NAME, OP_MODULE, OP_NUM, OP_TIME, CHECK_STATE, CHECK_MSG, 
    AUDIT_MSG, AUDIT_NAME, AUDIT_TIME
  </sql>
  <!-- 获取主键ID -->
  <select id="selectIdFromSeq" resultType="java.lang.Long" >
    select SEQ_PAS_OPLOG_REPORT.nextval from dual
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from PAS_OPLOG_REPORT
    where REPORT_ID = #{reportId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from PAS_OPLOG_REPORT
    where REPORT_ID = #{reportId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.pas.domain.OpLogReport" >
    insert into PAS_OPLOG_REPORT (REPORT_ID, USER_NAME, REAL_NAME, 
      OP_MODULE, OP_NUM, OP_TIME, 
      CHECK_STATE, CHECK_MSG, AUDIT_MSG, 
      AUDIT_NAME, AUDIT_TIME)
    values (#{reportId,jdbcType=DECIMAL}, #{userName,jdbcType=VARCHAR}, #{realName,jdbcType=VARCHAR}, 
      #{opModule,jdbcType=VARCHAR}, #{opNum,jdbcType=DECIMAL}, #{opTime,jdbcType=VARCHAR}, 
      #{checkState,jdbcType=VARCHAR}, #{checkMsg,jdbcType=VARCHAR}, #{auditMsg,jdbcType=VARCHAR}, 
      #{auditName,jdbcType=VARCHAR}, #{auditTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.pas.domain.OpLogReport" >
    insert into PAS_OPLOG_REPORT
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="reportId != null" >
        REPORT_ID,
      </if>
      <if test="userName != null" >
        USER_NAME,
      </if>
      <if test="realName != null" >
        REAL_NAME,
      </if>
      <if test="opModule != null" >
        OP_MODULE,
      </if>
      <if test="opNum != null" >
        OP_NUM,
      </if>
      <if test="opTime != null" >
        OP_TIME,
      </if>
      <if test="checkState != null" >
        CHECK_STATE,
      </if>
      <if test="checkMsg != null" >
        CHECK_MSG,
      </if>
      <if test="auditMsg != null" >
        AUDIT_MSG,
      </if>
      <if test="auditName != null" >
        AUDIT_NAME,
      </if>
      <if test="auditTime != null" >
        AUDIT_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="reportId != null" >
        #{reportId,jdbcType=DECIMAL},
      </if>
      <if test="userName != null" >
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="realName != null" >
        #{realName,jdbcType=VARCHAR},
      </if>
      <if test="opModule != null" >
        #{opModule,jdbcType=VARCHAR},
      </if>
      <if test="opNum != null" >
        #{opNum,jdbcType=DECIMAL},
      </if>
      <if test="opTime != null" >
        #{opTime,jdbcType=VARCHAR},
      </if>
      <if test="checkState != null" >
        #{checkState,jdbcType=VARCHAR},
      </if>
      <if test="checkMsg != null" >
        #{checkMsg,jdbcType=VARCHAR},
      </if>
      <if test="auditMsg != null" >
        #{auditMsg,jdbcType=VARCHAR},
      </if>
      <if test="auditName != null" >
        #{auditName,jdbcType=VARCHAR},
      </if>
      <if test="auditTime != null" >
        #{auditTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.pas.domain.OpLogReport" >
    update PAS_OPLOG_REPORT
    <set >
      <if test="userName != null" >
        USER_NAME = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="realName != null" >
        REAL_NAME = #{realName,jdbcType=VARCHAR},
      </if>
      <if test="opModule != null" >
        OP_MODULE = #{opModule,jdbcType=VARCHAR},
      </if>
      <if test="opNum != null" >
        OP_NUM = #{opNum,jdbcType=DECIMAL},
      </if>
      <if test="opTime != null" >
        OP_TIME = #{opTime,jdbcType=VARCHAR},
      </if>
      <if test="checkState != null" >
        CHECK_STATE = #{checkState,jdbcType=VARCHAR},
      </if>
      <if test="checkMsg != null" >
        CHECK_MSG = #{checkMsg,jdbcType=VARCHAR},
      </if>
      <if test="auditMsg != null" >
        AUDIT_MSG = #{auditMsg,jdbcType=VARCHAR},
      </if>
      <if test="auditName != null" >
        AUDIT_NAME = #{auditName,jdbcType=VARCHAR},
      </if>
      <if test="auditTime != null" >
        AUDIT_TIME = #{auditTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where REPORT_ID = #{reportId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.pas.domain.OpLogReport" >
    update PAS_OPLOG_REPORT
    set USER_NAME = #{userName,jdbcType=VARCHAR},
      REAL_NAME = #{realName,jdbcType=VARCHAR},
      OP_MODULE = #{opModule,jdbcType=VARCHAR},
      OP_NUM = #{opNum,jdbcType=DECIMAL},
      OP_TIME = #{opTime,jdbcType=VARCHAR},
      CHECK_STATE = #{checkState,jdbcType=VARCHAR},
      CHECK_MSG = #{checkMsg,jdbcType=VARCHAR},
      AUDIT_MSG = #{auditMsg,jdbcType=VARCHAR},
      AUDIT_NAME = #{auditName,jdbcType=VARCHAR},
      AUDIT_TIME = #{auditTime,jdbcType=TIMESTAMP}
    where REPORT_ID = #{reportId,jdbcType=DECIMAL}
  </update>
  <!-- 按日期查询操作日志统计信息 -->
  <select id="queryOperationByDate" resultType="com.epaylinks.efps.pas.pas.domain.OperationLogReport" parameterType="java.lang.String">
    select t.user_name userName, t.real_name realName, t.op_module opModule, to_char(op_time,'yyyy-mm-dd') operDate, count(*) operCount
    from operation_log t
    where to_char(t.op_time,'yyyy-mm-dd') = #{operDate,jdbcType=VARCHAR}
    group by t.user_name, t.real_name, t.op_module, to_char(t.op_time,'yyyy-mm-dd')
  </select>
  <!-- 按日期查询某天是否有审计记录-->
  <select id="queryOpAuditExist" resultType="boolean">
    select count(*)
    from PAS_OPLOG_REPORT
    where OP_TIME = #{operDate,jdbcType=VARCHAR}
  </select>
  <!-- 根据模块查询对应权限-->
  <select id="selectPermByModule" resultType="java.lang.String" parameterType="java.lang.String" >
    select PERM_IDS
    from PAS_OP_MODULE_PERM
    where OP_MODULE = #{opModule,jdbcType=VARCHAR}
  </select>

  <select id="countOperLogReport" resultType="java.lang.Integer"
          parameterType="java.util.Map">
    select count(*)
    from PAS_OPLOG_REPORT
    <where>
      <include refid="reportQueryCondition"/>
    </where>
  </select>
  <select id="pageOperLogReport" resultMap="BaseResultMap"
          parameterType="java.util.Map">
    select
    <include refid="Base_Column_List" />
    from (
    select A.*, rownum RN
    from (
    select * from PAS_OPLOG_REPORT
    <where>
      <include refid="reportQueryCondition"/>
    </where>
    order by OP_TIME desc
    ) A
    where rownum <![CDATA[ <= ]]> #{endNum,jdbcType=DECIMAL}
    )
    where RN <![CDATA[ >= ]]>
    #{startNum,jdbcType=DECIMAL}
  </select>
  <sql id = "reportQueryCondition">
    <if test="startTime != null and startTime != ''">
      AND op_time <![CDATA[ >= ]]>  #{startTime,jdbcType=VARCHAR}
    </if>
    <if test="endTime != null and endTime != ''">
      AND op_time <![CDATA[ <= ]]> #{endTime,jdbcType=VARCHAR}
    </if>
    <if test="userName != null and userName != ''">
      AND user_name LIKE '%'||#{userName,jdbcType=VARCHAR}||'%'
    </if>
    <if test="realName != null and realName != ''">
      AND real_name LIKE '%'||#{realName,jdbcType=VARCHAR}||'%'
    </if>
    <if test="checkState != null and checkState != ''">
      AND check_state = #{checkState,jdbcType=VARCHAR}
    </if>
  </sql>
</mapper>