<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.pas.pas.dao.AliBusinessCategoryMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.pas.domain.AliBusinessCategory" >
    <id column="MCC" property="mcc" jdbcType="VARCHAR" />
    <result column="LEVEL1_CODE" property="level1Code" jdbcType="VARCHAR" />
    <result column="LEVEL1_NAME" property="level1Name" jdbcType="VARCHAR" />
    <result column="LEVEL2_CODE" property="level2Code" jdbcType="VARCHAR" />
    <result column="LEVEL2_NAME" property="level2Name" jdbcType="VARCHAR" />
    <result column="LEVEL3_CODE" property="level3Code" jdbcType="VARCHAR" />
    <result column="LEVEL3_NAME" property="level3Name" jdbcType="VARCHAR" />
    <result column="UNION_MCC" property="unionMcc" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    MCC, LEVEL1_CODE, LEVEL1_NAME, LEVEL2_CODE, LEVEL2_NAME, LEVEL3_CODE, LEVEL3_NAME, UNION_MCC
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select
    <include refid="Base_Column_List" />
    from PAS_ALI_BUSINESS_CATEGORY
    where MCC = #{mcc,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from PAS_ALI_BUSINESS_CATEGORY
    where MCC = #{mcc,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.pas.domain.AliBusinessCategory" >
    insert into PAS_ALI_BUSINESS_CATEGORY (MCC, LEVEL1_CODE, LEVEL1_NAME, 
      LEVEL2_CODE, LEVEL2_NAME, LEVEL3_CODE, 
      LEVEL3_NAME, UNION_MCC)
    values (#{mcc,jdbcType=VARCHAR}, #{level1Code,jdbcType=VARCHAR}, #{level1Name,jdbcType=VARCHAR}, 
      #{level2Code,jdbcType=VARCHAR}, #{level2Name,jdbcType=VARCHAR}, #{level3Code,jdbcType=VARCHAR}, 
      #{level3Name,jdbcType=VARCHAR}, #{unionMcc,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.pas.domain.AliBusinessCategory" >
    insert into PAS_ALI_BUSINESS_CATEGORY
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="mcc != null" >
        MCC,
      </if>
      <if test="level1Code != null" >
        LEVEL1_CODE,
      </if>
      <if test="level1Name != null" >
        LEVEL1_NAME,
      </if>
      <if test="level2Code != null" >
        LEVEL2_CODE,
      </if>
      <if test="level2Name != null" >
        LEVEL2_NAME,
      </if>
      <if test="level3Code != null" >
        LEVEL3_CODE,
      </if>
      <if test="level3Name != null" >
        LEVEL3_NAME,
      </if>
      <if test="unionMcc != null" >
        UNION_MCC,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="mcc != null" >
        #{mcc,jdbcType=VARCHAR},
      </if>
      <if test="level1Code != null" >
        #{level1Code,jdbcType=VARCHAR},
      </if>
      <if test="level1Name != null" >
        #{level1Name,jdbcType=VARCHAR},
      </if>
      <if test="level2Code != null" >
        #{level2Code,jdbcType=VARCHAR},
      </if>
      <if test="level2Name != null" >
        #{level2Name,jdbcType=VARCHAR},
      </if>
      <if test="level3Code != null" >
        #{level3Code,jdbcType=VARCHAR},
      </if>
      <if test="level3Name != null" >
        #{level3Name,jdbcType=VARCHAR},
      </if>
      <if test="unionMcc != null" >
        #{unionMcc,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.pas.domain.AliBusinessCategory" >
    update PAS_ALI_BUSINESS_CATEGORY
    <set >
      <if test="level1Code != null" >
        LEVEL1_CODE = #{level1Code,jdbcType=VARCHAR},
      </if>
      <if test="level1Name != null" >
        LEVEL1_NAME = #{level1Name,jdbcType=VARCHAR},
      </if>
      <if test="level2Code != null" >
        LEVEL2_CODE = #{level2Code,jdbcType=VARCHAR},
      </if>
      <if test="level2Name != null" >
        LEVEL2_NAME = #{level2Name,jdbcType=VARCHAR},
      </if>
      <if test="level3Code != null" >
        LEVEL3_CODE = #{level3Code,jdbcType=VARCHAR},
      </if>
      <if test="level3Name != null" >
        LEVEL3_NAME = #{level3Name,jdbcType=VARCHAR},
      </if>
      <if test="unionMcc != null" >
        UNION_MCC = #{unionMcc,jdbcType=VARCHAR},
      </if>
    </set>
    where MCC = #{mcc,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.pas.domain.AliBusinessCategory" >
    update PAS_ALI_BUSINESS_CATEGORY
    set LEVEL1_CODE = #{level1Code,jdbcType=VARCHAR},
      LEVEL1_NAME = #{level1Name,jdbcType=VARCHAR},
      LEVEL2_CODE = #{level2Code,jdbcType=VARCHAR},
      LEVEL2_NAME = #{level2Name,jdbcType=VARCHAR},
      LEVEL3_CODE = #{level3Code,jdbcType=VARCHAR},
      LEVEL3_NAME = #{level3Name,jdbcType=VARCHAR},
      UNION_MCC = #{unionMcc,jdbcType=VARCHAR}
    where MCC = #{mcc,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="java.util.List">
    INSERT ALL
    <foreach collection="records" item="item" index="index">
      into PAS_ALI_BUSINESS_CATEGORY (MCC, LEVEL1_CODE, LEVEL1_NAME, LEVEL2_CODE, LEVEL2_NAME, LEVEL3_CODE, LEVEL3_NAME)
      values (#{item.mcc,jdbcType=VARCHAR}, #{item.level1Code,jdbcType=VARCHAR}, #{item.level1Name,jdbcType=VARCHAR},
      #{item.level2Code,jdbcType=VARCHAR}, #{item.level2Name,jdbcType=VARCHAR}, #{item.level3Code,jdbcType=VARCHAR},
      #{item.level3Name,jdbcType=VARCHAR})
    </foreach>
    SELECT * FROM DUAL
  </insert>
  <select id="selectLevel1s" resultType="com.epaylinks.efps.pas.pas.vo.CodeAndName">
    select distinct LEVEL1_CODE as code, LEVEL1_NAME as name
    from PAS_ALI_BUSINESS_CATEGORY
  </select>
  <select id="selectLevel2s" resultType="com.epaylinks.efps.pas.pas.vo.CodeAndName">
    select distinct LEVEL2_CODE as code, LEVEL2_NAME as name
    from PAS_ALI_BUSINESS_CATEGORY
    where LEVEL1_CODE = #{level1,jdbcType=VARCHAR}
  </select>
  <select id="selectLevel3s" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from PAS_ALI_BUSINESS_CATEGORY
    where LEVEL1_CODE = #{level1,jdbcType=VARCHAR} and LEVEL2_CODE = #{level2,jdbcType=VARCHAR}
  </select>
  
  <select id="selectByUnionMcc" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from PAS_ALI_BUSINESS_CATEGORY
    where union_mcc like '%' || #{unionMcc,jdbcType=VARCHAR} || '%'
  </select>

    <select id="getAliBusinessCategoryByWord" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from PAS_ALI_BUSINESS_CATEGORY
      where LEVEL3_CODE like '%' || #{keyword,jdbcType=VARCHAR} || '%'
        or LEVEL3_NAME like '%' || #{keyword,jdbcType=VARCHAR} || '%'
    </select>
</mapper>