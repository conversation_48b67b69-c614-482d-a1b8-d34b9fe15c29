<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.pas.pas.dao.ComplainTradeMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.pas.domain.ComplainTrade" >
    <id column="CT_ID" property="ctId" jdbcType="DECIMAL" />
    <result column="CHANNEL_TYPE" property="channelType" jdbcType="VARCHAR" />
    <result column="RISK_TIME" property="riskTime" jdbcType="TIMESTAMP" />
    <result column="TRADE_TIME" property="tradeTime" jdbcType="TIMESTAMP" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="AMOUNT" property="amount" jdbcType="VARCHAR" />
    <result column="RISK_DESC" property="riskDesc" jdbcType="VARCHAR" />
    <result column="COMPLAIN_CONTENT" property="complainContent" jdbcType="VARCHAR" />
    <result column="USER_MOBILE" property="userMobile" jdbcType="VARCHAR" />
    <result column="CHANNEL_MCH_ID" property="channelMchId" jdbcType="VARCHAR" />
    <result column="CUSTOMER_CODE" property="customerCode" jdbcType="VARCHAR" />
    <result column="CUST_NAME" property="custName" jdbcType="VARCHAR" />
    <result column="AGENT_CUSTOMER_CODE" property="agentCustomerCode" jdbcType="VARCHAR" />
    <result column="AGENT_CUST_NAME" property="agentCustName" jdbcType="VARCHAR" />
    <result column="PLAT_CUSTOMER_CODE" property="platCustomerCode" jdbcType="VARCHAR" />
    <result column="PLAT_CUST_NAME" property="platCustName" jdbcType="VARCHAR" />
    <result column="BUSINESS_MAN" property="businessMan" jdbcType="VARCHAR" />
    <result column="BATCH_NO" property="batchNo" jdbcType="VARCHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    CT_ID, CHANNEL_TYPE, RISK_TIME, TRADE_TIME, ORDER_NO, AMOUNT, RISK_DESC, COMPLAIN_CONTENT, USER_MOBILE,
    CHANNEL_MCH_ID, CUSTOMER_CODE, CUST_NAME, AGENT_CUSTOMER_CODE, AGENT_CUST_NAME, PLAT_CUSTOMER_CODE, 
    PLAT_CUST_NAME, BUSINESS_MAN, BATCH_NO,CREATE_TIME
  </sql>

  <!-- 获取主键ID -->
  <select id="selectIdFromSeq" resultType="java.lang.Long" >
    select SEQ_PAS_COMPLAIN_TRADE.nextval from dual
  </select>

  <select id="isComplainExist" resultType="boolean">
    select count(*)
    from PAS_COMPLAIN_TRADE
    where ORDER_NO = #{orderNo,jdbcType=VARCHAR} and AMOUNT = #{amount,jdbcType=VARCHAR}
    and COMPLAIN_CONTENT = #{complainContent,jdbcType=VARCHAR}
  </select>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from PAS_COMPLAIN_TRADE
    where CT_ID = #{ctId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from PAS_COMPLAIN_TRADE
    where CT_ID = #{ctId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.pas.domain.ComplainTrade" >
    insert into PAS_COMPLAIN_TRADE (CT_ID, CHANNEL_TYPE, RISK_TIME, 
      TRADE_TIME, ORDER_NO, AMOUNT, RISK_DESC,
      COMPLAIN_CONTENT, USER_MOBILE, CHANNEL_MCH_ID, 
      CUSTOMER_CODE, CUST_NAME, AGENT_CUSTOMER_CODE, 
      AGENT_CUST_NAME, PLAT_CUSTOMER_CODE, PLAT_CUST_NAME, 
      BUSINESS_MAN, BATCH_NO,CREATE_TIME)
    values (#{ctId,jdbcType=DECIMAL}, #{channelType,jdbcType=VARCHAR}, #{riskTime,jdbcType=TIMESTAMP}, 
      #{tradeTime,jdbcType=TIMESTAMP}, #{orderNo,jdbcType=VARCHAR}, #{amount,jdbcType=VARCHAR}, #{riskDesc,jdbcType=VARCHAR},
      #{complainContent,jdbcType=VARCHAR}, #{userMobile,jdbcType=VARCHAR}, #{channelMchId,jdbcType=VARCHAR}, 
      #{customerCode,jdbcType=VARCHAR}, #{custName,jdbcType=VARCHAR}, #{agentCustomerCode,jdbcType=VARCHAR}, 
      #{agentCustName,jdbcType=VARCHAR}, #{platCustomerCode,jdbcType=VARCHAR}, #{platCustName,jdbcType=VARCHAR}, 
      #{businessMan,jdbcType=VARCHAR}, #{batchNo,jdbcType=VARCHAR},#{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.pas.domain.ComplainTrade" >
    insert into PAS_COMPLAIN_TRADE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="ctId != null" >
        CT_ID,
      </if>
      <if test="channelType != null" >
        CHANNEL_TYPE,
      </if>
      <if test="riskTime != null" >
        RISK_TIME,
      </if>
      <if test="tradeTime != null" >
        TRADE_TIME,
      </if>
      <if test="orderNo != null" >
        ORDER_NO,
      </if>
      <if test="amount != null" >
        AMOUNT,
      </if>
      <if test="riskDesc != null" >
        RISK_DESC,
      </if>
      <if test="complainContent != null" >
        COMPLAIN_CONTENT,
      </if>
      <if test="userMobile != null" >
        USER_MOBILE,
      </if>
      <if test="channelMchId != null" >
        CHANNEL_MCH_ID,
      </if>
      <if test="customerCode != null" >
        CUSTOMER_CODE,
      </if>
      <if test="custName != null" >
        CUST_NAME,
      </if>
      <if test="agentCustomerCode != null" >
        AGENT_CUSTOMER_CODE,
      </if>
      <if test="agentCustName != null" >
        AGENT_CUST_NAME,
      </if>
      <if test="platCustomerCode != null" >
        PLAT_CUSTOMER_CODE,
      </if>
      <if test="platCustName != null" >
        PLAT_CUST_NAME,
      </if>
      <if test="businessMan != null" >
        BUSINESS_MAN,
      </if>
      <if test="batchNo != null" >
        BATCH_NO,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="ctId != null" >
        #{ctId,jdbcType=DECIMAL},
      </if>
      <if test="channelType != null" >
        #{channelType,jdbcType=VARCHAR},
      </if>
      <if test="riskTime != null" >
        #{riskTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tradeTime != null" >
        #{tradeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="amount != null" >
        #{amount,jdbcType=VARCHAR},
      </if>
      <if test="riskDesc != null" >
        #{riskDesc,jdbcType=VARCHAR},
      </if>
      <if test="complainContent != null" >
        #{complainContent,jdbcType=VARCHAR},
      </if>
      <if test="userMobile != null" >
        #{userMobile,jdbcType=VARCHAR},
      </if>
      <if test="channelMchId != null" >
        #{channelMchId,jdbcType=VARCHAR},
      </if>
      <if test="customerCode != null" >
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="custName != null" >
        #{custName,jdbcType=VARCHAR},
      </if>
      <if test="agentCustomerCode != null" >
        #{agentCustomerCode,jdbcType=VARCHAR},
      </if>
      <if test="agentCustName != null" >
        #{agentCustName,jdbcType=VARCHAR},
      </if>
      <if test="platCustomerCode != null" >
        #{platCustomerCode,jdbcType=VARCHAR},
      </if>
      <if test="platCustName != null" >
        #{platCustName,jdbcType=VARCHAR},
      </if>
      <if test="businessMan != null" >
        #{businessMan,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null" >
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.pas.domain.ComplainTrade" >
    update PAS_COMPLAIN_TRADE
    <set >
      <if test="channelType != null" >
        CHANNEL_TYPE = #{channelType,jdbcType=VARCHAR},
      </if>
      <if test="riskTime != null" >
        RISK_TIME = #{riskTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tradeTime != null" >
        TRADE_TIME = #{tradeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderNo != null" >
        ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="amount != null" >
        AMOUNT = #{amount,jdbcType=VARCHAR},
      </if>
      <if test="riskDesc != null" >
        RISK_DESC = #{riskDesc,jdbcType=VARCHAR},
      </if>
      <if test="complainContent != null" >
        COMPLAIN_CONTENT = #{complainContent,jdbcType=VARCHAR},
      </if>
      <if test="userMobile != null" >
        USER_MOBILE = #{userMobile,jdbcType=VARCHAR},
      </if>
      <if test="channelMchId != null" >
        CHANNEL_MCH_ID = #{channelMchId,jdbcType=VARCHAR},
      </if>
      <if test="customerCode != null" >
        CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="custName != null" >
        CUST_NAME = #{custName,jdbcType=VARCHAR},
      </if>
      <if test="agentCustomerCode != null" >
        AGENT_CUSTOMER_CODE = #{agentCustomerCode,jdbcType=VARCHAR},
      </if>
      <if test="agentCustName != null" >
        AGENT_CUST_NAME = #{agentCustName,jdbcType=VARCHAR},
      </if>
      <if test="platCustomerCode != null" >
        PLAT_CUSTOMER_CODE = #{platCustomerCode,jdbcType=VARCHAR},
      </if>
      <if test="platCustName != null" >
        PLAT_CUST_NAME = #{platCustName,jdbcType=VARCHAR},
      </if>
      <if test="businessMan != null" >
        BUSINESS_MAN = #{businessMan,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null" >
        BATCH_NO = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where CT_ID = #{ctId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.pas.domain.ComplainTrade" >
    update PAS_COMPLAIN_TRADE
    set CHANNEL_TYPE = #{channelType,jdbcType=VARCHAR},
      RISK_TIME = #{riskTime,jdbcType=TIMESTAMP},
      TRADE_TIME = #{tradeTime,jdbcType=TIMESTAMP},
      ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      AMOUNT = #{amount,jdbcType=VARCHAR},
      RISK_DESC = #{riskDesc,jdbcType=VARCHAR},
      COMPLAIN_CONTENT = #{complainContent,jdbcType=VARCHAR},
      USER_MOBILE = #{userMobile,jdbcType=VARCHAR},
      CHANNEL_MCH_ID = #{channelMchId,jdbcType=VARCHAR},
      CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      CUST_NAME = #{custName,jdbcType=VARCHAR},
      AGENT_CUSTOMER_CODE = #{agentCustomerCode,jdbcType=VARCHAR},
      AGENT_CUST_NAME = #{agentCustName,jdbcType=VARCHAR},
      PLAT_CUSTOMER_CODE = #{platCustomerCode,jdbcType=VARCHAR},
      PLAT_CUST_NAME = #{platCustName,jdbcType=VARCHAR},
      BUSINESS_MAN = #{businessMan,jdbcType=VARCHAR},
      BATCH_NO = #{batchNo,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
    where CT_ID = #{ctId,jdbcType=DECIMAL}
  </update>
  <select id="selectCount" resultType="java.lang.Integer"
          parameterType="java.util.Map">
    select count(*)
    from PAS_COMPLAIN_TRADE t
    <where>
      <include refid="tradeQueryCondition"/>
    </where>
  </select>
  <select id="selectByPage" resultMap="BaseResultMap"
          parameterType="java.util.Map">
    select
    <include refid="Base_Column_List" />
    from (
    select A.*, rownum RN
    from (
    select * from PAS_COMPLAIN_TRADE t
    <where>
      <include refid="tradeQueryCondition"/>
    </where>
    order by CT_ID desc
    ) A
    where rownum &lt;= #{endRowNo,jdbcType=DECIMAL}
    )
    where RN &gt;=
    #{beginRowNo,jdbcType=DECIMAL}
  </select>
  <sql id = "tradeQueryCondition">

    <if test="dtStartTime != null and dtStartTime != ''">
      and RISK_TIME <![CDATA[ >= ]]> to_date(#{dtStartTime},'yyyyMMddhh24miss')
    </if>
    <if test="dtEndTime != null and dtEndTime != ''">
      and RISK_TIME <![CDATA[ < ]]> to_date(#{dtEndTime},'yyyyMMddhh24miss')
    </if>
    <if test="startCreateTime != null and startCreateTime != ''">
      and CREATE_TIME <![CDATA[ >= ]]> to_date(#{startCreateTime},'yyyyMMddhh24miss')
    </if>
    <if test="endCreateTime != null and endCreateTime != ''">
      and CREATE_TIME <![CDATA[ < ]]> to_date(#{endCreateTime},'yyyyMMddhh24miss')
    </if>
    <if test="customerCode != null and customerCode != ''">
      and CUSTOMER_CODE = #{customerCode,jdbcType = VARCHAR}
    </if>
    <if test="channelMchId != null and channelMchId != ''">
      and CHANNEL_MCH_ID = #{channelMchId,jdbcType = VARCHAR}
    </if>
    <if test="platCustomerCode != null and platCustomerCode != ''">
      and PLAT_CUSTOMER_CODE = #{platCustomerCode,jdbcType = VARCHAR}
    </if>
    <if test="agentCustomerCode != null and agentCustomerCode != ''">
      and AGENT_CUSTOMER_CODE = #{agentCustomerCode,jdbcType = VARCHAR}
    </if>
    <if test="businessMan != null and businessMan != ''">
      and BUSINESS_MAN = #{businessMan,jdbcType = VARCHAR}
    </if>
    <if test="orderNo != null and orderNo != ''">
      and ORDER_NO = #{orderNo,jdbcType = VARCHAR}
    </if>
    <if test="channelType != null and channelType != ''">
      and CHANNEL_TYPE = #{channelType,jdbcType = VARCHAR}
    </if>
    <if test="batchNo != null and batchNo != ''">
      and BATCH_NO = #{batchNo,jdbcType = VARCHAR}
    </if>
    <if test="userCompanyId != null">
      AND t.customer_code in (
        select c.customer_no from cust_customer c where c.company_id in (
          select company_id from pas_company
          start with company_id =  #{userCompanyId,jdbcType=DECIMAL}
          connect by parent_id = prior company_id
        )
      )
    </if>
    <if test="businessManId != null">
      AND t.customer_code in (
        select c.customer_no from cust_customer c where c.business_man_id = #{businessManId,jdbcType=DECIMAL}
      )
    </if>
    <if test="companyIds != null">
      and exists (
        select 1 from cust_customer_draft cd
        where t.CUSTOMER_CODE = cd.CUSTOMER_NO
        and cd.company_id in
        <foreach collection="companyIds" item="item" index="index" open="(" close=")" separator=",">
          #{item}
        </foreach>
      )
    </if>
  </sql>
  <select id="selectCustByChannel" resultType="com.epaylinks.efps.pas.pas.domain.ComplainCustVo" >
    select CUSTOMER_CODE as customerCode,INLET_TYPE as inletType from CUST_INLET_RECORD
    where CHANNEL_MCHT_NO = #{channelMchId,jdbcType=VARCHAR}
  </select>
  
  <select id="selectCustomerNoByMchtNo" resultType="java.lang.String" parameterType="java.lang.String">
    select CUSTOMER_CODE from cust_union_inlet_record
    where channel_mcht_no = #{mchtNo,jdbcType=VARCHAR}
  </select>

  <select id="selectCustomerNoByCustNo" resultType="java.lang.String" parameterType="java.lang.String">
    select CUSTOMER_NO from cust_nu_report_detail
    where channel_cust_no = #{custNo,jdbcType=VARCHAR}
  </select>

  <select id="selectByCust" resultMap="BaseResultMap" parameterType="java.lang.String" >
     select c.customer_no as CUSTOMER_CODE, c.name as CUST_NAME, c.plat_customer_no as PLAT_CUSTOMER_CODE, c3.name as PLAT_CUST_NAME,
     c.service_customer_no as AGENT_CUSTOMER_CODE, c4.name as AGENT_CUST_NAME, u.real_name as BUSINESS_MAN
    from cust_customer c
      left join cust_customer_draft c2 on c2.customer_id = c.customer_id
      left join cust_customer_draft c3 on c3.customer_no = c.plat_customer_no
      left join cust_customer_draft c4 on c4.customer_no = c.service_customer_no
      left join pas_user u on u.user_id = c.business_man_id
    where c.customer_no = #{customerCode,jdbcType=VARCHAR}
  </select>
</mapper>