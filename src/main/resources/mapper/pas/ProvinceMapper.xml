<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.pas.pas.dao.ProvinceMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.pas.domain.Province" >
    <id column="PROV_CD" property="provCd" jdbcType="VARCHAR" />
    <result column="PROV_LVL" property="provLvl" jdbcType="VARCHAR" />
    <result column="PROV_ACS_STS" property="provAcsSts" jdbcType="VARCHAR" />
    <result column="CNCT_BOSS_STS" property="cnctBossSts" jdbcType="VARCHAR" />
    <result column="BOSS_PROV" property="bossProv" jdbcType="VARCHAR" />
    <result column="LPS_CD" property="lpsCd" jdbcType="VARCHAR" />
    <result column="VGOP_PROV" property="vgopProv" jdbcType="VARCHAR" />
    <result column="PPD_OPN_FLG" property="ppdOpnFlg" jdbcType="VARCHAR" />
    <result column="UPD_DT" property="updDt" jdbcType="VARCHAR" />
    <result column="TM_SMP" property="tmSmp" jdbcType="VARCHAR" />
    <result column="PROV_LNM" property="provLnm" jdbcType="VARCHAR" />
    <result column="PROV_NM" property="provNm" jdbcType="VARCHAR" />
    <result column="NOD_ID" property="nodId" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    PROV_CD, PROV_LVL, PROV_ACS_STS, CNCT_BOSS_STS, BOSS_PROV, LPS_CD, VGOP_PROV, PPD_OPN_FLG, 
    UPD_DT, TM_SMP, PROV_LNM, PROV_NM, NOD_ID
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from PAS_PCOP
    where PROV_CD = #{provCd,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from PAS_PCOP
    where PROV_CD = #{provCd,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.pas.domain.Province" >
    insert into PAS_PCOP (PROV_CD, PROV_LVL, PROV_ACS_STS, 
      CNCT_BOSS_STS, BOSS_PROV, LPS_CD, 
      VGOP_PROV, PPD_OPN_FLG, UPD_DT, 
      TM_SMP, PROV_LNM, PROV_NM, 
      NOD_ID)
    values (#{provCd,jdbcType=VARCHAR}, #{provLvl,jdbcType=VARCHAR}, #{provAcsSts,jdbcType=VARCHAR}, 
      #{cnctBossSts,jdbcType=VARCHAR}, #{bossProv,jdbcType=VARCHAR}, #{lpsCd,jdbcType=VARCHAR}, 
      #{vgopProv,jdbcType=VARCHAR}, #{ppdOpnFlg,jdbcType=VARCHAR}, #{updDt,jdbcType=VARCHAR}, 
      #{tmSmp,jdbcType=VARCHAR}, #{provLnm,jdbcType=VARCHAR}, #{provNm,jdbcType=VARCHAR}, 
      #{nodId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.pas.domain.Province" >
    insert into PAS_PCOP
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="provCd != null" >
        PROV_CD,
      </if>
      <if test="provLvl != null" >
        PROV_LVL,
      </if>
      <if test="provAcsSts != null" >
        PROV_ACS_STS,
      </if>
      <if test="cnctBossSts != null" >
        CNCT_BOSS_STS,
      </if>
      <if test="bossProv != null" >
        BOSS_PROV,
      </if>
      <if test="lpsCd != null" >
        LPS_CD,
      </if>
      <if test="vgopProv != null" >
        VGOP_PROV,
      </if>
      <if test="ppdOpnFlg != null" >
        PPD_OPN_FLG,
      </if>
      <if test="updDt != null" >
        UPD_DT,
      </if>
      <if test="tmSmp != null" >
        TM_SMP,
      </if>
      <if test="provLnm != null" >
        PROV_LNM,
      </if>
      <if test="provNm != null" >
        PROV_NM,
      </if>
      <if test="nodId != null" >
        NOD_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="provCd != null" >
        #{provCd,jdbcType=VARCHAR},
      </if>
      <if test="provLvl != null" >
        #{provLvl,jdbcType=VARCHAR},
      </if>
      <if test="provAcsSts != null" >
        #{provAcsSts,jdbcType=VARCHAR},
      </if>
      <if test="cnctBossSts != null" >
        #{cnctBossSts,jdbcType=VARCHAR},
      </if>
      <if test="bossProv != null" >
        #{bossProv,jdbcType=VARCHAR},
      </if>
      <if test="lpsCd != null" >
        #{lpsCd,jdbcType=VARCHAR},
      </if>
      <if test="vgopProv != null" >
        #{vgopProv,jdbcType=VARCHAR},
      </if>
      <if test="ppdOpnFlg != null" >
        #{ppdOpnFlg,jdbcType=VARCHAR},
      </if>
      <if test="updDt != null" >
        #{updDt,jdbcType=VARCHAR},
      </if>
      <if test="tmSmp != null" >
        #{tmSmp,jdbcType=VARCHAR},
      </if>
      <if test="provLnm != null" >
        #{provLnm,jdbcType=VARCHAR},
      </if>
      <if test="provNm != null" >
        #{provNm,jdbcType=VARCHAR},
      </if>
      <if test="nodId != null" >
        #{nodId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.pas.domain.Province" >
    update PAS_PCOP
    <set >
      <if test="provLvl != null" >
        PROV_LVL = #{provLvl,jdbcType=VARCHAR},
      </if>
      <if test="provAcsSts != null" >
        PROV_ACS_STS = #{provAcsSts,jdbcType=VARCHAR},
      </if>
      <if test="cnctBossSts != null" >
        CNCT_BOSS_STS = #{cnctBossSts,jdbcType=VARCHAR},
      </if>
      <if test="bossProv != null" >
        BOSS_PROV = #{bossProv,jdbcType=VARCHAR},
      </if>
      <if test="lpsCd != null" >
        LPS_CD = #{lpsCd,jdbcType=VARCHAR},
      </if>
      <if test="vgopProv != null" >
        VGOP_PROV = #{vgopProv,jdbcType=VARCHAR},
      </if>
      <if test="ppdOpnFlg != null" >
        PPD_OPN_FLG = #{ppdOpnFlg,jdbcType=VARCHAR},
      </if>
      <if test="updDt != null" >
        UPD_DT = #{updDt,jdbcType=VARCHAR},
      </if>
      <if test="tmSmp != null" >
        TM_SMP = #{tmSmp,jdbcType=VARCHAR},
      </if>
      <if test="provLnm != null" >
        PROV_LNM = #{provLnm,jdbcType=VARCHAR},
      </if>
      <if test="provNm != null" >
        PROV_NM = #{provNm,jdbcType=VARCHAR},
      </if>
      <if test="nodId != null" >
        NOD_ID = #{nodId,jdbcType=VARCHAR},
      </if>
    </set>
    where PROV_CD = #{provCd,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.pas.domain.Province" >
    update PAS_PCOP
    set PROV_LVL = #{provLvl,jdbcType=VARCHAR},
      PROV_ACS_STS = #{provAcsSts,jdbcType=VARCHAR},
      CNCT_BOSS_STS = #{cnctBossSts,jdbcType=VARCHAR},
      BOSS_PROV = #{bossProv,jdbcType=VARCHAR},
      LPS_CD = #{lpsCd,jdbcType=VARCHAR},
      VGOP_PROV = #{vgopProv,jdbcType=VARCHAR},
      PPD_OPN_FLG = #{ppdOpnFlg,jdbcType=VARCHAR},
      UPD_DT = #{updDt,jdbcType=VARCHAR},
      TM_SMP = #{tmSmp,jdbcType=VARCHAR},
      PROV_LNM = #{provLnm,jdbcType=VARCHAR},
      PROV_NM = #{provNm,jdbcType=VARCHAR},
      NOD_ID = #{nodId,jdbcType=VARCHAR}
    where PROV_CD = #{provCd,jdbcType=VARCHAR}
  </update>

  <select id="selectAll" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from PAS_PCOP
    where PROV_LVL != '0'
  </select>
  
  <select id="queryProviceCodeByName" resultType="java.lang.String" >
    SELECT PROV_CD FROM PAS_PCOP WHERE PROV_NM = #{provinceName,jdbcType=VARCHAR}
  </select>


</mapper>