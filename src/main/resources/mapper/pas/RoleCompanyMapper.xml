<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.pas.pas.dao.RoleCompanyMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.pas.domain.RoleCompany" >
    <id column="ROLE_ID" property="roleId" jdbcType="DECIMAL" />
    <result column="ROLE_NAME" property="roleName" jdbcType="VARCHAR" />
    <result column="COMPANY_ID" property="companyId" jdbcType="DECIMAL" />
    <result column="COMPANY_NAME" property="companyName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    ROLE_ID, ROLE_NAME, COMPANY_ID, COMPANY_NAME
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from PAS_ROLE_COMPANY
    where ROLE_ID = #{roleId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from PAS_ROLE_COMPANY
    where ROLE_ID = #{roleId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.pas.domain.RoleCompany" >
    insert into PAS_ROLE_COMPANY (ROLE_ID, ROLE_NAME, COMPANY_ID, 
      COMPANY_NAME)
    values (#{roleId,jdbcType=DECIMAL}, #{roleName,jdbcType=VARCHAR}, #{companyId,jdbcType=DECIMAL}, 
      #{companyName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.pas.domain.RoleCompany" >
    insert into PAS_ROLE_COMPANY
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="roleId != null" >
        ROLE_ID,
      </if>
      <if test="roleName != null" >
        ROLE_NAME,
      </if>
      <if test="companyId != null" >
        COMPANY_ID,
      </if>
      <if test="companyName != null" >
        COMPANY_NAME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="roleId != null" >
        #{roleId,jdbcType=DECIMAL},
      </if>
      <if test="roleName != null" >
        #{roleName,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null" >
        #{companyId,jdbcType=DECIMAL},
      </if>
      <if test="companyName != null" >
        #{companyName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.pas.domain.RoleCompany" >
    update PAS_ROLE_COMPANY
    <set >
      <if test="roleName != null" >
        ROLE_NAME = #{roleName,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null" >
        COMPANY_ID = #{companyId,jdbcType=DECIMAL},
      </if>
      <if test="companyName != null" >
        COMPANY_NAME = #{companyName,jdbcType=VARCHAR},
      </if>
    </set>
    where ROLE_ID = #{roleId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.pas.domain.RoleCompany" >
    update PAS_ROLE_COMPANY
    set ROLE_NAME = #{roleName,jdbcType=VARCHAR},
      COMPANY_ID = #{companyId,jdbcType=DECIMAL},
      COMPANY_NAME = #{companyName,jdbcType=VARCHAR}
    where ROLE_ID = #{roleId,jdbcType=DECIMAL}
  </update>

  <select id="selectCompanyIdByRoleId" parameterType="java.lang.Long" resultType="java.lang.Long">
    select COMPANY_ID  from PAS_ROLE_COMPANY
    where ROLE_ID = #{roleId,jdbcType=DECIMAL}
  </select>

  <select id="selectCompanyIdByRoleIds" resultType="java.lang.Long">
    select distinct COMPANY_ID from PAS_ROLE_COMPANY
    where ROLE_ID in
    <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <select id="selectByRoleId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from PAS_ROLE_COMPANY
    where ROLE_ID = #{roleId,jdbcType=DECIMAL}
  </select>

  <delete id="deleteByRoleId">
    DELETE FROM PAS_ROLE_COMPANY WHERE ROLE_ID = #{roleId,jdbcType=DECIMAL}
  </delete>

</mapper>