<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.pas.pas.dao.DkteRecordMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.pas.domain.DkteRecord" >
    <id column="DKTE_ID" property="dkteId" jdbcType="DECIMAL" />
    <result column="TRANSACTION_NO" property="transactionNo" jdbcType="VARCHAR" />
    <result column="ACC_VOUCHER_NO" property="accVoucherNo" jdbcType="VARCHAR" />
    <result column="CUSTOMER_CODE" property="customerCode" jdbcType="VARCHAR" />
    <result column="CUSTOMER_NAME" property="customerName" jdbcType="VARCHAR" />
    <result column="ADJUST_TYPE" property="adjustType" jdbcType="VARCHAR" />
    <result column="ADJUST_REASON" property="adjustReason" jdbcType="VARCHAR" />
    <result column="ADJUST_AMOUT" property="adjustAmout" jdbcType="DECIMAL" />
    <result column="ATTACHMENT_UID" property="attachmentUid" jdbcType="VARCHAR" />
    <result column="ATTACHMENT_NAME" property="attachmentName" jdbcType="VARCHAR" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
    <result column="RECORD_STATE" property="recordState" jdbcType="VARCHAR" />
    <result column="CREATE_PERSON_ID" property="createPersonId" jdbcType="VARCHAR" />
    <result column="CREATE_PERSON_NAME" property="createPersonName" jdbcType="VARCHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="AUDIT_TIME" property="auditTime" jdbcType="TIMESTAMP" />
    <result column="AUDIT_PERSON_ID" property="auditPersonId" jdbcType="VARCHAR" />
    <result column="AUDIT_PERSON_NAME" property="auditPersonName" jdbcType="VARCHAR" />
    <result column="AUDIT_RESULT" property="auditResult" jdbcType="VARCHAR" />
    <result column="AUDIT_REASON" property="auditReason" jdbcType="VARCHAR" />
    <result column="ORIG_FREEZE_NO" property="origFreezeNo" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    DKTE_ID, TRANSACTION_NO, ACC_VOUCHER_NO, CUSTOMER_CODE, CUSTOMER_NAME, ADJUST_TYPE, ADJUST_REASON, ADJUST_AMOUT,
    ATTACHMENT_UID, ATTACHMENT_NAME, REMARK, RECORD_STATE, CREATE_PERSON_ID, CREATE_PERSON_NAME, CREATE_TIME, UPDATE_TIME, AUDIT_TIME, AUDIT_PERSON_ID,
    AUDIT_PERSON_NAME, AUDIT_RESULT, AUDIT_REASON, ORIG_FREEZE_NO
  </sql>
  <!-- 获取主键ID -->
  <select id="selectIdFromSeq" resultType="java.lang.Long" >
    select SEQ_PAS_DKTE_RECORD.nextval from dual
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from PAS_DKTE_RECORD
    where DKTE_ID = #{dkteId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from PAS_DKTE_RECORD
    where DKTE_ID = #{dkteId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.pas.domain.DkteRecord" >
    insert into PAS_DKTE_RECORD (DKTE_ID, TRANSACTION_NO, ACC_VOUCHER_NO, CUSTOMER_CODE, CUSTOMER_NAME,
      ADJUST_TYPE, ADJUST_REASON, ADJUST_AMOUT, 
      ATTACHMENT_UID, ATTACHMENT_NAME, REMARK, RECORD_STATE,CREATE_PERSON_ID, CREATE_PERSON_NAME,
      CREATE_TIME, UPDATE_TIME, AUDIT_TIME, 
      AUDIT_PERSON_ID, AUDIT_PERSON_NAME, AUDIT_RESULT, 
      AUDIT_REASON, ORIG_FREEZE_NO)
    values (#{dkteId,jdbcType=DECIMAL}, #{transactionNo,jdbcType=VARCHAR}, #{accVoucherNo,jdbcType=VARCHAR},
      #{customerCode,jdbcType=VARCHAR},  #{customerName,jdbcType=VARCHAR},
      #{adjustType,jdbcType=VARCHAR}, #{adjustReason,jdbcType=VARCHAR}, #{adjustAmout,jdbcType=DECIMAL}, 
      #{attachmentUid,jdbcType=VARCHAR}, #{attachmentName,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},
      #{recordState,jdbcType=VARCHAR}, #{createPersonId,jdbcType=VARCHAR}, #{createPersonName,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{auditTime,jdbcType=TIMESTAMP}, 
      #{auditPersonId,jdbcType=VARCHAR}, #{auditPersonName,jdbcType=VARCHAR}, #{auditResult,jdbcType=VARCHAR}, 
      #{auditReason,jdbcType=VARCHAR}, #{origFreezeNo,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.pas.domain.DkteRecord" >
    insert into PAS_DKTE_RECORD
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="dkteId != null" >
        DKTE_ID,
      </if>
      <if test="transactionNo != null" >
        TRANSACTION_NO,
      </if>
      <if test="accVoucherNo != null" >
        ACC_VOUCHER_NO,
      </if>
      <if test="customerCode != null" >
        CUSTOMER_CODE,
      </if>
      <if test="customerName != null" >
        CUSTOMER_NAME,
      </if>
      <if test="adjustType != null" >
        ADJUST_TYPE,
      </if>
      <if test="adjustReason != null" >
        ADJUST_REASON,
      </if>
      <if test="adjustAmout != null" >
        ADJUST_AMOUT,
      </if>
      <if test="attachmentUid != null" >
        ATTACHMENT_UID,
      </if>
      <if test="attachmentName != null" >
        ATTACHMENT_NAME,
      </if>
      <if test="remark != null" >
        REMARK,
      </if>
      <if test="recordState != null" >
        RECORD_STATE,
      </if>
      <if test="createPersonId != null" >
        CREATE_PERSON_ID,
      </if>
      <if test="createPersonName != null" >
        CREATE_PERSON_NAME,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME,
      </if>
      <if test="auditTime != null" >
        AUDIT_TIME,
      </if>
      <if test="auditPersonId != null" >
        AUDIT_PERSON_ID,
      </if>
      <if test="auditPersonName != null" >
        AUDIT_PERSON_NAME,
      </if>
      <if test="auditResult != null" >
        AUDIT_RESULT,
      </if>
      <if test="auditReason != null" >
        AUDIT_REASON,
      </if>
      <if test="origFreezeNo != null">
        ORIG_FREEZE_NO,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="dkteId != null" >
        #{dkteId,jdbcType=DECIMAL},
      </if>
      <if test="transactionNo != null" >
        #{transactionNo,jdbcType=VARCHAR},
      </if>
      <if test="accVoucherNo != null" >
        #{accVoucherNo,jdbcType=VARCHAR},
      </if>
      <if test="customerCode != null" >
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="customerName != null" >
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="adjustType != null" >
        #{adjustType,jdbcType=VARCHAR},
      </if>
      <if test="adjustReason != null" >
        #{adjustReason,jdbcType=VARCHAR},
      </if>
      <if test="adjustAmout != null" >
        #{adjustAmout,jdbcType=DECIMAL},
      </if>
      <if test="attachmentUid != null" >
        #{attachmentUid,jdbcType=VARCHAR},
      </if>
      <if test="attachmentName != null" >
        #{attachmentName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="recordState != null" >
        #{recordState,jdbcType=VARCHAR},
      </if>
      <if test="createPersonId != null" >
        #{createPersonId,jdbcType=VARCHAR},
      </if>
      <if test="createPersonName != null" >
        #{createPersonName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditTime != null" >
        #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditPersonId != null" >
        #{auditPersonId,jdbcType=VARCHAR},
      </if>
      <if test="auditPersonName != null" >
        #{auditPersonName,jdbcType=VARCHAR},
      </if>
      <if test="auditResult != null" >
        #{auditResult,jdbcType=VARCHAR},
      </if>
      <if test="auditReason != null" >
        #{auditReason,jdbcType=VARCHAR},
      </if>
      <if test="origFreezeNo != null">
        #{origFreezeNo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.pas.domain.DkteRecord" >
    update PAS_DKTE_RECORD
    <set >
      <if test="transactionNo != null" >
        TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR},
      </if>
      <if test="accVoucherNo != null" >
        ACC_VOUCHER_NO = #{accVoucherNo,jdbcType=VARCHAR},
      </if>
      <if test="customerCode != null" >
        CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="customerName != null" >
        CUSTOMER_NAME = #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="adjustType != null" >
        ADJUST_TYPE = #{adjustType,jdbcType=VARCHAR},
      </if>
      <if test="adjustReason != null" >
        ADJUST_REASON = #{adjustReason,jdbcType=VARCHAR},
      </if>
      <if test="adjustAmout != null" >
        ADJUST_AMOUT = #{adjustAmout,jdbcType=DECIMAL},
      </if>
      <if test="attachmentUid != null" >
        ATTACHMENT_UID = #{attachmentUid,jdbcType=VARCHAR},
      </if>
      <if test="attachmentName != null" >
        ATTACHMENT_NAME = #{attachmentName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="recordState != null" >
        RECORD_STATE = #{recordState,jdbcType=VARCHAR},
      </if>
      <if test="createPersonId != null" >
        CREATE_PERSON_ID = #{createPersonId,jdbcType=VARCHAR},
      </if>
      <if test="createPersonName != null" >
        CREATE_PERSON_NAME = #{createPersonName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditTime != null" >
        AUDIT_TIME = #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditPersonId != null" >
        AUDIT_PERSON_ID = #{auditPersonId,jdbcType=VARCHAR},
      </if>
      <if test="auditPersonName != null" >
        AUDIT_PERSON_NAME = #{auditPersonName,jdbcType=VARCHAR},
      </if>
      <if test="auditResult != null" >
        AUDIT_RESULT = #{auditResult,jdbcType=VARCHAR},
      </if>
      <if test="auditReason != null" >
        AUDIT_REASON = #{auditReason,jdbcType=VARCHAR},
      </if>
      <if test="origFreezeNo != null" >
        ORIG_FREEZE_NO = #{origFreezeNo,jdbcType=VARCHAR},
      </if>
    </set>
    where DKTE_ID = #{dkteId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.pas.domain.DkteRecord" >
    update PAS_DKTE_RECORD
    set TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR},
      ACC_VOUCHER_NO = #{accVoucherNo,jdbcType=VARCHAR},
      CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      CUSTOMER_NAME = #{customerName,jdbcType=VARCHAR},
      ADJUST_TYPE = #{adjustType,jdbcType=VARCHAR},
      ADJUST_REASON = #{adjustReason,jdbcType=VARCHAR},
      ADJUST_AMOUT = #{adjustAmout,jdbcType=DECIMAL},
      ATTACHMENT_UID = #{attachmentUid,jdbcType=VARCHAR},
      ATTACHMENT_NAME = #{attachmentName,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      RECORD_STATE = #{recordState,jdbcType=VARCHAR},
      CREATE_PERSON_ID = #{createPersonId,jdbcType=VARCHAR},
      CREATE_PERSON_NAME = #{createPersonName,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      AUDIT_TIME = #{auditTime,jdbcType=TIMESTAMP},
      AUDIT_PERSON_ID = #{auditPersonId,jdbcType=VARCHAR},
      AUDIT_PERSON_NAME = #{auditPersonName,jdbcType=VARCHAR},
      AUDIT_RESULT = #{auditResult,jdbcType=VARCHAR},
      AUDIT_REASON = #{auditReason,jdbcType=VARCHAR},
        ORIG_FREEZE_NO = #{origFreezeNo,jdbcType=VARCHAR}
    where DKTE_ID = #{dkteId,jdbcType=DECIMAL}
  </update>
  <select id="selectCount" resultType="java.lang.Integer"
          parameterType="java.util.Map">
    select count(*)
    from PAS_DKTE_RECORD
    <where>
      <include refid="tradeQueryCondition"/>
    </where>
  </select>
  <select id="selectByPage" resultMap="BaseResultMap"
          parameterType="java.util.Map">
    select
    <include refid="Base_Column_List" />
    from (
    select A.*, rownum RN
    from (
    select * from PAS_DKTE_RECORD
    <where>
      <include refid="tradeQueryCondition"/>
    </where>
    order by CREATE_TIME desc
    ) A
    where rownum <![CDATA[ <= ]]> #{endRowNo,jdbcType=DECIMAL}
    )
    where RN <![CDATA[ >= ]]>
    #{beginRowNo,jdbcType=DECIMAL}
  </select>
  <sql id = "tradeQueryCondition">

    <if test="startCreateTime != null and startCreateTime != ''">
      and CREATE_TIME <![CDATA[ >= ]]> to_date(#{startCreateTime},'yyyyMMddhh24miss')
    </if>
    <if test="endCreateTime != null and endCreateTime != ''">
      and CREATE_TIME <![CDATA[ < ]]> to_date(#{endCreateTime},'yyyyMMddhh24miss')
    </if>
    <if test="transactionNo != null and transactionNo != ''">
      and TRANSACTION_NO = #{transactionNo,jdbcType = VARCHAR}
    </if>
    <if test="adjustType != null and adjustType != ''">
      and ADJUST_TYPE = #{adjustType,jdbcType = VARCHAR}
    </if>
    <if test="recordState != null and recordState != ''">
      and RECORD_STATE = #{recordState,jdbcType = VARCHAR}
    </if>
    <if test="customerCode != null and customerCode != ''">
      and CUSTOMER_CODE = #{customerCode,jdbcType = VARCHAR}
    </if>
    <if test="origFreezeNo != null and origFreezeNo != ''">
      and ORIG_FREEZE_NO = #{origFreezeNo,jdbcType = VARCHAR}
    </if>

  </sql>

  <select id="queryOrigFreezeRecord" resultMap="BaseResultMap" parameterType="java.util.Map">
    select
    <include refid="Base_Column_List" />
    from PAS_DKTE_RECORD
    where 1=1
    <if test="customerNo != null and customerNo !=''">
      and CUSTOMER_CODE like '%' || #{customerNo} || '%'
    </if>
    <if test="origFreezeNo != null and origFreezeNo != ''">
      and TRANSACTION_NO like '%' ||  #{origFreezeNo} || '%'
    </if>
    and ADJUST_TYPE = '3'
    <!--
    and RECORD_STATE = '1'
    and not exists(
        select 1 from PAS_DKTE_RECORD
        where ORIG_FREEZE_NO = #{origFreezeNo}
        and RECORD_STATE != '2'
    ) -->
  </select>

  <select id="queryRecordByTransNo" resultMap="BaseResultMap" parameterType="java.util.Map">
    select
    <include refid="Base_Column_List" />
    from PAS_DKTE_RECORD
    where 1=1
    <if test="transactionNo != null and transactionNo != ''" >
      and TRANSACTION_NO = #{transactionNo}
    </if>
    <if test="adjustType != null and adjustType != ''">
      and ADJUST_TYPE = #{adjustType,jdbcType = VARCHAR}
    </if>
  </select>

  <select id="countRecordByOrigNo" resultType="java.lang.Integer" parameterType="java.util.Map">
    select
    count(*)
    from PAS_DKTE_RECORD
    where 1=1
    <if test="origFreezeNo != null and origFreezeNo != ''">
      and ORIG_FREEZE_NO = #{origFreezeNo}
    </if>
    <if test="recordState != null and recordState != ''">
      and RECORD_STATE = #{recordState}
    </if>
    <if test="adjustType != null and adjustType != ''">
      and ADJUST_TYPE = #{adjustType,jdbcType = VARCHAR}
    </if>
  </select>

  <select id="countDkjeAmount" resultType="java.lang.Long" parameterType="java.util.Map">
    with freeze as (
      select
        customer_code customerNo,
        sum(ADJUST_AMOUT) freezeAmount
      from PAS_DKTE_RECORD
      where CUSTOMER_CODE = #{customerNo}
      and ADJUST_TYPE = '3'
      and RECORD_STATE = '1'
      group by customer_code
    ),thaw as(
      select
        customer_code customerNo,
        sum(ADJUST_AMOUT) thawAmount
      from PAS_DKTE_RECORD
      where CUSTOMER_CODE = #{customerNo}
        and ADJUST_TYPE = '4'
        and RECORD_STATE != '2'
      group by customer_code
    )
    select nvl(f.freezeAmount,0) - nvl(t.thawAmount,0) from freeze f
    left join thaw t on f.customerNo = t.customerNo
  </select>
</mapper>