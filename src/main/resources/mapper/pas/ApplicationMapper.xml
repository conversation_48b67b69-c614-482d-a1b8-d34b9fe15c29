<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.pas.pas.dao.ApplicationMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.pas.domain.Application" >
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="EFPS_KEY" property="efpsKey" jdbcType="VARCHAR" />
    <result column="EFPS_NAME" property="efpsName" jdbcType="VARCHAR" />
    <result column="INSTITUTION_KEY" property="institutionKey" jdbcType="VARCHAR" />
    <result column="INSTITUTION_CODE" property="institutionCode" jdbcType="VARCHAR" />
    <result column="ALLOW_NULL" property="allowNull" jdbcType="CHAR" />
    <result column="IS_FILE" property="isFile" jdbcType="CHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, EFPS_KEY, EFPS_NAME, INSTITUTION_KEY, INSTITUTION_CODE, ALLOW_NULL, IS_FILE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from PAS_APPLICATION
    where ID = #{id,jdbcType=DECIMAL}
  </select>

  <select id="selectBySelective" resultMap="BaseResultMap" parameterType="com.epaylinks.efps.pas.pas.domain.Application" >
    select
    <include refid="Base_Column_List" />
    from PAS_APPLICATION
    <where>
      <if test="id != null and id != ''" >
        AND ID = #{id,jdbcType=DECIMAL}
      </if>
      <if test="efpsKey != null and efpsKey != ''" >
        AND EFPS_KEY = #{efpsKey,jdbcType=VARCHAR}
      </if>
      <if test="efpsName != null and efpsName != ''" >
        AND EFPS_NAME = #{efpsName,jdbcType=VARCHAR}
      </if>
      <if test="institutionKey != null and institutionKey != ''" >
        AND INSTITUTION_KEY = #{institutionKey,jdbcType=VARCHAR}
      </if>
      <if test="institutionCode != null and institutionCode != ''" >
        AND INSTITUTION_CODE = #{institutionCode,jdbcType=VARCHAR}
      </if>
      <if test="allowNull != null and allowNull != ''" >
        AND ALLOW_NULL = #{allowNull,jdbcType=CHAR}
      </if>
      <if test="isFile != null and isFile != ''" >
        AND IS_FILE = #{isFile,jdbcType=CHAR}
      </if>
    </where>
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from PAS_APPLICATION
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.pas.domain.Application" >
    insert into PAS_APPLICATION (ID, EFPS_KEY, EFPS_NAME, 
      INSTITUTION_KEY, INSTITUTION_CODE, ALLOW_NULL,IS_FILE
      )
    values (#{id,jdbcType=DECIMAL}, #{efpsKey,jdbcType=VARCHAR}, #{efpsName,jdbcType=VARCHAR}, 
      #{institutionKey,jdbcType=VARCHAR}, #{institutionCode,jdbcType=VARCHAR}, #{allowNull,jdbcType=CHAR},
      #{isFile,jdbcType=CHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.pas.domain.Application" >
    insert into PAS_APPLICATION
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null and id != ''" >
        ID,
      </if>
      <if test="efpsKey != null and efpsKey != ''" >
        EFPS_KEY,
      </if>
      <if test="efpsName != null and efpsName != ''" >
        EFPS_NAME,
      </if>
      <if test="institutionKey != null and institutionKey != ''" >
        INSTITUTION_KEY,
      </if>
      <if test="institutionCode != null and institutionCode != ''" >
        INSTITUTION_CODE,
      </if>
      <if test="allowNull != null and allowNull != ''" >
        ALLOW_NULL,
      </if>
      <if test="isFile != null and isFile != ''" >
        AND IS_FILE ,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null and id != ''" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="efpsKey != null and efpsKey != ''" >
        #{efpsKey,jdbcType=VARCHAR},
      </if>
      <if test="efpsName != null and efpsName != ''" >
        #{efpsName,jdbcType=VARCHAR},
      </if>
      <if test="institutionKey != null and institutionKey != ''" >
        #{institutionKey,jdbcType=VARCHAR},
      </if>
      <if test="institutionCode != null and institutionCode != ''" >
        #{institutionCode,jdbcType=VARCHAR},
      </if>
      <if test="allowNull != null and allowNull != ''" >
        #{allowNull,jdbcType=CHAR},
      </if>
      <if test="isFile != null and isFile != ''" >
        #{isFile,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.pas.domain.Application" >
    update PAS_APPLICATION
    <set >
      <if test="efpsKey != null and efpsKey != ''" >
        EFPS_KEY = #{efpsKey,jdbcType=VARCHAR},
      </if>
      <if test="efpsName != null and efpsName != ''" >
        EFPS_NAME = #{efpsName,jdbcType=VARCHAR},
      </if>
      <if test="institutionKey != null and institutionKey != ''" >
        INSTITUTION_KEY = #{institutionKey,jdbcType=VARCHAR},
      </if>
      <if test="institutionCode != null and institutionCode != ''" >
        INSTITUTION_CODE = #{institutionCode,jdbcType=VARCHAR},
      </if>
      <if test="allowNull != null and allowNull != ''" >
        ALLOW_NULL = #{allowNull,jdbcType=CHAR},
      </if>
      <if test="isFile != null and isFile != ''" >
        AND IS_FILE = #{isFile,jdbcType=CHAR}
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.pas.domain.Application" >
    update PAS_APPLICATION
    set EFPS_KEY = #{efpsKey,jdbcType=VARCHAR},
      EFPS_NAME = #{efpsName,jdbcType=VARCHAR},
      INSTITUTION_KEY = #{institutionKey,jdbcType=VARCHAR},
      INSTITUTION_CODE = #{institutionCode,jdbcType=VARCHAR},
      ALLOW_NULL = #{allowNull,jdbcType=CHAR},
      IS_FILE = #{isFile,jdbcType=CHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>

  <select id="checkUpValueIsNotNull" resultType="java.lang.String"  statementType="STATEMENT">
    select ${columnName}
    from ${tableName}
    WHERE CUSTOMER_CODE =  ${customerCode}
  </select>

  <select id="getFileUrlId" resultType="java.lang.String" parameterType="java.util.Map">
    select ATTACHMENT_URL
    from PAS_CUSTOMER_ATTACHMENT_INFO a , PAS_CUSTOMER_INFO i
    WHERE a.CUSTOMER_INFO_ID = i.INFO_ID
    AND a.ATTACHMENT_CODE = #{attachmentCode,jdbcType=VARCHAR}
    AND i.CUSTOMER_CODE =  #{customerCode,jdbcType=VARCHAR}
  </select>

  <select id="getInstitutionMCC" resultType="java.lang.String"  statementType="STATEMENT">
    select ${columnName}
    from PAS_TRADE_CATEGORY tc , PAS_CUSTOMER_INFO ci
    WHERE ci.INFO_ID = (select max(newest_customer_info_id) from PAS_CUSTOMER where customer_code = ${customerCode})
    AND  tc.MCC = "SUBSTR"(ci.TRADE_CATEGORY, 13, 4)
  </select>

</mapper>