<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.pas.pas.dao.BankBranchMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.pas.domain.BankBranch" >
    <id column="LBNK_NO" property="lbnkNo" jdbcType="VARCHAR" />
    <result column="LBNK_NM" property="lbnkNm" jdbcType="VARCHAR" />
    <result column="LBNK_CD" property="lbnkCd" jdbcType="VARCHAR" />
    <result column="CORP_ORG" property="corpOrg" jdbcType="VARCHAR" />
    <result column="ADM_CITY" property="admCity" jdbcType="VARCHAR" />
    <result column="ADM_PROV" property="admProv" jdbcType="VARCHAR" />
    <result column="ADM_RGN" property="admRgn" jdbcType="VARCHAR" />
    <result column="PROV_CD" property="provCd" jdbcType="VARCHAR" />
    <result column="CITY_CD" property="cityCd" jdbcType="VARCHAR" />
    <result column="TM_SMP" property="tmSmp" jdbcType="VARCHAR" />
    <result column="NOD_ID" property="nodId" jdbcType="VARCHAR" />
    <result column="UPD_DT" property="updDt" jdbcType="VARCHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="OPERATOR_ID" property="operatorId" jdbcType="VARCHAR" />
    <result column="FLAG" property="flag" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    LBNK_NO, LBNK_NM, LBNK_CD, CORP_ORG, ADM_CITY, ADM_PROV, ADM_RGN, PROV_CD, CITY_CD, 
    TM_SMP, NOD_ID, UPD_DT,CREATE_TIME,UPDATE_TIME,OPERATOR_ID,FLAG
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from PAS_BANK_BRANCH
    where LBNK_NO = #{lbnkNo,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from PAS_BANK_BRANCH
    where LBNK_NO = #{lbnkNo,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.pas.domain.BankBranch" >
    insert into PAS_BANK_BRANCH (LBNK_NO, LBNK_NM, LBNK_CD, 
      CORP_ORG, ADM_CITY, ADM_PROV, 
      ADM_RGN, PROV_CD, CITY_CD, 
      TM_SMP, NOD_ID, UPD_DT
      )
    values (#{lbnkNo,jdbcType=VARCHAR}, #{lbnkNm,jdbcType=VARCHAR}, #{lbnkCd,jdbcType=VARCHAR}, 
      #{corpOrg,jdbcType=VARCHAR}, #{admCity,jdbcType=VARCHAR}, #{admProv,jdbcType=VARCHAR}, 
      #{admRgn,jdbcType=VARCHAR}, #{provCd,jdbcType=VARCHAR}, #{cityCd,jdbcType=VARCHAR}, 
      #{tmSmp,jdbcType=VARCHAR}, #{nodId,jdbcType=VARCHAR}, #{updDt,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.pas.domain.BankBranch" >
    insert into PAS_BANK_BRANCH
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="lbnkNo != null" >
        LBNK_NO,
      </if>
      <if test="lbnkNm != null" >
        LBNK_NM,
      </if>
      <if test="lbnkCd != null" >
        LBNK_CD,
      </if>
      <if test="corpOrg != null" >
        CORP_ORG,
      </if>
      <if test="admCity != null" >
        ADM_CITY,
      </if>
      <if test="admProv != null" >
        ADM_PROV,
      </if>
      <if test="admRgn != null" >
        ADM_RGN,
      </if>
      <if test="provCd != null" >
        PROV_CD,
      </if>
      <if test="cityCd != null" >
        CITY_CD,
      </if>
      <if test="tmSmp != null" >
        TM_SMP,
      </if>
      <if test="nodId != null" >
        NOD_ID,
      </if>
      <if test="updDt != null" >
        UPD_DT,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME,
      </if>
      <if test="operatorId != null" >
        OPERATOR_ID,
      </if>
      <if test="flag != null" >
        FLAG,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="lbnkNo != null" >
        #{lbnkNo,jdbcType=VARCHAR},
      </if>
      <if test="lbnkNm != null" >
        #{lbnkNm,jdbcType=VARCHAR},
      </if>
      <if test="lbnkCd != null" >
        #{lbnkCd,jdbcType=VARCHAR},
      </if>
      <if test="corpOrg != null" >
        #{corpOrg,jdbcType=VARCHAR},
      </if>
      <if test="admCity != null" >
        #{admCity,jdbcType=VARCHAR},
      </if>
      <if test="admProv != null" >
        #{admProv,jdbcType=VARCHAR},
      </if>
      <if test="admRgn != null" >
        #{admRgn,jdbcType=VARCHAR},
      </if>
      <if test="provCd != null" >
        #{provCd,jdbcType=VARCHAR},
      </if>
      <if test="cityCd != null" >
        #{cityCd,jdbcType=VARCHAR},
      </if>
      <if test="tmSmp != null" >
        #{tmSmp,jdbcType=VARCHAR},
      </if>
      <if test="nodId != null" >
        #{nodId,jdbcType=VARCHAR},
      </if>
      <if test="updDt != null" >
        #{updDt,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operatorId != null" >
        #{operatorId,jdbcType=VARCHAR},
      </if>
      <if test="flag != null" >
        #{flag,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.pas.domain.BankBranch" >
    update PAS_BANK_BRANCH
    <set >
      <if test="lbnkNm != null" >
        LBNK_NM = #{lbnkNm,jdbcType=VARCHAR},
      </if>
      <if test="lbnkCd != null" >
        LBNK_CD = #{lbnkCd,jdbcType=VARCHAR},
      </if>
      <if test="corpOrg != null" >
        CORP_ORG = #{corpOrg,jdbcType=VARCHAR},
      </if>
      <if test="admCity != null" >
        ADM_CITY = #{admCity,jdbcType=VARCHAR},
      </if>
      <if test="admProv != null" >
        ADM_PROV = #{admProv,jdbcType=VARCHAR},
      </if>
      <if test="admRgn != null" >
        ADM_RGN = #{admRgn,jdbcType=VARCHAR},
      </if>
      <if test="provCd != null" >
        PROV_CD = #{provCd,jdbcType=VARCHAR},
      </if>
      <if test="cityCd != null" >
        CITY_CD = #{cityCd,jdbcType=VARCHAR},
      </if>
      <if test="tmSmp != null" >
        TM_SMP = #{tmSmp,jdbcType=VARCHAR},
      </if>
      <if test="nodId != null" >
        NOD_ID = #{nodId,jdbcType=VARCHAR},
      </if>
      <if test="updDt != null" >
        UPD_DT = #{updDt,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operatorId != null" >
        OPERATOR_ID = #{operatorId,jdbcType=VARCHAR},
      </if>
      <if test="flag != null" >
        FLAG = #{flag,jdbcType=VARCHAR},
      </if>
    </set>
    where LBNK_NO = #{lbnkNo,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.pas.domain.BankBranch" >
    update PAS_BANK_BRANCH
    set LBNK_NM = #{lbnkNm,jdbcType=VARCHAR},
      LBNK_CD = #{lbnkCd,jdbcType=VARCHAR},
      CORP_ORG = #{corpOrg,jdbcType=VARCHAR},
      ADM_CITY = #{admCity,jdbcType=VARCHAR},
      ADM_PROV = #{admProv,jdbcType=VARCHAR},
      ADM_RGN = #{admRgn,jdbcType=VARCHAR},
      PROV_CD = #{provCd,jdbcType=VARCHAR},
      CITY_CD = #{cityCd,jdbcType=VARCHAR},
      TM_SMP = #{tmSmp,jdbcType=VARCHAR},
      NOD_ID = #{nodId,jdbcType=VARCHAR},
      UPD_DT = #{updDt,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      OPERATOR_ID = #{operatorId,jdbcType=VARCHAR},
      FLAG = #{flag,jdbcType=VARCHAR}
    where LBNK_NO = #{lbnkNo,jdbcType=VARCHAR}
  </update>


  <select id="pageQueryBankBranch" resultMap="BaseResultMap">
    select
    LBNK_NO, LBNK_NM, LBNK_CD, CORP_ORG, ADM_CITY, ADM_PROV, ADM_RGN, PROV_CD, CITY_CD,
    TM_SMP, NOD_ID, UPD_DT
    from (
    select A.*, rownum RN
    from (
    select LBNK_NO, LBNK_NM, LBNK_CD, CORP_ORG, ADM_CITY, ADM_PROV, ADM_RGN, PROV_CD, CITY_CD,
    TM_SMP, NOD_ID, UPD_DT
    from PAS_BANK_BRANCH

    <where>
      <if test="lbnkNo != null and lbnkNo != '' ">
        AND LBNK_NO = #{lbnkNo,jdbcType=VARCHAR}
      </if>
      <if test="lbnkNm != null and lbnkNm != ''">
        AND LBNK_NM = #{lbnkNm,jdbcType=VARCHAR}
      </if>
      <if test="lbnkCd != null and lbnkCd != ''">
        AND LBNK_CD = #{lbnkCd,jdbcType=VARCHAR}
      </if>
      <if test="corpOrg != null and corpOrg != ''">
        AND CORP_ORG = #{corpOrg,jdbcType=VARCHAR}
      </if>
      <if test="provCd != null and provCd != ''">
        AND PROV_CD = #{provCd,jdbcType=VARCHAR}
      </if>
      <if test="cityCd != null and cityCd != ''">
        AND CITY_CD = #{cityCd,jdbcType=VARCHAR}
      </if>
    </where>
    ) A
    where rownum &lt;= #{endRowNo,jdbcType=DECIMAL}
    )
    where RN &gt;= #{beginRowNo,jdbcType=DECIMAL}
  </select>
  <select id="countPageQueryBankBranch" resultType="java.lang.Integer">
    select count(*)
    from PAS_BANK_BRANCH
    <where>
      <if test="lbnkNo != null and lbnkNo != '' ">
        AND LBNK_NO = #{lbnkNo,jdbcType=VARCHAR}
      </if>
      <if test="lbnkNm != null and lbnkNm != ''">
        AND LBNK_NM = #{lbnkNm,jdbcType=VARCHAR}
      </if>
      <if test="lbnkCd != null and lbnkCd != ''">
        AND LBNK_CD = #{lbnkCd,jdbcType=VARCHAR}
      </if>
      <if test="corpOrg != null and corpOrg != ''">
        AND CORP_ORG = #{corpOrg,jdbcType=VARCHAR}
      </if>
      <if test="provCd != null and provCd != ''">
        AND PROV_CD = #{provCd,jdbcType=VARCHAR}
      </if>
      <if test="cityCd != null and cityCd != ''">
        AND CITY_CD = #{cityCd,jdbcType=VARCHAR}
      </if>
    </where>
  </select>

  <select id="queryBankBranchByOptions" resultMap="BaseResultMap" >
    select * from (
        select
        <include refid="Base_Column_List" />
        from PAS_BANK_BRANCH
        where 1=1
          <if test="provinceCode != null and provinceCode != ''">
        	and ADM_PROV = #{provinceCode,jdbcType=VARCHAR}
          </if>
          <if test="cityCode != null and cityCode != ''">
          	and ADM_CITY = #{cityCode,jdbcType=VARCHAR}
          </if>
          	and LBNK_CD = (select max(LBNK_CD) from PAS_BKCD where CORP_ORG = #{bankIcon,jdbcType=VARCHAR})
          <if test="bankBranchName != null and bankBranchName != ''">
             and lbnk_nm like '%'|| #{bankBranchName,jdbcType=VARCHAR} ||'%'
          </if>
          order by lbnk_nm
      ) where rownum &lt;= 10
  </select>


  <select id="selectByPage" resultMap="BaseResultMap"
          parameterType="java.util.Map">
    select
    <include refid="Base_Column_List" />
    from (
    select A.*, rownum RN
    from (
    select * from PAS_BANK_BRANCH
    <where>
      <include refid="tradeQueryCondition"/>
    </where>
    order by CREATE_TIME desc
    ) A
    where rownum &lt;= #{endRowNo,jdbcType=DECIMAL}
    )
    where RN &gt;=
    #{beginRowNo,jdbcType=DECIMAL}
  </select>

  <select id="selectCount" resultType="java.lang.Integer"
          parameterType="java.util.Map">
    select count(*)
    from PAS_BANK_BRANCH
    <where>
      <include refid="tradeQueryCondition"/>
    </where>
  </select>

  <sql id = "tradeQueryCondition">
    <if test="beginTime != null">
      AND CREATE_TIME <![CDATA[ >= ]]>
      #{beginTime,jdbcType=TIMESTAMP}
    </if>
    <if test="endTime != null">
      AND CREATE_TIME <![CDATA[ <= ]]>
      #{endTime,jdbcType=TIMESTAMP}
    </if>
    <if test="updateBeginTime != null ">
      AND UPDATE_TIME <![CDATA[ >= ]]>
      #{updateBeginTime,jdbcType=TIMESTAMP}
    </if>
    <if test="updateEndTime != null">
      AND UPDATE_TIME <![CDATA[ <= ]]>
      #{updateEndTime,jdbcType=TIMESTAMP}
    </if>
    <if test="lbnkNo != null and lbnkNo != ''" >
      AND LBNK_NO like concat('%',concat(#{lbnkNo},'%'))
    </if>
    <if test="lbnkNm != null and lbnkNm != ''" >
      AND LBNK_NM like concat('%',concat(#{lbnkNm},'%'))
    </if>
  </sql>
</mapper>