<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.pas.pas.dao.PayMethodMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.pas.domain.PayMethod" >
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="PAY_METHOD" property="payMethod" jdbcType="VARCHAR" />
    <result column="PAY_METHOD_NAME" property="payMethodName" jdbcType="VARCHAR" />
    <result column="CREATOR" property="creator" jdbcType="VARCHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UPDATOR" property="updator" jdbcType="VARCHAR" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="CREDIT_PAY" property="creditPay" jdbcType="CHAR" />
    <result column="DEBIT_PAY" property="debitPay" jdbcType="CHAR" />
    <result column="PUBLIC_ACCOUNT" property="publicAccount" jdbcType="CHAR" />
    <result column="PRIVATE_ACCOUNT" property="privateAccount" jdbcType="CHAR" />
    <result column="STATE" property="state" jdbcType="CHAR" />
    <result column="ALLOW_CONFIG_APP_ID" property="allowConfigAppId" jdbcType="CHAR" />
    <result column="NEED_APPLY_IN_INSTITUTION" property="needApplyInInstitution" jdbcType="CHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, PAY_METHOD, PAY_METHOD_NAME, CREATOR, CREATE_TIME, UPDATOR, UPDATE_TIME, CREDIT_PAY, 
    DEBIT_PAY, PUBLIC_ACCOUNT, PRIVATE_ACCOUNT, STATE, ALLOW_CONFIG_APP_ID, NEED_APPLY_IN_INSTITUTION
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from PAS_PAY_METHOD
    where ID = #{id,jdbcType=DECIMAL}
  </select>

  <select id="selectByBusinessCode" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select PAS_PAY_METHOD.*
    from PAS_PAY_METHOD , PAS_BIZ_PAY_METHOD
    where PAS_PAY_METHOD.PAY_METHOD = PAS_BIZ_PAY_METHOD.PAY_METHOD_CODE
    and PAS_BIZ_PAY_METHOD.BUSINESS_CODE= #{businessCode,jdbcType=DECIMAL}
  </select>
  <select id="selectByBusinessCodes" resultType="java.util.Map" parameterType="java.util.List" >
    select PAS_PAY_METHOD.PAY_METHOD, PAS_PAY_METHOD.PAY_METHOD_NAME, PAS_BIZ_PAY_METHOD.BUSINESS_CODE, PAS_BUSINESS.NAME
    from PAS_PAY_METHOD , PAS_BIZ_PAY_METHOD, PAS_BUSINESS
    where PAS_PAY_METHOD.PAY_METHOD = PAS_BIZ_PAY_METHOD.PAY_METHOD_CODE
    and PAS_BUSINESS.CODE = PAS_BIZ_PAY_METHOD.BUSINESS_CODE
    and PAS_BIZ_PAY_METHOD.BUSINESS_CODE in
    <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
      #{item.businessCode,jdbcType=VARCHAR}
    </foreach>
  </select>
  <select id="selectBySelective" resultMap="BaseResultMap" parameterType="com.epaylinks.efps.pas.pas.domain.PayMethod" >
    select
    <include refid="Base_Column_List" />
    from PAS_PAY_METHOD
    <where>
      <if test="id != null and id != ''" >
        AND ID = #{id,jdbcType=DECIMAL}
      </if>
      <if test="payMethod != null and payMethod != ''" >
        AND PAY_METHOD = #{payMethod,jdbcType=VARCHAR}
      </if>
      <if test="payMethodName != null and payMethodName != ''" >
        AND PAY_METHOD_NAME = #{payMethodName,jdbcType=VARCHAR}
      </if>
      <if test="creator != null and creator != ''" >
        AND CREATOR = #{creator,jdbcType=VARCHAR}
      </if>
      <if test="createTime != null" >
        AND CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updator != null and updator != ''" >
        AND UPDATOR = #{updator,jdbcType=VARCHAR}
      </if>
      <if test="updateTime != null" >
        AND UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
      </if>
      <if test="creditPay != null and creditPay != ''" >
        AND CREDIT_PAY = #{creditPay,jdbcType=CHAR}
      </if>
      <if test="debitPay != null and debitPay != ''" >
        AND DEBIT_PAY = #{debitPay,jdbcType=CHAR}
      </if>
      <if test="publicAccount != null and publicAccount != ''" >
        AND PUBLIC_ACCOUNT = #{publicAccount,jdbcType=CHAR}
      </if>
      <if test="privateAccount != null and privateAccount != ''" >
        AND PRIVATE_ACCOUNT = #{privateAccount,jdbcType=CHAR}
      </if>
      <if test="state != null and state != ''" >
        AND STATE = #{state,jdbcType=CHAR}
      </if>
      <if test="allowConfigAppId != null and allowConfigAppId != ''" >
        AND ALLOW_CONFIG_APP_ID = #{allowConfigAppId,jdbcType=CHAR}
      </if>
      <if test="needApplyInInstitution != null and needApplyInInstitution != ''" >
        AND NEED_APPLY_IN_INSTITUTION = #{needApplyInInstitution,jdbcType=CHAR}
      </if>
    </where>
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from PAS_PAY_METHOD
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.pas.domain.PayMethod" >
    insert into PAS_PAY_METHOD (ID, PAY_METHOD, PAY_METHOD_NAME, 
      CREATOR, CREATE_TIME, UPDATOR, 
      UPDATE_TIME, CREDIT_PAY, DEBIT_PAY, 
      PUBLIC_ACCOUNT, PRIVATE_ACCOUNT, STATE, 
      ALLOW_CONFIG_APP_ID, NEED_APPLY_IN_INSTITUTION)
    values (#{id,jdbcType=DECIMAL}, #{payMethod,jdbcType=VARCHAR}, #{payMethodName,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updator,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{creditPay,jdbcType=CHAR}, #{debitPay,jdbcType=CHAR}, 
      #{publicAccount,jdbcType=CHAR}, #{privateAccount,jdbcType=CHAR}, #{state,jdbcType=CHAR}, 
      #{allowConfigAppId,jdbcType=CHAR}, #{needApplyInInstitution,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.pas.domain.PayMethod" >
    insert into PAS_PAY_METHOD
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null and id != ''" >
        ID,
      </if>
      <if test="payMethod != null and payMethod != ''" >
        PAY_METHOD,
      </if>
      <if test="payMethodName != null and payMethodName != ''" >
        PAY_METHOD_NAME,
      </if>
      <if test="creator != null and creator != ''" >
        CREATOR,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="updator != null and updator != ''" >
        UPDATOR,
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME,
      </if>
      <if test="creditPay != null and creditPay != ''" >
        CREDIT_PAY,
      </if>
      <if test="debitPay != null and debitPay != ''" >
        DEBIT_PAY,
      </if>
      <if test="publicAccount != null and publicAccount != ''" >
        PUBLIC_ACCOUNT,
      </if>
      <if test="privateAccount != null and privateAccount != ''" >
        PRIVATE_ACCOUNT,
      </if>
      <if test="state != null and state != ''" >
        STATE,
      </if>
      <if test="allowConfigAppId != null and allowConfigAppId != ''" >
        ALLOW_CONFIG_APP_ID,
      </if>
      <if test="needApplyInInstitution != null and needApplyInInstitution != ''" >
        NEED_APPLY_IN_INSTITUTION,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null and id != ''" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="payMethod != null and payMethod != ''" >
        #{payMethod,jdbcType=VARCHAR},
      </if>
      <if test="payMethodName != null and payMethodName != ''" >
        #{payMethodName,jdbcType=VARCHAR},
      </if>
      <if test="creator != null and creator != ''" >
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null and updator != ''" >
        #{updator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creditPay != null and creditPay != ''" >
        #{creditPay,jdbcType=CHAR},
      </if>
      <if test="debitPay != null and debitPay != ''" >
        #{debitPay,jdbcType=CHAR},
      </if>
      <if test="publicAccount != null and publicAccount != ''" >
        #{publicAccount,jdbcType=CHAR},
      </if>
      <if test="privateAccount != null and privateAccount != ''" >
        #{privateAccount,jdbcType=CHAR},
      </if>
      <if test="state != null and state != ''" >
        #{state,jdbcType=CHAR},
      </if>
      <if test="allowConfigAppId != null and allowConfigAppId != ''" >
        #{allowConfigAppId,jdbcType=CHAR},
      </if>
      <if test="needApplyInInstitution != null and needApplyInInstitution != ''" >
        #{needApplyInInstitution,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.pas.domain.PayMethod" >
    update PAS_PAY_METHOD
    <set >
      <if test="payMethod != null and payMethod != ''" >
        PAY_METHOD = #{payMethod,jdbcType=VARCHAR},
      </if>
      <if test="payMethodName != null and payMethodName != ''" >
        PAY_METHOD_NAME = #{payMethodName,jdbcType=VARCHAR},
      </if>
      <if test="creator != null and creator != ''" >
        CREATOR = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null and updator != ''" >
        UPDATOR = #{updator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creditPay != null and creditPay != ''" >
        CREDIT_PAY = #{creditPay,jdbcType=CHAR},
      </if>
      <if test="debitPay != null and debitPay != ''" >
        DEBIT_PAY = #{debitPay,jdbcType=CHAR},
      </if>
      <if test="publicAccount != null and publicAccount != ''" >
        PUBLIC_ACCOUNT = #{publicAccount,jdbcType=CHAR},
      </if>
      <if test="privateAccount != null and privateAccount != ''" >
        PRIVATE_ACCOUNT = #{privateAccount,jdbcType=CHAR},
      </if>
      <if test="state != null and state != ''" >
        STATE = #{state,jdbcType=CHAR},
      </if>
      <if test="allowConfigAppId != null and allowConfigAppId != ''" >
        ALLOW_CONFIG_APP_ID = #{allowConfigAppId,jdbcType=CHAR},
      </if>
      <if test="needApplyInInstitution != null and needApplyInInstitution != ''" >
        NEED_APPLY_IN_INSTITUTION = #{needApplyInInstitution,jdbcType=CHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.pas.domain.PayMethod" >
    update PAS_PAY_METHOD
    set PAY_METHOD = #{payMethod,jdbcType=VARCHAR},
      PAY_METHOD_NAME = #{payMethodName,jdbcType=VARCHAR},
      CREATOR = #{creator,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATOR = #{updator,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      CREDIT_PAY = #{creditPay,jdbcType=CHAR},
      DEBIT_PAY = #{debitPay,jdbcType=CHAR},
      PUBLIC_ACCOUNT = #{publicAccount,jdbcType=CHAR},
      PRIVATE_ACCOUNT = #{privateAccount,jdbcType=CHAR},
      STATE = #{state,jdbcType=CHAR},
      ALLOW_CONFIG_APP_ID = #{allowConfigAppId,jdbcType=CHAR},
      NEED_APPLY_IN_INSTITUTION = #{needApplyInInstitution,jdbcType=CHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>

  <select id="getInstitutionCodeByPayMethods" resultType="java.lang.String" >
    SELECT distinct t4.institution_code
    FROM  clr_pay_method     t1,
          clr_channel_method t2,
          clr_pay_channel    t3,
          clr_institution    t4
    WHERE t1.id = t2.pay_method_id
          AND t2.pay_channel_id = t3.id
          AND t3.institution_id = t4.id
          AND t1.pay_method IN
    <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <select id="selectByState" resultMap="BaseResultMap">
  	select 
    <include refid="Base_Column_List" />
    from PAS_PAY_METHOD
    where STATE = #{state , jdbcType = VARCHAR}
  </select>
  
  <select id="selectByPayMethodCodes" resultType="java.lang.String">
  	select PAY_METHOD_NAME 
  	from PAS_PAY_METHOD 
  	where PAY_METHOD in 
  	<foreach collection="payMethodCodes" open="(" close=")" separator="," item="payMethodCode">
  		#{payMethodCode , jdbcType = VARCHAR}
  	</foreach>
  </select>

  <select id="queryPayMethodByBusinessCategory" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from PAS_PAY_METHOD t
    where t.pay_method in(
      select p.pay_method_code from pas_biz_pay_method p where p.business_code in(
        select k.code from pas_business k where k.business_category=#{businessCategory,jdbcType=VARCHAR}
      )
    )
  </select>

  <select id="selectAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from PAS_PAY_METHOD
  </select>

</mapper>