<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.pas.pas.dao.LoginReportMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.pas.domain.LoginReport" >
    <id column="REPORT_ID" property="reportId" jdbcType="DECIMAL" />
    <result column="LOGIN_ACCOUNT" property="loginAccount" jdbcType="VARCHAR" />
    <result column="LOGIN_ROLE" property="loginRole" jdbcType="VARCHAR" />
    <result column="NAME" property="name" jdbcType="VARCHAR" />
    <result column="CUSTOMER_CODE" property="customerCode" jdbcType="VARCHAR" />
    <result column="LOGIN_IP" property="loginIp" jdbcType="VARCHAR" />
    <result column="USER_TYPE" property="userType" jdbcType="CHAR" />
    <result column="LOGIN_NUM" property="loginNum" jdbcType="DECIMAL" />
    <result column="LOGIN_TIME" property="loginTime" jdbcType="VARCHAR" />
    <result column="REPORT_STATE" property="reportState" jdbcType="CHAR" />
    <result column="REPORT_DESC" property="reportDesc" jdbcType="VARCHAR" />
    <result column="EXCEPTION_HANDLE" property="exceptionHandle" jdbcType="VARCHAR" />
    <result column="OPERATOR" property="operator" jdbcType="DECIMAL" />
    <result column="HANDLE_TIME" property="handleTime" jdbcType="TIMESTAMP" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    REPORT_ID, LOGIN_ACCOUNT, LOGIN_ROLE, NAME, CUSTOMER_CODE, LOGIN_IP, USER_TYPE, LOGIN_NUM, 
    LOGIN_TIME, REPORT_STATE, REPORT_DESC, EXCEPTION_HANDLE, OPERATOR, HANDLE_TIME, CREATE_TIME
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from PAS_LOGIN_REPORT
    where REPORT_ID = #{reportId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from PAS_LOGIN_REPORT
    where REPORT_ID = #{reportId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.pas.domain.LoginReport" >
    insert into PAS_LOGIN_REPORT (REPORT_ID, LOGIN_ACCOUNT, LOGIN_ROLE, 
      NAME, CUSTOMER_CODE, LOGIN_IP, 
      USER_TYPE, LOGIN_NUM, LOGIN_TIME, 
      REPORT_STATE, REPORT_DESC, EXCEPTION_HANDLE, 
      OPERATOR, HANDLE_TIME, CREATE_TIME
      )
    values (#{reportId,jdbcType=DECIMAL}, #{loginAccount,jdbcType=VARCHAR}, #{loginRole,jdbcType=VARCHAR}, 
      #{name,jdbcType=VARCHAR}, #{customerCode,jdbcType=VARCHAR}, #{loginIp,jdbcType=VARCHAR}, 
      #{userType,jdbcType=CHAR}, #{loginNum,jdbcType=DECIMAL}, #{loginTime,jdbcType=VARCHAR}, 
      #{reportState,jdbcType=CHAR}, #{reportDesc,jdbcType=VARCHAR}, #{exceptionHandle,jdbcType=VARCHAR}, 
      #{operator,jdbcType=DECIMAL}, #{handleTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.pas.domain.LoginReport" >
    insert into PAS_LOGIN_REPORT
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="reportId != null" >
        REPORT_ID,
      </if>
      <if test="loginAccount != null" >
        LOGIN_ACCOUNT,
      </if>
      <if test="loginRole != null" >
        LOGIN_ROLE,
      </if>
      <if test="name != null" >
        NAME,
      </if>
      <if test="customerCode != null" >
        CUSTOMER_CODE,
      </if>
      <if test="loginIp != null" >
        LOGIN_IP,
      </if>
      <if test="userType != null" >
        USER_TYPE,
      </if>
      <if test="loginNum != null" >
        LOGIN_NUM,
      </if>
      <if test="loginTime != null" >
        LOGIN_TIME,
      </if>
      <if test="reportState != null" >
        REPORT_STATE,
      </if>
      <if test="reportDesc != null" >
        REPORT_DESC,
      </if>
      <if test="exceptionHandle != null" >
        EXCEPTION_HANDLE,
      </if>
      <if test="operator != null" >
        OPERATOR,
      </if>
      <if test="handleTime != null" >
        HANDLE_TIME,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="reportId != null" >
        #{reportId,jdbcType=DECIMAL},
      </if>
      <if test="loginAccount != null" >
        #{loginAccount,jdbcType=VARCHAR},
      </if>
      <if test="loginRole != null" >
        #{loginRole,jdbcType=VARCHAR},
      </if>
      <if test="name != null" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="customerCode != null" >
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="loginIp != null" >
        #{loginIp,jdbcType=VARCHAR},
      </if>
      <if test="userType != null" >
        #{userType,jdbcType=CHAR},
      </if>
      <if test="loginNum != null" >
        #{loginNum,jdbcType=DECIMAL},
      </if>
      <if test="loginTime != null" >
        #{loginTime,jdbcType=VARCHAR},
      </if>
      <if test="reportState != null" >
        #{reportState,jdbcType=CHAR},
      </if>
      <if test="reportDesc != null" >
        #{reportDesc,jdbcType=VARCHAR},
      </if>
      <if test="exceptionHandle != null" >
        #{exceptionHandle,jdbcType=VARCHAR},
      </if>
      <if test="operator != null" >
        #{operator,jdbcType=DECIMAL},
      </if>
      <if test="handleTime != null" >
        #{handleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.pas.domain.LoginReport" >
    update PAS_LOGIN_REPORT
    <set >
      <if test="loginAccount != null" >
        LOGIN_ACCOUNT = #{loginAccount,jdbcType=VARCHAR},
      </if>
      <if test="loginRole != null" >
        LOGIN_ROLE = #{loginRole,jdbcType=VARCHAR},
      </if>
      <if test="name != null" >
        NAME = #{name,jdbcType=VARCHAR},
      </if>
      <if test="customerCode != null" >
        CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="loginIp != null" >
        LOGIN_IP = #{loginIp,jdbcType=VARCHAR},
      </if>
      <if test="userType != null" >
        USER_TYPE = #{userType,jdbcType=CHAR},
      </if>
      <if test="loginNum != null" >
        LOGIN_NUM = #{loginNum,jdbcType=DECIMAL},
      </if>
      <if test="loginTime != null" >
        LOGIN_TIME = #{loginTime,jdbcType=VARCHAR},
      </if>
      <if test="reportState != null" >
        REPORT_STATE = #{reportState,jdbcType=CHAR},
      </if>
      <if test="reportDesc != null" >
        REPORT_DESC = #{reportDesc,jdbcType=VARCHAR},
      </if>
      <if test="exceptionHandle != null" >
        EXCEPTION_HANDLE = #{exceptionHandle,jdbcType=VARCHAR},
      </if>
      <if test="operator != null" >
        OPERATOR = #{operator,jdbcType=DECIMAL},
      </if>
      <if test="handleTime != null" >
        HANDLE_TIME = #{handleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where REPORT_ID = #{reportId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.pas.domain.LoginReport" >
    update PAS_LOGIN_REPORT
    set LOGIN_ACCOUNT = #{loginAccount,jdbcType=VARCHAR},
      LOGIN_ROLE = #{loginRole,jdbcType=VARCHAR},
      NAME = #{name,jdbcType=VARCHAR},
      CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      LOGIN_IP = #{loginIp,jdbcType=VARCHAR},
      USER_TYPE = #{userType,jdbcType=CHAR},
      LOGIN_NUM = #{loginNum,jdbcType=DECIMAL},
      LOGIN_TIME = #{loginTime,jdbcType=VARCHAR},
      REPORT_STATE = #{reportState,jdbcType=CHAR},
      REPORT_DESC = #{reportDesc,jdbcType=VARCHAR},
      EXCEPTION_HANDLE = #{exceptionHandle,jdbcType=VARCHAR},
      OPERATOR = #{operator,jdbcType=DECIMAL},
      HANDLE_TIME = #{handleTime,jdbcType=TIMESTAMP},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
    where REPORT_ID = #{reportId,jdbcType=DECIMAL}
  </update>

  <select id="reportPage" parameterType="java.util.Map" resultType="com.epaylinks.efps.pas.pas.controller.response.LoginReportResponse" >
    select *
    from (
      select A.*,rownum RN
      from (
        select
            rt.REPORT_ID reportId,
            rt.LOGIN_ACCOUNT loginAccount,
            rt.LOGIN_ROLE loginRole,
            rt.NAME name,
            rt.CUSTOMER_CODE customerCode,
            rt.LOGIN_IP loginIp,
            rt.USER_TYPE userType,
            rt.LOGIN_NUM loginNum,
            rt.LOGIN_TIME loginTime,
            rt.REPORT_STATE reportState,
            rt.REPORT_DESC reportDesc,
            rt.EXCEPTION_HANDLE exceptionHandle,
            nvl(u.NAME,u.REAL_NAME) operator,
            to_char(rt.HANDLE_TIME,'yyyy-MM-dd hh24:mi:ss') handleTime
        from pas_login_report rt
        left join pas_user u on rt.OPERATOR = u.USER_ID
        where 1=1
        <if test="loginAccount != null and loginAccount != ''">
          and rt.LOGIN_ACCOUNT = #{loginAccount,jdbcType=VARCHAR}
        </if>
        <if test="userType != null and userType != ''">
          and rt.USER_TYPE = #{userType,jdbcType=CHAR}
        </if>
        <if test="startTime != null and startTime != ''">
          and rt.LOGIN_TIME &gt;= #{startTime,jdbcType=VARCHAR}
        </if>
        <if test="endTime != null and endTime != ''">
          and rt.LOGIN_TIME &lt;= #{endTime,jdbcType=VARCHAR}
        </if>
        <if test="reportState != null and reportState != ''">
          and rt.REPORT_STATE = #{reportState,jdbcType=VARCHAR}
        </if>
        order by rt.REPORT_ID desc
      )A where rownum &lt;= #{endRowNo}
    ) where RN &gt;= #{beginRowNo}
  </select>

  <select id="countReportPage" parameterType="java.util.Map" resultType="java.lang.Integer" >
    select count(*)
    from pas_login_report rt
    where 1=1
    <if test="loginAccount != null and loginAccount != ''">
      and rt.LOGIN_ACCOUNT = #{loginAccount,jdbcType=VARCHAR}
    </if>
    <if test="userType != null and userType != ''">
      and rt.USER_TYPE = #{userType,jdbcType=CHAR}
    </if>
    <if test="startTime != null and startTime != ''">
      and rt.LOGIN_TIME &gt;= #{startTime,jdbcType=VARCHAR}
    </if>
    <if test="endTime != null and endTime != ''">
      and rt.LOGIN_TIME &lt;= #{endTime,jdbcType=VARCHAR}
    </if>
    <if test="reportState != null and reportState != ''">
      and rt.REPORT_STATE = #{reportState,jdbcType=VARCHAR}
    </if>
  </select>

  <select id="queryReportSeq" resultType="java.lang.Long">
    select seq_pas_login_report.nextval from dual
  </select>
</mapper>