<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.pas.pas.dao.BkBankMapper">
    <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.pas.model.BkBank">
        <result column="ID" jdbcType="DECIMAL" property="id"/>
        <result column="BANK_ID" jdbcType="VARCHAR" property="bankId"/>
        <result column="BANK_CODE" jdbcType="VARCHAR" property="bankCode"/>
        <result column="NEW_BANK_CODE" jdbcType="VARCHAR" property="newBankCode"/>
        <result column="BANK_NAME" jdbcType="VARCHAR" property="bankName"/>
        <result column="FULL_NAME" jdbcType="VARCHAR" property="fullName"/>
        <result column="INSTITUTION_CODE" jdbcType="VARCHAR" property="institutionCode"/>
        <result column="ISSUE_BANK_NO" jdbcType="VARCHAR" property="issueBankNo"/>
        <result column="IS_AREA_BANK" jdbcType="DECIMAL" property="isAreaBank"/>
        <result column="FLAG" jdbcType="VARCHAR" property="flag"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="OPERATOR_ID" jdbcType="VARCHAR" property="operatorId"/>
    </resultMap>
    <sql id="Base_Column_List">
    ID, BANK_ID, BANK_CODE, NEW_BANK_CODE, BANK_NAME, FULL_NAME, INSTITUTION_CODE, ISSUE_BANK_NO, IS_AREA_BANK, FLAG, CREATE_TIME,
    UPDATE_TIME, OPERATOR_ID
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from BK_BANK
        where ID = #{id,jdbcType=DECIMAL}
    </select>

    <select id="selectByBankId" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from BK_BANK
        where bank_id = #{bankId,jdbcType=DECIMAL}
    </select>

  <insert id="insert" parameterType="com.epaylinks.efps.pas.pas.model.BkBank">
    insert into BK_BANK (ID, BANK_ID, BANK_CODE, 
      NEW_BANK_CODE, BANK_NAME, FULL_NAME, 
      INSTITUTION_CODE, ISSUE_BANK_NO, IS_AREA_BANK, 
      FLAG, CREATE_TIME, UPDATE_TIME, 
      OPERATOR_ID)
    values (#{id,jdbcType=DECIMAL}, #{bankId,jdbcType=VARCHAR}, #{bankCode,jdbcType=VARCHAR}, 
      #{newBankCode,jdbcType=VARCHAR}, #{bankName,jdbcType=VARCHAR}, #{fullName,jdbcType=VARCHAR}, 
      #{institutionCode,jdbcType=VARCHAR}, #{issueBankNo,jdbcType=VARCHAR}, #{isAreaBank,jdbcType=DECIMAL}, 
      #{flag,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{operatorId,jdbcType=VARCHAR})
  </insert>

    <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.pas.model.BkBank">
        insert into BK_BANK
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                ID,
            </if>
            <if test="bankId != null">
                BANK_ID,
            </if>
            <if test="bankCode != null">
                BANK_CODE,
            </if>
            <if test="newBankCode != null">
                NEW_BANK_CODE,
            </if>
            <if test="bankName != null">
                BANK_NAME,
            </if>
            <if test="fullName != null">
                FULL_NAME,
            </if>
            <if test="institutionCode != null">
                INSTITUTION_CODE,
            </if>
            <if test="issueBankNo != null">
                ISSUE_BANK_NO,
            </if>
            <if test="isAreaBank != null">
                IS_AREA_BANK,
            </if>
            <if test="flag != null">
                FLAG,
            </if>
            <if test="createTime != null">
                CREATE_TIME,
            </if>
            <if test="updateTime != null">
                UPDATE_TIME,
            </if>
            <if test="operatorId != null">
                OPERATOR_ID,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=DECIMAL},
            </if>
            <if test="bankId != null">
                #{bankId,jdbcType=VARCHAR},
            </if>
            <if test="bankCode != null">
                #{bankCode,jdbcType=VARCHAR},
            </if>
            <if test="newBankCode != null">
                #{newBankCode,jdbcType=VARCHAR},
            </if>
            <if test="bankName != null">
                #{bankName,jdbcType=VARCHAR},
            </if>
            <if test="fullName != null">
                #{fullName,jdbcType=VARCHAR},
            </if>
            <if test="institutionCode != null">
                #{institutionCode,jdbcType=VARCHAR},
            </if>
            <if test="issueBankNo != null">
                #{issueBankNo,jdbcType=VARCHAR},
            </if>
            <if test="isAreaBank != null">
                #{isAreaBank,jdbcType=DECIMAL},
            </if>
            <if test="flag != null">
                #{flag,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="operatorId != null">
                #{operatorId,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.pas.model.BkBank">
        update BK_BANK
        <set>
            <if test="bankId != null">
                BANK_ID = #{bankId,jdbcType=VARCHAR},
            </if>
            <if test="bankCode != null">
                BANK_CODE = #{bankCode,jdbcType=VARCHAR},
            </if>
            <if test="newBankCode != null">
                NEW_BANK_CODE = #{newBankCode,jdbcType=VARCHAR},
            </if>
            <if test="bankName != null">
                BANK_NAME = #{bankName,jdbcType=VARCHAR},
            </if>
            <if test="fullName != null">
                FULL_NAME = #{fullName,jdbcType=VARCHAR},
            </if>
            <if test="institutionCode != null">
                INSTITUTION_CODE = #{institutionCode,jdbcType=VARCHAR},
            </if>
            <if test="issueBankNo != null">
                ISSUE_BANK_NO = #{issueBankNo,jdbcType=VARCHAR},
            </if>
            <if test="isAreaBank != null">
                IS_AREA_BANK = #{isAreaBank,jdbcType=DECIMAL},
            </if>
            <if test="flag != null">
                FLAG = #{flag,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="operatorId != null">
                OPERATOR_ID = #{operatorId,jdbcType=VARCHAR},
            </if>
        </set>
        where ID = #{id,jdbcType=DECIMAL}
    </update>

    <select id="selectBankPage" resultMap="BaseResultMap"
            parameterType="java.util.Map">
        select
        <include refid="Base_Column_List"/>
        from (
        select A.*, rownum RN
        from (
        select * from BK_BANK
        <where>
            <include refid="queryCondition"/>
        </where>
        order by CREATE_TIME desc
        ) A
        where rownum &lt;= #{endRowNo,jdbcType=DECIMAL}
        )
        where RN &gt;=
        #{beginRowNo,jdbcType=DECIMAL}
    </select>

    <select id="selectBankNotPage" resultMap="BaseResultMap"
            parameterType="java.util.Map">
        select
        <include refid="Base_Column_List"/>
        from BK_BANK
        <where>
            <include refid="queryCondition"/>
        </where>
        order by CREATE_TIME desc
    </select>

    <select id="selectCountByMap" resultType="java.lang.Integer"
            parameterType="java.util.Map">
        select count(*)
        from BK_BANK
        <where>
            <include refid="queryCondition"/>
        </where>
    </select>

    <sql id="queryCondition">
        <if test="beginTime != null">
            AND CREATE_TIME <![CDATA[ >= ]]>
            #{beginTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            AND CREATE_TIME <![CDATA[ <= ]]>
            #{endTime,jdbcType=TIMESTAMP}
        </if>
        <if test="bankId != null and bankId != ''">
            AND BANK_ID = #{bankId,jdbcType=VARCHAR}
        </if>
        <if test="bankName != null and bankName != ''">
            AND BANK_NAME like concat('%',concat(#{bankName},'%'))
        </if>
        <if test="bankCode != null and bankCode != ''">
            AND BANK_CODE like concat('%',concat(#{bankCode,jdbcType=VARCHAR},'%'))
        </if>
        <if test="flag != null and flag != ''">
            AND FLAG = #{flag,jdbcType=VARCHAR}
        </if>
        <if test="institutionCode != null and institutionCode != ''">
            AND INSTITUTION_CODE = #{institutionCode,jdbcType=VARCHAR}
        </if>
        <if test="isAreaBank != null">
            AND IS_AREA_BANK = #{isAreaBank,jdbcType=DECIMAL}
        </if>
        <if test="issueBankNo != null and issueBankNo != ''">
            AND ISSUE_BANK_NO = #{issueBankNo,jdbcType=VARCHAR}
        </if>
    </sql>

    <select id="selectByBankCodeOrBankName" resultMap="BaseResultMap"
            parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from BK_BANK
        where
        1=1
        <if test="bankName != null and bankName != ''">
            AND (BANK_NAME like concat('%',concat(#{bankName},'%'))
            OR FULL_NAME like concat('%',concat(#{bankName},'%')))
        </if>
        <if test="bankCode != null and bankCode != ''">
            AND BANK_CODE = #{bankCode,jdbcType=VARCHAR}
        </if>
        and flag = '1'
        order by CREATE_TIME desc
    </select>

    <select id="selectByIssueBankNo" resultMap="BaseResultMap"
            parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from BK_BANK
        where
        instr(ISSUE_BANK_NO, #{issueBankNo,jdbcType=VARCHAR}) > 0
        and flag = '1'
        order by CREATE_TIME desc
    </select>
</mapper>