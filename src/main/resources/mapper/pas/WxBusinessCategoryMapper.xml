<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.pas.pas.dao.WxBusinessCategoryMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.pas.domain.WxBusinessCategory" >
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="PARENT_NAV" property="parentNav" jdbcType="VARCHAR" />
    <result column="NAV" property="nav" jdbcType="VARCHAR" />
    <result column="CATEGORY_SYS" property="categorySys" jdbcType="VARCHAR" />
    <result column="CATEGORY" property="category" jdbcType="VARCHAR" />
    <result column="UNION_MCC" property="unionMcc" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, PARENT_NAV, NAV, CATEGORY_SYS, CATEGORY, UNION_MCC
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from PAS_WX_BUSINESS_CATEGORY
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from PAS_WX_BUSINESS_CATEGORY
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.pas.domain.WxBusinessCategory" >
    insert into PAS_WX_BUSINESS_CATEGORY (ID, PARENT_NAV, NAV, 
      CATEGORY_SYS, CATEGORY, UNION_MCC)
    values (#{id,jdbcType=DECIMAL}, #{parentNav,jdbcType=VARCHAR}, #{nav,jdbcType=VARCHAR}, 
      #{categorySys,jdbcType=VARCHAR}, #{category,jdbcType=VARCHAR}, #{unionMcc,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.pas.domain.WxBusinessCategory" >
    insert into PAS_WX_BUSINESS_CATEGORY
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="parentNav != null" >
        PARENT_NAV,
      </if>
      <if test="nav != null" >
        NAV,
      </if>
      <if test="categorySys != null" >
        CATEGORY_SYS,
      </if>
      <if test="category != null" >
        CATEGORY,
      </if>
      <if test="unionMcc != null" >
        UNION_MCC,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="parentNav != null" >
        #{parentNav,jdbcType=VARCHAR},
      </if>
      <if test="nav != null" >
        #{nav,jdbcType=VARCHAR},
      </if>
      <if test="categorySys != null" >
        #{categorySys,jdbcType=VARCHAR},
      </if>
      <if test="category != null" >
        #{category,jdbcType=VARCHAR},
      </if>
      <if test="unionMcc != null" >
        #{unionMcc,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.pas.domain.WxBusinessCategory" >
    update PAS_WX_BUSINESS_CATEGORY
    <set >
      <if test="parentNav != null" >
        PARENT_NAV = #{parentNav,jdbcType=VARCHAR},
      </if>
      <if test="nav != null" >
        NAV = #{nav,jdbcType=VARCHAR},
      </if>
      <if test="categorySys != null" >
        CATEGORY_SYS = #{categorySys,jdbcType=VARCHAR},
      </if>
      <if test="category != null" >
        CATEGORY = #{category,jdbcType=VARCHAR},
      </if>
      <if test="unionMcc != null" >
        UNION_MCC = #{unionMcc,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.pas.domain.WxBusinessCategory" >
    update PAS_WX_BUSINESS_CATEGORY
    set PARENT_NAV = #{parentNav,jdbcType=VARCHAR},
      NAV = #{nav,jdbcType=VARCHAR},
      CATEGORY_SYS = #{categorySys,jdbcType=VARCHAR},
      CATEGORY = #{category,jdbcType=VARCHAR},
      UNION_MCC = #{unionMcc,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <insert id="batchInsert" parameterType="java.util.List">
    INSERT ALL
    <foreach collection="records" item="item" index="index">
      into PAS_WX_BUSINESS_CATEGORY (ID, PARENT_NAV, NAV, CATEGORY_SYS, CATEGORY)
      values (#{item.id,jdbcType=DECIMAL}, #{item.parentNav,jdbcType=VARCHAR}, #{item.nav,jdbcType=VARCHAR},
      #{item.categorySys,jdbcType=VARCHAR}, #{item.category,jdbcType=VARCHAR})
    </foreach>
    SELECT * FROM DUAL
  </insert>
  <select id="selectLevel1s" resultType="String">
    select distinct PARENT_NAV
    from PAS_WX_BUSINESS_CATEGORY
  </select>
  <select id="selectLevel2s" resultType="String">
    select distinct NAV
    from PAS_WX_BUSINESS_CATEGORY
    where PARENT_NAV = #{level1,jdbcType=VARCHAR}
  </select>
  <select id="selectLevel3s" resultType="String">
    select distinct CATEGORY_SYS
    from PAS_WX_BUSINESS_CATEGORY
    where PARENT_NAV = #{level1,jdbcType=VARCHAR} and NAV = #{level2,jdbcType=VARCHAR}
  </select>
  <select id="selectLevel4s" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from PAS_WX_BUSINESS_CATEGORY
    where PARENT_NAV = #{level1,jdbcType=VARCHAR} and NAV = #{level2,jdbcType=VARCHAR} and CATEGORY_SYS = #{level3,jdbcType=VARCHAR}
  </select>
  <select id="selectByUnionMcc" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from PAS_WX_BUSINESS_CATEGORY
    where union_mcc like '%' || #{unionMcc,jdbcType=VARCHAR} || '%'
    <if test="mccType != null" >
        and MCC_TYPE = #{mccType, jdbcType=DECIMAL}
    </if>
  </select>
</mapper>