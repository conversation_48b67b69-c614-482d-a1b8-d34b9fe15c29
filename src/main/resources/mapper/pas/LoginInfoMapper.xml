<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.pas.pas.dao.LoginInfoMapper">
    
    <!-- 根据用户名和用户类型查询是否存在 -->
	<select id="checkPasLoginInfoExist" resultType="Integer" parameterType="com.epaylinks.efps.pas.pas.domain.PasLoginInfo">
	  select error_count
	  from pas_login_info
	  where 1=1 
	  <if test="username != null and username !=''">
	  		and username = #{username,jdbcType=VARCHAR}
      </if> 
      <if test="usertype != null and usertype !=''">
          	and usertype = #{usertype,jdbcType=VARCHAR}
      </if>
	</select>
    
    <!-- 定时任务将登录错误次数清零 -->
    <update id="updateErrorRecordCount">
        UPDATE PAS_LOGIN_INFO
        <set>
            error_count = 0
        </set>
    </update>
    
    <!-- 新增登录信息-->
    <insert id="insertLoginInfo" parameterType="com.epaylinks.efps.pas.pas.domain.PasLoginInfo">
		INSERT INTO
		pas_Login_info(info_id,username,usertype,
		login_time,login_ip,error_count,
		create_time
		)
		VALUES(#{info_id,jdbcType=VARCHAR},#{username,jdbcType=VARCHAR},#{usertype,jdbcType=VARCHAR},
		to_date(#{login_time,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss'),#{login_ip,jdbcType=VARCHAR},#{error_count,jdbcType=VARCHAR},
		to_date(#{create_time,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
	
		)
	</insert>

	<!-- 更新登录信息-->
    <update id="updateLoginInfo" parameterType="com.epaylinks.efps.pas.pas.domain.PasLoginInfo">
        update pas_Login_info
        <set>
            login_time=to_date(#{login_time,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')   
            ,login_ip=#{login_ip,jdbcType=VARCHAR}       
            ,error_count=#{error_count,jdbcType=VARCHAR}
            ,update_time=to_date(#{update_time,jdbcType=VARCHAR},'yyyy/mm/dd hh24:mi:ss')
        </set>
	  		 where 1=1 
		  <if test="username != null and username !=''">
		  		and username = #{username,jdbcType=VARCHAR}
	      </if> 
	      <if test="usertype != null and usertype !=''">
	          	and usertype = #{usertype,jdbcType=VARCHAR}
	      </if>
    </update>

	<!-- 根据用户名和用户类型查询登录信息 -->
	<select id="searchLoginInfo" resultType="com.epaylinks.efps.pas.pas.domain.PasLoginInfo" parameterType="com.epaylinks.efps.pas.pas.domain.PasLoginInfo">
	  select username,
	  	usertype,
	  	to_char(login_time, 'yyyy-mm-dd hh24:mi:ss') as login_time,
	  	login_ip,
       	error_count
	  from pas_login_info
	  where 1=1 
	  <if test="username != null and username !=''">
	  		and username = #{username,jdbcType=VARCHAR}
      </if> 
      <if test="usertype != null and usertype !=''">
          	and usertype = #{usertype,jdbcType=VARCHAR}
      </if>
	</select>
	
    <!-- 根据用户名和用户类对登录次数清零 -->
    <update id="recoveryErrorCountByUsernameAndType" >
      update pas_login_info t
         set t.error_count = 0 , t.update_time = sysdate 
      where  username = #{username,jdbcType=VARCHAR}
            and usertype = #{usertype,jdbcType=VARCHAR}
    </update>
    
</mapper>