<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.pas.pas.dao.BusinessParamInstMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.pas.domain.BusinessParamInst">
    <id column="CODE" jdbcType="VARCHAR" property="code" />
    <id column="BUSINESS_INST_DB_ID" jdbcType="DECIMAL" property="businessInstDBId" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="TYPE" jdbcType="VARCHAR" property="type" />
    <result column="VALUE" jdbcType="VARCHAR" property="value" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="UPDATOR" jdbcType="VARCHAR" property="updator" />
    <result column="BUSINESS_EXAM_ID" jdbcType="VARCHAR" property="businessExamId" />
  </resultMap>
  <sql id="Base_Column_List">
    CODE, BUSINESS_EXAM_ID, NAME, TYPE, VALUE, CREATE_TIME, CREATOR, UPDATE_TIME, UPDATOR,BUSINESS_INST_DB_ID
  </sql>
  <select id="selectByPrimaryKey" parameterType="com.epaylinks.efps.pas.pas.domain.BusinessParamInstKey" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from PAS_BUSINESS_PARAM_INST
    where CODE = #{code,jdbcType=VARCHAR}
      and BUSINESS_INST_DB_ID = #{businessInstDBId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="com.epaylinks.efps.pas.pas.domain.BusinessParamInstKey">
    delete from PAS_BUSINESS_PARAM_INST
    where CODE = #{code,jdbcType=VARCHAR}
      and BUSINESS_INST_DB_ID = #{businessInstDBId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.pas.domain.BusinessParamInst">
    insert into PAS_BUSINESS_PARAM_INST (CODE, BUSINESS_EXAM_ID, NAME, 
      TYPE, VALUE, CREATE_TIME, 
      CREATOR, UPDATE_TIME, UPDATOR,BUSINESS_INST_DB_ID
      )
    values (#{code,jdbcType=VARCHAR}, #{businessExamId,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{type,jdbcType=VARCHAR}, #{value,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{creator,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{updator,jdbcType=VARCHAR},
      #{businessInstDBId,jdbcType=DECIMAL}
      )
  </insert>
 
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.pas.domain.BusinessParamInst">
    update PAS_BUSINESS_PARAM_INST
    <set>
      <if test="name != null">
        NAME = #{name,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        TYPE = #{type,jdbcType=VARCHAR},
      </if>
      <if test="value != null">
        VALUE = #{value,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null">
        UPDATOR = #{updator,jdbcType=VARCHAR},
      </if>
      <if test="businessExamId != null">
        BUSINESS_EXAM_ID = #{businessExamId,jdbcType=VARCHAR},
      </if>
    </set>
    where CODE = #{code,jdbcType=VARCHAR}
      and BUSINESS_INST_DB_ID = #{businessInstDBId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.pas.domain.BusinessParamInst">
    update PAS_BUSINESS_PARAM_INST
    set NAME = #{name,jdbcType=VARCHAR},
      TYPE = #{type,jdbcType=VARCHAR},
      VALUE = #{value,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      UPDATOR = #{updator,jdbcType=VARCHAR},
      BUSINESS_EXAM_ID = #{businessExamId,jdbcType=VARCHAR}
    where CODE = #{code,jdbcType=VARCHAR}
      and BUSINESS_INST_DB_ID = #{businessInstDBId,jdbcType=DECIMAL}
  </update>
  
  <update id="updateBusExamId">
  	update PAS_BUSINESS_PARAM_INST
    set BUSINESS_EXAM_ID = #{newBusExamId , jdbcType = VARCHAR} 
    where BUSINESS_INST_DB_ID = #{businessInstDBId , jdbcType = DECIMAL}
  </update>
  
  <select id="selectByBusinessInstDBId" resultMap = "BaseResultMap">
  	select 
    <include refid="Base_Column_List"/>
    from PAS_BUSINESS_PARAM_INST 
    where BUSINESS_INST_DB_ID = #{businessInstDBId , jdbcType = DECIMAL}
  </select>
  
  <select id="selectByBusinessInstDBIdAndCode" resultMap = "BaseResultMap">
  	select 
    <include refid="Base_Column_List"/>
    from PAS_BUSINESS_PARAM_INST 
    where BUSINESS_INST_DB_ID = #{businessId , jdbcType = DECIMAL} 
    and CODE = #{code , jdbcType = VARCHAR}
  </select>

  <delete id="deleteByBusinessExamId">
    delete from PAS_BUSINESS_PARAM_INST
    where BUSINESS_EXAM_ID = #{businessExamId,jdbcType=VARCHAR}
  </delete>

</mapper>