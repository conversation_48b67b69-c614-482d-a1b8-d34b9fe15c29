<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.pas.pas.dao.OperationLogMapper">
    
    <insert id="insert" parameterType="com.epaylinks.efps.pas.pas.domain.OperationLog">
        INSERT INTO OPERATION_LOG (LOG_ID,USER_NAME,OP_MODULE,OP_METHOD,OP_CONTENT,OP_TIME,CREATE_TIME
        <if test="realName != null and realName != ''">
            ,REAL_NAME
        </if>        )
        VALUES (#{logId},#{userName},#{opModule},#{opMethod},#{opContent},to_date(#{opTime},'yyyymmdd hh24miss'),#{createTime,jdbcType=TIMESTAMP}
        <if test="realName != null and realName != ''">
            ,#{realName}
        </if>
        )
    </insert>

    <select id="pageQueryTotal" resultType="int">
        SELECT COUNT(log_id) FROM OPERATION_LOG WHERE 1 = 1
        <if test="startTime != null and startTime != ''">
            AND op_time &gt;  to_date(#{startTime},'yyyymmddhh24miss')
        </if>
        <if test="endTime != null and endTime != ''">
            AND op_time &lt;= to_date(#{endTime},'yyyymmddhh24miss')
        </if>
        <if test="userName != null and userName != ''">
            AND user_name LIKE '%'||#{userName}||'%'
        </if>
        <if test="realName != null and realName != ''">
            AND real_name LIKE '%'||#{realName}||'%'
        </if>
        <if test="module != null and module != ''">
            AND OP_MODULE = #{module}
        </if>
    </select>

    <select id="pageQuery" resultType="com.epaylinks.efps.pas.pas.domain.OperationLog">
       SELECT * FROM (
        SELECT a.* ,rownum rn FROM (
        SELECT LOG_ID as "logId" , USER_NAME as "userName" ,REAL_NAME as "realName" ,OP_MODULE as "opModule",OP_METHOD as "opMethod",OP_CONTENT as "opContent",to_char(OP_TIME,'yyyy-mm-dd hh24:mi:ss') as "opTime"    ,CREATE_TIME as "createTime"
        FROM OPERATION_LOG WHERE 1 = 1
        <if test="startTime != null and startTime != ''">
            AND op_time &gt;  to_date(#{startTime},'yyyymmddhh24miss')
        </if>
        <if test="endTime != null and endTime != ''">
            AND op_time &lt;= to_date(#{endTime},'yyyymmddhh24miss')
        </if>
        <if test="userName != null and userName != ''">
            AND user_name LIKE '%'||#{userName}||'%'
        </if>
        <if test="realName != null and realName != ''">
            AND real_name LIKE '%'||#{realName}||'%'
        </if>
        <if test="module != null and module != ''">
            AND OP_MODULE = #{module}
        </if>
        ORDER BY op_time DESC
        )a WHERE rownum &lt;= #{endNum}
        ) WHERE rn &gt;= #{startNum}
    </select>
    
    
    <!-- 查询操作日志报表总数 -->
    <select id="countOperLogReport" resultType="int" parameterType="java.util.Map">
        SELECT COUNT(*) FROM(
            SELECT t.user_name
                FROM operation_log t WHERE 1 = 1
                <if test="startTime != null and startTime != ''">
                    AND op_time &gt;  to_date(#{startTime},'yyyymmddhh24miss')
                </if>
                <if test="endTime != null and endTime != ''">
                    AND op_time &lt;= to_date(#{endTime},'yyyymmddhh24miss')
                </if>
                <if test="userName != null and userName != ''">
                    AND user_name LIKE '%'||#{userName}||'%'
                </if>
                <if test="realName != null and realName != ''">
                    AND real_name LIKE '%'||#{realName}||'%'
                </if>
            GROUP BY t.user_name, t.real_name, t.op_module, to_char(t.op_time,'yyyy-mm-dd')
        )
    </select>

    <!-- 分页查询操作日志报表 -->
    <select id="pageQueryOperLogReport" resultType="com.epaylinks.efps.pas.pas.domain.OperationLogReport" parameterType="java.util.Map">
       SELECT * FROM (
        SELECT a.* ,rownum rn FROM (
            SELECT t.user_name userName, t.real_name realName, t.op_module opModule, to_char(op_time,'yyyy-mm-dd') operDate, count(*) operCount
            FROM operation_log t WHERE 1 = 1
            <if test="startTime != null and startTime != ''">
                AND op_time &gt;  to_date(#{startTime},'yyyymmddhh24miss')
            </if>
            <if test="endTime != null and endTime != ''">
                AND op_time &lt;= to_date(#{endTime},'yyyymmddhh24miss')
            </if>
            <if test="userName != null and userName != ''">
                AND user_name LIKE '%'||#{userName}||'%'
            </if>
            <if test="realName != null and realName != ''">
                AND real_name LIKE '%'||#{realName}||'%'
            </if>
            GROUP BY t.user_name, t.real_name, t.op_module, to_char(t.op_time,'yyyy-mm-dd')
            ORDER BY to_char(t.op_time,'yyyy-mm-dd') DESC
        )a WHERE rownum &lt;= #{endNum}
        ) WHERE rn &gt;= #{startNum}
    </select>
    
</mapper>