<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.pas.pas.dao.BusinessParamMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.pas.domain.BusinessParam">
    <result column="ID" jdbcType="DECIMAL" property="id" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="CODE" jdbcType="VARCHAR" property="code" />
    <result column="MUST" jdbcType="CHAR" property="must" />
    <result column="TYPE" jdbcType="VARCHAR" property="type" />
    <result column="BUSINESS_ID" jdbcType="DECIMAL" property="businessId" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="CREATOR" jdbcType="DECIMAL" property="creator" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="UPDATOR" jdbcType="DECIMAL" property="updator" />
    <result column="DEFAULTVALUE" jdbcType="VARCHAR" property="defaultValue" />
    <result column="DATA_URL" jdbcType="VARCHAR" property="dataUrl" />
    <result column="EXTENDED_FIELD" jdbcType="VARCHAR" property="extendedField" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, NAME, CODE, MUST, TYPE, BUSINESS_ID, CREATE_TIME, CREATOR, UPDATE_TIME, UPDATOR, DEFAULTVALUE, DATA_URL,EXTENDED_FIELD
  </sql>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.pas.domain.BusinessParam">
    insert into PAS_BUSINESS_PARAM (ID, NAME, CODE, 
      MUST, TYPE, BUSINESS_ID, 
      CREATE_TIME, CREATOR, UPDATE_TIME, 
      UPDATOR, DEFAULTVALUE, DATA_URL,EXTENDED_FIELD
      )
    values (#{id,jdbcType=DECIMAL}, #{name,jdbcType=VARCHAR}, #{code,jdbcType=VARCHAR}, 
      #{must,jdbcType=CHAR}, #{type,jdbcType=VARCHAR}, #{businessId,jdbcType=DECIMAL}, 
      #{createTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=DECIMAL}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{updator,jdbcType=DECIMAL}, #{defaultValue,jdbcType=VARCHAR}, #{dataUrl,jdbcType=VARCHAR}, #{extendedField,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.pas.domain.BusinessParam">
    insert into PAS_BUSINESS_PARAM
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="name != null">
        NAME,
      </if>
      <if test="code != null">
        CODE,
      </if>
      <if test="must != null">
        MUST,
      </if>
      <if test="type != null">
        TYPE,
      </if>
      <if test="businessId != null">
        BUSINESS_ID,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="updator != null">
        UPDATOR,
      </if>
      <if test="defaultValue != null">
        DEFAULTVALUE,
      </if>
      <if test="dataUrl != null">
        DATA_URL,
      </if>
      <if test="dataUrl != null">
        EXTENDED_FIELD,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="must != null">
        #{must,jdbcType=CHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=DECIMAL},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null">
        #{updator,jdbcType=DECIMAL},
      </if>
      <if test="defaultValue != null">
        #{defaultValue,jdbcType=VARCHAR},
      </if>
      <if test="dataUrl != null">
        #{dataUrl,jdbcType=VARCHAR},
      </if>
      <if test="dataUrl != null">
        #{extendedField,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="selectByBusinessId" resultMap="BaseResultMap">
  	select 
    <include refid="Base_Column_List" />
    from PAS_BUSINESS_PARAM 
    where BUSINESS_ID = #{businessId , jdbcType = INTEGER}
  </select>
</mapper>