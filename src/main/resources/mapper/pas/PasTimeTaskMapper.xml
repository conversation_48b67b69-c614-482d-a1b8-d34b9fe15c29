<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.pas.pas.dao.PasTimeTaskMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.pas.domain.PasTimeTask" >
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="GROUP_NAME" property="groupName" jdbcType="VARCHAR" />
    <result column="JOB_STATUS" property="jobStatus" jdbcType="CHAR" />
    <result column="JOB_DATA" property="jobData" jdbcType="VARCHAR" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
    <result column="CREATE_NAME" property="createName" jdbcType="VARCHAR" />
    <result column="UPDATE_NAME" property="updateName" jdbcType="VARCHAR" />
    <result column="JOB_NAME" property="jobName" jdbcType="VARCHAR" />
    <result column="CRON" property="cron" jdbcType="VARCHAR" />
    <result column="CREATE_ID" property="createId" jdbcType="DECIMAL" />
    <result column="UPDATE_ID" property="updateId" jdbcType="DECIMAL" />
    <result column="CONCURRENT" property="concurrent" jdbcType="CHAR" />
    <result column="BEAN_NAME" property="beanName" jdbcType="VARCHAR" />
    <result column="LOCK_TIME" property="lockTime" jdbcType="DECIMAL" />
    <result column="IP_ADDRES" property="ipAddres" jdbcType="VARCHAR" />
    <result column="REAL_BAEN_NAME" property="realBaenName" jdbcType="VARCHAR" />
    <result column="PARTITION" property="partition" jdbcType="VARCHAR" />
    <result column="LAST_EXECUTE_RECORD_ID" jdbcType="DECIMAL" property="lastExecuteRecordId" />
    <result column="MAX_EXECUTE_DURATION" jdbcType="DECIMAL" property="maxExecuteDuration" />
    <result column="LAST_FIRE_TIME" jdbcType="TIMESTAMP" property="lastFireTime" />
    <result column="RECORD_SAVE_DAYS" jdbcType="DECIMAL" property="recordSaveDays" />
    <result column="PARENT_JOBS" property="parentJobs" jdbcType="VARCHAR" />
    <result column="DEADLINE" property="deadline" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, GROUP_NAME, JOB_STATUS, JOB_DATA, REMARK, CREATE_NAME, UPDATE_NAME, JOB_NAME,
    CRON, CREATE_ID, UPDATE_ID, CONCURRENT, BEAN_NAME, LOCK_TIME, IP_ADDRES, REAL_BAEN_NAME,PARTITION, LAST_EXECUTE_RECORD_ID, MAX_EXECUTE_DURATION, LAST_FIRE_TIME, RECORD_SAVE_DAYS,
    PARENT_JOBS,DEADLINE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from PAS_TIME_TASK
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from PAS_TIME_TASK
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.pas.domain.PasTimeTask" >
    insert into PAS_TIME_TASK (ID, GROUP_NAME, JOB_STATUS,
    JOB_DATA, REMARK, CREATE_NAME,
    UPDATE_NAME, JOB_NAME, CRON,
    CREATE_ID, UPDATE_ID, CONCURRENT,
    BEAN_NAME, LOCK_TIME, IP_ADDRES,
    REAL_BAEN_NAME,PARTITION, LAST_EXECUTE_RECORD_ID, MAX_EXECUTE_DURATION,
      LAST_FIRE_TIME, RECORD_SAVE_DAYS, PARENT_JOBS,DEADLINE)
    values (#{id,jdbcType=DECIMAL}, #{groupName,jdbcType=VARCHAR}, #{jobStatus,jdbcType=CHAR},
    #{jobData,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{createName,jdbcType=VARCHAR},
    #{updateName,jdbcType=VARCHAR}, #{jobName,jdbcType=VARCHAR}, #{cron,jdbcType=VARCHAR},
    #{createId,jdbcType=DECIMAL}, #{updateId,jdbcType=DECIMAL}, #{concurrent,jdbcType=CHAR},
    #{beanName,jdbcType=VARCHAR}, #{lockTime,jdbcType=DECIMAL}, #{ipAddres,jdbcType=VARCHAR},
    #{realBaenName,jdbcType=VARCHAR}, #{partition,jdbcType=VARCHAR}, #{lastExecuteRecordId,jdbcType=DECIMAL}, #{maxExecuteDuration,jdbcType=DECIMAL},
    #{lastFireTime,jdbcType=TIMESTAMP}, #{recordSaveDays,jdbcType=DECIMAL}, #{parentJobs,jdbcType=VARCHAR}, #{deadline,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.pas.domain.PasTimeTask" >
    insert into PAS_TIME_TASK
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="groupName != null" >
        GROUP_NAME,
      </if>
      <if test="jobStatus != null" >
        JOB_STATUS,
      </if>
      <if test="jobData != null" >
        JOB_DATA,
      </if>
      <if test="remark != null" >
        REMARK,
      </if>
      <if test="createName != null" >
        CREATE_NAME,
      </if>
      <if test="updateName != null" >
        UPDATE_NAME,
      </if>
      <if test="jobName != null" >
        JOB_NAME,
      </if>
      <if test="cron != null" >
        CRON,
      </if>
      <if test="createId != null" >
        CREATE_ID,
      </if>
      <if test="updateId != null" >
        UPDATE_ID,
      </if>
      <if test="concurrent != null" >
        CONCURRENT,
      </if>
      <if test="beanName != null" >
        BEAN_NAME,
      </if>
      <if test="lockTime != null" >
        LOCK_TIME,
      </if>
      <if test="ipAddres != null" >
        IP_ADDRES,
      </if>
      <if test="realBaenName != null" >
        REAL_BAEN_NAME,
      </if>
      <if test="partition != null" >
        PARTITION,
      </if>
      <if test="lastExecuteRecordId != null">
        LAST_EXECUTE_RECORD_ID,
      </if>
      <if test="maxExecuteDuration != null">
        MAX_EXECUTE_DURATION,
      </if>
      <if test="lastFireTime != null">
        LAST_FIRE_TIME,
      </if>
      <if test="recordSaveDays != null">
        RECORD_SAVE_DAYS,
      </if>
      <if test="parentJobs != null">
        PARENT_JOBS,
      </if>
      <if test="deadline != null">
        DEADLINE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="groupName != null" >
        #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="jobStatus != null" >
        #{jobStatus,jdbcType=CHAR},
      </if>
      <if test="jobData != null" >
        #{jobData,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createName != null" >
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="updateName != null" >
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="jobName != null" >
        #{jobName,jdbcType=VARCHAR},
      </if>
      <if test="cron != null" >
        #{cron,jdbcType=VARCHAR},
      </if>
      <if test="createId != null" >
        #{createId,jdbcType=DECIMAL},
      </if>
      <if test="updateId != null" >
        #{updateId,jdbcType=DECIMAL},
      </if>
      <if test="concurrent != null" >
        #{concurrent,jdbcType=CHAR},
      </if>
      <if test="beanName != null" >
        #{beanName,jdbcType=VARCHAR},
      </if>
      <if test="lockTime != null" >
        #{lockTime,jdbcType=DECIMAL},
      </if>
      <if test="ipAddres != null" >
        #{ipAddres,jdbcType=VARCHAR},
      </if>
      <if test="realBaenName != null" >
        #{realBaenName,jdbcType=VARCHAR},
      </if>
      <if test="partition != null" >
        #{partition,jdbcType=VARCHAR},
      </if>
      <if test="lastExecuteRecordId != null">
        #{lastExecuteRecordId,jdbcType=DECIMAL},
      </if>
      <if test="maxExecuteDuration != null">
        #{maxExecuteDuration,jdbcType=DECIMAL},
      </if>
      <if test="lastFireTime != null">
        #{lastFireTime,jdbcType=TIMESTAMP},
      </if>
      <if test="recordSaveDays != null">
        #{recordSaveDays,jdbcType=DECIMAL},
      </if>
      <if test="parentJobs != null" >
        #{parentJobs,jdbcType=VARCHAR},
      </if>
      <if test="deadline != null" >
        #{deadline,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.pas.domain.PasTimeTask" >
    update PAS_TIME_TASK
    <set >
      <if test="groupName != null" >
        GROUP_NAME = #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="jobStatus != null" >
        JOB_STATUS = #{jobStatus,jdbcType=CHAR},
      </if>
      <if test="jobData != null" >
        JOB_DATA = #{jobData,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createName != null" >
        CREATE_NAME = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="updateName != null" >
        UPDATE_NAME = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="jobName != null" >
        JOB_NAME = #{jobName,jdbcType=VARCHAR},
      </if>
      <if test="cron != null" >
        CRON = #{cron,jdbcType=VARCHAR},
      </if>
      <if test="createId != null" >
        CREATE_ID = #{createId,jdbcType=DECIMAL},
      </if>
      <if test="updateId != null" >
        UPDATE_ID = #{updateId,jdbcType=DECIMAL},
      </if>
      <if test="concurrent != null" >
        CONCURRENT = #{concurrent,jdbcType=CHAR},
      </if>
      <if test="beanName != null" >
        BEAN_NAME = #{beanName,jdbcType=VARCHAR},
      </if>
      <if test="lockTime != null" >
        LOCK_TIME = #{lockTime,jdbcType=DECIMAL},
      </if>
      <if test="ipAddres != null" >
        IP_ADDRES = #{ipAddres,jdbcType=VARCHAR},
      </if>
      <if test="realBaenName != null" >
        REAL_BAEN_NAME = #{realBaenName,jdbcType=VARCHAR},
      </if>
      <if test="partition != null" >
        PARTITION = #{partition,jdbcType=VARCHAR},
      </if>
      <if test="lastExecuteRecordId != null">
        LAST_EXECUTE_RECORD_ID = #{lastExecuteRecordId,jdbcType=DECIMAL},
      </if>
      <if test="maxExecuteDuration != null">
        MAX_EXECUTE_DURATION = #{maxExecuteDuration,jdbcType=DECIMAL},
      </if>
      <if test="lastFireTime != null">
        LAST_FIRE_TIME = #{lastFireTime,jdbcType=TIMESTAMP},
      </if>
      <if test="recordSaveDays != null">
        RECORD_SAVE_DAYS = #{recordSaveDays,jdbcType=DECIMAL},
      </if>
      <if test="parentJobs != null">
        PARENT_JOBS = #{parentJobs,jdbcType=VARCHAR},
      </if>
      <if test="deadline != null">
        DEADLINE = #{deadline,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.pas.domain.PasTimeTask" >
    update PAS_TIME_TASK
    set GROUP_NAME = #{groupName,jdbcType=VARCHAR},
    JOB_STATUS = #{jobStatus,jdbcType=CHAR},
    JOB_DATA = #{jobData,jdbcType=VARCHAR},
    REMARK = #{remark,jdbcType=VARCHAR},
    CREATE_NAME = #{createName,jdbcType=VARCHAR},
    UPDATE_NAME = #{updateName,jdbcType=VARCHAR},
    JOB_NAME = #{jobName,jdbcType=VARCHAR},
    CRON = #{cron,jdbcType=VARCHAR},
    CREATE_ID = #{createId,jdbcType=DECIMAL},
    UPDATE_ID = #{updateId,jdbcType=DECIMAL},
    CONCURRENT = #{concurrent,jdbcType=CHAR},
    BEAN_NAME = #{beanName,jdbcType=VARCHAR},
    LOCK_TIME = #{lockTime,jdbcType=DECIMAL},
    IP_ADDRES = #{ipAddres,jdbcType=VARCHAR},
    REAL_BAEN_NAME = #{realBaenName,jdbcType=VARCHAR},
    PARTITION = #{partition,jdbcType=VARCHAR},
    LAST_EXECUTE_RECORD_ID = #{lastExecuteRecordId,jdbcType=DECIMAL},
    MAX_EXECUTE_DURATION = #{maxExecuteDuration,jdbcType=DECIMAL},
    LAST_FIRE_TIME = #{lastFireTime,jdbcType=TIMESTAMP},
    RECORD_SAVE_DAYS = #{recordSaveDays,jdbcType=DECIMAL},
    PARENT_JOBS = #{parentJobs,jdbcType=VARCHAR},
    DEADLINE = #{deadline,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <select id="selectAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from PAS_TIME_TASK
  </select>

  <select id="selectByJobName" parameterType="String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from PAS_TIME_TASK
    WHERE JOB_NAME = #{jobName}
  </select>

  <select id="findByJobNameForUpdate" parameterType="String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from PAS_TIME_TASK
    WHERE JOB_NAME = #{jobName} for update
  </select>

  <update id="switchJobPartition" parameterType="java.util.Map">
    update pas_time_task set partition = #{partition,jdbcType=VARCHAR}
    where 1=1
    and job_status = '0'
    <if test="jobName != null and jobName.size() > 0">
      and job_name in
      <foreach item="item" index="index" collection="jobName" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
  </update>
</mapper>