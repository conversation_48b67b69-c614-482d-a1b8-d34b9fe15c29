<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.pas.pas.dao.ManagementPersonnelMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.pas.domain.ManagementPersonnel" >
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="RECEIVER_ID" property="receiverId" jdbcType="VARCHAR" />
    <result column="RECEIVER" property="receiver" jdbcType="VARCHAR" />
    <result column="PHONE" property="phone" jdbcType="VARCHAR" />
    <result column="PHONE_ENCRYPT" property="phoneEncrypt" jdbcType="VARCHAR" />
    <result column="OPERATOR" property="operator" jdbcType="DECIMAL" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, RECEIVER_ID, RECEIVER, PHONE, PHONE_ENCRYPT, OPERATOR, CREATE_TIME
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from PAS_MANAGEMENT_PERSONNEL
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from PAS_MANAGEMENT_PERSONNEL
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.pas.domain.ManagementPersonnel" >
    insert into PAS_MANAGEMENT_PERSONNEL (ID, RECEIVER_ID, RECEIVER, 
      PHONE, PHONE_ENCRYPT, OPERATOR, 
      CREATE_TIME)
    values (#{id,jdbcType=DECIMAL}, #{receiverId,jdbcType=VARCHAR}, #{receiver,jdbcType=VARCHAR}, 
      #{phone,jdbcType=VARCHAR}, #{phoneEncrypt,jdbcType=VARCHAR}, #{operator,jdbcType=DECIMAL}, 
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.pas.domain.ManagementPersonnel" >
    insert into PAS_MANAGEMENT_PERSONNEL
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="receiverId != null" >
        RECEIVER_ID,
      </if>
      <if test="receiver != null" >
        RECEIVER,
      </if>
      <if test="phone != null" >
        PHONE,
      </if>
      <if test="phoneEncrypt != null" >
        PHONE_ENCRYPT,
      </if>
      <if test="operator != null" >
        OPERATOR,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="receiverId != null" >
        #{receiverId,jdbcType=VARCHAR},
      </if>
      <if test="receiver != null" >
        #{receiver,jdbcType=VARCHAR},
      </if>
      <if test="phone != null" >
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="phoneEncrypt != null" >
        #{phoneEncrypt,jdbcType=VARCHAR},
      </if>
      <if test="operator != null" >
        #{operator,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.pas.domain.ManagementPersonnel" >
    update PAS_MANAGEMENT_PERSONNEL
    <set >
      <if test="receiverId != null" >
        RECEIVER_ID = #{receiverId,jdbcType=VARCHAR},
      </if>
      <if test="receiver != null" >
        RECEIVER = #{receiver,jdbcType=VARCHAR},
      </if>
      <if test="phone != null" >
        PHONE = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="phoneEncrypt != null" >
        PHONE_ENCRYPT = #{phoneEncrypt,jdbcType=VARCHAR},
      </if>
      <if test="operator != null" >
        OPERATOR = #{operator,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.pas.domain.ManagementPersonnel" >
    update PAS_MANAGEMENT_PERSONNEL
    set RECEIVER_ID = #{receiverId,jdbcType=VARCHAR},
      RECEIVER = #{receiver,jdbcType=VARCHAR},
      PHONE = #{phone,jdbcType=VARCHAR},
      PHONE_ENCRYPT = #{phoneEncrypt,jdbcType=VARCHAR},
      OPERATOR = #{operator,jdbcType=DECIMAL},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=DECIMAL}
  </update>

  <select id="queryMPNextSeq" resultType="java.lang.Long">
    select seq_PAS_MANAGEMENT_PERSONNEL.nextval from dual
  </select>

  <select id="selectPersonnelList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from PAS_MANAGEMENT_PERSONNEL
    order by id desc
  </select>

  <select id="getReceiveId" resultType="java.lang.String">
    select nvl(max(receiver_id),'000') from PAS_MANAGEMENT_PERSONNEL
  </select>

  <select id="selectByReceiverId" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from PAS_MANAGEMENT_PERSONNEL
    where RECEIVER_ID = #{receiverId,jdbcType=VARCHAR}
  </select>
</mapper>