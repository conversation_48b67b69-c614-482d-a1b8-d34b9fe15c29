<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.pas.pas.dao.ApplicationRecordMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.pas.domain.ApplicationRecord" >
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="CUSTOMER_CODE" property="customerCode" jdbcType="VARCHAR" />
    <result column="CUSTOMER_INFO_ID" property="customerInfoId" jdbcType="DECIMAL" />
    <result column="INSTITUTION_CODE" property="institutionCode" jdbcType="VARCHAR" />
    <result column="INPUT_PARAMS" property="inputParams" jdbcType="VARCHAR" />
    <result column="STATE" property="state" jdbcType="CHAR" />
    <result column="RESULT_MSG" property="resultMsg" jdbcType="VARCHAR" />
    <result column="INSTITUTION_MERCH_CODE" property="institutionMerchCode" jdbcType="VARCHAR" />
    <result column="INSTITUTION_MERCH_SECRET" property="institutionMerchSecret" jdbcType="VARCHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, CUSTOMER_CODE, CUSTOMER_INFO_ID, INSTITUTION_CODE, INPUT_PARAMS, STATE, RESULT_MSG, 
    INSTITUTION_MERCH_CODE, INSTITUTION_MERCH_SECRET, CREATE_TIME, UPDATE_TIME
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from PAS_APPLICATION_RECORD
    where ID = #{id,jdbcType=DECIMAL}
  </select>

  <select id="selectBySelective" resultMap="BaseResultMap" parameterType="com.epaylinks.efps.pas.pas.domain.ApplicationRecord" >
    select
    <include refid="Base_Column_List" />
    from PAS_APPLICATION_RECORD
    <where>
      <if test="id != null and id != ''" >
        AND ID = #{id,jdbcType=DECIMAL}
      </if>
      <if test="customerCode != null and customerCode != ''" >
        AND CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
      </if>
      <if test="customerInfoId != null and customerInfoId != ''" >
        AND CUSTOMER_INFO_ID = #{customerInfoId,jdbcType=DECIMAL}
      </if>
      <if test="institutionCode != null and institutionCode != ''" >
        AND INSTITUTION_CODE = #{institutionCode,jdbcType=VARCHAR}
      </if>
      <if test="inputParams != null and inputParams != ''" >
        AND INPUT_PARAMS = #{inputParams,jdbcType=VARCHAR}
      </if>
      <if test="state != null and state != ''" >
        AND STATE = #{state,jdbcType=CHAR}
      </if>
      <if test="resultMsg != null and resultMsg != ''" >
        AND RESULT_MSG = #{resultMsg,jdbcType=VARCHAR}
      </if>
      <if test="institutionMerchCode != null and institutionMerchCode != ''" >
        AND INSTITUTION_MERCH_CODE = #{institutionMerchCode,jdbcType=VARCHAR}
      </if>
      <if test="institutionMerchSecret != null and institutionMerchSecret != ''" >
        AND INSTITUTION_MERCH_SECRET = #{institutionMerchSecret,jdbcType=VARCHAR}
      </if>
      <if test="createTime != null" >
        AND CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updateTime != null" >
        AND UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
      </if>
    </where>
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from PAS_APPLICATION_RECORD
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.pas.domain.ApplicationRecord" >
    insert into PAS_APPLICATION_RECORD (ID, CUSTOMER_CODE, CUSTOMER_INFO_ID, 
      INSTITUTION_CODE, INPUT_PARAMS, STATE, 
      RESULT_MSG, INSTITUTION_MERCH_CODE, INSTITUTION_MERCH_SECRET, 
      CREATE_TIME, UPDATE_TIME)
    values (#{id,jdbcType=DECIMAL}, #{customerCode,jdbcType=VARCHAR}, #{customerInfoId,jdbcType=DECIMAL}, 
      #{institutionCode,jdbcType=VARCHAR}, #{inputParams,jdbcType=VARCHAR}, #{state,jdbcType=CHAR},
      #{resultMsg,jdbcType=VARCHAR}, #{institutionMerchCode,jdbcType=VARCHAR}, #{institutionMerchSecret,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.pas.domain.ApplicationRecord" >
    insert into PAS_APPLICATION_RECORD
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null and id != ''" >
        ID,
      </if>
      <if test="customerCode != null and customerCode != ''" >
        CUSTOMER_CODE,
      </if>
      <if test="customerInfoId != null and customerInfoId != ''" >
        CUSTOMER_INFO_ID,
      </if>
      <if test="institutionCode != null and institutionCode != ''" >
        INSTITUTION_CODE,
      </if>
      <if test="inputParams != null and inputParams != ''" >
        INPUT_PARAMS,
      </if>
      <if test="state != null and state != ''" >
        STATE,
      </if>
      <if test="resultMsg != null and resultMsg != ''" >
        RESULT_MSG,
      </if>
      <if test="institutionMerchCode != null and institutionMerchCode != ''" >
        INSTITUTION_MERCH_CODE,
      </if>
      <if test="institutionMerchSecret != null and institutionMerchSecret != ''" >
        INSTITUTION_MERCH_SECRET,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null and id != ''" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="customerCode != null and customerCode != ''" >
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="customerInfoId != null and customerInfoId != ''" >
        #{customerInfoId,jdbcType=DECIMAL},
      </if>
      <if test="institutionCode != null and institutionCode != ''" >
        #{institutionCode,jdbcType=VARCHAR},
      </if>
      <if test="inputParams != null and inputParams != ''" >
        #{inputParams,jdbcType=VARCHAR},
      </if>
      <if test="state != null and state != ''" >
        #{state,jdbcType=CHAR},
      </if>
      <if test="resultMsg != null and resultMsg != ''" >
        #{resultMsg,jdbcType=VARCHAR},
      </if>
      <if test="institutionMerchCode != null and institutionMerchCode != ''" >
        #{institutionMerchCode,jdbcType=VARCHAR},
      </if>
      <if test="institutionMerchSecret != null and institutionMerchSecret != ''" >
        #{institutionMerchSecret,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.pas.domain.ApplicationRecord" >
    update PAS_APPLICATION_RECORD
    <set >
      <if test="customerCode != null and customerCode != ''" >
        CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="customerInfoId != null and customerInfoId != ''" >
        CUSTOMER_INFO_ID = #{customerInfoId,jdbcType=DECIMAL},
      </if>
      <if test="institutionCode != null and institutionCode != ''" >
        INSTITUTION_CODE = #{institutionCode,jdbcType=VARCHAR},
      </if>
      <if test="inputParams != null and inputParams != ''" >
        INPUT_PARAMS = #{inputParams,jdbcType=VARCHAR},
      </if>
      <if test="state != null and state != ''" >
        STATE = #{state,jdbcType=CHAR},
      </if>
      <if test="resultMsg != null and resultMsg != ''" >
        RESULT_MSG = #{resultMsg,jdbcType=VARCHAR},
      </if>
      <if test="institutionMerchCode != null and institutionMerchCode != ''" >
        INSTITUTION_MERCH_CODE = #{institutionMerchCode,jdbcType=VARCHAR},
      </if>
      <if test="institutionMerchSecret != null and institutionMerchSecret != ''" >
        INSTITUTION_MERCH_SECRET = #{institutionMerchSecret,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.pas.domain.ApplicationRecord" >
    update PAS_APPLICATION_RECORD
    set CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      CUSTOMER_INFO_ID = #{customerInfoId,jdbcType=DECIMAL},
      INSTITUTION_CODE = #{institutionCode,jdbcType=VARCHAR},
      INPUT_PARAMS = #{inputParams,jdbcType=VARCHAR},
      STATE = #{state,jdbcType=CHAR},
      RESULT_MSG = #{resultMsg,jdbcType=VARCHAR},
      INSTITUTION_MERCH_CODE = #{institutionMerchCode,jdbcType=VARCHAR},
      INSTITUTION_MERCH_SECRET = #{institutionMerchSecret,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=DECIMAL}
  </update>

  <select id="selectByPage" resultMap="BaseResultMap"
          parameterType="java.util.Map">
    select
    <include refid="Base_Column_List" />
    from (
    select A.*, rownum RN
    from (
    select * from PAS_APPLICATION_RECORD
    <where>
      <if test="id != null and id != ''" >
        AND ID = #{id,jdbcType=DECIMAL}
      </if>
      <if test="customerCode != null and customerCode != ''" >
        AND CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
      </if>
      <if test="customerInfoId != null and customerInfoId != ''" >
        AND CUSTOMER_INFO_ID = #{customerInfoId,jdbcType=DECIMAL}
      </if>
      <if test="institutionCode != null and institutionCode != ''" >
        AND INSTITUTION_CODE = #{institutionCode,jdbcType=VARCHAR}
      </if>
      <if test="inputParams != null and inputParams != ''" >
        AND INPUT_PARAMS = #{inputParams,jdbcType=VARCHAR}
      </if>
      <if test="state != null and state != ''" >
        AND STATE = #{state,jdbcType=CHAR}
      </if>
      <if test="resultMsg != null and resultMsg != ''" >
        AND RESULT_MSG = #{resultMsg,jdbcType=VARCHAR}
      </if>
      <if test="institutionMerchCode != null and institutionMerchCode != ''" >
        AND INSTITUTION_MERCH_CODE = #{institutionMerchCode,jdbcType=VARCHAR}
      </if>
      <if test="institutionMerchSecret != null and institutionMerchSecret != ''" >
        AND INSTITUTION_MERCH_SECRET = #{institutionMerchSecret,jdbcType=VARCHAR}
      </if>
      <if test="beginTime!=null">
        AND CREATE_TIME <![CDATA[ >= ]]>  #{beginTime,jdbcType=TIMESTAMP}
      </if>
      <if test="endTime!=null">
        AND CREATE_TIME <![CDATA[ <= ]]>  #{endTime,jdbcType=TIMESTAMP}
      </if>
    </where>
    order by CREATE_TIME desc
    ) A
    where rownum &lt;= #{endRowNo,jdbcType=DECIMAL}
    )
    where RN &gt;= #{beginRowNo,jdbcType=DECIMAL}

  </select>

</mapper>