<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.pas.pas.dao.AppliAddParaMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.pas.domain.AppliAddPara" >
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="CUSTOMER_CODE" property="customerCode" jdbcType="VARCHAR" />
    <result column="EFPS_KEY" property="efpsKey" jdbcType="VARCHAR" />
    <result column="VALUE" property="value" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, CUSTOMER_CODE, EFPS_KEY, VALUE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from PAS_APPLI_ADD_PARA
    where ID = #{id,jdbcType=DECIMAL}
  </select>

  <select id="selectBySelective" resultMap="BaseResultMap" parameterType="com.epaylinks.efps.pas.pas.domain.AppliAddPara" >
    select
    <include refid="Base_Column_List" />
    from PAS_APPLI_ADD_PARA
    <where>
      <if test="id != null and id != ''" >
        AND ID = #{id,jdbcType=DECIMAL}
      </if>
      <if test="customerCode != null and customerCode != ''" >
        AND CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
      </if>
      <if test="efpsKey != null and efpsKey != ''" >
        AND EFPS_KEY = #{efpsKey,jdbcType=VARCHAR}
      </if>
      <if test="value != null and value != ''" >
        AND VALUE = #{value,jdbcType=VARCHAR}
      </if>
    </where>
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from PAS_APPLI_ADD_PARA
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.pas.domain.AppliAddPara" >
    insert into PAS_APPLI_ADD_PARA (ID, CUSTOMER_CODE, EFPS_KEY, 
      VALUE)
    values (#{id,jdbcType=DECIMAL}, #{customerCode,jdbcType=VARCHAR}, #{efpsKey,jdbcType=VARCHAR}, 
      #{value,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.pas.domain.AppliAddPara" >
    insert into PAS_APPLI_ADD_PARA
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null and id != ''" >
        ID,
      </if>
      <if test="customerCode != null and customerCode != ''" >
        CUSTOMER_CODE,
      </if>
      <if test="efpsKey != null and efpsKey != ''" >
        EFPS_KEY,
      </if>
      <if test="value != null and value != ''" >
        VALUE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null and id != ''" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="customerCode != null and customerCode != ''" >
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="efpsKey != null and efpsKey != ''" >
        #{efpsKey,jdbcType=VARCHAR},
      </if>
      <if test="value != null and value != ''" >
        #{value,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.pas.domain.AppliAddPara" >
    update PAS_APPLI_ADD_PARA
    <set >
      <if test="customerCode != null and customerCode != ''" >
        CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="efpsKey != null and efpsKey != ''" >
        EFPS_KEY = #{efpsKey,jdbcType=VARCHAR},
      </if>
      <if test="value != null and value != ''" >
        VALUE = #{value,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.pas.domain.AppliAddPara" >
    update PAS_APPLI_ADD_PARA
    set CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR},
      EFPS_KEY = #{efpsKey,jdbcType=VARCHAR},
      VALUE = #{value,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>

</mapper>