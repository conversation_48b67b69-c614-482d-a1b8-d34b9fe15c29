<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.pas.pas.dao.PasTransWarnMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.pas.vo.PasTransWarn" >
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="TRANSACTION_NO" property="transactionNo" jdbcType="VARCHAR" />
    <result column="TYPE" property="type" jdbcType="VARCHAR" />
    <result column="TRANSACTION_MSG" property="transactionMsg" jdbcType="VARCHAR" />
    <result column="STATUS" property="status" jdbcType="CHAR" />
    <result column="WARN_TIMES" property="warnTimes" jdbcType="DECIMAL" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="USER_ID" property="userId" jdbcType="DECIMAL" />
    <result column="USER_NAME" property="userName" jdbcType="VARCHAR" />
    <result column="REASON" property="reason" jdbcType="VARCHAR" />
    <result column="SOURCE" property="source" jdbcType="VARCHAR" />
    <result column="WARN_LEVEL" property="warnLevel" jdbcType="VARCHAR" />
    <result column="CATEGORY" property="category" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, TRANSACTION_NO, TYPE, TRANSACTION_MSG, STATUS, WARN_TIMES, CREATE_TIME, UPDATE_TIME,
    USER_ID, USER_NAME, REASON, SOURCE, WARN_LEVEL, CATEGORY
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from PAS_TRANS_WARN
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from PAS_TRANS_WARN
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.pas.vo.PasTransWarn" >
    insert into PAS_TRANS_WARN (ID, TRANSACTION_NO, TYPE,
    TRANSACTION_MSG, STATUS, WARN_TIMES,
    CREATE_TIME, UPDATE_TIME, USER_ID,
    USER_NAME, REASON, SOURCE,
    WARN_LEVEL, CATEGORY)
    values (#{id,jdbcType=DECIMAL}, #{transactionNo,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR},
    #{transactionMsg,jdbcType=VARCHAR}, #{status,jdbcType=CHAR}, #{warnTimes,jdbcType=DECIMAL},
    #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{userId,jdbcType=DECIMAL},
    #{userName,jdbcType=VARCHAR}, #{reason,jdbcType=VARCHAR}, #{source,jdbcType=VARCHAR},
    #{warnLevel,jdbcType=VARCHAR}, #{category,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.pas.vo.PasTransWarn" >
    insert into PAS_TRANS_WARN
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="transactionNo != null" >
        TRANSACTION_NO,
      </if>
      <if test="type != null" >
        TYPE,
      </if>
      <if test="transactionMsg != null" >
        TRANSACTION_MSG,
      </if>
      <if test="status != null" >
        STATUS,
      </if>
      <if test="warnTimes != null" >
        WARN_TIMES,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME,
      </if>
      <if test="userId != null" >
        USER_ID,
      </if>
      <if test="userName != null" >
        USER_NAME,
      </if>
      <if test="reason != null" >
        REASON,
      </if>
      <if test="source != null" >
        SOURCE,
      </if>
      <if test="warnLevel != null" >
        WARN_LEVEL,
      </if>
      <if test="category != null" >
        CATEGORY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="transactionNo != null" >
        #{transactionNo,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="transactionMsg != null" >
        #{transactionMsg,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=CHAR},
      </if>
      <if test="warnTimes != null" >
        #{warnTimes,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=DECIMAL},
      </if>
      <if test="userName != null" >
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="reason != null" >
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="source != null" >
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="warnLevel != null" >
        #{warnLevel,jdbcType=VARCHAR},
      </if>
      <if test="category != null" >
        #{category,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.pas.vo.PasTransWarn" >
    update PAS_TRANS_WARN
    <set >
      <if test="transactionNo != null" >
        TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        TYPE = #{type,jdbcType=VARCHAR},
      </if>
      <if test="transactionMsg != null" >
        TRANSACTION_MSG = #{transactionMsg,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        STATUS = #{status,jdbcType=CHAR},
      </if>
      <if test="warnTimes != null" >
        WARN_TIMES = #{warnTimes,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="userId != null" >
        USER_ID = #{userId,jdbcType=DECIMAL},
      </if>
      <if test="userName != null" >
        USER_NAME = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="reason != null" >
        REASON = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="source != null" >
        SOURCE = #{source,jdbcType=VARCHAR},
      </if>
      <if test="warnLevel != null" >
        WARN_LEVEL = #{warnLevel,jdbcType=VARCHAR},
      </if>
      <if test="category != null" >
        CATEGORY = #{category,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.pas.vo.PasTransWarn" >
    update PAS_TRANS_WARN
    set TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR},
      TYPE = #{type,jdbcType=VARCHAR},
      TRANSACTION_MSG = #{transactionMsg,jdbcType=VARCHAR},
      STATUS = #{status,jdbcType=CHAR},
      WARN_TIMES = #{warnTimes,jdbcType=DECIMAL},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      USER_ID = #{userId,jdbcType=DECIMAL},
      USER_NAME = #{userName,jdbcType=VARCHAR},
      REASON = #{reason,jdbcType=VARCHAR},
      SOURCE = #{source,jdbcType=VARCHAR},
      WARN_LEVEL = #{warnLevel,jdbcType=VARCHAR},
      CATEGORY = #{category,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByTransactionNo" parameterType="com.epaylinks.efps.pas.pas.vo.PasTransWarn" >
    update PAS_TRANS_WARN
    set
    TYPE = #{type,jdbcType=VARCHAR},
    TRANSACTION_MSG = #{transactionMsg,jdbcType=VARCHAR},
    STATUS = #{status,jdbcType=CHAR},
    WARN_TIMES = WARN_TIMES + 1,
    UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
    USER_ID = #{userId,jdbcType=DECIMAL},
    USER_NAME = #{userName,jdbcType=VARCHAR},
    REASON = #{reason,jdbcType=VARCHAR},
    SOURCE = #{source,jdbcType=VARCHAR},
    WARN_LEVEL = #{warnLevel,jdbcType=VARCHAR},
    CATEGORY = #{category,jdbcType=VARCHAR}
    where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
  </update>
  <select id="selectByTransactionNo" parameterType="String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from PAS_TRANS_WARN
    where TRANSACTION_NO = #{transactionNo}
  </select>

</mapper>