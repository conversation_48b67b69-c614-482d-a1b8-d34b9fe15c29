<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.pas.pas.dao.PasTimeTaskRecordMapper">
    <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.pas.domain.PasTimeTaskRecord">
        <id column="ID" property="id" jdbcType="DECIMAL"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="STATUS" property="status" jdbcType="CHAR"/>
        <result column="IP_ADDRESS" property="ipAddress" jdbcType="CHAR"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="RETURN_CODE" property="returnCode" jdbcType="VARCHAR"/>
        <result column="RETURN_MESSAGE" property="returnMessage" jdbcType="VARCHAR"/>
        <result column="JOB_NAME" property="jobName" jdbcType="VARCHAR"/>
        <result column="CALLER_IP_ADDRESS" property="callerIpAddress" jdbcType="VARCHAR"/>
        <result column="PARTITION" property="partition" jdbcType="VARCHAR"/>
        <result column="RECORD_KEY" property="recordKey" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        ID, CREATE_TIME, STATUS, IP_ADDRESS, UPDATE_TIME, RETURN_CODE, RETURN_MESSAGE, JOB_NAME ,CALLER_IP_ADDRESS, PARTITION, RECORD_KEY
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from PAS_TIME_TASK_RECORD
        where ID = #{id,jdbcType=DECIMAL}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from PAS_TIME_TASK_RECORD
        where ID = #{id,jdbcType=DECIMAL}
    </delete>
    <insert id="insert" parameterType="com.epaylinks.efps.pas.pas.domain.PasTimeTaskRecord">
        insert into PAS_TIME_TASK_RECORD (ID, CREATE_TIME, STATUS,
                                          IP_ADDRESS, UPDATE_TIME, RETURN_CODE,
                                          RETURN_MESSAGE, JOB_NAME, CALLER_IP_ADDRESS, PARTITION, RECORD_KEY)
        values (#{id,jdbcType=DECIMAL}, #{createTime,jdbcType=TIMESTAMP}, #{status,jdbcType=CHAR},
                #{ipAddress,jdbcType=CHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{returnCode,jdbcType=VARCHAR},
                #{returnMessage,jdbcType=VARCHAR}, #{jobName,jdbcType=VARCHAR}, #{callerIpAddress,jdbcType=VARCHAR},
                #{partition,jdbcType=VARCHAR}, #{recordKey,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.pas.domain.PasTimeTaskRecord">
        insert into PAS_TIME_TASK_RECORD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                ID,
            </if>
            <if test="createTime != null">
                CREATE_TIME,
            </if>
            <if test="status != null">
                STATUS,
            </if>
            <if test="ipAddress != null">
                IP_ADDRESS,
            </if>
            <if test="updateTime != null">
                UPDATE_TIME,
            </if>
            <if test="returnCode != null">
                RETURN_CODE,
            </if>
            <if test="returnMessage != null">
                RETURN_MESSAGE,
            </if>
            <if test="jobName != null">
                JOB_NAME,
            </if>
            <if test="callerIpAddress != null">
                CALLER_IP_ADDRESS,
            </if>
            <if test="partition != null">
                PARTITION,
            </if>
            <if test="recordKey != null">
                RECORD_KEY,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=DECIMAL},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                #{status,jdbcType=CHAR},
            </if>
            <if test="ipAddress != null">
                #{ipAddress,jdbcType=CHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="returnCode != null">
                #{returnCode,jdbcType=VARCHAR},
            </if>
            <if test="returnMessage != null">
                #{returnMessage,jdbcType=VARCHAR},
            </if>
            <if test="jobName != null">
                #{jobName,jdbcType=VARCHAR},
            </if>
            <if test="callerIpAddress != null">
                #{callerIpAddress,jdbcType=VARCHAR},
            </if>
            <if test="partition != null">
                #{partition,jdbcType=VARCHAR},
            </if>
            <if test="recordKey != null">
                #{recordKey,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.pas.domain.PasTimeTaskRecord">
        update PAS_TIME_TASK_RECORD
        <set>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                STATUS = #{status,jdbcType=CHAR},
            </if>
            <if test="ipAddress != null">
                IP_ADDRESS = #{ipAddress,jdbcType=CHAR},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="returnCode != null">
                RETURN_CODE = #{returnCode,jdbcType=VARCHAR},
            </if>
            <if test="returnMessage != null">
                RETURN_MESSAGE = #{returnMessage,jdbcType=VARCHAR},
            </if>
            <if test="jobName != null">
                JOB_NAME = #{jobName,jdbcType=VARCHAR},
            </if>
            <if test="callerIpAddress != null">
                CALLER_IP_ADDRESS = #{callerIpAddress,jdbcType=VARCHAR},
            </if>
            <if test="partition != null">
                PARTITION = #{partition,jdbcType=VARCHAR},
            </if>
            <if test="recordKey != null">
                RECORD_KEY = #{recordKey,jdbcType=VARCHAR},
            </if>
        </set>
        where ID = #{id,jdbcType=DECIMAL}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.pas.domain.PasTimeTaskRecord">
        update PAS_TIME_TASK_RECORD
        set CREATE_TIME    = #{createTime,jdbcType=TIMESTAMP},
            STATUS         = #{status,jdbcType=CHAR},
            IP_ADDRESS     = #{ipAddress,jdbcType=CHAR},
            UPDATE_TIME    = #{updateTime,jdbcType=TIMESTAMP},
            RETURN_CODE    = #{returnCode,jdbcType=VARCHAR},
            RETURN_MESSAGE = #{returnMessage,jdbcType=VARCHAR},
            JOB_NAME       = #{jobName,jdbcType=VARCHAR} CALLER_IP_ADDRESS = #{callerIpAddress,jdbcType=VARCHAR},
            PARTITION = #{partition,jdbcType=VARCHAR},
            RECORD_KEY = #{recordKey,jdbcType=VARCHAR}
        where ID = #{id,jdbcType=DECIMAL}
    </update>

    <select id="selectLastTimeByJobName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from (
        SELECT
        <include refid="Base_Column_List"/>
        FROM PAS_TIME_TASK_RECORD
        WHERE JOB_NAME = #{name}
        order by create_time desc
        ) WHERE rownum = 1
    </select>
    <select id="countJobDone" resultType="java.lang.Integer">
        select count(1)
        from PAS_TIME_TASK_RECORD
        where CREATE_TIME <![CDATA[ >= ]]> #{createTimeStart,jdbcType=TIMESTAMP}
          and CREATE_TIME <![CDATA[ < ]]> #{createTimeEnd,jdbcType=TIMESTAMP}
          and JOB_NAME = #{jobName,jdbcType=VARCHAR}
          and STATUS = '0'
    </select>

    <delete id="deleteByCreateTimeLessThanAndJobName">
        delete
        from PAS_TIME_TASK_RECORD
        where CREATE_TIME <![CDATA[ < ]]> #{date,jdbcType=TIMESTAMP}
          and JOB_NAME = #{jobName,jdbcType=VARCHAR}
    </delete>
</mapper>