<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.pas.pas.dao.UnionQuotaRecordMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.pas.domain.UnionQuotaRecord" >
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="ISSRID_ACCOUNT" property="issridAccount" jdbcType="VARCHAR" />
    <result column="TRANSACTION_NO" property="transactionNo" jdbcType="VARCHAR" />
    <result column="AMOUNT" property="amount" jdbcType="DECIMAL" />
    <result column="TRX_CATEGORY" property="trxCategory" jdbcType="VARCHAR" />
    <result column="STATE" property="state" jdbcType="VARCHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="USER_ID" property="userId" jdbcType="DECIMAL" />
    <result column="CHANNEL_TRADE_NO" property="channelTradeNo" jdbcType="VARCHAR" />
    <result column="CHANNEL_RETURN_CODE" property="channelReturnCode" jdbcType="VARCHAR" />
    <result column="CHANNEL_RETURN_MESSAGE" property="channelReturnMessage" jdbcType="VARCHAR" />
    <result column="CHANNEL_RETURN_DATE" property="channelReturnDate" jdbcType="TIMESTAMP" />
    <result column="CHANNEL_STATE" property="channelState" jdbcType="VARCHAR" />
    <result column="USER_NAME" property="userName" jdbcType="VARCHAR" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List">
     ID, ISSRID_ACCOUNT, AMOUNT,
           TRX_CATEGORY, STATE, CREATE_TIME,
           USER_ID, CHANNEL_TRADE_NO, CHANNEL_RETURN_CODE,
           CHANNEL_RETURN_MESSAGE, CHANNEL_RETURN_DATE,TRANSACTION_NO,CHANNEL_STATE ,USER_NAME,REMARK
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.math.BigDecimal" >
    select
    <include refid="Base_Column_List" />
    from PAS_UNION_QUOTA_RECORD
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.pas.domain.UnionQuotaRecord" >
    insert into PAS_UNION_QUOTA_RECORD (ID, ISSRID_ACCOUNT, AMOUNT, 
      TRX_CATEGORY, STATE, CREATE_TIME, 
      USER_ID, CHANNEL_TRADE_NO, CHANNEL_RETURN_CODE, 
      CHANNEL_RETURN_MESSAGE, CHANNEL_RETURN_DATE,TRANSACTION_NO,CHANNEL_STATE
      )
    values (#{id,jdbcType=DECIMAL}, #{issridAccount,jdbcType=VARCHAR}, #{amount,jdbcType=DECIMAL}, 
      #{trxCategory,jdbcType=VARCHAR}, #{state,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{userId,jdbcType=DECIMAL}, #{channelTradeNo,jdbcType=VARCHAR}, #{channelReturnCode,jdbcType=VARCHAR},
      #{channelReturnMessage,jdbcType=VARCHAR}, #{channelReturnDate,jdbcType=TIMESTAMP} , #{transactionNo,jdbcType=VARCHAR}
       , #{channelState,jdbcType=VARCHAR}
      )
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.pas.domain.UnionQuotaRecord" >
      update PAS_UNION_QUOTA_RECORD
      <set >
        <if test="issridAccount != null" >
          ISSRID_ACCOUNT = #{issridAccount,jdbcType=VARCHAR},
        </if>
        <if test="amount != null" >
          AMOUNT = #{amount,jdbcType=DECIMAL},
        </if>
        <if test="trxCategory != null" >
          TRX_CATEGORY = #{trxCategory,jdbcType=VARCHAR},
        </if>
        <if test="state != null" >
          STATE = #{state,jdbcType=VARCHAR},
        </if>
        <if test="userId != null" >
          USER_ID = #{userId,jdbcType=DECIMAL},
        </if>
        <if test="channelTradeNo != null" >
          CHANNEL_TRADE_NO = #{channelTradeNo,jdbcType=VARCHAR},
        </if>
        <if test="channelReturnCode != null" >
          CHANNEL_RETURN_CODE = #{channelReturnCode,jdbcType=VARCHAR},
        </if>
        <if test="channelReturnMessage != null" >
          CHANNEL_RETURN_MESSAGE = #{channelReturnMessage,jdbcType=VARCHAR},
        </if>
        <if test="channelReturnDate != null" >
          CHANNEL_RETURN_DATE = #{channelReturnDate,jdbcType=TIMESTAMP},
        </if>
        <if test="transactionNo != null" >
          TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR},
        </if>
        <if test="channelState != null" >
          CHANNEL_STATE = #{channelState,jdbcType=VARCHAR},
        </if>
      </set>
      where ID = #{id,jdbcType=DECIMAL}
    </update>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.pas.domain.UnionQuotaRecord" >
    insert into PAS_UNION_QUOTA_RECORD
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="issridAccount != null" >
        ISSRID_ACCOUNT,
      </if>
      <if test="transactionNo != null" >
        TRANSACTION_NO,
      </if>
      <if test="amount != null" >
        AMOUNT,
      </if>
      <if test="trxCategory != null" >
        TRX_CATEGORY,
      </if>
      <if test="state != null" >
        STATE,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="userId != null" >
        USER_ID,
      </if>
      <if test="channelTradeNo != null" >
        CHANNEL_TRADE_NO,
      </if>
      <if test="channelReturnCode != null" >
        CHANNEL_RETURN_CODE,
      </if>
      <if test="channelReturnMessage != null" >
        CHANNEL_RETURN_MESSAGE,
      </if>
      <if test="channelReturnDate != null" >
        CHANNEL_RETURN_DATE,
      </if>
      <if test="channelState != null">
        CHANNEL_STATE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="issridAccount != null" >
        #{issridAccount,jdbcType=VARCHAR},
      </if>
      <if test="transactionNo != null" >
        #{transactionNo,jdbcType=VARCHAR},
      </if>
      <if test="amount != null" >
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="trxCategory != null" >
        #{trxCategory,jdbcType=VARCHAR},
      </if>
      <if test="state != null" >
        #{state,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=DECIMAL},
      </if>
      <if test="channelTradeNo != null" >
        #{channelTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="channelReturnCode != null" >
        #{channelReturnCode,jdbcType=VARCHAR},
      </if>
      <if test="channelReturnMessage != null" >
        #{channelReturnMessage,jdbcType=VARCHAR},
      </if>
      <if test="channelReturnDate != null" >
        #{channelReturnDate,jdbcType=TIMESTAMP},
      </if>
      <if test="channelState != null">
        CHANNEL_STATE = #{channelState,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="selectByTransactionNo" resultMap="BaseResultMap">
    select *
    from PAS_UNION_QUOTA_RECORD
    where TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
  </select>

  <select id="selectByParam" resultType="java.lang.Integer"
     			parameterType="java.util.Map">
     		select  count(*)
    from PAS_UNION_QUOTA_RECORD t,pas_user u
              where t.user_id =u.user_id
      <if test="id != null ">
        AND t.ID = #{id,jdbcType=DECIMAL}
      </if>

      <if test="transactionNo != null ">
        AND t.TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
      </if>
      <!--<if test="state != null">-->
        <!--AND STATE = #{state,jdbcType=VARCHAR}-->
      <!--</if>-->
      <if test="channelState != null">
        AND t.CHANNEL_STATE = #{channelState,jdbcType=VARCHAR}
      </if>
      <if test="trxCategory != null">
        AND t.TRX_CATEGORY = #{trxCategory,jdbcType=VARCHAR}
      </if>
      <if test="beginCreateTime != null">
        AND t.CREATE_TIME <![CDATA[ >= ]]>  #{beginCreateTime,jdbcType=TIMESTAMP}
      </if>
      <if test="endCreateTime != null">
        AND t.CREATE_TIME <![CDATA[ <= ]]>  #{endCreateTime,jdbcType=TIMESTAMP}
      </if>
     	</select>

     	<select id="selectByParamByPage" resultMap="BaseResultMap"
     			parameterType="java.util.Map">
     		select
          <include refid="Base_Column_List"/>
     		from (
     		select A.*, rownum RN
     		from (
     		select t.ID, t.ISSRID_ACCOUNT, t.AMOUNT,
          t.TRX_CATEGORY, t.STATE, t.CREATE_TIME,
          t.USER_ID, t.CHANNEL_TRADE_NO, t.CHANNEL_RETURN_CODE,
          t.CHANNEL_RETURN_MESSAGE, t.CHANNEL_RETURN_DATE,t.TRANSACTION_NO ,t.CHANNEL_STATE ,t.REMARK,u.name  as USER_NAME
          from PAS_UNION_QUOTA_RECORD t,pas_user u
                    where t.user_id =u.user_id
            <if test="id != null ">
              AND t.ID = #{id,jdbcType=DECIMAL}
            </if>

            <if test="transactionNo != null ">
              AND t.TRANSACTION_NO = #{transactionNo,jdbcType=VARCHAR}
            </if>
            <!--<if test="state != null">-->
              <!--AND STATE = #{state,jdbcType=VARCHAR}-->
            <!--</if>-->
            <if test="channelState != null">
              AND t.CHANNEL_STATE = #{channelState,jdbcType=VARCHAR}
            </if>
            <if test="trxCategory != null">
              AND t.TRX_CATEGORY = #{trxCategory,jdbcType=VARCHAR}
            </if>
            <if test="beginCreateTime != null">
              AND t.CREATE_TIME <![CDATA[ >= ]]>  #{beginCreateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endCreateTime != null">
              AND t.CREATE_TIME <![CDATA[ <= ]]>  #{endCreateTime,jdbcType=TIMESTAMP}
            </if>
          order by t.CREATE_TIME desc
     		) A
     		where rownum &lt;= #{endRowNo,jdbcType=DECIMAL}
     		)
     		where RN &gt;= #{beginRowNo,jdbcType=DECIMAL}
     	</select>
</mapper>