<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.pas.pas.dao.BusinessCategoryMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.pas.domain.BusinessCategory">
    <id column="CODE" jdbcType="VARCHAR" property="code" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="STATUS" jdbcType="VARCHAR" property="status" />
    <result column="SETT_GRAINED" jdbcType="VARCHAR" property="settGrained"/>
  </resultMap>
  <sql id="Base_Column_List">
    CODE, name, CREATE_TIME, UPDATE_TIME, STATUS , SETT_GRAINED
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from PAS_BUSINESS_CATEGORY
    where CODE = #{code,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from PAS_BUSINESS_CATEGORY
    where CODE = #{code,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.epaylinks.efps.pas.pas.domain.BusinessCategory">
    insert into PAS_BUSINESS_CATEGORY (CODE, name, CREATE_TIME, 
      UPDATE_TIME, STATUS , SETT_GRAINED)
    values (#{code,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{status,jdbcType=VARCHAR} , 
      #{settGrained , jdbcType = VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.pas.domain.BusinessCategory">
    insert into PAS_BUSINESS_CATEGORY
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="code != null">
        CODE,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="status != null">
        STATUS,
      </if>
      <if test="settGrained != null">
        SETT_GRAINED,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="settGrained != null">
        #{settGrained,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.pas.domain.BusinessCategory">
    update PAS_BUSINESS_CATEGORY
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        STATUS = #{status,jdbcType=VARCHAR},
      </if>
      <if test="settGrained != null">
        SETT_GRAINED = #{settGrained,jdbcType=VARCHAR},
      </if>
    </set>
    where CODE = #{code,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.pas.domain.BusinessCategory">
    update PAS_BUSINESS_CATEGORY
    set name = #{name,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      STATUS = #{status,jdbcType=VARCHAR},
      SETT_GRAINED = #{settGrained,jdbcType=VARCHAR}
    where CODE = #{code,jdbcType=VARCHAR}
  </update>
  <select id="queryAll" resultMap="BaseResultMap">
  	select 
  	<include refid="Base_Column_List" /> 
  	from PAS_BUSINESS_CATEGORY   where status='1'
  </select>

  <select id="selectBySelective" resultMap="BaseResultMap" parameterType="com.epaylinks.efps.pas.pas.domain.BusinessCategory" >
      select
      <include refid="Base_Column_List" />
      from PAS_BUSINESS_CATEGORY
      <where>
        <if test="code != null and code != ''" >
          AND code = #{code,jdbcType=DECIMAL}
        </if>
        <if test="name != null and name != ''" >
          AND name = #{name,jdbcType=VARCHAR}
        </if>

      </where>
    </select>
  <select id="getBusinessCategoryByCodes" resultMap="BaseResultMap"  >
    select
    <include refid="Base_Column_List" />
    from PAS_BUSINESS_CATEGORY where CODE IN
    <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>
</mapper>