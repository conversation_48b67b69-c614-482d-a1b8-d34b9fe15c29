<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.pas.pas.dao.HolidayMapper">
    <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.pas.domain.Holiday">
        <result column="DATE_STR" jdbcType="VARCHAR" property="dateStr"/>
        <result column="TYPE" jdbcType="VARCHAR" property="type"/>
        <result column="CREATOR" jdbcType="DECIMAL" property="creator"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="DATE_YEAR" jdbcType="VARCHAR" property="dateYear"/>
        <result column="NAME" jdbcType="VARCHAR" property="name"/>
    </resultMap>

    <resultMap id="BaseResultMap1" type="com.epaylinks.efps.pas.mch.domain.PasHoliday">
        <result column="DATE_STR" jdbcType="VARCHAR" property="dateStr"/>
        <result column="TYPE" jdbcType="VARCHAR" property="type"/>
    </resultMap>


    <sql id="Base_Column_List">
    DATE_STR, TYPE ,CREATOR  ,CREATE_TIME,NAME
  </sql>
    <!-- 批量保存用户,并返回每个用户插入的ID -->
    <!--<insert id="batchSave" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">-->
    <!--<insert id="batchSave" parameterType="java.util.List" >
        INSERT INTO PAS_HOLIDAY(DATE_STR, TYPE)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.dateStr}, #{item.type})
        </foreach>
    </insert>-->
    <insert id="batchSave" parameterType="java.util.List">
        BEGIN
        <foreach collection="list" item="item" separator="">
            INSERT INTO PAS_HOLIDAY(DATE_STR, TYPE,creator,create_time)
            VALUES
            (
            #{item.dateStr}, #{item.type}, #{item.creator}, #{item.createTime});
        </foreach>
        COMMIT;
        END;
    </insert>
    <delete id="delete">
     DELETE FROM PAS_HOLIDAY WHERE to_char(to_date(date_str,'yyyy-MM-dd'),'yyyy') = #{dateYear,jdbcType=VARCHAR}
   </delete>

    <select id="selectByYear" resultMap="BaseResultMap"
            parameterType="java.util.Map">
        select to_char(to_date(date_str,'yyyy-MM-dd'),'yyyy') as DATE_YEAR,
        DATE_STR, TYPE ,CREATOR ,CREATE_TIME
        from PAS_HOLIDAY
        <where>
            <if test="dateYear != null and dateYear !=''">
                to_char(to_date(date_str,'yyyy-MM-dd'),'yyyy') = #{dateYear,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="selectCountHoliday" resultType="java.lang.Integer"
            parameterType="java.util.Map">
        select count(sum(1))
        from PAS_HOLIDAY
        <where>
            <if test="dateYear != null and dateYear !=''">
                to_char(to_date(date_str,'yyyy-MM-dd'),'yyyy') = #{dateYear,jdbcType=VARCHAR}
            </if>
        </where>
        group by to_char(to_date(date_str,'yyyy-MM-dd'),'yyyy')
    </select>

    <select id="selectByPage" resultMap="BaseResultMap"
            parameterType="java.util.Map">
        select DATE_YEAR,
        DATE_STR, TYPE ,CREATOR ,CREATE_TIME
        from (
        select A.*, rownum RN
        from (
        select to_char(to_date(date_str,'yyyy-MM-dd'),'yyyy') as DATE_YEAR,
        DATE_STR, TYPE ,CREATOR ,CREATE_TIME
        from PAS_HOLIDAY
        <where>
            <if test="dateYear != null and dateYear !=''">
                to_char(to_date(date_str,'yyyy-MM-dd'),'yyyy') = #{dateYear,jdbcType=VARCHAR}
            </if>
        </where>
        order by DATE_YEAR desc,DATE_STR
        ) A
        where rownum &lt;= #{endRowNo,jdbcType=DECIMAL}
        )
        where RN &gt;= #{beginRowNo,jdbcType=DECIMAL}
    </select>

    <select id="selectByPage2" resultMap="BaseResultMap"
            parameterType="java.util.Map">
        select DATE_YEAR,
        DATE_STR, CREATOR ,CREATE_TIME,NAME
        from (
        select A.*, rownum RN
        from (
        SELECT t.date_year ,max(substr(sys_connect_by_path(t.date_m, ','), 2)) as DATE_STR, CREATE_TIME,creator,name
        FROM (SELECT date_year, date_m, row_number() over(PARTITION BY date_year order by rowid ) rn ,
        CREATE_TIME,creator,name
        FROM (select date_year,date_m, CREATE_TIME,creator,name from (
        select to_char(to_date(date_str,'yyyy-MM-dd'),'yyyy') as date_year ,
        to_char(to_date(date_str,'yyyy-MM-dd'),'MM-dd') as date_m, p.CREATE_TIME,p.creator,pas.name
        from pas_holiday p,pas_user pas where pas.user_id(+) =p.creator
        <where>
            <if test="dateYear != null and dateYear !=''">
                to_char(to_date(date_str,'yyyy-MM-dd'),'yyyy') = #{dateYear,jdbcType=VARCHAR}
            </if>
        </where>
        ))) t
        START WITH rn = 1
        CONNECT BY rn = PRIOR rn + 1
        AND date_year = PRIOR date_year
        GROUP BY t.date_year, t.CREATE_TIME,t.creator,name
        order by t.date_year desc,date_str
        ) A
        where rownum &lt;= #{endRowNo,jdbcType=DECIMAL}
        )
        where RN &gt;= #{beginRowNo,jdbcType=DECIMAL}
    </select>

    <select id="selectByDate" resultMap="BaseResultMap1" parameterType="String">
        SELECT
        DATE_STR,TYPE
        FROM pas_holiday
        WHERE DATE_STR = #{date,jdbcType = VARCHAR}
    </select>
</mapper>