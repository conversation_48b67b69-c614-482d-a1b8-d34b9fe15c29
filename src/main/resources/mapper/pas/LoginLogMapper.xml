<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.pas.pas.dao.LoginLogMapper">
    
    
    <insert id="insertLoginLog" parameterType="com.epaylinks.efps.pas.pas.domain.PasLoginLog">
		INSERT INTO
		pas_Login_Log(log_id,username,usertype,lrole,
		realname,customercode,platcustomer,
		servicecustomer,login_time,login_ip,
		state,remark,create_time, log_type, target_customercode
		)
		VALUES(#{log_id,jdbcType=VARCHAR},#{username,jdbcType=VARCHAR},#{usertype,jdbcType=VARCHAR},#{lrole,jdbcType=VARCHAR},
		#{realname,jdbcType=VARCHAR},#{customercode,jdbcType=VARCHAR},#{platcustomer,jdbcType=VARCHAR},
		#{servicecustomer,jdbcType=VARCHAR},to_date(#{login_time,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss'),#{login_ip,jdbcType=VARCHAR},
		#{state,jdbcType=VARCHAR},#{remark,jdbcType=VARCHAR},to_date(#{create_time,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss'),
        #{logType,jdbcType=DECIMAL}, #{targetCustomercode,jdbcType=VARCHAR}
		)
	</insert>
    
	<!-- 统计总数  -->
    <select id="pageQueryTotal" resultType="int" parameterType="com.epaylinks.efps.pas.pas.vo.PasLoginLogReq">
        SELECT COUNT(log_id) FROM pas_Login_Log WHERE 1 = 1
        <if test="startTime != null and startTime !=''">
		    and login_time &gt;= to_date(#{startTime,jdbcType=VARCHAR},'yyyymmddhh24miss')
		</if>
		<if test="endTime != null and endTime !=''">
		    and login_time &lt;= to_date(#{endTime,jdbcType=VARCHAR},'yyyymmddhh24miss')
		</if>
        <if test="username != null and username != ''">
            and username = #{username,jdbcType=VARCHAR}
        </if>
        <if test="usertype != null and usertype != ''">
            and usertype = #{usertype,jdbcType=VARCHAR}
        </if>
        <if test="state != null and state != ''">
            and state = #{state,jdbcType=VARCHAR}
        </if>
        <if test="customercode != null and customercode != ''">
            and customercode = #{customercode,jdbcType=VARCHAR}
        </if>
        <if test="platcustomer != null and platcustomer != ''">
            and platcustomer like '%'|| #{platcustomer,jdbcType=VARCHAR} || '%'
        </if>
        <if test="servicecustomer != null and servicecustomer != ''">
            and servicecustomer like '%'|| #{servicecustomer,jdbcType=VARCHAR} || '%'
        </if>
        <if test="logType != null">
            and log_type = #{logType,jdbcType=DECIMAL}
        </if>
    </select>
    
	<!-- 运营门户登录日志查询导出  -->
    <select id="queryLoginLogForPasNoPage" resultType="com.epaylinks.efps.pas.pas.vo.PasLoginLogResp" parameterType="com.epaylinks.efps.pas.pas.vo.PasLoginLogReq">
        SELECT log_id,username,lrole,realname,customercode,platcustomer,servicecustomer,
        to_char(login_time,'yyyy-mm-dd hh24:mi:ss') as "login_time",login_ip,
        (case state when '1' then '成功' else '失败' end) as state,remark
        FROM pas_Login_Log WHERE 1 = 1
         <if test="startTime != null and startTime !=''">
		    and login_time &gt;= to_date(#{startTime,jdbcType=VARCHAR},'yyyymmddhh24miss')
		</if>
		<if test="endTime != null and endTime !=''">
		    and login_time &lt;= to_date(#{endTime,jdbcType=VARCHAR},'yyyymmddhh24miss')
		</if>
        <if test="username != null and username != ''">
            and username = #{username,jdbcType=VARCHAR}
        </if>
        <if test="usertype != null and usertype != ''">
            and usertype = #{usertype,jdbcType=VARCHAR}
        </if>
        <if test="state != null and state != ''">
            and state = #{state,jdbcType=VARCHAR}
        </if>
        <if test="customercode != null and customercode != ''">
            and customercode = #{customercode,jdbcType=VARCHAR}
        </if>
        <if test="platcustomer != null and platcustomer != ''">
            and platcustomer like '%'|| #{platcustomer,jdbcType=VARCHAR} || '%'
        </if>
        <if test="servicecustomer != null and servicecustomer != ''">
            and servicecustomer like '%'|| #{servicecustomer,jdbcType=VARCHAR} || '%'
        </if>
        <if test="logType != null">
            and log_type = #{logType,jdbcType=DECIMAL}
        </if>
        ORDER BY login_time DESC
    </select>
    
    <!-- 商户门户登录日志查询导出  -->
    <select id="queryLoginLogForPpsNoPage" resultType="com.epaylinks.efps.pas.pas.vo.PasLoginLogResp" parameterType="com.epaylinks.efps.pas.pas.vo.PasLoginLogReq">
        SELECT 
        log_id,username,lrole,realname,customercode,platcustomer,servicecustomer,
        to_char(login_time,'yyyy-mm-dd hh24:mi:ss') as "login_time",login_ip,
        (case state when '1' then '成功' else '失败' end) as state,remark
        FROM pas_Login_Log WHERE 1 = 1
         <if test="startTime != null and startTime !=''">
		    and login_time &gt;= to_date(#{startTime,jdbcType=VARCHAR},'yyyymmddhh24miss')
		</if>
		<if test="endTime != null and endTime !=''">
		    and login_time &lt;= to_date(#{endTime,jdbcType=VARCHAR},'yyyymmddhh24miss')
		</if>
        <if test="username != null and username != ''">
            and username = #{username,jdbcType=VARCHAR}
        </if>
        <if test="usertype != null and usertype != ''">
            and usertype = #{usertype,jdbcType=VARCHAR}
        </if>
        <if test="state != null and state != ''">
            and state = #{state,jdbcType=VARCHAR}
        </if>
        <if test="customercode != null and customercode != ''">
            and customercode = #{customercode,jdbcType=VARCHAR}
        </if>
        <if test="platcustomer != null and platcustomer != ''">
            and platcustomer like '%'|| #{platcustomer,jdbcType=VARCHAR} || '%'
        </if>
        <if test="servicecustomer != null and servicecustomer != ''">
            and servicecustomer like '%'|| #{servicecustomer,jdbcType=VARCHAR} || '%'
        </if>
        <if test="logType != null">
            and log_type = #{logType,jdbcType=DECIMAL}
        </if>
        ORDER BY login_time DESC
    </select>

	<!-- 运营门户登录日志查询 -->
    <select id="queryLoginLogForPas" resultType="com.epaylinks.efps.pas.pas.vo.PasLoginLogResp" parameterType="com.epaylinks.efps.pas.pas.vo.PasLoginLogReq">
       SELECT * FROM (
        SELECT a.* ,rownum rn FROM (
        SELECT log_id,username,lrole,realname,customercode,platcustomer,servicecustomer,
        to_char(login_time,'yyyy-mm-dd hh24:mi:ss') as "login_time",login_ip,
        (case state when '1' then '成功' else '失败' end) as state,remark
        FROM pas_Login_Log WHERE 1 = 1
         <if test="startTime != null and startTime !=''">
		    and login_time &gt;= to_date(#{startTime,jdbcType=VARCHAR},'yyyymmddhh24miss')
		</if>
		<if test="endTime != null and endTime !=''">
		    and login_time &lt;= to_date(#{endTime,jdbcType=VARCHAR},'yyyymmddhh24miss')
		</if>
        <if test="username != null and username != ''">
            and username = #{username,jdbcType=VARCHAR}
        </if>
        <if test="usertype != null and usertype != ''">
            and usertype = #{usertype,jdbcType=VARCHAR}
        </if>
        <if test="state != null and state != ''">
            and state = #{state,jdbcType=VARCHAR}
        </if>
        <if test="customercode != null and customercode != ''">
            and customercode = #{customercode,jdbcType=VARCHAR}
        </if>
        <if test="platcustomer != null and platcustomer != ''">
            and platcustomer like '%'|| #{platcustomer,jdbcType=VARCHAR} || '%'
        </if>
        <if test="servicecustomer != null and servicecustomer != ''">
            and servicecustomer like '%'|| #{servicecustomer,jdbcType=VARCHAR} || '%'
        </if>
        <if test="logType != null">
            and log_type = #{logType,jdbcType=DECIMAL}
        </if>
        ORDER BY login_time DESC
        )a WHERE rownum &lt;= #{endNum}
        ) WHERE rn &gt;= #{startNum}
    </select>
    
    <!-- 商户门户登录日志查询 -->
    <select id="queryLoginLogForPps" resultType="com.epaylinks.efps.pas.pas.vo.PasLoginLogResp" parameterType="com.epaylinks.efps.pas.pas.vo.PasLoginLogReq">
       SELECT * FROM (
        SELECT a.* ,rownum rn FROM (
        SELECT 
        log_id,username,lrole,realname,customercode,platcustomer,servicecustomer,
        to_char(login_time,'yyyy-mm-dd hh24:mi:ss') as "login_time",login_ip,
        (case state when '1' then '成功' else '失败' end) as state,remark
        FROM pas_Login_Log WHERE 1 = 1
         <if test="startTime != null and startTime !=''">
		    and login_time &gt;= to_date(#{startTime,jdbcType=VARCHAR},'yyyymmddhh24miss')
		</if>
		<if test="endTime != null and endTime !=''">
		    and login_time &lt;= to_date(#{endTime,jdbcType=VARCHAR},'yyyymmddhh24miss')
		</if>
        <if test="username != null and username != ''">
            and username = #{username,jdbcType=VARCHAR}
        </if>
        <if test="usertype != null and usertype != ''">
            and usertype = #{usertype,jdbcType=VARCHAR}
        </if>
        <if test="state != null and state != ''">
            and state = #{state,jdbcType=VARCHAR}
        </if>
        <if test="customercode != null and customercode != ''">
            and customercode = #{customercode,jdbcType=VARCHAR}
        </if>
        <if test="platcustomer != null and platcustomer != ''">
            and platcustomer like '%'|| #{platcustomer,jdbcType=VARCHAR} || '%'
        </if>
        <if test="servicecustomer != null and servicecustomer != ''">
            and servicecustomer like '%'|| #{servicecustomer,jdbcType=VARCHAR} || '%'
        </if>
        <if test="logType != null">
            and log_type = #{logType,jdbcType=DECIMAL}
        </if>
        ORDER BY login_time DESC
        )a WHERE rownum &lt;= #{endNum}
        ) WHERE rn &gt;= #{startNum}
    </select>
    
    
    <!-- 登录日志汇总总数 -->
    <select id="countReport" resultType="int" parameterType="com.epaylinks.efps.pas.pas.vo.PasLoginLogReq">
        select count(*) from(
            select t.username from 
            pas_Login_Log t where 1 =1  
            <if test="startTime != null and startTime !=''">
                and login_time &gt;= to_date(#{startTime,jdbcType=VARCHAR},'yyyymmddhh24miss')
            </if>
            <if test="endTime != null and endTime !=''">
                and login_time &lt;= to_date(#{endTime,jdbcType=VARCHAR},'yyyymmddhh24miss')
            </if>
            <if test="username != null and username != ''">
                and username = #{username,jdbcType=VARCHAR}
            </if>
            <if test="usertype != null and usertype != ''">
                and usertype = #{usertype,jdbcType=VARCHAR}
            </if>
            <if test="state != null and state != ''">
                and state = #{state,jdbcType=VARCHAR}
            </if>
            <if test="logType != null">
                and log_type = #{logType,jdbcType=DECIMAL}
            </if>
            group by t.username, t.customercode, t.usertype , t.lrole, t.realname, to_char(login_time,'yyyy-mm-dd') , t.login_ip , t.state   
        )
    </select>
    
    
    <!-- 分页查询登录日志汇总 -->
    <select id="pageQueryReport" resultType="com.epaylinks.efps.pas.pas.vo.PasLoginLogReportResp" 
        parameterType="com.epaylinks.efps.pas.pas.vo.PasLoginLogReq">
       SELECT * FROM (
        SELECT a.* ,rownum rn FROM (
            SELECT 
            username, lrole, realname, customercode,
            to_char(login_time,'yyyy-mm-dd') as loginDate, login_ip,
            (case state when '1' then '成功' else '失败' end) as state,
            count(*) loginCount
            FROM pas_Login_Log t WHERE 1 = 1
             <if test="startTime != null and startTime !=''">
                and login_time &gt;= to_date(#{startTime,jdbcType=VARCHAR},'yyyymmddhh24miss')
            </if>
            <if test="endTime != null and endTime !=''">
                and login_time &lt;= to_date(#{endTime,jdbcType=VARCHAR},'yyyymmddhh24miss')
            </if>
            <if test="username != null and username != ''">
                and username = #{username,jdbcType=VARCHAR}
            </if>
            <if test="usertype != null and usertype != ''">
                and usertype = #{usertype,jdbcType=VARCHAR}
            </if>
            <if test="state != null and state != ''">
                and state = #{state,jdbcType=VARCHAR}
            </if>
            <if test="logType != null">
                and log_type = #{logType,jdbcType=DECIMAL}
            </if>
            GROUP BY t.username, t.customercode, t.usertype , t.lrole, t.realname, to_char(login_time,'yyyy-mm-dd'), t.login_ip , t.state   
            ORDER BY loginDate DESC
          ) a WHERE rownum &lt;= #{endNum}
        ) WHERE rn &gt;= #{startNum}
    </select>

    <select id="queryBeforeDayList" resultType="com.epaylinks.efps.pas.pas.domain.LoginReport" >
        SELECT
            t.username loginAccount,
            t.REALNAME name,
            t.lrole loginRole,
            t.customercode customerCode,
            to_char(t.login_time,'yyyy-mm-dd') as loginTime,
            t.login_ip loginIp,
            t.usertype userType,
            count(1) loginNum
        FROM pas_Login_Log t
        WHERE 1 = 1
        and to_char(login_time,'yyyy-mm-dd') = to_char(sysdate - 1,'yyyy-mm-dd')
        GROUP BY t.username, t.customercode, t.usertype , t.lrole, t.realname, to_char(login_time,'yyyy-mm-dd'), t.login_ip
    </select>

    <select id="checkLoginTime" parameterType="java.util.Map" resultType="java.lang.Integer">
        select count(*) from pas_Login_Log l
        where l.username = #{loginAccount,jdbcType=VARCHAR}
        and l.customercode = #{customerCode,jdbcType=VARCHAR}
        and l.usertype = #{userType,jdbcType=VARCHAR}
        and l.lrole = #{loginRole,jdbcType=VARCHAR}
        and l.realname = #{name,jdbcType=VARCHAR}
        and to_char(l.login_time,'yyyy-mm-dd') = #{loginTime,jdbcType=VARCHAR}
        and l.login_ip = #{loginIp,jdbcType=VARCHAR}
        and to_char(l.login_time,'hh24:mi') &gt; #{startTime,jdbcType=VARCHAR}
        and to_char(l.login_time,'hh24:mi') &lt; #{endTime,jdbcType=VARCHAR}
    </select>
</mapper>