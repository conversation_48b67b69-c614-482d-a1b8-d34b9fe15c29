<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.epaylinks.efps.pas.pas.dao.BankMapper" >
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.pas.domain.Bank" >
    <id column="LBNK_CD" property="lbnkCd" jdbcType="VARCHAR" />
    <result column="BNK_NM" property="bnkNm" jdbcType="VARCHAR" />
    <result column="CORP_ORG" property="corpOrg" jdbcType="VARCHAR" />
    <result column="FLG" property="flg" jdbcType="VARCHAR" />
    <result column="TM_SMP" property="tmSmp" jdbcType="VARCHAR" />
    <result column="NOD_ID" property="nodId" jdbcType="VARCHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="OPERATOR_ID" property="operatorId" jdbcType="VARCHAR" />
    <result column="FLAG" property="flag" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    LBNK_CD, BNK_NM, CORP_ORG, FLG, TM_SMP, NOD_ID,CREATE_TIME,UPDATE_TIME,OPERATOR_ID,FLAG
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from PAS_BKCD
    where LBNK_CD = #{lbnkCd,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from PAS_BKCD
    where LBNK_CD = #{lbnkCd,jdbcType=VARCHAR}
  </delete>
  <insert id="insertSelective" parameterType="com.epaylinks.efps.pas.pas.domain.Bank" >
    insert into PAS_BKCD
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="lbnkCd != null" >
        LBNK_CD,
      </if>
      <if test="bnkNm != null" >
        BNK_NM,
      </if>
      <if test="corpOrg != null" >
        CORP_ORG,
      </if>
      <if test="flg != null" >
        FLG,
      </if>
      <if test="tmSmp != null" >
        TM_SMP,
      </if>
      <if test="nodId != null" >
        NOD_ID,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME,
      </if>
      <if test="operatorId != null" >
        OPERATOR_ID,
      </if>
      <if test="flag != null" >
        FLAG,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="lbnkCd != null" >
        #{lbnkCd,jdbcType=VARCHAR},
      </if>
      <if test="bnkNm != null" >
        #{bnkNm,jdbcType=VARCHAR},
      </if>
      <if test="corpOrg != null" >
        #{corpOrg,jdbcType=VARCHAR},
      </if>
      <if test="flg != null" >
        #{flg,jdbcType=VARCHAR},
      </if>
      <if test="tmSmp != null" >
        #{tmSmp,jdbcType=VARCHAR},
      </if>
      <if test="nodId != null" >
        #{nodId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operatorId != null" >
        #{operatorId,jdbcType=VARCHAR},
      </if>
      <if test="flag != null" >
        #{flag,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.epaylinks.efps.pas.pas.domain.Bank" >
    update PAS_BKCD
    <set >
      <if test="bnkNm != null" >
        BNK_NM = #{bnkNm,jdbcType=VARCHAR},
      </if>
      <if test="corpOrg != null" >
        CORP_ORG = #{corpOrg,jdbcType=VARCHAR},
      </if>
      <if test="flg != null" >
        FLG = #{flg,jdbcType=VARCHAR},
      </if>
      <if test="tmSmp != null" >
        TM_SMP = #{tmSmp,jdbcType=VARCHAR},
      </if>
      <if test="nodId != null" >
        NOD_ID = #{nodId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operatorId != null" >
        OPERATOR_ID = #{operatorId,jdbcType=VARCHAR},
      </if>
      <if test="flag != null" >
        FLAG = #{flag,jdbcType=VARCHAR},
      </if>
    </set>
    where LBNK_CD = #{lbnkCd,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.epaylinks.efps.pas.pas.domain.Bank" >
    update PAS_BKCD
    set BNK_NM = #{bnkNm,jdbcType=VARCHAR},
      CORP_ORG = #{corpOrg,jdbcType=VARCHAR},
      FLG = #{flg,jdbcType=VARCHAR},
      TM_SMP = #{tmSmp,jdbcType=VARCHAR},
      NOD_ID = #{nodId,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      OPERATOR_ID = #{operatorId,jdbcType=VARCHAR},
      FLAG = #{flag,jdbcType=VARCHAR}
    where LBNK_CD = #{lbnkCd,jdbcType=VARCHAR}
  </update>

  <select id="selectAll" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from PAS_BKCD
  </select>

  <select id="selectBankPage" resultMap="BaseResultMap"
          parameterType="java.util.Map">
    select
    <include refid="Base_Column_List" />
    from (
    select A.*, rownum RN
    from (
    select * from PAS_BKCD
    <where>
      <include refid="tradeQueryCondition"/>
    </where>
    order by CREATE_TIME desc
    ) A
    where rownum &lt;= #{endRowNo,jdbcType=DECIMAL}
    )
    where RN &gt;=
    #{beginRowNo,jdbcType=DECIMAL}
  </select>

  <select id="selectCountByMap" resultType="java.lang.Integer"
          parameterType="java.util.Map">
    select count(*)
    from PAS_BKCD
    <where>
      <include refid="tradeQueryCondition"/>
    </where>
  </select>

  <sql id = "tradeQueryCondition">
    <if test="beginTime != null">
      AND CREATE_TIME <![CDATA[ >= ]]>
      #{beginTime,jdbcType=TIMESTAMP}
    </if>
    <if test="endTime != null">
      AND CREATE_TIME <![CDATA[ <= ]]>
      #{endTime,jdbcType=TIMESTAMP}
    </if>
    <if test="updateBeginTime != null ">
      AND UPDATE_TIME <![CDATA[ >= ]]>
      #{updateBeginTime,jdbcType=TIMESTAMP}
    </if>
    <if test="updateEndTime != null">
      AND UPDATE_TIME <![CDATA[ <= ]]>
      #{updateEndTime,jdbcType=TIMESTAMP}
    </if>
    <if test="bankId != null and bankId != ''" >
      AND LBNK_CD like concat('%',concat(#{bankId},'%'))
    </if>
    <if test="bankName != null and bankName != ''" >
      AND BNK_NM like concat('%',concat(#{bankName},'%'))
    </if>
  </sql>
</mapper>