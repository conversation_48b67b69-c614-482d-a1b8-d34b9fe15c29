<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epaylinks.efps.pas.cum.dao.CumCustomerInfoMapper">
  <resultMap id="BaseResultMap" type="com.epaylinks.efps.pas.cum.domain.CumCustomerInfo">
    <id column="INFO_ID" jdbcType="DECIMAL" property="infoId" />
    <result column="CUSTOMER_ID" jdbcType="DECIMAL" property="customerId" />
    <result column="CUSTOMER_CODE" jdbcType="VARCHAR" property="customerCode" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="SHORT_NAME" jdbcType="VARCHAR" property="shortName" />
    <result column="MOBILE" jdbcType="VARCHAR" property="mobile" />
    <result column="TELEPHONE" jdbcType="VARCHAR" property="telephone" />
    <result column="BUSINESS_ADDRESS" jdbcType="VARCHAR" property="businessAddress" />
    <result column="USE_USCC" jdbcType="DECIMAL" property="useUscc" />
    <result column="CUSTOMER_TYPE" jdbcType="DECIMAL" property="customerType" />
    <result column="BUSINESS_LICENSE_NO" jdbcType="VARCHAR" property="businessLicenseNo" />
    <result column="BUSINESS_LICENSE_EXP_DATE" jdbcType="TIMESTAMP" property="businessLicenseExpDate" />
    <result column="REGISTERED_ADDRESS" jdbcType="VARCHAR" property="registeredAddress" />
    <result column="NAT_TAX_REG_CER_NO" jdbcType="VARCHAR" property="natTaxRegCerNo" />
    <result column="NAT_TAX_REG_EXP_DATE" jdbcType="TIMESTAMP" property="natTaxRegExpDate" />
    <result column="LOC_TAX_REG_CER_NO" jdbcType="VARCHAR" property="locTaxRegCerNo" />
    <result column="LOC_TAX_REG_CER_EXP_DATE" jdbcType="TIMESTAMP" property="locTaxRegCerExpDate" />
    <result column="ORG_STRUCTURE_CODE" jdbcType="VARCHAR" property="orgStructureCode" />
    <result column="LEA_PERSON_NAME" jdbcType="VARCHAR" property="leaPersonName" />
    <result column="LEA_PERSONI_DENTIFICATION_TYPE" jdbcType="DECIMAL" property="leaPersoniDentificationType" />
    <result column="LEA_PERSONI_DENTIFICATION_NO" jdbcType="VARCHAR" property="leaPersoniDentificationNo" />
    <result column="LEA_PER_DEN_EXP_DATE" jdbcType="TIMESTAMP" property="leaPerDenExpDate" />
    <result column="PARENT_CUSTOMER_CODE" jdbcType="VARCHAR" property="parentCustomerCode" />
    <result column="WEB_ADDRESS" jdbcType="VARCHAR" property="webAddress" />
    <result column="ICP_LICENSE_NO" jdbcType="VARCHAR" property="icpLicenseNo" />
    <result column="BUSINESS_SCOPE" jdbcType="VARCHAR" property="businessScope" />
    <result column="INDUSTRY_DESCRIPTION" jdbcType="VARCHAR" property="industryDescription" />
    <result column="EMPLOYEE_COUNT" jdbcType="DECIMAL" property="employeeCount" />
    <result column="BUSINESS_TYPE" jdbcType="CHAR" property="businessType" />
    <result column="AREA_CODE" jdbcType="CHAR" property="areaCode" />
    <result column="TRADE_CATEGORY" jdbcType="CHAR" property="tradeCategory" />
    <result column="REGISTERED_CAPITAL" jdbcType="DECIMAL" property="registeredCapital" />
  </resultMap>
  <sql id="Base_Column_List">
    INFO_ID, CUSTOMER_ID, CUSTOMER_CODE, NAME, SHORT_NAME, MOBILE, TELEPHONE, BUSINESS_ADDRESS, 
    USE_USCC, CUSTOMER_TYPE, BUSINESS_LICENSE_NO, BUSINESS_LICENSE_EXP_DATE, REGISTERED_ADDRESS, 
    NAT_TAX_REG_CER_NO, NAT_TAX_REG_EXP_DATE, LOC_TAX_REG_CER_NO, LOC_TAX_REG_CER_EXP_DATE, 
    ORG_STRUCTURE_CODE, LEA_PERSON_NAME, LEA_PERSONI_DENTIFICATION_TYPE, LEA_PERSONI_DENTIFICATION_NO, 
    LEA_PER_DEN_EXP_DATE, PARENT_CUSTOMER_CODE, WEB_ADDRESS, ICP_LICENSE_NO, BUSINESS_SCOPE, 
    INDUSTRY_DESCRIPTION, EMPLOYEE_COUNT, BUSINESS_TYPE, AREA_CODE, TRADE_CATEGORY, REGISTERED_CAPITAL
  </sql>
  
  <select id="selectBySelective" resultMap="BaseResultMap" parameterType="com.epaylinks.efps.pas.cum.domain.CumCustomerInfo" >
    select
    <include refid="Base_Column_List" />
    from CUM_CUSTOMER_INFO
    <where>
      <if test="customerCode != null">
        AND CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
      </if>
      <if test="name != null">
        AND NAME = #{name,jdbcType=VARCHAR}
      </if>
    </where>
  </select>

  <select id="selectByNameOnLike" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select
    <include refid="Base_Column_List" />
    from CUM_CUSTOMER_INFO
    <where>
      <if test="name != null">
        AND NAME like "%"#{name,jdbcType=VARCHAR}"%"
      </if>
    </where>
  </select>

  <select id="getCustomerInfoByCodes" resultMap="BaseResultMap"  >
    select
    <include refid="Base_Column_List" />
    from CUM_CUSTOMER_INFO where CUSTOMER_CODE IN
    <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>
  
</mapper>