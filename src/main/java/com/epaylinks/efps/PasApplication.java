package com.epaylinks.efps;

import com.epaylinks.efps.common.util.SpringContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.netflix.feign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.client.RestTemplate;

@RefreshScope
@EnableEurekaClient
@EnableFeignClients
@EnableKafka
@EnableScheduling
@EnableAspectJAutoProxy(proxyTargetClass = true, exposeProxy = true)
@SpringBootApplication
public class PasApplication {
    // 启动的时候要注意，由于我们在controller中注入了RestTemplate，所以启动的时候需要实例化该类的一个实例
    @Autowired
    private RestTemplateBuilder builder;

    @Bean("balancedRestTemplate")
    @LoadBalanced
    public RestTemplate restTemplate() {
        return builder.setConnectTimeout(30000).setReadTimeout(30000).build();
    }

    public static void main(String[] args) {
        SpringApplication springApplication = new SpringApplication(PasApplication.class);
        SpringContextUtils.setApplicationContext(springApplication.run(args));
    }
}