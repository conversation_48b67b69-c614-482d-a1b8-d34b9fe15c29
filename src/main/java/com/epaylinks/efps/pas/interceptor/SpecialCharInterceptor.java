package com.epaylinks.efps.pas.interceptor;

import com.epaylinks.efps.common.business.pay.request.UserType;
import com.epaylinks.efps.common.interceptor.SpecUrlFilter;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.pas.common.PasConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.MultipartResolver;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

@Component
public class SpecialCharInterceptor implements HandlerInterceptor {
    @Autowired
    private CommonLogger logger;

    @Autowired
    private SpecUrlFilter specUrlFilter;

    @Autowired
    private Environment env;

    @Override
    public boolean preHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o) throws Exception {
        if (!"1".equals(specUrlFilter.getValid())) { // 1：需校验
            return true;
        }
        String userType = httpServletRequest.getHeader("x-user-type");
        if (UserType.PAS_USER.code.equals(userType)) {
            String contentType = httpServletRequest.getContentType();
            if("GET".equals(httpServletRequest.getMethod())) {
                if(specUrlFilter.checkSpecials(httpServletRequest.getQueryString())) {
                    throw new SpecialCharException(PasConstants.REQPARAMCHECK.REQ_PARAM_CONTAIN_SPEC_CHAR.comment);
                }
            } else {
                if (contentType != null && contentType.contains("multipart/form-data")) {
                    MultipartResolver resolver = new CommonsMultipartResolver(httpServletRequest.getSession().getServletContext());
                    MultipartHttpServletRequest multipartRequest = resolver.resolveMultipart(httpServletRequest);
                    if(specUrlFilter.checkSpecials(multipartRequest.getParameterMap())) {
                        throw new SpecialCharException(PasConstants.REQPARAMCHECK.REQ_PARAM_CONTAIN_SPEC_CHAR.comment);
                    }
                } else if (contentType != null && contentType.contains("application/x-www-form-urlencoded")) {
                    Map<String, String[]> parameterMap = httpServletRequest.getParameterMap();
                    for (Map.Entry<String, String[]> entry : parameterMap.entrySet()) {
                        String[] paramValues = entry.getValue();
                        for (String paramValue : paramValues) {
                            if(specUrlFilter.checkSpecials(paramValue)) {
                                throw new SpecialCharException(PasConstants.REQPARAMCHECK.REQ_PARAM_CONTAIN_SPEC_CHAR.comment);
                            }
                        }
                    }
                } else {
                    if (specUrlFilter.checkSpecials(httpServletRequest)) {
                        throw new SpecialCharException(PasConstants.REQPARAMCHECK.REQ_PARAM_CONTAIN_SPEC_CHAR.comment);
                    }
                }
            }
        }
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, ModelAndView modelAndView) throws Exception {

    }

    @Override
    public void afterCompletion(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, Exception e) throws Exception {

    }
}
