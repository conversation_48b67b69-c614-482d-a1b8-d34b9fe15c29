package com.epaylinks.efps.pas.pas.domain;

import lombok.Data;

import java.util.Date;

@Data
public class LoginReport {
    /**
     */
    private Long reportId;

    /**
     * 登录账号
     */
    private String loginAccount;

    /**
     * 角色
     */
    private String loginRole;

    /**
     * 姓名/商户名称
     */
    private String name;

    /**
     * 商户编号
     */
    private String customerCode;

    /**
     * 登录IP
     */
    private String loginIp;

    /**
     * 用户类型(1:运营管理系统用户; 2:商户门户用户; 3:钱包APP商户门户用户)
     */
    private String userType;

    /**
     * 登录次数
     */
    private Integer loginNum;

    /**
     * 登录日期
     */
    private String loginTime;

    /**
     * 审计状态(1:正常；2:异常)
     */
    private String reportState;

    /**
     * 审计说明
     */
    private String reportDesc;

    /**
     * 异常处理
     */
    private String exceptionHandle;

    /**
     * 处理人
     */
    private Long operator;

    /**
     * 处理时间
     */
    private Date handleTime;

    /**
     * 创建时间
     */
    private Date createTime;
}