package com.epaylinks.efps.pas.pas.timetask.job;

import com.epaylinks.efps.common.timetask.TaskJob;
import com.epaylinks.efps.common.timetask.TaskRequest;
import com.epaylinks.efps.common.tool.json.JsonUtils;
import com.epaylinks.efps.common.tool.time.Timex;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ExecuteProcedureJob implements TaskJob {

    @Autowired
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    public void executeProcedure(String name, LinkedHashMap<String, Object> params) {
        String paramName = "";
        if (params != null) {
            paramName = params.keySet().stream().map(k -> ":" + k).collect(Collectors.joining(","));
        }

        String sql = String.format("call %s(%s)", name, paramName);

        log.info("sql:[{}]", sql);
        log.info("sqlParams:[{}]", params);

        namedParameterJdbcTemplate.execute(sql, params, ps -> {
            ps.execute();
            return null;
        });
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class JobParam {
        private String procedureName;
        private LinkedHashMap<String, Object> params;
    }

    @Override
    public void execute(TaskRequest request) {
        if (request.getJobParams() != null) {
            JobParam jobParam = JsonUtils.jsonToObj(request.getJobParams(), JobParam.class);
            for (Map.Entry<String, Object> entry : jobParam.getParams().entrySet()) {
                if ("$CUR_DATE".equals(entry.getValue())) {
                    entry.setValue(Timex.now().to(Timex.Format.yyyyMMdd));
                }
            }
            executeProcedure(jobParam.getProcedureName(), jobParam.getParams());
        }
    }

}
