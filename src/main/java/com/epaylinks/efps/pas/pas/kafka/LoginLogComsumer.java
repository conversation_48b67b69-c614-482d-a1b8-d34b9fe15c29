package com.epaylinks.efps.pas.pas.kafka;

import java.util.Optional;

import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.KafkaListener;

import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.pas.pas.service.LoginLogService;
import com.epaylinks.efps.pas.pas.vo.PasLoginLogJson;

import net.sf.json.JSONObject;


/**
* 用户登录日志消费者类
* <AUTHOR>
* @date：2020年5月23日 上午10:23:53
*/
@Configuration
public class LoginLogComsumer {

    @Autowired
    LoginLogService loginLogService;
	
	
    @Logable(businessTag = "LoginLog")
    @KafkaListener(topics = {"user_action_log"},containerFactory = "customContainerFactory")
    public void consumeMsg(ConsumerRecord<?, ?> record) {
        Optional<?> kafkaMessage = Optional.ofNullable(record.value());
        if (kafkaMessage.isPresent()) {
            String message = (String) kafkaMessage.get();
            //解析Kafka的json消息
            JSONObject jsonObject = JSONObject.fromObject(message);
            PasLoginLogJson pasLoginLogJson = (PasLoginLogJson) JSONObject.toBean(jsonObject, PasLoginLogJson.class);
            
            if ("2".equals(String.valueOf(pasLoginLogJson.getLogType()))) {
                loginLogService.saveOperLog(pasLoginLogJson);
            } else {
                loginLogService.saveLoginLog(pasLoginLogJson);    
            }
           
        }
    }

}
