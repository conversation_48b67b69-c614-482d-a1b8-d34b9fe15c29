package com.epaylinks.efps.pas.pas.domain;

import java.util.List;

public class BusinessTemplate {

    private String code;
    private String name;
    private String groupCode;
    private String groupName;
    private String settCycle;
    private String maxProfitProportion;

    public String getSettCycle() {
        return settCycle;
    }

    public void setSettCycle(String settCycle) {
        this.settCycle = settCycle;
    }

    public String getMaxProfitProportion() {
        return maxProfitProportion;
    }

    public void setMaxProfitProportion(String maxProfitProportion) {
        this.maxProfitProportion = maxProfitProportion;
    }

    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    private List<Business> businessList;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<Business> getBusinessList() {
        return businessList;
    }

    public void setBusinessList(List<Business> businessList) {
        this.businessList = businessList;
    }
}
