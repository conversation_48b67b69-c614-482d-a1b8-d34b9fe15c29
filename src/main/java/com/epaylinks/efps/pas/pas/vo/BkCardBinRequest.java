package com.epaylinks.efps.pas.pas.vo;

import org.springframework.web.bind.annotation.RequestParam;

public class BkCardBinRequest {

    private String issueBankNo;
    private String issueBankName;
    private String cardNoRange;
    private String cardType;
    private String flag;
    private String cardName;
    private String cardNoLen;
    private String beginTime;
    private String endTime;
    private Integer pageNum;
    private Integer pageSize;
    private String parentType;
    private String sonType;
    private String loadFrom;


    public String getIssueBankNo() {
        return issueBankNo;
    }

    public void setIssueBankNo(String issueBankNo) {
        this.issueBankNo = issueBankNo;
    }

    public String getIssueBankName() {
        return issueBankName;
    }

    public void setIssueBankName(String issueBankName) {
        this.issueBankName = issueBankName;
    }

    public String getCardNoRange() {
        return cardNoRange;
    }

    public void setCardNoRange(String cardNoRange) {
        this.cardNoRange = cardNoRange;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }

    public String getCardName() {
        return cardName;
    }

    public void setCardName(String cardName) {
        this.cardName = cardName;
    }

    public String getCardNoLen() {
        return cardNoLen;
    }

    public void setCardNoLen(String cardNoLen) {
        this.cardNoLen = cardNoLen;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getParentType() {
        return parentType;
    }

    public void setParentType(String parentType) {
        this.parentType = parentType;
    }

    public String getSonType() {
        return sonType;
    }

    public void setSonType(String sonType) {
        this.sonType = sonType;
    }

    public String getLoadFrom() {
        return loadFrom;
    }

    public void setLoadFrom(String loadFrom) {
        this.loadFrom = loadFrom;
    }
}
