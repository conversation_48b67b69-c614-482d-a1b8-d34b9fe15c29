package com.epaylinks.efps.pas.pas.controller;

import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.pas.pas.domain.Application;
import com.epaylinks.efps.pas.pas.service.MchinletService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * <AUTHOR> 5m
 * @Date : 2018/1/2 10:28
 * @Description :
 */
@RestController
@RequestMapping("/Mchinlet")
@Api(value = "MchinletController", description = "商户进件")
public class MchinletController {

    @Autowired
    MchinletService mchinletService;

    @RequestMapping(value ="/judge", method = RequestMethod.GET)
    @Logable(businessTag = "judge")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "判断商户是否具备进件所需要素", notes = "判断商户是否具备进件所需要素", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "institutionCode", value = "上游机构Cdde", required = true, dataType = "String", length =50, paramType = "query"),
            @ApiImplicitParam(name = "customerCode", value = "商户Code,多个时用逗号(,)分隔", required = true, dataType = "String", length =200, paramType = "query")})
    public String judge(@RequestParam("institutionCode") String institutionCode,
                        @RequestParam("customerCode") String customerCode) {


        return mchinletService.judge(institutionCode ,customerCode);

    }


    @RequestMapping(value ="/Inlet", method = RequestMethod.POST)
    @Logable(businessTag = "Inlet")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "商户进件", notes = "商户进件", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "businessCode", value = "业务Code", required = true, dataType = "String", length =50, paramType = "query"),
            @ApiImplicitParam(name = "customerCode", value = "商户Code", required = true, dataType = "String", length =200, paramType = "query")})
    public String inlet(@RequestParam("businessCode") String businessCode,
                        @RequestParam("customerCode") String customerCode) {
        return mchinletService.inlet(null ,customerCode ,businessCode) ;

    }

    @RequestMapping(value ="/reInlet", method = RequestMethod.POST)
    @Logable(businessTag = "reInlet")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "重新进件", notes = "重新进件", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "id", required = true, dataType = "long", length =50, paramType = "query") })
    public String reInlet(@RequestParam Long id) {
        return mchinletService.reInlet(id) ;

    }

//    @RequestMapping(value ="/InletResult", method = RequestMethod.POST)
//    @Logable(businessTag = "InletResult")
//    @Exceptionable
//    @Validatable
//    @ApiOperation(value = "商户进件结果", notes = "商户进件结果", httpMethod = "POST")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "requestId", value = "请求ID", required = true, dataType = "String", length =50, paramType = "query"),
//            @ApiImplicitParam(name = "resultCode", value = "状态：S/F", required = true, dataType = "String", length =1, paramType = "query"),
//            @ApiImplicitParam(name = "institutionMerchCode", value = "上游进件返回的商户号", required = false, dataType = "String", length =50, paramType = "query"),
//            @ApiImplicitParam(name = "institutionMerchSecret", value = "上游进件返回的商户交易秘钥", required = false, dataType = "String", length =100, paramType = "query"),
//            @ApiImplicitParam(name = "resultMsg", value = "进件处理结果描述", required = false, dataType = "String", length =200, paramType = "query")})
//    public String inletResult(@RequestParam(value = "requestId", required = true) String requestId,
//                              @RequestParam(value = "resultCode", required = true) String resultCode,
//                              @RequestParam(value = "institutionMerchCode", required = false) String institutionMerchCode,
//                              @RequestParam(value = "institutionMerchSecret", required = false) String institutionMerchSecret,
//                              @RequestParam(value = "resultMsg", required = false) String resultMsg) {
//        return mchinletService.inletResult(requestId , resultCode , institutionMerchCode , institutionMerchSecret, resultMsg);
//
//    }

}
