package com.epaylinks.efps.pas.pas.util;

import com.epaylinks.efps.common.log.CommonLogger;
import org.apache.commons.lang3.StringUtils;

import java.io.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;

public class ReadFileUtils {

    public static void readFileByLinesAndPrint(String fileName) {
        File file = new File(fileName);
        BufferedReader reader = null;
        StringBuffer keyString = new StringBuffer();
        String tempString = null;
        String[] tempStrArr = null;
        List<String> resList = new ArrayList<>();
        try {
            InputStreamReader inputRead = new InputStreamReader(new FileInputStream(file), "GBK");
            reader = new BufferedReader(inputRead);
            while ((tempString = reader.readLine()) != null) {
                if(tempString.startsWith("--")){
                    continue;
                }
                if(StringUtils.isNotBlank(tempString)){
                    /*tempStrArr = tempString.split(",");
                    if(tempStrArr!=null && tempStrArr.length>2){
                        String cardLen = tempStrArr[0];
                        String cardRange = tempStrArr[1];
                        String sql = "insert into BK_CARD_BIN (ID,CARD_NO_RANGE,CARD_NO_RANGE_LEN,CREATE_TIME,UPDATE_TIME,PARENT_TYPE, SON_TYPE) values (SEQ_BKCARDBIN.nextval,'"+
                                cardRange.trim()+"','"+cardLen+"',systimestamp,systimestamp,'1','ALLBIN');";
                        resList.add(sql);
                    }*/
                    tempStrArr = tempString.split(" ");
                    if(tempStrArr!=null && tempStrArr.length>2){
                        for(String data : tempStrArr){
                            if(StringUtils.isNotBlank(data)){
                                //System.out.println("=>"+new String(data.getBytes("ISO-8859-1"), "gbk")+"<=");
                                System.out.println("=>"+data+"<=");
                            }
                        }
                    }
                    return;
                }
            }
            reader.close();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        for(String sql : resList){
            System.out.println(sql);
        }
    }

    public static List<String> readFileByLines(String fileName,CommonLogger logger) {
        File file = new File(fileName);
        BufferedReader reader = null;
        StringBuffer keyString = new StringBuffer();
        String tempString = null;
        String[] tempStrArr = null;
        List<String> resList = new ArrayList<>();
        String totalExpStr = null;
        int totalExp = 0;
        try {
            reader = new BufferedReader(new InputStreamReader(new FileInputStream(file), "GBK"));
            while ((tempString = reader.readLine()) != null) {
                if(tempString.startsWith("--")){
                    continue;
                }
                if(StringUtils.isNotBlank(tempString) && tempString.indexOf("TOTAL")<0){
                    resList.add(tempString);
                }
                if(StringUtils.isNotBlank(tempString) && tempString.startsWith("TOTAL")){
                    totalExpStr = tempString.replace("TOTAL","");
                    totalExp = Integer.parseInt(totalExpStr);
                }
            }
            reader.close();
            logger.printMessage("读取卡bin数据，数量："+resList.size()+"，如果有结尾汇总，数量："+totalExp);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return resList;
    }

    public static void main(String[] args) throws Exception {
        //readFileByLinesAndPrint("d:/epl/DWBIN");
        String loadDate = "20220625";
        System.out.println(loadDate.endsWith("10"));
        System.out.println(loadDate.endsWith("25"));
        System.out.println(loadDate.substring(2));
        List<String> list = new LinkedList<>();
        list.add("1");
        list.add("2");
        System.out.println(list.get(list.size()-1));

        List<String> dataList = null;
        String record = "00010033    银联国际支付标记               16 62636111     1 2      ";
        System.out.println(record.length());
        dataList = new LinkedList<>();      //清洗后的数据字段
        byte[] temp = record.getBytes("gbk");
        System.out.println("获取到的数组长度："+temp.length);
        if(record!=null && record.length()>=55){
            byte[] word = Arrays.copyOfRange(temp,0,12);
            String data = new String(word).trim();
            dataList.add(data);
            word = Arrays.copyOfRange(temp,12,43);
            data = new String(word,"gbk").trim();
            dataList.add(data);
            word = Arrays.copyOfRange(temp,43,45);
            data = new String(word).trim();
            dataList.add(data);
            word = Arrays.copyOfRange(temp,46,59);
            data = new String(word).trim();
            dataList.add(data);
            word = Arrays.copyOfRange(temp,59,61);
            data = new String(word).trim();
            dataList.add(data);
            word = Arrays.copyOfRange(temp,61,63);
            data = new String(word).trim();
            dataList.add(data);
            word = Arrays.copyOfRange(temp,63,65);
            data = new String(word).trim();
            dataList.add(data);
        }
        for(String item : dataList){
            System.out.println("=>"+item+"<=");
        }
    }
}
