package com.epaylinks.efps.pas.pas.domain;

/**
 * 心跳日志打印类
 */
public class TimeTaskHeart {
    /**
     * 定时任务名称
     */
    private String jobName;
    /**
     * 定时任务状态
     *
     */
    private String jobState;
    /**
     * 定时任务表达式
     */
    private String cron;
    /**
     * 上一次执行时间
     */
    private String lastTime;
    /**
     * 下一次执行时间
     */
    private String nextTime;

    public String getJobName() {
        return jobName;
    }

    public void setJobName(String jobName) {
        this.jobName = jobName;
    }

    public String getJobState() {
        return jobState;
    }

    public void setJobState(String jobState) {
        this.jobState = jobState;
    }

    public String getCron() {
        return cron;
    }

    public void setCron(String cron) {
        this.cron = cron;
    }

    public String getLastTime() {
        return lastTime;
    }

    public void setLastTime(String lastTime) {
        this.lastTime = lastTime;
    }

    public String getNextTime() {
        return nextTime;
    }

    public void setNextTime(String nextTime) {
        this.nextTime = nextTime;
    }


    @Override
    public String toString() {
        return "TimeTaskHeart{" +
                "jobName='" + jobName + '\'' +
                ", jobState='" + jobState + '\'' +
                ", cron='" + cron + '\'' +
                ", lastTime='" + lastTime + '\'' +
                ", nextTime='" + nextTime + '\'' +
                '}';
    }
}
