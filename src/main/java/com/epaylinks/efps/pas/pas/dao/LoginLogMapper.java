package com.epaylinks.efps.pas.pas.dao;

import java.util.List;
import java.util.Map;

import com.epaylinks.efps.pas.pas.controller.response.LoginReportResponse;
import com.epaylinks.efps.pas.pas.domain.LoginReport;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.transaction.annotation.Transactional;

import com.epaylinks.efps.pas.pas.domain.PasLoginLog;
import com.epaylinks.efps.pas.pas.vo.PasLoginLogReportResp;
import com.epaylinks.efps.pas.pas.vo.PasLoginLogReq;
import com.epaylinks.efps.pas.pas.vo.PasLoginLogResp;

@Mapper
public interface LoginLogMapper {

	int insertLoginLog(PasLoginLog pasLoginLog);

	int pageQueryTotal(PasLoginLogReq pasLoginLogReq);

	List<PasLoginLogResp> queryLoginLogForPas(PasLoginLogReq pasLoginLogReq);
	
	List<PasLoginLogResp> queryLoginLogForPps(PasLoginLogReq pasLoginLogReq);
	
	List<PasLoginLogResp> queryLoginLogForPasNoPage(PasLoginLogReq pasLoginLogReq);
	
	List<PasLoginLogResp> queryLoginLogForPpsNoPage(PasLoginLogReq pasLoginLogReq);

	/**
	 * 查询登录汇总（报表）总数
	 * @param pasLoginLogReq
	 * @return
	 */
    int countReport(PasLoginLogReq pasLoginLogReq);

    /**
     * 分页查询登录汇总（报表）列表
     * @param pasLoginLogReq
     * @return
     */
    List<PasLoginLogReportResp> pageQueryReport(PasLoginLogReq pasLoginLogReq);

	List<LoginReport> queryBeforeDayList();

	Integer checkLoginTime(Map map);

}
