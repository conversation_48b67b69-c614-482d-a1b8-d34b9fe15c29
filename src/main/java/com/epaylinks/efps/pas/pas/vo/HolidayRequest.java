package com.epaylinks.efps.pas.pas.vo;

import com.epaylinks.efps.pas.pas.domain.Holiday;

import java.util.List;

/**
 * Created by adm on 2018/8/23.
 */
public class HolidayRequest {
    private List<String> holidayList;
    private Long creator;

    public Long getCreator() {
        return creator;
    }

    public void setCreator(Long creator) {
        this.creator = creator;
    }
/*

    public List<Holiday> getHolidayList() {
        return holidayList;
    }

    public void setHolidayList(List<Holiday> holidayList) {
        this.holidayList = holidayList;
    }
*/

    public List<String> getHolidayList() {
        return holidayList;
    }

    public void setHolidayList(List<String> holidayList) {
        this.holidayList = holidayList;
    }
}
