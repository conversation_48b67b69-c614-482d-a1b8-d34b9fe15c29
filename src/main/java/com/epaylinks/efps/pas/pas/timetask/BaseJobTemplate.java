package com.epaylinks.efps.pas.pas.timetask;

import com.alibaba.fastjson.JSON;
import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.timetask.TimeTaskRequest;
import com.epaylinks.efps.common.timetask.TimeTaskResponse;
import com.epaylinks.efps.common.timetask.TimeTaskSend;
import com.epaylinks.efps.common.util.DateUtils;
import com.epaylinks.efps.pas.common.PasConstants;
import com.epaylinks.efps.pas.pas.domain.PasTimeTask;
import com.epaylinks.efps.pas.pas.domain.PasTimeTaskRecord;
import org.quartz.JobExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;

@Component("BaseJobTemplate")
public class BaseJobTemplate implements BaseTaskJob {

    @Autowired
    TaskService taskService;

    @Autowired
    private BaseJobTemplate self;

    @Autowired
    private TimeTaskSend timeTaskSend;

    @Override
    public void execute(JobExecutionContext context) {
        PasTimeTask task = taskService.prepare(context);
        if (task == null) {
            return;
        }
        PasTimeTaskRecord record = task.getLastExecuteRecord();
        String jobName = task.getJobName();
        TimeTaskRequest request = new TimeTaskRequest();
        //获取当前ip地址
        try {
            //组装request
            request.setTaskId(record.getId());
            request.setIpAddress(task.getIpAddres());
            Map<String, String> dataMap = (Map<String, String>) JSON.parse(task.getJobData());
            request.setData(dataMap);
            request.setRealBeanName(task.getRealBaenName());

            //执行方法
            CommonOuterResponse<TimeTaskResponse> sendResponse = null;
           /* StringBuilder builder = new StringBuilder();
            builder.append("定时任务").append(jobName).append(DateUtils.formatDate(new Date(), "yyyyMMdd hhmmss")).append("开始");
            self.log(builder.toString());*/
            try {
                sendResponse = timeTaskSend.send(request);
            } catch (Exception e) {
                //如果是发送之后出问题
                self.log("定时任务：" + jobName + "发送后出错。错误原因是：" + e.getMessage());
                sendResponse.setReturnCode("9999");
                sendResponse.setReturnMsg("发送出错:" + e.getMessage());
            }
            if (!CommonOuterResponse.SUCCEE.equals(sendResponse.getReturnCode())) {
                record.setReturnCode(sendResponse.getReturnCode());
                record.setReturnMessage(sendResponse.getReturnMsg());
                record.setStatus(PasConstants.timeTaskRecordStatus.FAIL.code);
            }
            TimeTaskResponse taskResponse = sendResponse.getData();
            record.setIpAddress(taskResponse != null ? taskResponse.getIpAddress() : null);
            record.setUpdateTime(new Date());

            taskService.updateRecord(record);
        } catch (Exception e) {
            self.log("定时任务：" + jobName + "发送前出错。错误原因是：" + e.getMessage());
        }
    }

    @Logable(businessTag = "baseJobLog")
    public void log(String message) {
    }
}
