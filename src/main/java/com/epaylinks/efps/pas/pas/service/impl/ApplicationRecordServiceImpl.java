package com.epaylinks.efps.pas.pas.service.impl;

import com.epaylinks.efps.pas.clr.domain.Institution;
import com.epaylinks.efps.pas.clr.service.InstitutionService;
import com.epaylinks.efps.pas.cum.domain.CumCustomerInfo;
import com.epaylinks.efps.pas.cum.service.impl.CumCumCustomerInfoServiceImpl;
import com.epaylinks.efps.pas.pas.dao.ApplicationRecordMapper;
import com.epaylinks.efps.pas.pas.domain.ApplicationRecord;
import com.epaylinks.efps.pas.pas.service.ApplicationRecordService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 5m
 * @Date : 2017/12/28 17:05
 * @Description :
 */
@Service
public class ApplicationRecordServiceImpl implements ApplicationRecordService {

    @Autowired
    ApplicationRecordMapper applicationRecordMapper;

    @Autowired
    private CumCumCustomerInfoServiceImpl cumCumCustomerInfoServiceImpl;

    @Autowired
    private InstitutionService institutionService;

    @Override
    public int deleteByPrimaryKey(Long id) {
        return applicationRecordMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(ApplicationRecord record) {
        return applicationRecordMapper.insert(record);
    }

    @Override
    public int insertSelective(ApplicationRecord record) {
        return applicationRecordMapper.insertSelective(record);
    }

    @Override
    public ApplicationRecord selectByPrimaryKey(Long id) {
        return applicationRecordMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(ApplicationRecord record) {
        return applicationRecordMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(ApplicationRecord record) {
        return applicationRecordMapper.updateByPrimaryKey(record);
    }

    @Override
    public List<ApplicationRecord> selectBySelective(ApplicationRecord record) {
        return applicationRecordMapper.selectBySelective(record);
    }

    @Override
    public List<ApplicationRecord> selectByPage(Map map){

        List<ApplicationRecord> rowsNew = new ArrayList<ApplicationRecord>();

        //根据商户名称获得商户号
        if(StringUtils.isBlank((String)map.get("customerCode")) && StringUtils.isNotBlank((String)map.get("customerName"))){
            CumCustomerInfo cumCustomerInfo = new CumCustomerInfo();
            cumCustomerInfo.setName((String)map.get("customerName"));
            List<CumCustomerInfo> list = cumCumCustomerInfoServiceImpl.selectBySelective(cumCustomerInfo);

            if(list.size()>0){
                map.put("customerCode",list.get(0).getCustomerCode());
            }
        }

        List<ApplicationRecord> list = applicationRecordMapper.selectByPage(map);
        if(list.size()>0){
            //获取所有商户号
            List<String> customerCodeList = new ArrayList<String>();
            //获取所有机构号
            List<String> institutionCodeList = new ArrayList<String>();
            for (ApplicationRecord row : list) {
                if(!customerCodeList.contains(row.getCustomerCode())){
                    customerCodeList.add(row.getCustomerCode());
                }
                if(!institutionCodeList.contains(row.getInstitutionCode())){
                    institutionCodeList.add(row.getInstitutionCode());
                }
            }

            //获取所有商户名称
            HashMap<String, String> chp = new HashMap<String, String>();
            List<CumCustomerInfo> cumCustomerInfoList = cumCumCustomerInfoServiceImpl.getCustomerInfoByCodes(customerCodeList);
            if(cumCustomerInfoList.size()>0){
                for (CumCustomerInfo cumCustomerInfo : cumCustomerInfoList) {
                    if(!chp.containsKey(cumCustomerInfo.getCustomerCode())){
                        chp.put(cumCustomerInfo.getCustomerCode(), cumCustomerInfo.getName());
                    }
                }
            }

            //获取所有机构名称
            HashMap<String, String> ihp = new HashMap<String, String>();
            List<Institution> institutionInfoList = institutionService.getInstitutionaByCodes(institutionCodeList);
            if(institutionInfoList.size()>0){
                for (Institution institution : institutionInfoList) {
                    if(!ihp.containsKey(institution.getInstitutionCode())){
                        ihp.put(institution.getInstitutionCode(), institution.getInstitutionName());
                    }
                }
            }


            //给商户名称,机构名称赋值
            for (ApplicationRecord row : list) {
                row.setCustomerName(chp.get(row.getCustomerCode())!=null ? chp.get(row.getCustomerCode()):"");
                row.setInstitutionName(ihp.get(row.getInstitutionCode())!=null ? ihp.get(row.getInstitutionCode()):"");
                rowsNew.add(row);
            }
        }
        return rowsNew;

    }
}
