package com.epaylinks.efps.pas.pas.controller;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.validate.Validatable;

import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.pas.domain.BusinessGroup;

import com.epaylinks.efps.pas.pas.domain.BusinessRoot;
import com.epaylinks.efps.pas.pas.service.BusinessGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;


import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 分组模版控制器
 */
@RestController
@RequestMapping("/businessGroup")
@Api(value = "DepartmentController", description = "分组模版控制器")
public class BusinessGroupController {

    @Autowired
    private CommonLogger logger;

    @Autowired
    private BusinessGroupService businessGroupService;

    @Value("${excludeBusinessList}")
    private String excludeBusinessList;  // 排除列表git配置

    /**
     * 查询分组树
     */
    @GetMapping("/getBusinessGroupTree")
    @Exceptionable
    @Validatable
    @Logable(businessTag = "BusinessGroupController.getBusinessGroupTree",outputResult = false)
    @ApiOperation(value = "查询分组树", notes = "查询分组树", httpMethod = "GET")
    @ApiImplicitParams({
//            @ApiImplicitParam(name = "groupCode", value = "编码", required = false, dataType = "String", paramType = "query"),
    })
    public BusinessRoot getBusinessGroupTree() {
        BusinessRoot response = new BusinessRoot();
        try {
            response = businessGroupService.getBasicTree();
        }  catch (AppException e) {
            response.setReturnCode(e.getErrorCode());
            response.setReturnMsg(e.getErrorMsg());
            logger.printMessage("查询分组树，错误："+e.getLocalizedMessage());
            logger.printLog(e);
        } catch (Exception e) {
            response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
            response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            logger.printMessage("查询分组树，异常："+e.getLocalizedMessage());
            logger.printLog(e);
        }
        return response;
    }

    /**
     * 排除指定业务后的业务分组
     */
    @GetMapping("/getExcludeBasicTree")
    @Exceptionable
    @Validatable
    @Logable(businessTag = "BusinessGroupController.getExcludeBasicTree",outputResult = false)
    @ApiOperation(value = "排除指定业务后的业务分组", notes = "排除指定业务后的业务分组", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "source", value = "来源：1、费率模板查询，2、业务管理查询", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "excludeList", value = "要排除的业务编码，多个用英文逗号分隔", required = false, dataType = "String", paramType = "query"),
    })
    public BusinessRoot getExcludeBasicTree(@RequestParam(value = "source",required = false) String source,
                                            @RequestParam(value = "excludeList",required = false) String excludeList) {
        BusinessRoot response = new BusinessRoot();
        try {
            if("1".equals(source)){
                excludeList = excludeBusinessList;
            }
            logger.printMessage("查询业务树，当前来源："+source);
            logger.printMessage("查询业务树，当前业务排除列表："+excludeList);
            response = businessGroupService.getExcludeBasicTree(source, excludeList);
        } catch (AppException e) {
            response.setReturnCode(e.getErrorCode());
            response.setReturnMsg(e.getErrorMsg());
            logger.printMessage("排除指定业务后的业务分组，错误："+e.getLocalizedMessage());
            logger.printLog(e);
        } catch (Exception e) {
            response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
            response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            logger.printMessage("排除指定业务后的业务分组，异常："+e.getLocalizedMessage());
            logger.printLog(e);
        }
        return response;
    }

    @GetMapping("/getH5BusinessGroupTree")
    @Exceptionable
    @Validatable
    @Logable(businessTag = "BusinessGroupController.getH5BusinessGroupTree")
    @ApiOperation(value = "H5查询分组树", notes = "H5查询分组树", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "customerId", value = "商户ID", required = false, dataType = "Long", paramType = "query"),
    })
    public BusinessRoot getH5BusinessGroupTree(@RequestParam(value = "customerId",required = false) Long customerId) {
        BusinessRoot response = new BusinessRoot();
        try {
            response = businessGroupService.getCustomerTree(customerId);
        } catch (AppException e) {
            response.setReturnCode(e.getErrorCode());
            response.setReturnMsg(e.getErrorMsg());
            logger.printMessage("H5查询分组树，错误："+e.getLocalizedMessage());
            logger.printLog(e);
        } catch (Exception e) {
            response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
            response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            logger.printMessage("H5查询分组树，异常："+e.getLocalizedMessage());
            logger.printLog(e);
        }
        return response;
    }

    /**
     * 查询分组/模版
     */
    @GetMapping("/getTemplateGroup")
    @Exceptionable
    @Validatable
    @Logable(businessTag = "BusinessGroupController.getBusinessGroupTree",outputResult = false)
    @ApiOperation(value = "查询分组或模版", notes = "查询分组树", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "isTemplate", value = "分组/模版，0查分组，1查模版,2，查相关费率数据", required = true, dataType = "String", paramType = "query"),
    })

    public CommonOuterResponse getTemplateGroup(@RequestParam(required = true) String isTemplate) {
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            List<BusinessGroup> list = businessGroupService.selectAllBusinssGroup(isTemplate);
            response.setData(list);
        } catch (AppException e) {
            response.setReturnCode(e.getErrorCode());
            response.setReturnMsg(e.getErrorMsg());
            logger.printMessage("查询分组或模版，错误："+e.getLocalizedMessage());
            logger.printLog(e);
        } catch (Exception e) {
            response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
            response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            logger.printMessage("查询分组或模版，异常："+e.getLocalizedMessage());
            logger.printLog(e);
        }
        return response;
    }


    /**
     * 查询终端业务分组树
     */
    @PostMapping("/getBasicTreeByBusinss")
    @Exceptionable
    @Validatable
    @Logable(businessTag = "BusinessGroupController.getBasicTreeByBusinss",outputResult = false)
    @ApiOperation(value = "查询终端业务分组树", notes = "查询终端业务分组树", httpMethod = "POST")
    public BusinessRoot getBasicTreeByBusinss(@RequestParam List<String> businssCodes) {
        BusinessRoot response = new BusinessRoot();
        try {
            response = businessGroupService.getBasicTreeByBusinss(businssCodes);
        } catch (AppException e) {
            response.setReturnCode(e.getErrorCode());
            response.setReturnMsg(e.getErrorMsg());
            logger.printMessage("查询终端业务分组树，错误："+e.getLocalizedMessage());
            logger.printLog(e);
        } catch (Exception e) {
            response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
            response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            logger.printMessage("查询终端业务分组树，异常："+e.getLocalizedMessage());
            logger.printLog(e);
        }
        return response;
    }
}
