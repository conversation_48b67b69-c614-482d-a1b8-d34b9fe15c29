package com.epaylinks.efps.pas.pas.service;

import com.epaylinks.efps.pas.acc.feign.dto.acc.AccountVo;
import com.epaylinks.efps.pas.pas.domain.DkteRecord;
import com.epaylinks.efps.pas.pas.domain.DkteRecordVo;
import org.springframework.validation.BindingResult;

import java.util.List;
import java.util.Map;

public interface DkteService {

    void check(DkteRecordVo record, BindingResult bindingResult);

    List<DkteRecord> pageQuery(Map map) ;

    int queryCount(Map map);

    int add(DkteRecordVo record,String userId);

    int update(DkteRecordVo record,String userId);

    int audit(Long dkteId, String auditResult, String auditReason, String userId);

    AccountVo accountIsExist(String customerNo);

    List<DkteRecord> getFreezeRecord(String customerNo,String transactionNo);
}
