package com.epaylinks.efps.pas.pas.domain;

import java.util.Date;

public class OperationLog {

	private Long logId; //日志ID

	private String userName; //用户名

	private String realName; //真实名称

	private String opModule; //操作模块

	private String opMethod; //操作方式

	private String opContent; //操作内容

	private String opTime; //操作时间

	private Date createTime; //创建时间

	public Long getLogId() {
		return logId;
	}

	public void setLogId(Long logId) {
		this.logId = logId;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}

	public String getOpModule() {
		return opModule;
	}

	public void setOpModule(String opModule) {
		this.opModule = opModule;
	}

	public String getOpMethod() {
		return opMethod;
	}

	public void setOpMethod(String opMethod) {
		this.opMethod = opMethod;
	}

	public String getOpContent() {
		return opContent;
	}

	public void setOpContent(String opContent) {
		this.opContent = opContent;
	}

	public String getOpTime() {
		return opTime;
	}

	public void setOpTime(String opTime) {
		this.opTime = opTime;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	@Override
	public String toString() {
		return "{" +
				"logId=" + logId +
				", userName='" + userName + '\'' +
				", realName='" + realName + '\'' +
				", opModule='" + opModule + '\'' +
				", opMethod='" + opMethod + '\'' +
				", opContent='" + opContent + '\'' +
				", opTime='" + opTime + '\'' +
				'}';
	}




}
