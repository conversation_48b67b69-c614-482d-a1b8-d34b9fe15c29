package com.epaylinks.efps.pas.pas.timetask.job;

import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.common.timetask.TaskJob;
import com.epaylinks.efps.common.timetask.TaskRequest;
import com.epaylinks.efps.pas.pas.service.LoginReportService;
import com.epaylinks.efps.pas.pas.service.OpLogReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class LoginReportJob implements TaskJob {
    @Autowired
    private CommonLogger logger;

    @Autowired
    private LoginReportService loginReportService;

    @Autowired
    private OpLogReportService opLogReportService;

    @Override
    public void execute(TaskRequest taskRequest) throws Exception {
        logger.printMessage("保存每日登录审计报告开始");
        try {
            loginReportService.saveBeforeDay();
        } catch (Exception e) {
            logger.printMessage("保存每日登录审计报告错误：" + e.getMessage());
            logger.printLog(e);
        }
        logger.printMessage("保存每日登录审计报告结束");

        try{
            opLogReportService.checkOpLogReportByDate(null);
        }catch (Exception e){
            logger.printMessage("审计指定日期的操作日志，异常："+e.getMessage());
            logger.printLog(e);
        }
    }
}
