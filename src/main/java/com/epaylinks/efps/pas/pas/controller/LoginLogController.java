package com.epaylinks.efps.pas.pas.controller;


import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.epaylinks.efps.common.business.pay.request.UserType;
import com.epaylinks.efps.common.datadownload.annotation.DownloadAble;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.pas.acl.service.UserService;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.common.Result;
import com.epaylinks.efps.pas.pas.service.LoginLogService;
import com.epaylinks.efps.pas.pas.vo.PasLoginLogReportResp;
import com.epaylinks.efps.pas.pas.vo.PasLoginLogReq;
import com.epaylinks.efps.pas.pas.vo.PasLoginLogResp;
import com.epaylinks.efps.pas.pas.vo.PasOperLogResp;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/**
* 登录日志控制类
* <AUTHOR>
* @date：2020年5月11日 下午4:17:10
*/
@RestController
@RequestMapping("/loginLog")
@Api(value = "LoginLogController", description = "登录日志控制类")
public class LoginLogController {


	@Autowired
	private LoginLogService loginLogService;

    @Autowired
    private UserService userService;

	/**
     * 同步所有的业务信息（往kafka发送数据）
     * @return 成功 "1" ， 失败  "0"
     */
    @RequestMapping(value = "/sync" , method = RequestMethod.GET)
    @ApiImplicitParams({
		@ApiImplicitParam(name = "value", value = "发送Kafka的值", required = false, dataType = "String", paramType = "query"),
    })
    public Result<String> syncBusinessPayMethod(String value) {
    	
    	loginLogService.syncAllBizPayMethod(value);
    	Result<String> result = new Result<>();
    	result.setData(Constants.SUCCESS);
    	return result;
    }
	

	
	
	@RequestMapping(value = "/pageQuery", method = RequestMethod.GET)
    @Logable(businessTag = "LoginLogController.query")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "分页查询登录日志", notes = "分页查询登录日志", httpMethod = "GET")
    @DownloadAble
    @ApiImplicitParams({
    		@ApiImplicitParam(name = "startTime", value = "开始时间YYYYMMDD", required = false, dataType = "String", paramType = "query"),
    		@ApiImplicitParam(name = "endTime", value = "结束时间YYYYMMDD", required = false, dataType = "String", paramType = "query"),
    		@ApiImplicitParam(name = "userName", value = "登录账号", required = false, dataType = "String", paramType = "query"),
    		@ApiImplicitParam(name = "state", value = "登录状态:0失败；1成功", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "customercode", value = "商户编号", required = false, dataType = "String", paramType = "query"),
    		@ApiImplicitParam(name = "platcustomer", value = "所属平台商", required = false, dataType = "String", paramType = "query"),
    		@ApiImplicitParam(name = "servicecustomer", value = "所属代理商", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "download", value = "是否导出，true/false", required = false, dataType = "Boolean", paramType = "query"),
            @ApiImplicitParam(name = "fileName", value = "导出文件名称", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "type", value = "导出文件类型，例如 csv", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageNum", value = "查询开始页", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "查询的页面大小", required = true, dataType = "int", paramType = "query")})
    
    public PageResult<PasLoginLogResp> pageQuery(
			@RequestParam(value = "startTime", required = false) String startTime,
			@RequestParam(value = "endTime", required = false) String endTime,
			@RequestParam(value = "userName", required = false) String userName,
			@RequestParam(value = "state", required = false) String state,
            @RequestParam(value = "customercode", required = false) String customercode,
			@RequestParam(value = "platcustomer", required = false) String platcustomer,
			@RequestParam(value = "servicecustomer", required = false) String servicecustomer,
    		@RequestParam(value = "download", required = false) Boolean download,
    		@RequestParam(value = "fileName", required = false) String fileName,
    		@RequestParam(value = "type", required = false) String type,
            @RequestParam(value = "pageNum", required = true) Integer pageNum,
            @RequestParam(value = "pageSize", required = true) Integer pageSize,
            @RequestHeader(value = "x-user-type", required = false) String userType,
            @RequestHeader(value = "x-userid", required = false) Long userId,
            @RequestHeader(value = "x-customer-code", required = false) String customerCodeHead
    ){
	    
        	PageResult<PasLoginLogResp> pageResult = null;
        	try {
        		PasLoginLogReq pasLoginLogReq = new PasLoginLogReq();
        		if(StringUtils.isNotBlank(startTime)){
        			startTime = startTime +"000000";
        		}

        		if(StringUtils.isNotBlank(endTime)){
        			endTime = endTime+"235959";
        		}
        		//判断是运营平台还是商户平台
                if (UserType.PPS_USER.code.equals(userType)) {
                	pasLoginLogReq.setUsertype(UserType.PPS_USER.code);
                	String loginName = userService.queryCustLoginName(userId);
                	userName = loginName == null ? customerCodeHead : loginName; // 商户门户有管理员与登录用户区分，不能直接用customerCode过滤
                }else{
//                	pasLoginLogReq.setUsertype(UserType.PAS_USER.code);    // 注释调，pas 运营门户不限制登录数据类型
                }

        		//用于封装查询条件
        		int endNum = pageSize*pageNum;
        		int startNum = endNum - pageSize + 1;
        		
        		pasLoginLogReq.setStartTime(startTime);
        		pasLoginLogReq.setEndTime(endTime);
        		pasLoginLogReq.setUsername(userName);
        		pasLoginLogReq.setState(state);
                pasLoginLogReq.setCustomercode(customercode);
        		pasLoginLogReq.setPlatcustomer(platcustomer);
        		pasLoginLogReq.setServicecustomer(servicecustomer);
        		pasLoginLogReq.setEndNum(endNum);
        		pasLoginLogReq.setStartNum(startNum);
        		pasLoginLogReq.setDownload(download);
        		pasLoginLogReq.setLogType(new Short("1"));
        		
        		pageResult = loginLogService.pageQuery(pasLoginLogReq);
        		pageResult.setResult("0000");
        	} catch (Exception e) {
        	    e.printStackTrace();
        		pageResult = new PageResult<>();
                if (e instanceof AppException) {
                	pageResult.setResult(((AppException) e).getErrorCode());
                	pageResult.setErrorMsg(((AppException) e).getErrorMsg());
                } else {
                	pageResult.setResult(PasCode.LOGINLOG_ERROR.code);
                	pageResult.setErrorMsg(PasCode.LOGINLOG_ERROR.message);
                }
                return pageResult;
            }
    		
    		return pageResult;

    }
	
	
	
    @RequestMapping(value = "/pageQueryReport", method = RequestMethod.GET)
    @Logable(businessTag = "LoginLogController.pageQueryReport")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "分页查询登录日志报表(运营门户)", notes = "分页查询登录日志报表(运营门户)", httpMethod = "GET")
    @DownloadAble
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startTime", value = "开始时间YYYYMMDD", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "结束时间YYYYMMDD", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "userName", value = "登录账号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "state", value = "登录状态:0失败；1成功", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageNum", value = "查询开始页", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "查询的页面大小", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "download", value = "是否导出，true/false", required = false, dataType = "Boolean", paramType = "query"),
            @ApiImplicitParam(name = "fileName", value = "导出文件名称(带文件类型后缀，例如.csv)", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "type", value = "导出文件类型，例如 csv", required = false, dataType = "String", paramType = "query")
    })
    public PageResult<PasLoginLogReportResp> pageQueryReport(
            @RequestParam(value = "startTime", required = false) String startTime,
            @RequestParam(value = "endTime", required = false) String endTime,
            @RequestParam(value = "userName", required = false) String userName,
            @RequestParam(value = "state", required = false) String state,
            @RequestParam(value = "pageNum", required = true) Integer pageNum,
            @RequestParam(value = "pageSize", required = true) Integer pageSize,
            @RequestParam(value = "download", required = false) Boolean download,
            @RequestParam(value = "fileName", required = false) String fileName,
            @RequestParam(value = "type", required = false) String type,
            @RequestHeader(value = "x-userid", required = false) Long userId
        ) {

        PageResult<PasLoginLogReportResp> pageResult = null;
        try {
            PasLoginLogReq pasLoginLogReq = new PasLoginLogReq();
            if (StringUtils.isNotBlank(startTime)) {
                startTime = startTime + "000000";
            }

            if (StringUtils.isNotBlank(endTime)) {
                endTime = endTime + "235959";
            }

            // 用于封装查询条件
            int endNum = pageSize * pageNum;
            int startNum = endNum - pageSize + 1;

            pasLoginLogReq.setStartTime(startTime);
            pasLoginLogReq.setEndTime(endTime);
            pasLoginLogReq.setUsername(userName);
            pasLoginLogReq.setState(state);
            pasLoginLogReq.setEndNum(endNum);
            pasLoginLogReq.setStartNum(startNum);
            pasLoginLogReq.setDownload(download);
            pasLoginLogReq.setLogType(new Short("1"));

            pageResult = loginLogService.pageQueryReport(pasLoginLogReq);
            pageResult.setResult("0000");
        } catch (Exception e) {
            pageResult = new PageResult<PasLoginLogReportResp>();
            if (e instanceof AppException) {
                pageResult.setResult(((AppException) e).getErrorCode());
                pageResult.setErrorMsg(((AppException) e).getErrorMsg());
            } else {
                pageResult.setResult(PasCode.LOGINLOG_ERROR.code);
                pageResult.setErrorMsg(PasCode.LOGINLOG_ERROR.message);
            }
            return pageResult;
        }

        return pageResult;
    }
	
	
	
    @RequestMapping(value = "/pageQueryOperLog", method = RequestMethod.GET)
    @Logable(businessTag = "LoginLogController.pageQueryOperLog", outputResult = false)
    @Exceptionable
    @Validatable
    @ApiOperation(value = "分页查询操作日志（商户门户）", notes = "分页查询操作日志（商户门户）", httpMethod = "GET")
    @DownloadAble
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startTime", value = "开始时间YYYYMMDD", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "结束时间YYYYMMDD", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "download", value = "是否导出，true/false", required = false, dataType = "Boolean", paramType = "query"),
            @ApiImplicitParam(name = "fileName", value = "导出文件名称", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "type", value = "导出文件类型，例如 csv", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageNum", value = "查询开始页", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "查询的页面大小", required = true, dataType = "int", paramType = "query")})
    
    public PageResult<PasOperLogResp> pageQueryOperLog(
            @RequestParam(value = "startTime", required = false) String startTime,
            @RequestParam(value = "endTime", required = false) String endTime,
            @RequestParam(value = "download", required = false) Boolean download,
            @RequestParam(value = "fileName", required = false) String fileName,
            @RequestParam(value = "type", required = false) String type,
            @RequestParam(value = "pageNum", required = true) Integer pageNum,
            @RequestParam(value = "pageSize", required = true) Integer pageSize,
            @RequestHeader(value = "x-user-type", required = true) String userType,
            @RequestHeader(value = "x-userid", required = true) Long userId,
            @RequestHeader(value = "x-customer-code", required = true) String customerCodeHead
    ){
        
            PageResult<PasOperLogResp> pageResult = null;
            try {
                //用于封装查询条件
                int endNum = pageSize*pageNum;
                int startNum = endNum - pageSize + 1;
                
                startTime = StringUtils.isNotBlank(startTime) ? startTime +"000000" : startTime;
                endTime = StringUtils.isNotBlank(endTime) ?  endTime+"235959" : endTime;
//                String loginName = userService.queryCustLoginName(userId);
//                String userName = loginName == null ? customerCodeHead : loginName; // 商户门户有管理员与登录用户区分，不能直接用customerCode过滤

                PasLoginLogReq param = new PasLoginLogReq();
                param.setUsertype(UserType.PPS_USER.code);
                param.setStartTime(startTime);
                param.setEndTime(endTime);
//                param.setUsername(userName);
                param.setCustomercode(customerCodeHead); // 以操作员所属商户为维度查询操作记录
                param.setEndNum(endNum);
                param.setStartNum(startNum);
                param.setDownload(download);
                param.setLogType(new Short("2"));
                
                pageResult = loginLogService.pageQueryOperLog(param);
                pageResult.setResult("0000");
            } catch (Exception e) {
                pageResult = new PageResult<PasOperLogResp>();
                if (e instanceof AppException) {
                    pageResult.setResult(((AppException) e).getErrorCode());
                    pageResult.setErrorMsg(((AppException) e).getErrorMsg());
                } else {
                    pageResult.setResult(PasCode.LOGINLOG_ERROR.code);
                    pageResult.setErrorMsg(PasCode.LOGINLOG_ERROR.message);
                }
                return pageResult;
            }
            
            return pageResult;

    }
    

}
