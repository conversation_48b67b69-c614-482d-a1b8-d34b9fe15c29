package com.epaylinks.efps.pas.pas.dao;

import com.epaylinks.efps.pas.common.PageResult;
import com.epaylinks.efps.pas.mch.domain.PasHoliday;
import com.epaylinks.efps.pas.pas.domain.Holiday;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * Created by adm on 2018/8/23.
 */

@Mapper
@Transactional
public interface HolidayMapper {

    int batchSave(List<Holiday> list);

    int delete(String dateYear);

    int selectCountHoliday(Map map);

    List<Holiday> selectByPage(Map map);

    List<Holiday> selectByPage2(Map map);

    PasHoliday selectByDate(String date);
}
