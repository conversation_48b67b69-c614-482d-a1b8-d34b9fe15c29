package com.epaylinks.efps.pas.pas.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.epaylinks.efps.pas.pas.dao.LoginInfoMapper;
import com.epaylinks.efps.pas.pas.domain.PasLoginInfo;
import com.epaylinks.efps.pas.pas.service.LoginInfoService;

@Service
@Transactional
public class LoginInfoServiceImpl implements LoginInfoService {

	@Autowired
	private LoginInfoMapper loginInfoMapper;
	
	//定时任务更新登录错误次数
	@Override
	public void updateErrorRecordCount() {
		loginInfoMapper.updateErrorRecordCount();	
	}
	
	@Override
	public Integer checkPasLoginInfoExist(PasLoginInfo pasLoginInfo) {
		return loginInfoMapper.checkPasLoginInfoExist(pasLoginInfo);
	}

	@Override
	public void insertLoginInfo(PasLoginInfo pasLoginInfo) {
		loginInfoMapper.insertLoginInfo(pasLoginInfo);
	}

	@Override
	public void updateLoginInfo(PasLoginInfo pasLoginInfo) {
		loginInfoMapper.updateLoginInfo(pasLoginInfo);
	}

	//根据用户名和用户类型查询登录信息
	@Override
	public PasLoginInfo searchLoginInfo(PasLoginInfo pasLoginInfo) {
		return loginInfoMapper.searchLoginInfo(pasLoginInfo);
	}

    @Override
    public int recoveryErrorCountByUsernameAndType(String username, String usertype) {

        return loginInfoMapper.recoveryErrorCountByUsernameAndType(username, usertype);
    }





	

	

}
