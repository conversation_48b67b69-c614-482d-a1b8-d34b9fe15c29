package com.epaylinks.efps.pas.pas.service;

import com.epaylinks.efps.pas.pas.domain.PayMethod;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR> 5m
 * @Date : 2017/12/27 14:36
 * @Description :
 */
public interface PayMethodService {

    public int deleteByPrimaryKey(Long id);

    public int insert(PayMethod record);

    public int insertSelective(PayMethod record);

    public PayMethod selectByPrimaryKey(Long id);

    public int updateByPrimaryKeySelective(PayMethod record);

    public int updateByPrimaryKey(PayMethod record);

    public List<PayMethod> selectBySelective(PayMethod record);

    public List<PayMethod> selectByBusinessCode(String businessCode);

    public List<String> getInstitutionCodeByPayMethods(List<String> payMethodList);

    public void syncPayMethod();

    List<PayMethod> queryPayMethodByType(String type);
}
