package com.epaylinks.efps.pas.pas.service.impl;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.business.CommonResponse;
import com.epaylinks.efps.common.business.TransactionType;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.pas.acc.feign.AccClient;
import com.epaylinks.efps.pas.acc.feign.dto.acc.AccountResponse;
import com.epaylinks.efps.pas.acc.feign.dto.acc.AccountVo;
import com.epaylinks.efps.pas.acl.dao.UserMapper;
import com.epaylinks.efps.pas.acl.po.User;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.common.PasConstants;
import com.epaylinks.efps.pas.common.sm3.StringUtil;
import com.epaylinks.efps.pas.pas.dao.DkteRecordMapper;
import com.epaylinks.efps.pas.pas.domain.DkteRecord;
import com.epaylinks.efps.pas.pas.domain.DkteRecordVo;
import com.epaylinks.efps.pas.pas.service.DkteService;
import com.epaylinks.efps.pas.pas.util.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.BindingResult;

import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class DkteServiceImpl implements DkteService {

    private static SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");


    @Autowired
    private DkteRecordMapper dkteRecordMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private AccClient accClient;

    @Autowired
    private CommonLogger logger;

    /**
     * 生成交易编号
     *
     * @return
     */
    public static String createTradeNo(String tradeType) {
        StringBuilder tradeNo = new StringBuilder(tradeType);
        tradeNo.append(sdf.format(new Date()));
        tradeNo.append(RandomUtils.makeRandom(28 - tradeNo.length()));
        return tradeNo.toString();
    }

    @Override
    public void check(DkteRecordVo record, BindingResult bindingResult) {
        if (PasConstants.AdjustmentType.getCommentByCode(record.getAdjustType()) == null) {
            throw new AppException(PasCode.DATA_ERROR.code,"调额类型有误");
        }
        if(Objects.isNull(record.getAdjustAmout()) || record.getAdjustAmout() <= 0){
            throw new AppException(PasCode.DATA_ERROR.code, "调整金额有误");
        }
        if (!PasConstants.AdjustSubjectMatter.getMattersByAdjustType(record.getAdjustType(),"code").contains(record.getAdjustReason())) {
            throw new AppException(PasCode.DATA_ERROR.code,"选择"
                    + PasConstants.AdjustmentType.getCommentByCode(record.getAdjustType())
                    + "时，调额事由只能为："
                    + String.join("、",PasConstants.AdjustSubjectMatter.getMattersByAdjustType(record.getAdjustType(),"comment")));
        }
        if (bindingResult.hasErrors()) {
            throw new AppException(PasCode.DATA_ERROR.code,bindingResult.getAllErrors().get(0).getDefaultMessage());
        }
    }

    @Override
    public List<DkteRecord> pageQuery(Map map) {
        List<DkteRecord> list = dkteRecordMapper.selectByPage(map);
        if(Objects.nonNull(list) && !list.isEmpty()){
            for(DkteRecord record : list){      //展示时，减额记录的金额要加上负号
                if(Objects.nonNull(record)){
                    record.setCustomer(record.getCustomer());
                    record.setAdjustAmoutShow(record.getAdjustAmoutShow());
                    logger.printMessage("展示商户："+record.getCustomer());
                    logger.printMessage("展示金额："+record.getAdjustAmoutShow());
                }
            }
        }
        return list;
    }

    @Override
    public int queryCount(Map map) {
        return dkteRecordMapper.selectCount(map);
    }

    @Override
    public int add(DkteRecordVo record, String userId) {
        //查询是否存在分润账户，开通分润结算的代理商才有分润账户

        AccountVo accountVo = accountIsExist(record.getCustomerCode());

        if(Objects.isNull(accountVo)){
            throw new AppException(PasCode.DATA_ERROR.code,"只有开通分润结算的代理商才能进行调额");
        }
        DkteRecord dbRecord = new DkteRecord();
        if (PasConstants.AdjustmentType.THAW.code.equals(record.getAdjustType())) {
            checkThaw(record);
            dbRecord.setOrigFreezeNo(record.getOrigFreezeNo());
        }
        dbRecord.setCustomerCode(record.getCustomerCode());
        dbRecord.setCustomerName(record.getCustomerName());
        dbRecord.setAdjustType(record.getAdjustType());
        dbRecord.setAdjustReason(record.getAdjustReason());
        dbRecord.setAdjustAmout(record.getAdjustAmout());
        dbRecord.setRemark(record.getRemark());
        dbRecord.setAttachmentUid(record.getAttachmentUid());
        dbRecord.setAttachmentName(record.getAttachmentName());
        dbRecord.setCreatePersonId(userId);
        User user = userMapper.selectByPrimaryKey(Long.valueOf(userId));
        if(user != null){
            dbRecord.setCreatePersonName(user.getName());
        }
        Long id = dkteRecordMapper.selectIdFromSeq();
        String txNo = createTradeNo("FRDP");
        dbRecord.setDkteId(id);
        dbRecord.setTransactionNo(txNo);
        dbRecord.setRecordState("3");   //状态，1：审核通过；2：审核不通过；3：待审核
        dbRecord.setCreateTime(new Date());
        return dkteRecordMapper.insertSelective(dbRecord);
    }

    @Override
    public int update(DkteRecordVo record, String userId) {
        DkteRecord dbRecord = dkteRecordMapper.selectByPrimaryKey(record.getDkteId());
        if(Objects.isNull(dbRecord)){
            throw new AppException(PasCode.DATA_ERROR.code,  "记录ID有误");
        }
        if(!"2".equals(dbRecord.getRecordState())){
            throw new AppException(PasCode.DATA_ERROR.code,  "只有审核不通过的记录才能进行修改");
        }
        if (PasConstants.AdjustmentType.THAW.code.equals(record.getAdjustType())) {
            checkThaw(record);
            dbRecord.setOrigFreezeNo(record.getOrigFreezeNo());
        }
        dbRecord.setCustomerCode(record.getCustomerCode());
        dbRecord.setCustomerName(record.getCustomerName());
        dbRecord.setAdjustType(record.getAdjustType());
        dbRecord.setAdjustReason(record.getAdjustReason());
        dbRecord.setAdjustAmout(record.getAdjustAmout());
        dbRecord.setRemark(record.getRemark());
        dbRecord.setAttachmentUid(record.getAttachmentUid());
        dbRecord.setAttachmentName(record.getAttachmentName());
        dbRecord.setRecordState("3");   //状态，1：审核通过；2：审核不通过；3：待审核
        dbRecord.setUpdateTime(new Date());
        return dkteRecordMapper.updateByPrimaryKey(dbRecord);
    }

    @Override
    public int audit(Long dkteId, String auditResult, String auditReason, String userId) {
        DkteRecord dbRecord = dkteRecordMapper.selectByPrimaryKey(dkteId);
        if(Objects.isNull(dbRecord)){
            throw new AppException(PasCode.DATA_ERROR.code,  "记录ID有误");
        }
        if(!"3".equals(dbRecord.getRecordState())){
            throw new AppException(PasCode.DATA_ERROR.code,  "只有待审核的记录才能进行审核");
        }
        dbRecord.setAuditPersonId(userId);
        User user = userMapper.selectByPrimaryKey(Long.valueOf(userId));
        if(user != null){
            dbRecord.setAuditPersonName(user.getName());
        }
        dbRecord.setAuditResult(auditResult);   //审核结果1：审核通过；2：审核不通过；
        dbRecord.setAuditReason(auditReason);
        dbRecord.setRecordState(auditResult);   //状态，1：审核通过；2：审核不通过；3：待审核
        dbRecord.setAuditTime(new Date());
        if("1".equals(auditResult)){    //审核通过的，调用ACC接口
            Long amount = dbRecord.getAdjustAmout();
            String summary = null;
            if (PasConstants.AdjustSubjectMatter.KCFR.code.equals(dbRecord.getAdjustReason())
                    || PasConstants.AdjustSubjectMatter.DJFR.code.equals(dbRecord.getAdjustReason())
                    || PasConstants.AdjustSubjectMatter.JDFR.code.equals(dbRecord.getAdjustReason())) {
                summary = dbRecord.getRemark();
            }
            if("2".equals(dbRecord.getAdjustType()) || PasConstants.AdjustmentType.FREEZE.code.equals(dbRecord.getAdjustType())){
                amount = amount*(-1);
            }
            CommonOuterResponse res = accClient.frte(dbRecord.getCustomerCode(),dbRecord.getAdjustReason(),amount.intValue(),dbRecord.getTransactionNo(),summary, null);
            if(Objects.nonNull(res)){
                logger.printMessage("调用ACC调额接口返回："+res.getReturnCode()+"，"+res.getReturnMsg());
            }
            if(Objects.isNull(res)){
                throw new AppException(PasCode.DATA_ERROR.code,  "审核失败：调额失败");
            }
            if(!CommonOuterResponse.SUCCEE.equals(res.getReturnCode())){
                throw new AppException(PasCode.DATA_ERROR.code,  "审核失败："+res.getReturnMsg());
            }
            if(Objects.nonNull(res.getData())){
                dbRecord.setAccVoucherNo(res.getData().toString());
            }
        }
        return dkteRecordMapper.updateByPrimaryKey(dbRecord);
    }

    @Override
    public AccountVo accountIsExist(String customerNo) {
        AccountResponse accRes = accClient.queryAccount("1",1,1,null,
                customerNo,"XF-B",1);
        AccountVo accountVo = null;
        if(Objects.nonNull(accRes) && CommonResponse.SUCCEE.equals(accRes.getResult())){
            PageResult<AccountVo> accountList = accRes.getAccountList();
            if(Objects.nonNull(accountList) && Objects.nonNull(accountList.getRows())){
                List<AccountVo> accList = accountList.getRows();
                if(!accList.isEmpty()){
                    accountVo = accList.get(0);
                }
            }
        }
        return accountVo;
    }

    @Override
    public List<DkteRecord> getFreezeRecord(String customerNo, String transactionNo) {
        List<DkteRecord> vos = dkteRecordMapper.queryOrigFreezeRecord(customerNo,transactionNo);
        return vos;
    }

    /**
     * 校验解冻
     * @param record
     */
    private void checkThaw(DkteRecordVo record) {
        Map<String,Object> paramMap = new HashMap<>();
        if (StringUtils.isNotBlank(record.getOrigFreezeNo())) {
            paramMap.put("adjustType",PasConstants.AdjustmentType.FREEZE.code);
            paramMap.put("transactionNo",record.getOrigFreezeNo());
            DkteRecord dkteRecord = dkteRecordMapper.queryRecordByTransNo(paramMap);
            if (dkteRecord == null) {
                throw new AppException(PasCode.RECORD_NOT_EXIST.code,PasCode.RECORD_NOT_EXIST.message + "：原冻结流水");
            }
            if (!PasConstants.DkteAuditStatus.PASS.code.equals(dkteRecord.getRecordState())) {
                throw new AppException(PasCode.STATUS_ERROR.code,PasCode.STATUS_ERROR.message + "：原冻结流水未审核通过");
            }
            if (!dkteRecord.getCustomerCode().equals(record.getCustomerCode())) {
                throw new AppException(PasCode.DATA_ERROR.code,"商户与原冻结流水商户不一致");
            }
            if (dkteRecord.getAdjustAmout().intValue() != record.getAdjustAmout().intValue()) {
                throw new AppException(PasCode.DATA_ERROR.code,"解冻调整金额与原冻结流水不一致");
            }
            // 原冻结流水是否已解冻和占用
            paramMap.clear();
            paramMap.put("adjustType",PasConstants.AdjustmentType.THAW.code);
            paramMap.put("origFreezeNo",record.getOrigFreezeNo());
            paramMap.put("recordState",PasConstants.DkteAuditStatus.PASS.code);
            Integer count = 0;
            count = dkteRecordMapper.countRecordByOrigNo(paramMap);
            if (count > 0) {
                throw new AppException(PasCode.STATUS_ERROR.code,PasCode.STATUS_ERROR.message + "：原冻结流水已解冻");
            }
            paramMap.put("recordState",PasConstants.DkteAuditStatus.WAIT_AUDIT.code);
            count = dkteRecordMapper.countRecordByOrigNo(paramMap);
            if (count > 0) {
                throw new AppException(PasCode.STATUS_ERROR.code,PasCode.STATUS_ERROR.message + "：原冻结流水被其他待审核解冻单占用");
            }
        }
        // 校验解冻金额是否超过冻结金额
        Long count = dkteRecordMapper.countDkjeAmount(record.getCustomerCode());
        if (count == null || record.getAdjustAmout() > count) {
            throw new AppException(PasCode.DATA_ERROR.code,"调整金额超过现有冻结金额：" + (count == null ? 0 : count / 100));
        }
    }
}
