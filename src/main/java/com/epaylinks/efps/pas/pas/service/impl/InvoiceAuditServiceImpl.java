package com.epaylinks.efps.pas.pas.service.impl;

import com.alibaba.fastjson.JSON;
import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.business.pay.request.UserType;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.tool.time.Timex;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.pas.acc.feign.AccClient;
import com.epaylinks.efps.pas.acc.feign.dto.acc.AccountVo;
import com.epaylinks.efps.pas.acl.po.User;
import com.epaylinks.efps.pas.acl.service.UserService;
import com.epaylinks.efps.pas.common.FsCommonService;
import com.epaylinks.efps.pas.common.LogService;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.pas.controller.request.InvoiceAuditRequest;
import com.epaylinks.efps.pas.pas.controller.response.InvoiceAuditResponse;
import com.epaylinks.efps.pas.pas.dao.InvoiceAuditMapper;
import com.epaylinks.efps.pas.pas.domain.InvoiceAudit;
import com.epaylinks.efps.pas.pas.service.DkteService;
import com.epaylinks.efps.pas.pas.service.InvoiceAuditService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class InvoiceAuditServiceImpl implements InvoiceAuditService {
    @Autowired
    private InvoiceAuditMapper invoiceAuditMapper;

    @Autowired
    private DkteService dkteService;

    @Autowired
    private AccClient accClient;

    @Autowired
    private LogService logService;

    @Autowired
    private UserService userService;

    @Autowired
    private FsCommonService fsCommonService;

    private final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

    private final SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    public InvoiceAuditRequest checkParam(InvoiceAuditRequest request, Long userId, String customerNo,String source,String userType) {
        if (request == null || StringUtils.isEmpty(request.getCustomerNo())
                || StringUtils.isEmpty(request.getCustomerName()) || StringUtils.isEmpty(request.getInvoiceMedium())
                || StringUtils.isEmpty(request.getInvoiceType()) || StringUtils.isEmpty(request.getInvoiceNo())
                || request.getInvoiceTime() == null || request.getTaxRatio() == null || request.getInvoiceAmount() == null) {
            throw new AppException(PasCode.MISSING_PARAMETER.code,PasCode.MISSING_PARAMETER.message);
        }
        if (UserType.PPS_USER.code.equals(userType) && !customerNo.equals(request.getCustomerNo())) {
            throw new AppException(PasCode.DATA_ERROR.code,"商户信息不匹配");
        }
        if (!StringUtils.isEmpty(request.getRemarks()) && getByteLength(request.getRemarks()) > 100) {
            throw new AppException(PasCode.DATA_ERROR.code,"备注信息长度超过限制");
        }
        if (getByteLength(request.getInvoiceNo()) > 30) {
            throw new AppException(PasCode.DATA_ERROR.code,"发票编号长度超过限制");
        }
        try {
            sdf.parse(request.getInvoiceTime());
        } catch (Exception e) {
            throw new AppException(PasCode.DATA_ERROR.code,"时间格式不正确");
        }
        request.setInvoiceNo(request.getInvoiceNo().trim());
        return request;
    }

    @Override
    public void add(InvoiceAuditRequest request, Long userId, String customerNo,String userType) throws ParseException {
        // 校验是否开通了发票账户
        AccountVo accountVo = dkteService.accountIsExist(request.getCustomerNo());
        if(Objects.isNull(accountVo)){
            throw new AppException(PasCode.DATA_ERROR.code,"商户未开通发票账户");
        }
        // 校验是否重复
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("invoiceNo",request.getInvoiceNo());
        Integer count = invoiceAuditMapper.count(paramMap);
        if (count > 0) {
            throw new AppException(PasCode.DATA_ERROR.code,"发票编号重复");
        }
        // 新增发票
        InvoiceAudit invoiceAudit = new InvoiceAudit();
        BeanUtils.copyProperties(request,invoiceAudit);

        invoiceAudit.setAuditStatus("0");
        invoiceAudit.setEplSerialNo(DkteServiceImpl.createTradeNo("ZKFP"));
        invoiceAudit.setInvoiceTime(sdf.parse(request.getInvoiceTime()));
        invoiceAudit.setCreateTime(new Date());
        invoiceAudit.setUpdateTime(new Date());
        invoiceAudit.setCreator(userId);
        if (UserType.PAS_USER.code.equals(userType)) {
            invoiceAudit.setSource("PAS");
        } else if (UserType.PPS_USER.code.equals(userType)) {
            invoiceAudit.setSource("PPS");
        } else {
            throw new AppException(PasCode.DATA_ERROR.code,"获取登录用户信息失败");
        }
        invoiceAudit.setInvoiceId(invoiceAuditMapper.selectSeqInvoiceAudit());
        invoiceAuditMapper.insert(invoiceAudit);
    }

    @Override
    public void edit(InvoiceAuditRequest request, Long userId, String customerNo,String userType) throws ParseException {
        // 校验
        AccountVo accountVo = dkteService.accountIsExist(request.getCustomerNo());
        if(Objects.isNull(accountVo)){
            throw new AppException(PasCode.DATA_ERROR.code,"商户未开通发票账户");
        }
        if (request.getInvoiceId() == null) {
            throw new AppException(PasCode.MISSING_PARAMETER.code,PasCode.MISSING_PARAMETER.message);
        }
        InvoiceAudit invoiceAudit = invoiceAuditMapper.selectByPrimaryKey(request.getInvoiceId());
        if (invoiceAudit == null) {
            throw new AppException(PasCode.RECORD_NOT_EXIST.code,PasCode.RECORD_NOT_EXIST.message);
        }
        if (!"2".equals(invoiceAudit.getAuditStatus())) { // 审核不通过的可进行修改
            throw new AppException(PasCode.CURRENT_STATUS_CANNOT_MODIFIED.code,PasCode.CURRENT_STATUS_CANNOT_MODIFIED.message);
        }
        // 校验是否重复
        if (!invoiceAudit.getInvoiceNo().equals(request.getInvoiceNo())) {
            Map<String,Object> paramMap = new HashMap<>();
            paramMap.put("invoiceNo",request.getInvoiceNo());
            Integer count = invoiceAuditMapper.count(paramMap);
            if (count > 0) {
                throw new AppException(PasCode.DATA_ERROR.code,"发票编号重复");
            }
        }
        // 修改
        BeanUtils.copyProperties(request,invoiceAudit);
        invoiceAudit.setInvoiceTime(sdf.parse(request.getInvoiceTime()));
        invoiceAudit.setUpdateTime(new Date());
        invoiceAudit.setAuditStatus("0");
//        invoiceAudit.setReviewer(null);
//        invoiceAudit.setAuditComment(null);
//        invoiceAudit.setAuditTime(null);
        invoiceAudit.setCreator(userId);
        if (UserType.PAS_USER.code.equals(userType)) {
            invoiceAudit.setSource("PAS");
        } else if (UserType.PPS_USER.code.equals(userType)) {
            invoiceAudit.setSource("PPS");
        } else {
            throw new AppException(PasCode.DATA_ERROR.code,"获取登录用户信息失败");
        }
        invoiceAuditMapper.updateByPrimaryKey(invoiceAudit);
    }

    @Override
    public void audit(Long invoiceId, Long userId, String auditResult, String auditComment) {
        // 校验
        InvoiceAudit invoiceAudit = invoiceAuditMapper.selectByPrimaryKey(invoiceId);
        if (invoiceAudit == null) {
            throw new AppException(PasCode.RECORD_NOT_EXIST.code,PasCode.RECORD_NOT_EXIST.message);
        }
        if (!"0".equals(invoiceAudit.getAuditStatus())) {
            throw new AppException(PasCode.CURRENT_STATUS_CANNOT_AUDIT.code,PasCode.CURRENT_STATUS_CANNOT_AUDIT.message);
        }
        if ("2".equals(auditResult) && StringUtils.isEmpty(auditComment)) {
            throw new AppException(PasCode.MISSING_PARAMETER.code,PasCode.MISSING_PARAMETER.message + "：审核不通过时意见不能为空");
        }
        if (!StringUtils.isEmpty(auditComment) && getByteLength(auditComment) > 100) {
            throw new AppException(PasCode.DATA_ERROR.code,"审核意见长度超过限制");
        }
        // 审核通过调用ACC
        if ("1".equals(auditResult)) {
            try {
                String kpDate = null;
                if (invoiceAudit.getInvoiceTime() != null) {
                    kpDate = Timex.ofDate(invoiceAudit.getInvoiceTime()).to(Timex.Format.yyyyMMdd);
                }
                CommonOuterResponse response = accClient.frte(invoiceAudit.getCustomerNo()
                        ,"ZKFP"
                        ,invoiceAudit.getInvoiceAmount().intValue()
                        ,invoiceAudit.getEplSerialNo()
                        ,null, kpDate);
                logService.printLog("ZKFP-ACC:" + JSON.toJSONString(response));
                if(!CommonOuterResponse.SUCCEE.equals(response.getReturnCode())){
                    throw new AppException(PasCode.DATA_ERROR.code,  "审核开票失败：" + response.getReturnMsg());
                }
            } catch (Exception e) {
                logService.printLog("开票审核ACC调用失败：" + e.getMessage());
                throw new AppException(PasCode.DATA_ERROR.code,  "审核开票失败：ACC错误");
            }
        }
        // 审核
        invoiceAudit.setAuditStatus(auditResult);
        invoiceAudit.setAuditComment(auditComment);
        invoiceAudit.setAuditTime(new Date());
        invoiceAudit.setReviewer(userId);
        invoiceAuditMapper.updateByPrimaryKey(invoiceAudit);
    }

    @Override
    public PageResult<List<InvoiceAuditResponse>> pageQuery(Map paramMap, Long userId, String headCustomerNo,boolean download) {
        if (!StringUtils.isEmpty(headCustomerNo)) {
            paramMap.put("headCustomerNo",headCustomerNo);
        }
        List<InvoiceAuditResponse> responseList = new ArrayList<>();
        Integer count = invoiceAuditMapper.count(paramMap);
        if (download) {
            paramMap.put("endRowNo", count);
        }
        List<InvoiceAudit> invoiceAudits = invoiceAuditMapper.pageQueryInvoice(paramMap);
        if (invoiceAudits != null && invoiceAudits.size() > 0) {
            for (int i = 0; i < invoiceAudits.size(); i++) {
                InvoiceAuditResponse response = new InvoiceAuditResponse();
                InvoiceAudit invoiceAudit = invoiceAudits.get(i);
                BeanUtils.copyProperties(invoiceAudit,response);
//                if ("PAS".equals(invoiceAudit.getSource())) {
//                    User user = userService.queryUserById(invoiceAudit.getCreator());
//                    if (user != null) {
//                        response.setCreatorName(StringUtils.isEmpty(user.getRealName()) ? user.getName() : user.getRealName());
//                    }
//                } else if ("PPS".equals(invoiceAudit.getSource())) {
//                    response.setCreatorName(invoiceAuditMapper.selectCustUser(invoiceAudit.getCreator()));
//                }
//                User user = userService.queryUserById(invoiceAudit.getReviewer());
//                if (user != null) {
//                    response.setReviewerName(StringUtils.isEmpty(user.getRealName()) ? user.getName() : user.getRealName());
//                }
                // 转换url
                if (!StringUtils.isEmpty(invoiceAudit.getUniqueid())) {
                    Map<String, String> map = fsCommonService.queryFileUrlForDownload(invoiceAudit.getUniqueid(),true);
                    response.setUrl(map.get("url"));
                    response.setAttachmentName(map.get("name"));
                }

                response.setExportInvoiceAmount(invoiceAudit.getInvoiceAmount() / 100.00);
                response.setInvoiceAmount(invoiceAudit.getInvoiceAmount());
                response.setStatus(invoiceAudit.getAuditStatus());
                response.setReviewerId(invoiceAudit.getReviewer());
                response.setInvoiceTime(transDate(invoiceAudit.getInvoiceTime(),sdf));
                response.setCreateTime(transDate(invoiceAudit.getCreateTime(),format));
                response.setAuditTime(transDate(invoiceAudit.getAuditTime(),format));
                responseList.add(response);
            }
        }
        PageResult result = new PageResult();
        result.setTotal(count);
        result.setRows(responseList);
        return result;
    }

    private static Integer getByteLength(String param) {
        try {
            return param.getBytes("GBk").length;
        }catch (Exception e){
            System.out.println(e);
        }
        return 255;
    }

    private String transDate(Date date,SimpleDateFormat format) {
        if (date == null) {
            return null;
        }
        return format.format(date);
    }
}
