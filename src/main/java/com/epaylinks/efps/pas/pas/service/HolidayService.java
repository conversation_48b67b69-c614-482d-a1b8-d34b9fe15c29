package com.epaylinks.efps.pas.pas.service;

import com.epaylinks.efps.pas.common.PageResult;
import com.epaylinks.efps.pas.mch.domain.PasHoliday;
import com.epaylinks.efps.pas.pas.domain.Holiday;
import com.epaylinks.efps.pas.pas.vo.HolidayRequest;

import java.util.List;
import java.util.Map;

/**
 * Created by adm on 2018/8/23.
 */
public interface HolidayService {
    public int insert(HolidayRequest vo);

    public int update(HolidayRequest vo);

    public int delete(String dateYear);

    public PageResult<Holiday> selectByPage(Map map) ;

    public PageResult<Holiday> selectByPage2(Map map);

    PasHoliday selectByDate(String date);
}
