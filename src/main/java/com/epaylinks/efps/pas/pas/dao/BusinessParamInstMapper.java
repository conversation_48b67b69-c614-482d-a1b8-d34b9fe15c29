package com.epaylinks.efps.pas.pas.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.epaylinks.efps.pas.pas.domain.BusinessParamInst;
import com.epaylinks.efps.pas.pas.domain.BusinessParamInstKey;
@Mapper
public interface BusinessParamInstMapper {
    int deleteByPrimaryKey(BusinessParamInstKey key);

    int insert(BusinessParamInst record);

    BusinessParamInst selectByPrimaryKey(BusinessParamInstKey key);

    int updateByPrimaryKeySelective(BusinessParamInst record);

    int updateByPrimaryKey(BusinessParamInst record);
    
	List<BusinessParamInst> selectByBusinessInstDBId(Long businessInstDBId);
	
	int updateBusExamId(@Param("businessInstDBId")Long businessInstDBId , @Param("newBusExamId")String newBusExamId);

	BusinessParamInst selectByBusinessInstDBIdAndCode(@Param("businessId")Long businessId, @Param("code")String code);

    int deleteByBusinessExamId(@Param("businessExamId") String businessExamId);
}