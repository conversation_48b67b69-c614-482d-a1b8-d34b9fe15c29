package com.epaylinks.efps.pas.pas.dao;

import com.epaylinks.efps.pas.pas.domain.BizPayMethod;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


@Mapper
@Transactional
public interface BizPayMethodMapper {
	
    int deleteByPrimaryKey(Long id);

    int deleteBySelective(BizPayMethod record);

    int insert(BizPayMethod record);

    int insertSelective(BizPayMethod record);

    BizPayMethod selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(BizPayMethod record);

    int updateByPrimaryKey(BizPayMethod record);

    int addBizPayMethods(List<BizPayMethod> list);

    @Transactional(readOnly = true)
    List<BizPayMethod> selectBySelective(BizPayMethod record);
    
    @Transactional(readOnly = true)
    List<String> selectPayMethods(@Param("businessCode") String businessCode);
    
    List<BizPayMethod> selectAll();
}