package com.epaylinks.efps.pas.pas.service.impl;

import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.common.util.StringUtils;
import com.epaylinks.efps.pas.acl.dao.UserMapper;
import com.epaylinks.efps.pas.acl.po.User;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.pas.dao.BkCardBinMapper;
import com.epaylinks.efps.pas.pas.model.BkCardBin;
import com.epaylinks.efps.pas.pas.service.UnionCardBinFtpService;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class BkCardBinServiceImpl {

    @Autowired
    private CommonLogger logger;

    @Autowired
    private BkCardBinMapper bkCardBinMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private SequenceService sequenceService;

    public void saveBkCardBin(BkCardBin bkCardBin, String method) {
        switch (method) {
            case "add":
                insertRecord(bkCardBin);
                break;
            case "edit":
                editRecord(bkCardBin);
                break;
            case "saveJob":
                saveRecordForJob(bkCardBin);
                break;
            default:
                throw new AppException(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
        }
    }

    private void insertRecord(BkCardBin bkCardBin) {
        if(bkCardBinMapper.checkCardBinExist(bkCardBin.getParentType(),bkCardBin.getSonType(),bkCardBin.getCardNoRange(),bkCardBin.getCardNoLen(),bkCardBin.getLoadFrom())){
            throw new AppException(PasCode.DATA_NOT_EXIST.code, "相关父类子类下，卡Bin数据已存在："+bkCardBin.getCardNoRange());
        }
        bkCardBin.setCreateTime(new Date());
        bkCardBin.setFlag("1");
        bkCardBin.setId(sequenceService.nextValue("bkCardBin"));
        bkCardBinMapper.insertSelective(bkCardBin);
    }

    private void editRecord(BkCardBin bkCardBin) {
        BkCardBin newInput = bkCardBinMapper.selectCardBinByType(bkCardBin.getParentType(),bkCardBin.getSonType(),bkCardBin.getCardNoRange(),bkCardBin.getCardNoLen(),bkCardBin.getLoadFrom());
        BkCardBin record = bkCardBinMapper.selectByPrimaryKey(bkCardBin.getId());
        if (Objects.nonNull(record)) {
            if(newInput!=null && !record.getId().equals(newInput.getId())){
                throw new AppException(PasCode.DATA_NOT_EXIST.code, "相关父类子类下，卡Bin数据已存在");
            }
            bkCardBin.setUpdateTime(new Date());
            bkCardBinMapper.updateByPrimaryKeySelective(bkCardBin);
        } else {
            throw new AppException(PasCode.RECORD_NOT_EXIST.code, PasCode.RECORD_NOT_EXIST.message);
        }
    }

    private void saveRecordForJob(BkCardBin bkCardBin){
        BkCardBin newInput = bkCardBinMapper.selectCardBinByType(bkCardBin.getParentType(),bkCardBin.getSonType(),bkCardBin.getCardNoRange(),bkCardBin.getCardNoLen(),bkCardBin.getLoadFrom());
        if(Objects.nonNull(newInput) && Objects.nonNull(newInput.getId())){
            bkCardBin.setId(newInput.getId());
            bkCardBin.setUpdateTime(new Date());
            bkCardBinMapper.updateByPrimaryKeySelective(bkCardBin);
            logger.printMessage("更新卡BIN："+bkCardBin.getCardNoRange());
        }else {
            insertRecord(bkCardBin);
            logger.printMessage("新增卡BIN："+bkCardBin.getCardNoRange());
        }
    }

    public void enableBkCardBin(Long id, String flag, String userId) {
        BkCardBin record = bkCardBinMapper.selectByPrimaryKey(id);
        if (Objects.isNull(record)) {
            throw new AppException(PasCode.RECORD_NOT_EXIST.code, PasCode.RECORD_NOT_EXIST.message);
        } else {
            record.setUpdateTime(new Date());
            record.setFlag(flag);
            record.setOperatorId(getOpMan(userId));
            bkCardBinMapper.updateByPrimaryKeySelective(record);
        }
    }

    public BkCardBin queryById(String binId){
        if(StringUtils.isBlank(binId)){
            return null;
        }
        return bkCardBinMapper.selectByPrimaryKey(Long.parseLong(binId));
    }

    public int queryCount(Map map) {
        return bkCardBinMapper.selectCountByMap(map);
    }

    public String getOpMan(String opId){
        if(StringUtils.isBlank(opId) || !opId.matches("^([1-9]\\d*)|(0)$")){
            return null;
        }
        User user = userMapper.selectByPrimaryKey(Long.parseLong(opId));
        if(user!=null){
            return user.getName();
        }else {
            return null;
        }
    }

    public List<BkCardBin> bankPageQuery(Map map) {
        List<BkCardBin> bkCardBinList =  bkCardBinMapper.selectBkCardBinPage(map);
        //bkCardBinList.forEach(bkCardBin -> {});
        return bkCardBinList;
    }

    /**
     * 【PJ22】多卡表并用优化
     * 3、卡表查询顺序
     *  卡表数据查询顺序从前往后依次为：转账卡表 > 非标卡卡表 > 单位结算卡卡表 > 业务卡表，仅在上一张卡表找不到数据时，再往下一张卡表查询。
     * @param cardNoRang
     * @return
     */
    public List<BkCardBin> queryByCardNoRang(String cardNoRang){
        if (StringUtils.isBlank(cardNoRang)){
            return new ArrayList<>();
        }
        List<BkCardBin> list = bkCardBinMapper.selectBkCardBinByCardNoRang(cardNoRang, UnionCardBinFtpService.PARENT_TYPE_YW,UnionCardBinFtpService.TFRBIN);
        if(Objects.isNull(list) || list.isEmpty()){
            list = bkCardBinMapper.selectBkCardBinByCardNoRang(cardNoRang, UnionCardBinFtpService.PARENT_TYPE_LY,UnionCardBinFtpService.NONBIN);
        }
        if(Objects.isNull(list) || list.isEmpty()){
            list = bkCardBinMapper.selectBkCardBinByCardNoRang(cardNoRang, UnionCardBinFtpService.PARENT_TYPE_YW,UnionCardBinFtpService.DWBIN);
        }
        if(Objects.isNull(list) || list.isEmpty()){
            list = bkCardBinMapper.selectBkCardBinByCardNoRang(cardNoRang, UnionCardBinFtpService.PARENT_TYPE_YW,UnionCardBinFtpService.YWBIN);
        }
        //新加入境外发行银联卡卡表
        if(Objects.isNull(list) || list.isEmpty()){
            list = bkCardBinMapper.selectBkCardBinByCardNoRang(cardNoRang, UnionCardBinFtpService.PARENT_TYPE_YW,UnionCardBinFtpService.OCBIN);
        }
        if(Objects.isNull(list) || list.isEmpty()){
            list = new ArrayList<>();
        }
        return list;
    }

    /**
     * 【PJ22】多卡表并用优化
     * 3、卡表查询顺序
     *  卡表数据查询顺序从前往后依次为：转账卡表 > 非标卡卡表 > 单位结算卡卡表 > 业务卡表，仅在上一张卡表找不到数据时，再往下一张卡表查询。
     * @param cardNo
     * @return
     */
    public List<BkCardBin> queryByCardNo(String cardNo){
        if (StringUtils.isBlank(cardNo)){
            return new ArrayList<>();
        }
        List<BkCardBin> list = bkCardBinMapper.selectByCardNoAndCardNoLen(cardNo, cardNo.length(), UnionCardBinFtpService.PARENT_TYPE_YW,UnionCardBinFtpService.TFRBIN);
        if(Objects.isNull(list) || list.isEmpty()){
            list = bkCardBinMapper.selectByCardNoAndCardNoLen(cardNo, cardNo.length(), UnionCardBinFtpService.PARENT_TYPE_LY,UnionCardBinFtpService.NONBIN);
        }
        if(Objects.isNull(list) || list.isEmpty()){
            list = bkCardBinMapper.selectByCardNoAndCardNoLen(cardNo, cardNo.length(), UnionCardBinFtpService.PARENT_TYPE_YW,UnionCardBinFtpService.DWBIN);
        }
        if(Objects.isNull(list) || list.isEmpty()){
            list = bkCardBinMapper.selectByCardNoAndCardNoLen(cardNo, cardNo.length(), UnionCardBinFtpService.PARENT_TYPE_YW,UnionCardBinFtpService.YWBIN);
        }
        //新加入境外发行银联卡卡表
        if(Objects.isNull(list) || list.isEmpty()){
            list = bkCardBinMapper.selectByCardNoAndCardNoLen(cardNo, cardNo.length(), UnionCardBinFtpService.PARENT_TYPE_YW,UnionCardBinFtpService.OCBIN);
        }
        if(Objects.isNull(list) || list.isEmpty()){
            list = new ArrayList<>();
        }
        return list;
    }
}
