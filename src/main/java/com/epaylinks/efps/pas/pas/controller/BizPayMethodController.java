package com.epaylinks.efps.pas.pas.controller;

import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.pas.common.Result;
import com.epaylinks.efps.pas.pas.domain.BizPayMethod;
import com.epaylinks.efps.pas.pas.service.BizPayMethodService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 * <AUTHOR> 5m
 * @Date : 2017/12/27 17:02
 * @Description : 业务-支付方式关联
 */
@RestController
@RequestMapping("/BizPayMethod")
@Api(value = "BizPayMethodController", description = "业务-支付方式关联")
public class BizPayMethodController {

    @Autowired
    private SequenceService sequenceService;

    @Autowired
    private BizPayMethodService bizPayMethodService;


    @RequestMapping(value ="/create", method = RequestMethod.POST)
    @Logable(businessTag = "create")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "新增业务-支付方式关联", notes = "新增业务-支付方式关联", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "businessCode", value = "业务编码", required = true, dataType = "String", length =50, paramType = "query"),
            @ApiImplicitParam(name = "payMethodCode", value = "支付方式编码", required = true, dataType = "String", length =50, paramType = "query")})
    public String create(@ApiIgnore BizPayMethod record) {
        record.setId(sequenceService.nextValue("pas"));
        return bizPayMethodService.insert(record) == 1? Constants.SUCCESS: Constants.FAIL;

    }

    @RequestMapping(value ="/modify", method = RequestMethod.POST)
    @Logable(businessTag = "modify")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "修改业务-支付方式关联", notes = "修改业务-支付方式关联", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "id", required = true, dataType = "long", length =50, paramType = "query"),
            @ApiImplicitParam(name = "businessCode", value = "业务编码", required = false, dataType = "String", length =50, paramType = "query"),
            @ApiImplicitParam(name = "payMethodCode", value = "支付方式编码", required = false, dataType = "String", length =50, paramType = "query")})
    public String modify(@ApiIgnore BizPayMethod record ) {
        return bizPayMethodService.updateByPrimaryKeySelective(record) == 1? Constants.SUCCESS: Constants.FAIL;

    }


    @RequestMapping(value ="/delete", method = RequestMethod.GET)
    @Logable(businessTag = "delete")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "删除业务-支付方式关联", notes = "删除业务-支付方式关联", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "id", required = true, dataType = "long", length =50, paramType = "query")})
    public String delete(@RequestParam Long id) {
        return bizPayMethodService.deleteByPrimaryKey(id) == 1? Constants.SUCCESS: Constants.FAIL;
    }


    @RequestMapping(value ="/select", method = RequestMethod.GET)
    @Logable(businessTag = "select")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "搜索业务-支付方式关联", notes = "搜索业务-支付方式关联", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "businessCode", value = "业务编码", required = false, dataType = "String", length =50, paramType = "query"),
            @ApiImplicitParam(name = "payMethodCode", value = "支付方式编码", required = false, dataType = "String", length =50, paramType = "query")})
    public List<BizPayMethod> select(@ApiIgnore BizPayMethod record ) {
        return bizPayMethodService.selectBySelective(record) ;
    }

    @RequestMapping(value ="/selectById", method = RequestMethod.GET)
    @Logable(businessTag = "selectById")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "根据ID搜索单条业务-支付方式关联", notes = "根据ID搜索单条业务-支付方式关联", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "id", required = true, dataType = "long", length =50, paramType = "query")})
    public BizPayMethod selectById(@RequestParam Long id) {
        return bizPayMethodService.selectByPrimaryKey(id) ;
    }

    /**
     * 同步所有的业务信息（往kafka发送数据）
     * @return 成功 "1" ， 失败  "0"
     */
    @RequestMapping(value = "/sync" , method = RequestMethod.GET)
    public Result<String> syncBusinessPayMethod() {
    	bizPayMethodService.syncAllBizPayMethod();
    	Result<String> result = new Result<>();
    	result.setData(Constants.SUCCESS);
    	return result;
    }

}
