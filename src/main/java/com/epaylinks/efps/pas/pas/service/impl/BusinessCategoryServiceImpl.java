package com.epaylinks.efps.pas.pas.service.impl;

import java.util.List;

import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.pas.cum.domain.CumCustomerInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.common.PasConstants;
import com.epaylinks.efps.pas.pas.dao.BusinessCategoryMapper;
import com.epaylinks.efps.pas.pas.domain.BusinessCategory;
@Service
public class BusinessCategoryServiceImpl {
	@Autowired
	private BusinessCategoryMapper businessCategoryMapper;
	@Autowired
	private KafkaTemplate<String, String> kafkaTemplate;
	
	public static final String PAS_TOPIC = "PAS_BasicData";
	
	private static final String ALL_BUSINESS_CATEGORY_KEY = "AllBusinessCategory";
	
	public void syncAllBusinessCategory() {
		// TODO Auto-generated method stub
		List<BusinessCategory> businessCategories = businessCategoryMapper.queryAll();
		String data = JSON.toJSONString(businessCategories);
		try {
			kafkaTemplate.send(PAS_TOPIC, ALL_BUSINESS_CATEGORY_KEY, data);
		} catch (Exception e) {
			// TODO: handle exception
			throw new AppException(PasCode.SEND_KAFKA_EXCEPTION.code, e);
		}
	}




	/*
	 * 查询范围内的商户号对应商户名称
	 */

	@Logable(businessTag = "getBusinessCategoryByCodes")
	public List<BusinessCategory> getBusinessCategoryByCodes(List<String> codes) {
		return businessCategoryMapper.getBusinessCategoryByCodes(codes);
	}



}
