package com.epaylinks.efps.pas.pas.service.impl;

import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.common.util.StringUtils;
import com.epaylinks.efps.pas.acl.dao.UserMapper;
import com.epaylinks.efps.pas.acl.po.User;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.common.sm3.StringUtil;
import com.epaylinks.efps.pas.pas.dao.BkBankMapper;
import com.epaylinks.efps.pas.pas.model.BkBank;
import com.epaylinks.efps.pas.pas.model.BkBranch;
import com.epaylinks.efps.pas.pas.model.BkCardBin;
import com.epaylinks.efps.pas.pas.vo.BankDataRelevance;
import com.epaylinks.efps.pas.pas.vo.BkBankVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
public class BkBankServiceImpl {
    @Autowired
    private BkBankMapper bkBankMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private SequenceService sequenceService;
    @Autowired
    private BkCardBinServiceImpl bkCardBinService;

    @Autowired
    private BkBranchServiceImpl bkBranchService;

    public void saveBank(BkBank bkBank, String method) {
        switch (method){
            case "add": insertRecord(bkBank); break;
            case "edit": editRecord(bkBank); break;
            default: throw new AppException(PasCode.SYSTEM_EXCEPTION.code,PasCode.SYSTEM_EXCEPTION.message);
        }
    }

    private void insertRecord(BkBank bkBank){
        if(!StringUtils.isBlank(bkBank.getBankId()) && bkBankMapper.selectByBankId(Long.parseLong(bkBank.getBankId())).size() > 0){
            throw new AppException(PasCode.RECORD_EXIST.code, PasCode.RECORD_EXIST.message + ",联行号行别代码" + bkBank.getBankId() + "重复");
        }
        if(bkBankMapper.selectByBankCodeOrBankName(bkBank.getBankCode(),null).size() > 0){
            throw new AppException(PasCode.RECORD_EXIST.code, PasCode.RECORD_EXIST.message);
        }
        bkBank.setCreateTime(new Date());
        bkBank.setNewBankCode(bkBank.getBankCode());
        bkBank.setId(sequenceService.nextValue("bkBank"));
        bkBank.setFlag("1");
        bkBankMapper.insertSelective(bkBank);
    }

    private void editRecord(BkBank bkBank){
        BkBank record = bkBankMapper.selectByPrimaryKey(bkBank.getId());
        if(record != null){
            if(!StringUtils.isBlank(bkBank.getBankId()) && !bkBank.getBankId().equals(record.getBankId())){
                if(bkBankMapper.selectByBankId(Long.parseLong(bkBank.getBankId())).size() > 0){
                    throw new AppException(PasCode.RECORD_EXIST.code, PasCode.RECORD_EXIST.message + ",联行号行别代码" + bkBank.getBankId() + "重复");
                }
            }
            bkBank.setUpdateTime(new Date());
            bkBank.setNewBankCode(bkBank.getBankCode());
            bkBankMapper.updateByPrimaryKeySelective(bkBank);
        }else {
            throw new AppException(PasCode.RECORD_NOT_EXIST.code, PasCode.RECORD_NOT_EXIST.message);
        }
    }

    public void enableBank(Long id,String flag, String userId) {
        BkBank record = bkBankMapper.selectByPrimaryKey(id);
        if(record == null) {
            throw new AppException(PasCode.RECORD_NOT_EXIST.code,PasCode.RECORD_NOT_EXIST.message);
        }else {
            record.setUpdateTime(new Date());
            record.setFlag(flag);
            record.setOperatorId(userId);
            bkBankMapper.updateByPrimaryKeySelective(record);
        }
    }

    public int queryCount(Map map){
        return bkBankMapper.selectCountByMap(map);
    }

    public List<BkBank> bankPageQuery(Map map){
        List<BkBank> bkBankList = null;
        if(map.get("download") != null && (Boolean) map.get("download")){
            bkBankList = bkBankMapper.selectBankNotPage(map);
        }else {
            bkBankList = bkBankMapper.selectBankPage(map);
        }

        bkBankList.forEach(bkBank -> {
            if(!StringUtils.isBlank(bkBank.getOperatorId())){
                User user = userMapper.selectByPrimaryKey(Long.valueOf(bkBank.getOperatorId()));
                if(user != null){
                    bkBank.setOperatorId(userMapper.selectByPrimaryKey(Long.valueOf(bkBank.getOperatorId())).getName());
                }
            }
        });
        return bkBankList;
    }


    public BankDataRelevance dataRelevance(String cardNoRang){
        BankDataRelevance bankDataRelevance = new BankDataRelevance();
        List<BkBankVo> bkBankVoList = new ArrayList<>();
        List<BkCardBin> bkCardBinList = bkCardBinService.queryByCardNoRang(cardNoRang);
        bkCardBinList.forEach(bkCardBin -> {
            String bankCode = bkCardBin.getBankIcon();
            List<BkBank> bkBankList;
            if(!StringUtils.isBlank(bankCode)){
                bkBankList = bkBankMapper.selectByBankCodeOrBankName(bankCode,null);
            }else {
                bkBankList = bkBankMapper.selectByBankCodeOrBankName(null,bkCardBin.getIssueBankName());
            }
            bkBankList.forEach(bkBank -> {
                BkBankVo bkBankVo = new BkBankVo();
                bkBankVo.setBankName(bkBank.getBankName());
                bkBankVo.setInstitutionCode(bkBank.getInstitutionCode());
                bkBankVo.setBkBranchList(bkBranchService.queryByLbnkCd(bkBank.getBankId()));
                bkBankVoList.add(bkBankVo);
            });
        });

        bankDataRelevance.setBkBankVoList(bkBankVoList);
        return bankDataRelevance;
    }

    @Logable(businessTag = "queryBkBankByType", outputResult = false)
    public List<BkBank> queryBkBank(String cardNoRang, String bankName, String cardNo, String bankBranchNo, String type){
        switch (type){
            case "1": return queryBkBankByBankName(bankName);
            case "2": return queryBkBankByCardNo(cardNo);
            case "3": return queryBkBankByBankBranchNo(bankBranchNo);
            case "4": return queryBkBankByCardNoRang(cardNoRang);
            default: throw new AppException(PasCode.SYSTEM_EXCEPTION.code, "查询类型不存在");
        }
    }

    //根据银行名称查询
    private List<BkBank> queryBkBankByBankName(String bankName){
        return bkBankMapper.selectByBankCodeOrBankName(null, bankName);
    }

    //根据银行卡号查询
    private List<BkBank> queryBkBankByCardNo(String cardNo){
        List<BkCardBin> bkCardBinList = bkCardBinService.queryByCardNo(cardNo);
        return getBkBank(bkCardBinList);
    }

    //根据银行卡前几位匹配查询
    private List<BkBank> queryBkBankByCardNoRang(String cardNoRang){
        List<BkCardBin> bkCardBinList = bkCardBinService.queryByCardNoRang(cardNoRang);
        return getBkBank(bkCardBinList);
    }

    //根据联行号查询
    private List<BkBank> queryBkBankByBankBranchNo(String bankBranchNo){
        BkBranch bkBranch = bkBranchService.queryBkBranchByLbnkNo(bankBranchNo);
        if(bkBranch == null){
            return null;
        }

        return bkBankMapper.selectByBankId(Long.valueOf(bkBranch.getLbnkCd()));
    }

    private List<BkBank> getBkBank(List<BkCardBin> bkCardBinList){
        List<BkBank> bkBankList = new ArrayList<>();
        for (BkCardBin bkCardBin : bkCardBinList){
            List<BkBank> list = new ArrayList<>();
            if(!StringUtils.isBlank(bkCardBin.getIssueBankNo())){
                list = bkBankMapper.selectByIssueBankNo(bkCardBin.getIssueBankNo());
            }

            if(list.size() == 0 && !StringUtils.isBlank(bkCardBin.getBankIcon())){
                list = bkBankMapper.selectByBankCodeOrBankName(bkCardBin.getBankIcon(), null);
            }

            if(list.size() == 0 && !StringUtils.isBlank(bkCardBin.getIssueBankName())){
                list = bkBankMapper.selectByBankCodeOrBankName(null, bkCardBin.getIssueBankName());
            }
            bkBankList.addAll(list);
        }
        return bkBankList;
    }
}
