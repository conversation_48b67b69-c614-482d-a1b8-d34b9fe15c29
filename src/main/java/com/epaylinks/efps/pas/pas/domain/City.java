package com.epaylinks.efps.pas.pas.domain;

public class City {
    /**
     * 市公司编号
     */
    private String cityCd;

    /**
     * 省公司编号
     */
    private String provCd;

    /**
     */
    private String cityLnm;

    /**
     * 城市名称
     */
    private String cityNm;

    /**
     */
    private String bossCity;

    /**
     */
    private String vgopCity;

    /**
     * 更新日期
     */
    private String updDt;

    /**
     * 时间戳
     */
    private String tmSmp;

    /**
     * 终端流水号
     */
    private String nodId;

    public String getCityCd() {
        return cityCd;
    }

    public void setCityCd(String cityCd) {
        this.cityCd = cityCd;
    }

    public String getProvCd() {
        return provCd;
    }

    public void setProvCd(String provCd) {
        this.provCd = provCd;
    }

    public String getCityLnm() {
        return cityLnm;
    }

    public void setCityLnm(String cityLnm) {
        this.cityLnm = cityLnm;
    }

    public String getCityNm() {
        return cityNm;
    }

    public void setCityNm(String cityNm) {
        this.cityNm = cityNm;
    }

    public String getBossCity() {
        return bossCity;
    }

    public void setBossCity(String bossCity) {
        this.bossCity = bossCity;
    }

    public String getVgopCity() {
        return vgopCity;
    }

    public void setVgopCity(String vgopCity) {
        this.vgopCity = vgopCity;
    }

    public String getUpdDt() {
        return updDt;
    }

    public void setUpdDt(String updDt) {
        this.updDt = updDt;
    }

    public String getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(String tmSmp) {
        this.tmSmp = tmSmp;
    }

    public String getNodId() {
        return nodId;
    }

    public void setNodId(String nodId) {
        this.nodId = nodId;
    }
}