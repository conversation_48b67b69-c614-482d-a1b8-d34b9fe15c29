package com.epaylinks.efps.pas.pas.service;

import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.pas.txs.domain.TxsPayTradeOrder;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

/**
 * <AUTHOR> 5m
 * @Date : 2018/1/2 14:30
 * @Description :
 */
@FeignClient("clr")
public interface MchinletInletService {

    @RequestMapping(value = "/Inlet", method= RequestMethod.POST)
    public Map<String, String> inlet( @RequestParam(value = "fromSystemId", required = true) String fromSystemId,
                                      @RequestParam(value = "customerCode", required = true) String customerCode,
                                      @RequestParam(value = "requestId", required = true) String requestId,
                                      @RequestParam(value = "institutionCode", required = true) String institutionCode,
                                      @RequestParam(value = "params", required = false) String params );


}
