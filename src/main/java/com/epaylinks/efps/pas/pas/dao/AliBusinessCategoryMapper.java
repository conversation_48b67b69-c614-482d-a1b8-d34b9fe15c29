package com.epaylinks.efps.pas.pas.dao;

import com.epaylinks.efps.pas.pas.domain.AliBusinessCategory;
import com.epaylinks.efps.pas.pas.vo.CodeAndName;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AliBusinessCategoryMapper {

    int deleteByPrimaryKey(String mcc);

    int insert(AliBusinessCategory record);

    int insertSelective(AliBusinessCategory record);

    AliBusinessCategory selectByPrimaryKey(String mcc);

    int updateByPrimaryKeySelective(AliBusinessCategory record);

    int updateByPrimaryKey(AliBusinessCategory record);

    void batchInsert(@Param("records") List<AliBusinessCategory> records);

    List<CodeAndName> selectLevel1s();

    List<CodeAndName> selectLevel2s(@Param("level1") String level1);

    List<AliBusinessCategory> selectLevel3s(@Param("level1") String level1, @Param("level2") String level2);
    
    List<AliBusinessCategory> selectByUnionMcc(@Param("unionMcc") String unionMcc);

    List<AliBusinessCategory> getAliBusinessCategoryByWord(String keyword);
}