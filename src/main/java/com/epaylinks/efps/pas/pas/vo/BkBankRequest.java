package com.epaylinks.efps.pas.pas.vo;

public class BkBankRequest {
    private String bankName;
    private String institutionCode;
    private String issueBankNo;
    private String bankId;
    private String flag;
    private String bankCode;
    private Integer isAreaBank;
    private String beginTime;
    private String endTime;

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getInstitutionCode() {
        return institutionCode;
    }

    public void setInstitutionCode(String institutionCode) {
        this.institutionCode = institutionCode;
    }

    public String getIssueBankNo() {
        return issueBankNo;
    }

    public void setIssueBankNo(String issueBankNo) {
        this.issueBankNo = issueBankNo;
    }

    public String getBankId() {
        return bankId;
    }

    public void setBankId(String bankId) {
        this.bankId = bankId;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public Integer getIsAreaBank() {
        return isAreaBank;
    }

    public void setIsAreaBank(Integer isAreaBank) {
        this.isAreaBank = isAreaBank;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
}
