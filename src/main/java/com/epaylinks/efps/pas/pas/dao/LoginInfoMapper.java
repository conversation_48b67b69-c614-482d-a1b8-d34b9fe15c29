package com.epaylinks.efps.pas.pas.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.epaylinks.efps.pas.pas.domain.PasLoginInfo;

@Mapper
public interface LoginInfoMapper {

	Integer checkPasLoginInfoExist(PasLoginInfo pasLoginInfo);
	
	void updateErrorRecordCount();

	int insertLoginInfo(PasLoginInfo pasLoginInfo);
	
	int updateLoginInfo(PasLoginInfo pasLoginInfo);
	
	PasLoginInfo searchLoginInfo(PasLoginInfo pasLoginInfo);
	
	int recoveryErrorCountByUsernameAndType(@Param("username") String username, @Param("usertype") String usertype);
	
}
