package com.epaylinks.efps.pas.pas.controller.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
public class ManagementSendRecordResponse {

    private Long id;

    private Long sendId;

    @ApiModelProperty(value = "发送人",dataType = "String")
    private String sender;

    @ApiModelProperty(value = "接收人",dataType = "String")
    private String receiver;

    @ApiModelProperty(value = "文件名称",dataType = "String")
    private String fileName;

    @ApiModelProperty(value = "发送时间",dataType = "String")
    private String sendTime;

}
