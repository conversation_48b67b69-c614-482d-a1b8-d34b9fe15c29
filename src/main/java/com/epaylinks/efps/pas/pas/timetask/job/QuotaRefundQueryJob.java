package com.epaylinks.efps.pas.pas.timetask.job;

import com.epaylinks.efps.common.business.pay.request.UserType;
import com.epaylinks.efps.common.timetask.TaskJob;
import com.epaylinks.efps.common.timetask.TaskRequest;
import com.epaylinks.efps.common.tool.json.JsonUtils;
import com.epaylinks.efps.common.tool.time.Timex;
import com.epaylinks.efps.pas.acc.controller.request.AcctQuotaAuditRequest;
import com.epaylinks.efps.pas.acc.dao.AcctQuotaRecordMapper;
import com.epaylinks.efps.pas.acc.entity.AcctQuotaRecord;
import com.epaylinks.efps.pas.acc.service.AcctQuotaRecordService;
import com.epaylinks.efps.pas.common.PasConstants;
import com.epaylinks.efps.pas.txs.dao.TxsRefundPreOrderMapper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Date;
import java.util.List;

@Component
public class QuotaRefundQueryJob implements TaskJob {
    private static final Logger log = LoggerFactory.getLogger(QuotaRefundQueryJob.class);

    @Autowired
    AcctQuotaRecordMapper acctQuotaRecordMapper;

    @Autowired
    TxsRefundPreOrderMapper txsRefundPreOrderMapper;

    @Autowired
    AcctQuotaRecordService acctQuotaRecordService;


    @Override
    public void execute(TaskRequest request) throws Exception {
        int days = 30;
        if (StringUtils.isNotBlank(request.getJobParams())) {
            JobParam jobParam = JsonUtils.jsonToObj(request.getJobParams(), JobParam.class);
            if (jobParam.getDays() != null) {
                days = jobParam.getDays();
            }
        }

        Timex now = Timex.now();
        refundQuery(now.minus(Duration.ofDays(days)).toDate(), now.toDate());
    }

    public static class JobParam {
        private Integer days;

        public Integer getDays() {
            return days;
        }

        public void setDays(Integer days) {
            this.days = days;
        }
    }

    public void refundQuery(Date minCreateTime, Date maxCreateTime) {
        List<AcctQuotaRecord> records = acctQuotaRecordMapper.selectAllByFundTypeAndAuditStateAndCreateTimeBetween(
                PasConstants.AcctQuotaRechargeFundType.TRADE_RECHARGE.code,
                PasConstants.AcctQuotaAuditState.WAITING.code,
                minCreateTime,
                maxCreateTime
        );

        for (AcctQuotaRecord record : records) {
            try {
                refundQuery(record);
            } catch (Exception e) {
                log.error("异常", e);
            }
        }
    }

    public void refundQuery(AcctQuotaRecord record) throws Exception {
        long count = txsRefundPreOrderMapper.countByPayTransactionNoAndPayState(record.getTransactionNo(),
                "00");
        if (count > 0) {
            AcctQuotaAuditRequest request = new AcctQuotaAuditRequest();
            request.setId(record.getId());
            request.setAuditState(PasConstants.AcctQuotaAuditState.FAIL.code);
            request.setAuditComment("订单已退款");
            acctQuotaRecordService.auditRecord(request, 0L, UserType.PAS_USER.code);
        }
    }

}
