package com.epaylinks.efps.pas.pas.domain;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

public class ApplicationRecord {
    /**
     * 主键
     */
    private Long id;

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 进件使用的商户基本信息ID
     */
    private Long customerInfoId;

    /**
     * 进件目标上游合作机构
     */
    private String institutionCode;

    /**
     * 进件目标上游合作机构名称
     */
    private String institutionName;

    /**
     * 进件时使用的参数Json序列化后的结果
     */
    private String inputParams;

    /**
     * 进件处理状态:0-数据校验失败.1-成功.2-处理中.3-失败
     */
    private String state;

    /**
     * 进件处理结果描述
     */
    private String resultMsg;

    /**
     * 上游进件返回的商户号
     */
    private String institutionMerchCode;

    /**
     * 上游进件返回的商户交易秘钥
     */
    private String institutionMerchSecret;

    /**
     * 创建时间
     */
    @JsonFormat(shape= JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;

    /**
     * 修改时间
     */
    @JsonFormat(shape= JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public Long getCustomerInfoId() {
        return customerInfoId;
    }

    public void setCustomerInfoId(Long customerInfoId) {
        this.customerInfoId = customerInfoId;
    }

    public String getInstitutionCode() {
        return institutionCode;
    }

    public void setInstitutionCode(String institutionCode) {
        this.institutionCode = institutionCode;
    }

    public String getInputParams() {
        return inputParams;
    }

    public void setInputParams(String inputParams) {
        this.inputParams = inputParams;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getResultMsg() {
        return resultMsg;
    }

    public void setResultMsg(String resultMsg) {
        this.resultMsg = resultMsg;
    }

    public String getInstitutionMerchCode() {
        return institutionMerchCode;
    }

    public void setInstitutionMerchCode(String institutionMerchCode) {
        this.institutionMerchCode = institutionMerchCode;
    }

    public String getInstitutionMerchSecret() {
        return institutionMerchSecret;
    }

    public void setInstitutionMerchSecret(String institutionMerchSecret) {
        this.institutionMerchSecret = institutionMerchSecret;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getInstitutionName() {
        return institutionName;
    }

    public void setInstitutionName(String institutionName) {
        this.institutionName = institutionName;
    }
}