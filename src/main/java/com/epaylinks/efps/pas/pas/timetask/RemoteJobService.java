package com.epaylinks.efps.pas.pas.timetask;

import com.epaylinks.efps.common.timetask.TaskRequest;
import com.epaylinks.efps.pas.pas.domain.PasTimeTask;
import com.epaylinks.efps.pas.pas.domain.PasTimeTaskRecord;
import org.quartz.JobExecutionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Date;

@Service
public class RemoteJobService {
    private static Logger logger = LoggerFactory.getLogger(RemoteJobService.class);

    @Autowired
    @Qualifier("balancedRestTemplate")
    RestTemplate restTemplate;

    @Autowired
    private TaskService taskService;

    public void executeJob(JobExecutionContext context) {
        PasTimeTask task = taskService.prepare(context);
        if (task == null) {
            return;
        }
        executeJob(task);

    }

    public void executeJob(String jobName, Date fireTime) {
        PasTimeTask task = taskService.prepare(jobName, fireTime);
        if (task == null) {
            return;
        }
        executeJob(task);
    }

    private void executeJob(PasTimeTask task) {
        PasTimeTaskRecord record = task.getLastExecuteRecord();

        TaskRequest request = new TaskRequest();
        request.setJobBeanName(task.getRealBaenName());
        request.setJobParams(task.getJobData());
        request.setTaskId(record.getId());
        request.setFireTime(task.getLastFireTime());
        logger.info("调用远程任务[{}]ID[{}]", task.getJobName(), record.getId());

        String url = task.getIpAddres().startsWith("http") ?
                task.getIpAddres() :
                ("http://" + task.getIpAddres() + "/time-task");
        try {
            restTemplate.postForObject(url, request, String.class);
        } catch (Exception e) {
            record.setReturnMessage(e.getMessage());
            taskService.updateRecord(record);
        }
    }

    public Long reExecuteJob(String jobName) {
        PasTimeTask task = taskService.getReExecuteTask(jobName);
        executeJob(task);
        return task.getLastExecuteRecordId();
    }
}
