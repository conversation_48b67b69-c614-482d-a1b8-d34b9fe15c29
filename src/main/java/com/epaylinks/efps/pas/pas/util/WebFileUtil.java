package com.epaylinks.efps.pas.pas.util;
import com.epaylinks.efps.common.util.Base64;

import java.awt.image.BufferedImage;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;

import javax.imageio.ImageIO;

//@Service("webFileUtils")
public class WebFileUtil {

    private static final org.slf4j.Logger logger = org.slf4j.LoggerFactory.getLogger(WebFileUtil.class);

    /**
     * 根据url拿取file
     *
     * @param url
     * @param suffix
     *            文件后缀名
     * */
    public static File createFileByUrl(String url, String suffix) {
        byte[] byteFile = getImageFromNetByUrl(url);
        if (byteFile != null) {
            File file = getFileFromBytes(byteFile, suffix);
            return file;
        } else {
            logger.info("生成文件失败！");
            return null;
        }
    }

    /**
     * 根据地址获得数据的字节流
     *
     * @param strUrl
     *            网络连接地址
     * @return
     */
    private static byte[] getImageFromNetByUrl(String strUrl) {
        try {
            URL url = new URL(strUrl);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(5 * 1000);
            InputStream inStream = conn.getInputStream();// 通过输入流获取图片数据
            byte[] btImg = readInputStream(inStream);// 得到图片的二进制数据
            return btImg;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 从输入流中获取数据
     *
     * @param inStream
     *            输入流
     * @return
     * @throws Exception
     */
    private static byte[] readInputStream(InputStream inStream) throws Exception {
        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int len = 0;
        while ((len = inStream.read(buffer)) != -1) {
            outStream.write(buffer, 0, len);
        }
        inStream.close();
        return outStream.toByteArray();
    }

    // 创建临时文件
    public static File getFileFromBytes(byte[] b, String suffix) {
        BufferedOutputStream stream = null;
        File file = null;
        try {
            file = File.createTempFile("pattern", "." + suffix);
            FileOutputStream fstream = new FileOutputStream(file);
            stream = new BufferedOutputStream(fstream);
            stream.write(b);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (stream != null) {
                try {
                    stream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return file;
    }
    //-126
    public static File getFileFromBytes(byte[] b, String suffix,String path) {
        BufferedOutputStream stream = null;
        File file = null;
        try {
//            file = File.createTempFile(path+"pattern", "." + suffix);
            file = File.createTempFile(path+"pattern", "." + suffix,new File("d:/"));
            FileOutputStream fstream = new FileOutputStream(file);
            stream = new BufferedOutputStream(fstream);
            stream.write(b);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (stream != null) {
                try {
                    stream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return file;
    }

    public static void main(String[] args) {
        WebFileUtil.createFileByUrl("https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1508327007250&di=b7be3e44999983ec75ba54537287ff13&imgtype=0&src=http%3A%2F%2Fb.hiphotos.baidu.com%2Fimage%2Fpic%2Fitem%2Faa64034f78f0f736ce4ff5720055b319ebc4130b.jpg","jpg");
    }



    /**
         * 根据得到图片字节，获得图片后缀
         *
         * @param photoByte 图片字节
         * @return 图片后缀
         */
        public static String getFileExtendName(byte[] photoByte) {
            String strFileExtendName = ".jpg";
            if ((photoByte[0] == 71) && (photoByte[1] == 73) && (photoByte[2] == 70)
                    && (photoByte[3] == 56) && ((photoByte[4] == 55) || (photoByte[4] == 57))
                    && (photoByte[5] == 97)) {
                strFileExtendName = ".gif";
            } else if ((photoByte[6] == 74) && (photoByte[7] == 70) && (photoByte[8] == 73)
                    && (photoByte[9] == 70)) {
                strFileExtendName = ".jpg";
            } else if ((photoByte[0] == 66) && (photoByte[1] == 77)) {
                strFileExtendName = ".bmp";
            } else if ((photoByte[1] == 80) && (photoByte[2] == 78) && (photoByte[3] == 71)) {
                strFileExtendName = ".png";
            }
            return strFileExtendName;
        }

    //判断是否图片文件
    synchronized private static boolean isImageFromBase64(String base64Str) {
            boolean flag = false;
            try {
                BufferedImage bufImg = ImageIO.read(new ByteArrayInputStream(Base64.decode(base64Str)));
                if (null == bufImg) {
                    return flag;
                }
                flag = true;
            } catch (Exception e) {
                System.err.println(e.getMessage());
            }
            return flag;
        }


//    ImgUploadServiceImpl.java
  /*  public MultipartFile createImg(String url){
        try {
            // File转换成MutipartFile
            File file = WebFileUtils.createFileByUrl(url, "jpg");
            FileInputStream inputStream = new FileInputStream(file);
            MultipartFile multipartFile = new MockMultipartFile(file.getName(), inputStream);
            //注意这里面填啥，MultipartFile里面对应的参数就有啥，比如我只填了name，则
            //MultipartFile.getName()只能拿到name参数，但是originalFilename是空。
            return multipartFile;
        } catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
            return null;
        }
    }*/
}