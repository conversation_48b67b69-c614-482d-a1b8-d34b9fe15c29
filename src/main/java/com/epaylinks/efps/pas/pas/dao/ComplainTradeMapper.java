package com.epaylinks.efps.pas.pas.dao;

import com.epaylinks.efps.pas.pas.domain.ComplainCustVo;
import com.epaylinks.efps.pas.pas.domain.ComplainTrade;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;


@Mapper
public interface ComplainTradeMapper {
    /**
     * 获取主键ID
     * @return
     */
    Long selectIdFromSeq();

    boolean isComplainExist(@Param("orderNo") String orderNo,@Param("amount") String amount,
                            @Param("complainContent") String complainContent);

    int deleteByPrimaryKey(Long ctId);

    int insert(ComplainTrade record);

    int insertSelective(ComplainTrade record);

    ComplainTrade selectByPrimaryKey(Long ctId);

    int updateByPrimaryKeySelective(ComplainTrade record);

    int updateByPrimaryKey(ComplainTrade record);

    List<ComplainTrade> selectByPage(Map map);

    int selectCount(Map map);

    List<ComplainCustVo> selectCustByChannel(String channelMchId);

    List<String> selectCustomerNoByMchtNo(@Param("mchtNo") String mchtNo);

    List<String> selectCustomerNoByCustNo(@Param("custNo") String custNo);

    ComplainTrade selectByCust(String customerCode);
}