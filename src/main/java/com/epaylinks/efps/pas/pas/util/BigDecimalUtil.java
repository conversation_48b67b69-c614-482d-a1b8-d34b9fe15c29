package com.epaylinks.efps.pas.pas.util;

import java.math.BigDecimal;

/**
 * <AUTHOR> 5m
 * @Date : 2017/12/27 18:22
 * @Description :
 */
public class BigDecimalUtil {

    /**
     * 内部减法
     * @param v1 被减数
     * @param v2 减数
     * @return 两个参数的差
     */
    public static String subIn(String v1, String v2){
        BigDecimal b1 = new BigDecimal(v1);
        BigDecimal b2 = new BigDecimal(v2);
        return b1.subtract(b2).toString();
    }

    /**
     * 提供精确的乘法运算。
     * @param v1 被乘数
     * @param v2 乘数
     * @return 两个参数的积
     */
    public static String mul(String v1, String v2){
        BigDecimal b1 = new BigDecimal(v1);
        BigDecimal b2 = new BigDecimal(v2);
        return b1.multiply(b2).toString();
    }

    /**
     * 提供（相对）精确的除法运算。当发生除不尽的情况时，由scale参数指
     * 定精度，以后的数字四舍五入。
     * @param a 被除数
     * @param b 除数
     * @param scale 表示表示需要精确到小数点以后几位。
     * @return 两个参数的商
     */
    public static String div(String a, String b,int scale){
        BigDecimal b1 = new BigDecimal(a);
        BigDecimal b2 = new BigDecimal(b);
        return b1.divide(b2, scale, BigDecimal.ROUND_HALF_UP).toString();
    }

    public static void main(String[] args) {
        String op = "1234";
        System.out.println(op.matches("^([1-9]\\d*)|(0)$"));
    }
}
