package com.epaylinks.efps.pas.pas.dao;

import com.epaylinks.efps.pas.pas.domain.ApplicationRecord;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@Mapper
@Transactional
public interface ApplicationRecordMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ApplicationRecord record);

    int insertSelective(ApplicationRecord record);

    @Transactional(readOnly = true)
    ApplicationRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ApplicationRecord record);

    int updateByPrimaryKey(ApplicationRecord record);

    @Transactional(readOnly = true)
    List<ApplicationRecord> selectBySelective(ApplicationRecord record);

    @Transactional(readOnly = true)
    List<ApplicationRecord> selectByPage(Map map);
}