package com.epaylinks.efps.pas.pas.controller;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.business.CommonResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.pas.controller.response.LoginReportResponse;
import com.epaylinks.efps.pas.pas.service.LoginReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/loginReport")
@Api(value = "LoginReportController", description = "每日登录审计报告")
public class LoginReportController {
    @Autowired
    private LoginReportService loginReportService;

    @Autowired
    private CommonLogger logger;

    @GetMapping("/reportPage")
    @Logable(businessTag = "LoginReportController.reportPage",outputResult = false)
    @ApiOperation(value = "每日登录审计报告列表", httpMethod = "GET")
    public PageResult<LoginReportResponse> reportPage(@RequestParam(value = "startTime", required = false) String startTime,
                                                      @RequestParam(value = "endTime", required = false) String endTime,
                                                      @RequestParam(value = "loginAccount", required = false) String loginAccount,
                                                      @RequestParam(value = "userType",required = false) String userType,
                                                      @RequestParam(value = "reportState",required = false) String reportState,
                                                      @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                      @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                      @RequestHeader(value = "x-userid") Long userId) {
        PageResult page = new PageResult<>();
        try {
            int endRowNo = pageNum * pageSize;
            int beginRowNo = (pageNum - 1) * pageSize + 1;
            Map<String, Object> param = new HashMap<>();
            param.put("startTime", startTime);
            param.put("endTime", endTime);
            param.put("loginAccount", loginAccount);
            param.put("userType", userType);
            param.put("reportState",reportState);
            param.put("beginRowNo", beginRowNo);
            param.put("endRowNo", endRowNo);
            page = loginReportService.reportPage(param,userId);
            page.setCode(CommonResponse.SUCCEE);
        } catch (Exception e) {
            logger.printMessage("每日登录审计报告分页错误:" + e.getMessage());
            logger.printLog(e);
            if (e instanceof AppException) {
                page.setCode(((AppException) e).getErrorCode());
                page.setMessage(((AppException) e).getErrorMsg());
            } else {
                page.setCode(PasCode.SYSTEM_EXCEPTION.code);
                page.setMessage(PasCode.SYSTEM_EXCEPTION.message);
            }
        }
        return page;
    }

    @PostMapping("/handle")
    @ApiOperation(value = "异常处理", httpMethod = "POST")
    @Logable(businessTag = "LoginReportController.handle")
    public CommonOuterResponse handle(@RequestParam("reportId") Long reportId,
                                      @RequestParam(value = "exceptionHandle",required = false) String exceptionHandle,
                                      @RequestHeader("x-userid") Long userId) {
        return loginReportService.exceptionHandle(reportId,exceptionHandle,userId);
    }

    @PostMapping("/test")
    public void test() {
        try {
            loginReportService.saveBeforeDay();
        } catch (Exception e) {
            logger.printMessage("错误信息：" + e.getMessage());
            logger.printLog(e);
        }
    }
}
