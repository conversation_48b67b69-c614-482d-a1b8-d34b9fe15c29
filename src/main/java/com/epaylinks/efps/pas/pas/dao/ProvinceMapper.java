package com.epaylinks.efps.pas.pas.dao;

import com.epaylinks.efps.pas.pas.domain.Province;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ProvinceMapper {

    int deleteByPrimaryKey(String provCd);

    int insert(Province record);

    int insertSelective(Province record);

    Province selectByPrimaryKey(String provCd);

    int updateByPrimaryKeySelective(Province record);

    int updateByPrimaryKey(Province record);

    List<Province> selectAll();
    
    
    /**
     * 查询省份编码
     * @param provinceName
     * @return
     */
    String queryProviceCodeByName(@Param("provinceName") String provinceName);
    
  
}