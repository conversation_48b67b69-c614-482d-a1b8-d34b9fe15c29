package com.epaylinks.efps.pas.pas.service;

import com.epaylinks.efps.common.business.CommonResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.oplog.OpLogHandle;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.pas.acl.service.UserService;
import com.epaylinks.efps.pas.clr.domain.HuikuanRequest;
import com.epaylinks.efps.pas.clr.response.HuikuanResponse;
import com.epaylinks.efps.pas.clr.service.impl.WithdrawCountServiceImpl;
import com.epaylinks.efps.pas.common.PageResult;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.common.PasConstants;
import com.epaylinks.efps.pas.mch.client.AcsClient;
import com.epaylinks.efps.pas.mch.client.model.QuotaChangeResponse;
import com.epaylinks.efps.pas.mch.client.model.ResetWarnNoticeKey;
import com.epaylinks.efps.pas.pas.common.PasConstant;
import com.epaylinks.efps.pas.pas.controller.response.QueryQuotaResponse;
import com.epaylinks.efps.pas.pas.controller.response.UnionQuotaPayMentsResp;
import com.epaylinks.efps.pas.pas.dao.UnionQuotaPayMentsMapper;
import com.epaylinks.efps.pas.pas.dao.UnionQuotaRecordMapper;
import com.epaylinks.efps.pas.pas.domain.UnionQuotaPayMents;
import com.epaylinks.efps.pas.pas.domain.UnionQuotaRecord;
import org.apache.commons.lang.time.DateFormatUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

@Service
public class UnionQuotaService {

    /**
     * 订单号前缀（银联额度调整）
     */
    private static final String TXN_NO_PRE = "YLED";

    @Autowired
    private AcsClient acsClient;

    @Autowired
    private UnionQuotaRecordMapper unionQuotaRecordMapper;
    @Autowired
    private UnionQuotaPayMentsMapper unionQuotaPayMentsMapper;
    @Autowired
    private UserService userService;
    @Autowired
    private SequenceService sequenceService;
    @Autowired
    private WithdrawCountServiceImpl withdrawCountService;
    @Autowired
    private UnionQuotaService self;

    @Logable(businessTag = "changeQuota")
    public void changeQuota(String trxCategory, Long amount, Long userId) {
        if (PasConstant.UnionFundsettQuotaChangeType.ADD.code.equals(trxCategory)) {
            acsClient.resetWarnNotice(ResetWarnNoticeKey.UNION_AVLB_BAL);
        }
        // 初始化订单
        UnionQuotaRecord record = new UnionQuotaRecord();
        record.setAmount(amount);
        record.setTrxCategory(trxCategory);
        record.setUserId(userId);
        record.setState("0");
        Long transactionNo = sequenceService.nextValue("pas_union_quota");
        String random = String.format("%06d", transactionNo);
        random = random.substring(random.length() - 6, random.length());
        String dateString = DateFormatUtils.format(new Date(), "yyyyMMdd");
        String trxId = TXN_NO_PRE + dateString + random;
        record.setTransactionNo(trxId);
        record.setId(transactionNo);
        record.setCreateTime(new Date());
        unionQuotaRecordMapper.insert(record);
        // 记录日志
        OpLogHandle.setOpContent(trxId);
        // 提交给ACS(即上游)
        QuotaChangeResponse res = new QuotaChangeResponse();
        try {
            res = acsClient.changeQuota(record.getTransactionNo(), record.getAmount(), record.getTrxCategory());
        } catch (Exception e) {
            res.setState("01");
        }
        record.setChannelTradeNo(record.getTransactionNo());
        record.setChannelReturnCode(res.getChannelRespCode());
        record.setState("1");
        record.setChannelState(res.getState());
        record.setChannelReturnDate(new Date());
        record.setChannelReturnMessage(res.getChannelRespMsg());
        unionQuotaRecordMapper.updateByPrimaryKeySelective(record);
    }

    /**
     * 银总资金结算额度调整通知的处理
     * @param transactionNo
     * @param channelState
     * @param channelRespCode
     * @param channelRespMsg
     */
    @Logable(businessTag = "updateUnionFundsettQuotaChange")
    public void updateUnionFundsettQuotaChange(String transactionNo, String channelState, String channelRespCode, String channelRespMsg) {
        UnionQuotaRecord record = unionQuotaRecordMapper.selectByTransactionNo(transactionNo);
        if (record != null) {
            record.setChannelTradeNo(transactionNo);
            record.setChannelReturnCode(channelRespCode);
            record.setState("1");
            record.setChannelState(channelState);
            record.setChannelReturnDate(new Date());
            record.setChannelReturnMessage(channelRespMsg);
            unionQuotaRecordMapper.updateByPrimaryKeySelective(record);
        }
    }

    @Logable(businessTag = "pageQuery")
    public PageResult<UnionQuotaRecord> pageQuery(Map map) {
        int total = unionQuotaRecordMapper.selectByParam(map);
        List<UnionQuotaRecord> list = unionQuotaRecordMapper.selectByParamByPage(map);
        PageResult<UnionQuotaRecord> pagingResult = new PageResult<>();
        pagingResult.setRows(list);
        pagingResult.setTotal(total);
        return pagingResult;
    }

    @Logable(businessTag = "querUnionQuota")
    public QueryQuotaResponse querUnionQuota() {
        return acsClient.queryQuota();
    }

    /**
     * 回款新增
     * @param map
     */
    @Logable(businessTag = "huikuanCreate")
    public CommonResponse payMentsCreate(Map map) {
        CommonResponse response = new CommonResponse();
        String resultCode = null;
        String amount =(String)map.get("amount");
        String insSeq = (String)map.get("insSeq");
        String cfdPurpose = (String)map.get("cfdPurpose");
        String remark = (String)map.get("remark");
        String curUserId = (String)map.get("curUserId");
        HuikuanRequest request = new HuikuanRequest();
        String random = String.format("%06d", sequenceService.nextValue("CLR_bkTrxNo"));
        String dateString = DateFormatUtils.format(new Date(), "yyyyMMddHHmmss");
        String transactionNo = "HK" + dateString + random.substring(1,random.length());
        request.setTransactionNo(transactionNo);
        Double anew = Double.parseDouble(amount);
        Long Amount = BigDecimal.valueOf(anew).multiply(new BigDecimal(100)).longValue();
        request.setTxnAmt(Amount);
        request.setInsSeq(insSeq);
        request.setRemark(remark);
        request.setCurUserId(curUserId);
        request.setCfdPurpose(cfdPurpose);
        OpLogHandle.setOpContent(transactionNo);
        HuikuanResponse hkResponse = new HuikuanResponse();
        try {
            hkResponse = acsClient.huikuan(request);
        } catch (Exception e) {
            if (e instanceof AppException) {
                throw e;
            } else {
                throw new AppException(PasCode.CLR_CONNECT_ERROR.code, PasCode.CLR_CONNECT_ERROR.message);
            }
        }
        if(hkResponse != null){
            resultCode = hkResponse.getState();//00：成功，01：失败，02：处理中
        }else{
            response.setCode("00001");
            response.setResult("失败");
            response.setMessage("回款新增失败,clr调用上游返回空");
            return response;
        }
        if(resultCode != null){
            if(resultCode.equals("01")){
                response.setCode(CommonResponse.SUCCEE);
                response.setResult("失败");
                response.setMessage("回款新增失败");
            }
            if(resultCode.equals("02")){
                response.setCode(CommonResponse.SUCCEE);
                response.setResult("处理中");
                response.setMessage("回款新增处理中");
            }
            if(resultCode.equals("00")){
                response.setCode(CommonResponse.SUCCEE);
                response.setResult("成功");
                response.setMessage("回款新增成功");
            }
        }
        return response;
    }

    /**
     * 回款列表
     * @param map
     * @return
     */
    @Logable(businessTag = "queryHuikuanList")
    public com.epaylinks.efps.common.util.page.PageResult<UnionQuotaPayMentsResp> queryPayMentsList(Map map) {
        com.epaylinks.efps.common.util.page.PageResult<UnionQuotaPayMentsResp> pageResult = new com.epaylinks.efps.common.util.page.PageResult<>();
        List<UnionQuotaPayMents> unionQuotaPayMentsList = new ArrayList<>();
        List<UnionQuotaPayMentsResp> unionQuotaPayMentsRespList = new ArrayList<>();
        int total = 0;
        boolean download = (boolean) map.get("download");
        try {
            if (download) {
                unionQuotaPayMentsList = unionQuotaPayMentsMapper.notPageQueryList(map);
            } else {
                unionQuotaPayMentsList = unionQuotaPayMentsMapper.queryList(map);
                total = unionQuotaPayMentsMapper.countQueryList(map);
            }
        } catch (Exception e) {
            if (e instanceof AppException) {
                throw e;
            } else {
                throw new AppException(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
            }
        }
        Map<Long, String> mapOfUserIdAndName = self.getMapOfUserIdAndName(unionQuotaPayMentsList);
        for (UnionQuotaPayMents payMents : unionQuotaPayMentsList) {
            UnionQuotaPayMentsResp unionQuotaPayMentsResp = new UnionQuotaPayMentsResp();
            BeanUtils.copyProperties(payMents,unionQuotaPayMentsResp);
            unionQuotaPayMentsResp.setInsSeq(payMents.getInsSeq()+"头寸");
            unionQuotaPayMentsResp.setState(changeState(payMents.getState()));
            unionQuotaPayMentsResp.setCfdPurpose(PasConstants.CfdPurpose.getCommentByCode(payMents.getCfdPurpose()));
            unionQuotaPayMentsResp.setAmount(longToBigDecimal(payMents.getAmount()));
            unionQuotaPayMentsResp.setUserId(mapOfUserIdAndName.get(Long.valueOf(payMents.getUserId())));
            unionQuotaPayMentsRespList.add(unionQuotaPayMentsResp);
        }
        pageResult.setRows(unionQuotaPayMentsRespList);
        pageResult.setTotal(total);
        pageResult.setCode(com.epaylinks.efps.common.util.page.PageResult.SUCCEE);
        pageResult.setMessage("查询成功");
        return pageResult;
    }

    /**
     * 关联查询PAS_USER，得到UserIdAndName的映射
     */
    @Logable(businessTag = "getMapOfUserIdAndName")
    public Map<Long, String> getMapOfUserIdAndName(List<UnionQuotaPayMents> unionQuotaPayMentsList) {
        Map<Long, String> mapOfUserIdAndName = new HashMap<>();
        // 汇总所有userId并查询
        List<Long> userIdList = new ArrayList<>();
        for (UnionQuotaPayMents r : unionQuotaPayMentsList) {
            Long userId = Long.valueOf(r.getUserId());
            if (!userIdList.contains(userId)) {
                userIdList.add(userId);
            }
        }
        if (userIdList != null && !userIdList.isEmpty()) {
            mapOfUserIdAndName = userService.queryMapOfUserIdAndName(userIdList);
        }
        return mapOfUserIdAndName;
    }

    /**
     * Long转BigDecimal
     */
    public BigDecimal longToBigDecimal(Long amount){
        BigDecimal result = new BigDecimal(0);
        if(amount == null){
            return result;
        }
        BigDecimal totalFee = new BigDecimal(amount);
        BigDecimal d100 = new BigDecimal(100);
        result = totalFee.divide(d100,2,2);//小数点2位
        return result;
    }

    /**
     * 状态转换
     */
    public String changeState(String state){
        String str = "";
        if(state == null || state.equals("")){
            return str;
        }
        if(state.equals("00")){
            return "成功";
        }else if(state.equals("01")){
            return "失败";
        }else if(state.equals("02")){
            return "上游处理中";
        }else if(state.equals("03")){
            return "初始化";
        }else{
            return "";
        }
    }
}
