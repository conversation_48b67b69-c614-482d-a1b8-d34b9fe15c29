package com.epaylinks.efps.pas.pas.controller;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.business.pay.request.UserType;
import com.epaylinks.efps.common.datadownload.annotation.DownloadAble;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.util.BeanUtil;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.common.PasConstants;
import com.epaylinks.efps.pas.pas.common.LogService;
import com.epaylinks.efps.pas.pas.model.BkCardBin;
import com.epaylinks.efps.pas.pas.service.UnionCardBinFtpService;
import com.epaylinks.efps.pas.pas.service.impl.BkCardBinServiceImpl;
import com.epaylinks.efps.pas.pas.service.impl.UnionCardBinFtpServiceImpl;
import com.epaylinks.efps.pas.pas.vo.BkCardBinRequest;
import com.epaylinks.efps.pas.pas.vo.UnionCardBinFtpVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@Api(value = "BkCardBinController", description = "卡bin管理")
@RequestMapping("/bk/cardBin")
public class BkCardBinController {

    @Autowired
    private BkCardBinServiceImpl bkCardBinService;

    @Autowired
    private UnionCardBinFtpServiceImpl unionCardBinFtpService;

    @Autowired
    private CommonLogger logger;

    @GetMapping("/queryList")
    @Logable(businessTag = "BkCardBinController.queryList",outputResult = false)
    @Exceptionable
    @ApiOperation(value = "卡bin管理列表", notes = "卡bin管理列表", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "beginTime", value = "开始时间yyyyMMddHHmmss", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "结束时间yyyyMMddHHmmss", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "issueBankNo", value = "机构代码", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "issueBankName", value = "发卡行名称", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "cardNoRange", value = "发卡行标识取值", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "cardType", value = "卡种", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "flag", value = "状态：0停用，1启用", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "cardName", value = "卡名", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "cardNoLen", value = "主帐号长度", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "parentType", value = "卡表父类：1路由类卡表，2业务类卡表", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sonType", value = "卡表子类：银联标准卡表，ALLBIN；非标卡卡表，NONBIN；业务卡表，YWBIN；转账卡表，TFRBIN；农民工卡表，NMGBIN；单位结算卡卡表，DWBIN；境外发行银联卡卡表，OCBIN；",
                    required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "loadFrom", value = "来源：1-银联、2-网联、3-连通", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageNum", value = "查询开始页", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "查询的页面大小不需要分页则把此值填大一点", required = true, dataType = "int", paramType = "query")
    })
    @DownloadAble
    public PageResult<BkCardBin> queryList(
            @RequestHeader(value = "x-user-type", required = false) String userType,
            @RequestHeader(value = "x-userid", required = false) String userId,
            @ApiIgnore BkCardBinRequest request,
            @RequestParam(value = "download", required = false) Boolean download,
            @RequestParam(value = "fileName", required = false) String fileName,
            @RequestParam(value = "type", required = false) String type) {
        PageResult<BkCardBin> response = new PageResult<>();
        try {
            //该接口只支持运营门户
//            if (!UserType.PAS_USER.code.equals(userType)) {
//                throw new AppException(PasCode.CUSTOMER_TYPE_INVALID.code,"该接口只支持运营门户使用");
//            }
            //Map<String, Object> map = BeanUtil.transBean2Map(request);
            //map.entrySet().removeIf(x -> x.getValue() == null || x.getValue() == "");
            Map<String, Object> map = new HashMap<>();
            if(StringUtils.isNotBlank(request.getBeginTime())){
                map.put("beginTime",request.getBeginTime());
            }
            if(StringUtils.isNotBlank(request.getEndTime())){
                map.put("endTime",request.getEndTime());
            }
            if(StringUtils.isNotBlank(request.getIssueBankNo())){
                map.put("issueBankNo",request.getIssueBankNo());
            }
            if(StringUtils.isNotBlank(request.getIssueBankName())){
                map.put("issueBankName",request.getIssueBankName());
            }
            if(StringUtils.isNotBlank(request.getCardNoRange())){
                map.put("cardNoRange",request.getCardNoRange());
            }
            if(StringUtils.isNotBlank(request.getCardType())){
                map.put("cardType",request.getCardType());
            }
            if(StringUtils.isNotBlank(request.getFlag())){
                map.put("flag",request.getFlag());
            }
            if(StringUtils.isNotBlank(request.getCardName())){
                map.put("cardName",request.getCardName());
            }
            if(StringUtils.isNotBlank(request.getCardNoLen())){
                map.put("cardNoLen",request.getCardNoLen());
            }
            if(StringUtils.isNotBlank(request.getParentType())){
                map.put("parentType",request.getParentType());
            }
            if(StringUtils.isNotBlank(request.getSonType())){
                map.put("sonType",request.getSonType());
            }
            if(StringUtils.isNotBlank(request.getLoadFrom())){
                map.put("loadFrom",request.getLoadFrom());
            }
            int total = bkCardBinService.queryCount(map);
            response.setTotal(total);
            if (download != null && download) {
                if(StringUtils.isBlank(request.getParentType()) || StringUtils.isBlank(request.getSonType())){
                    if(total>1000){
                        throw new AppException(PasCode.DATA_NOT_EXIST.code,"请选择卡表父类和子类，避免导出数据过大");
                    }
                }
                request.setPageSize(total);
                request.setPageNum(1);
            }
            int endRowNo = request.getPageNum() * request.getPageSize();
            int beginRowNo = (request.getPageNum() - 1) * request.getPageSize() + 1;
            map.put("beginRowNo", beginRowNo);
            map.put("endRowNo", endRowNo);
            response.setRows(bkCardBinService.bankPageQuery(map));
            response.setCode(Constants.detailReturnCode.RETURN_SUCCESS.code);
            return response;
        } catch (Exception e) {
            logger.printMessage("查询列表异常："+e.getMessage());
            logger.printLog(e);
            if (e instanceof AppException) {
                response.setCode(((AppException) e).getErrorCode());
                response.setMessage(((AppException) e).getErrorMsg());
            } else {
                response.setCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setMessage(PasCode.SYSTEM_EXCEPTION.message);
            }
            return response;
        }
    }

    @RequestMapping(value = "/queryById", method = RequestMethod.GET)
    @Logable(businessTag = "BkCardBinController.queryById",outputResult = false)
    @Exceptionable
    @ApiOperation(value = "卡bin详情", notes = "卡bin详情", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "binId", value = "卡bin的主键Id", required = false, dataType = "String", paramType = "query"),
    })
    public CommonOuterResponse<BkCardBin> queryById(
            @RequestHeader(value = "x-user-type", required = false) String userType,
            @RequestHeader(value = "x-userid", required = false) String userId,
            @RequestParam(value = "binId") String binId) {
        CommonOuterResponse<BkCardBin> response = new CommonOuterResponse<>();
        try {
            //该接口只支持运营门户
            if (!UserType.PAS_USER.code.equals(userType)) {
                throw new AppException(PasCode.CUSTOMER_TYPE_INVALID.code,"该接口只支持运营门户使用");
            }
            if(StringUtils.isBlank(binId)){
                throw new AppException(PasCode.DATA_NOT_EXIST.code,"缺少卡Bin主键参数");
            }
            BkCardBin cardBin = bkCardBinService.queryById(binId);
            response.setData(cardBin);
            return response;
        } catch (Exception e) {
            logger.printMessage("查询详情异常："+e.getMessage());
            logger.printLog(e);
            if (e instanceof AppException) {
                return CommonOuterResponse.fail(((AppException) e).getErrorCode(), ((AppException) e).getErrorMsg());
            } else {
                return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
            }
        }
    }

    @RequestMapping(value = "/saveCardBin", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @Exceptionable
    @ApiOperation(value = "保存银行信息", notes = "保存银行信息", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "主键id（修改时必传）", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "issueBankNo", value = "机构代码", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "issueBankName", value = "发卡行名称", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "cardName", value = "卡名", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "issueBankAccount", value = "主帐号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "cardNoLen", value = "主帐号长度", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "cardNoRange", value = "发卡行标识取值", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "cardType", value = "卡种", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "loadFrom", value = "来源：1-银联、2-网联、3-连通", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "method", value = "请求方法：add新增，edit修改", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "unionBrand", value = "品牌分类：1银联品牌，2非银联品牌，3美国运通卡，4非美国运通卡", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "parentType", value = "卡表父类：1路由类卡表，2业务类卡表", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sonType", value = "卡表子类：银联标准卡表，ALLBIN；非标卡卡表，NONBIN；业务卡表，YWBIN；转账卡表，TFRBIN；农民工卡表，NMGBIN；单位结算卡卡表，DWBIN；境外发行银联卡卡表，OCBIN；",
                    required = false, dataType = "String", paramType = "query"),
    })
    @Logable(businessTag = "BkCardBinController.saveCardBin")
    public CommonOuterResponse saveCardBin(
            @RequestHeader(value = "x-user-type", required = false) String userType,
            @RequestHeader(value = "x-userid", required = false) String userId,
            @ApiIgnore BkCardBin bkCardBin,
            @RequestParam(value = "method") String method
    ){
        try {
            //该接口只支持运营门户
            if (!UserType.PAS_USER.code.equals(userType)) {
                throw new AppException(PasCode.CUSTOMER_TYPE_INVALID.code,"该接口只支持运营门户使用");
            }
            if(UnionCardBinFtpService.PARENT_TYPE_LY.equals(bkCardBin.getParentType()) &&
                    (!UnionCardBinFtpService.ALLBIN.equals(bkCardBin.getSonType()) && !UnionCardBinFtpService.NONBIN.equals(bkCardBin.getSonType()))){
                throw new AppException(PasCode.DATA_NOT_EXIST.code,"路由类卡表父类只能选择标准卡表和非标卡表子类");
            }
            if(UnionCardBinFtpService.PARENT_TYPE_YW.equals(bkCardBin.getParentType()) &&
                    (UnionCardBinFtpService.ALLBIN.equals(bkCardBin.getSonType()) || UnionCardBinFtpService.NONBIN.equals(bkCardBin.getSonType()))){
                throw new AppException(PasCode.DATA_NOT_EXIST.code,"业务类卡表父类只能选择业务卡表、转账卡表、农民工卡表、单位卡表子类");
            }
            if(bkCardBin!=null && !StringUtils.isBlank(bkCardBin.getCardNoRange())){
                bkCardBin.setCardNoRangeLen(new Long(bkCardBin.getCardNoRange().length()));
            }
            bkCardBin.setOperatorId(bkCardBinService.getOpMan(userId));
            if(StringUtils.isBlank(bkCardBin.getLoadFrom())){
                bkCardBin.setLoadFrom(PasConstants.CardBinLoadFrom.UN.code);
            }
            bkCardBinService.saveBkCardBin(bkCardBin,method);
            return CommonOuterResponse.success();
        } catch (Exception e) {
            logger.printMessage("保存卡Bin异常："+e.getMessage());
            logger.printLog(e);
            if (e instanceof AppException) {
                return CommonOuterResponse.fail(((AppException) e).getErrorCode(), ((AppException) e).getErrorMsg());
            } else {
                return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
            }
        }
    }


    @RequestMapping(value = "/enableBkCardBin", method = RequestMethod.POST)
    @Exceptionable
    @ApiOperation(value = "卡bin信息启用/停用", notes = "银行信息启用/停用", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "卡bin表id（批量逗号分隔）", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "flag", value = "是否启用 0：停用 1：启用", required = true, dataType = "String", paramType = "query"),
    })
    @Logable(businessTag = "BkCardBinController.enableBkCardBin")
    public CommonOuterResponse enableBkCardBin(
            @RequestHeader(value = "x-user-type", required = false) String userType,
            @RequestHeader(value = "x-userid", required = false) String userId,
            @RequestParam(value = "ids") String ids,
            @RequestParam(value = "flag") String flag
    ){
        try {
            //该接口只支持运营门户
            if (!UserType.PAS_USER.code.equals(userType)) {
                throw new AppException(PasCode.CUSTOMER_TYPE_INVALID.code,"该接口只支持运营门户使用");
            }
            for (String id : ids.split(",")){
                bkCardBinService.enableBkCardBin(Long.valueOf(id),flag,userId);
            }
            return CommonOuterResponse.success();
        } catch (Exception e) {
            logger.printMessage("卡bin信息启用停用异常："+e.getMessage());
            logger.printLog(e);
            if (e instanceof AppException) {
                return CommonOuterResponse.fail(((AppException) e).getErrorCode(), ((AppException) e).getErrorMsg());
            } else {
                return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
            }
        }
    }

    @GetMapping("/loadCardBin")
    @Logable(businessTag = "BkCardBinController.loadCardBin")
    @Exceptionable
    @ApiOperation(value = "加载指定日期的卡bin", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "inputDate", value = "指定日期，yyyyMMdd", dataType = "String", paramType = "query")
    })
    public CommonOuterResponse loadCardBin(@RequestParam(value = "inputDate", required = false) String inputDate) {
        CommonOuterResponse response = new CommonOuterResponse<>();
        try {
            unionCardBinFtpService.loadCardBinForJob(inputDate);   //"20220625"
            response.setData("处理完成");
        } catch (Exception e) {
            logger.printMessage("加载指定日期的卡bin异常："+e.getMessage());
            logger.printLog(e);
            response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
            response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            e.printStackTrace();
        }
        return response;
    }

    @GetMapping("/loadCardBinFile")
    @Logable(businessTag = "BkCardBinController.loadCardBinFile")
    @Exceptionable
    @ApiOperation(value = "加载指定文件的卡bin", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fileName", value = "文件名称", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "filePath", value = "文件路径", dataType = "String", paramType = "query")
    })
    public CommonOuterResponse loadCardBinFile(@RequestParam(value = "fileName", required = false) String fileName,
                                               @RequestParam(value = "filePath", required = false) String filePath) {
        CommonOuterResponse response = new CommonOuterResponse<>();
        try {
            List<UnionCardBinFtpVo> ftpFileList = new ArrayList<>();
            UnionCardBinFtpVo ftpVo = new UnionCardBinFtpVo();
            ftpVo.setFileName(fileName);
            ftpVo.setFilePath(filePath);
            ftpFileList.add(ftpVo);
            unionCardBinFtpService.loadCardBinIntoSystem(ftpFileList,PasConstants.CardBinLoadFrom.EXP.code);   //"20220625"
            response.setData("处理完成");
        } catch (Exception e) {
            logger.printMessage("加载指定文件的卡bin异常："+e.getMessage());
            logger.printLog(e);
            response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
            response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            e.printStackTrace();
        }
        return response;
    }

    @RequestMapping(value = "/queryByCardNo", method = RequestMethod.POST)
    @Exceptionable
    @ApiOperation(value = "通过银行卡号查询卡bin信息", notes = "通过银行卡号查询卡bin信息", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "cardNo", value = "银行卡号", required = true, dataType = "String", paramType = "query")
    })
    @Logable(businessTag = "BkCardBinController.queryByCardNo")
    public CommonOuterResponse<List<BkCardBin>> queryByCardNo(
            @RequestParam(value = "cardNo") String cardNo
    ){
        try {
            if(StringUtils.isBlank(cardNo)){
                throw new AppException(PasCode.DATA_NOT_EXIST.code,"缺少参数");
            }
            return CommonOuterResponse.success(bkCardBinService.queryByCardNo(cardNo));
        } catch (Exception e) {
            logger.printMessage("通过银行卡号查询卡bin信息异常："+e.getMessage());
            logger.printLog(e);
            if (e instanceof AppException) {
                return CommonOuterResponse.fail(((AppException) e).getErrorCode(), ((AppException) e).getErrorMsg());
            } else {
                return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
            }
        }
    }
}
