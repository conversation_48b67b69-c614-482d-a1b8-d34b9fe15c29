package com.epaylinks.efps.pas.pas.dao;

import com.epaylinks.efps.pas.pas.model.BkBranch;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface BkBranchMapper {
    int deleteByPrimaryKey(String lbnkNo);

    int insert(BkBranch record);

    int insertSelective(BkBranch record);

    BkBranch selectByPrimaryKey(String lbnkNo);

    int updateByPrimaryKeySelective(BkBranch record);

    int updateByPrimaryKey(BkBranch record);


    //联行号查询
    List<BkBranch> pageQueryBkBranch(
            @Param("beginRowNo") Integer beginRowNo,
            @Param("endRowNo") Integer endRowNo,
            @Param("lbnkNo") String lbnkNo,
            @Param("lbnkNm") String lbnkNm,
            @Param("lbnkCd") String lbnkCd,
            @Param("corpOrg") String corpOrg,
            @Param("provCd") String provCd,
            @Param("cityCd") String cityCd
    );

    Integer countPageQueryBkBranch(
            @Param("beginRowNo") Integer beginRowNo,
            @Param("endRowNo") Integer endRowNo,
            @Param("lbnkNo") String lbnkNo,
            @Param("lbnkNm") String lbnkNm,
            @Param("lbnkCd") String lbnkCd,
            @Param("corpOrg") String corpOrg,
            @Param("provCd") String provCd,
            @Param("cityCd") String cityCd
    );

    List<BkBranch> queryBkBranchByOptions(@Param("provinceCode") String provinceCode, @Param("cityCode") String cityCode,
                                          @Param("lbnkCd") String lbnkCd, @Param("bankBranchName") String bankBranchName,
                                          @Param("bankBranchNameList")List<String> bankBranchNameList,
                                          @Param("bankBranchNo") String bankBranchNo);

    List<BkBranch> selectByPage(Map map);

    int selectCount(Map map);

    List<BkBranch> selectByLbnkCd(@Param("lbnkCd") String bankId);
}