package com.epaylinks.efps.pas.pas.controller;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.business.pay.request.UserType;
import com.epaylinks.efps.common.datadownload.annotation.DownloadAble;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.pas.domain.Bank;
import com.epaylinks.efps.pas.pas.service.impl.BankServiceImpl;
import com.epaylinks.efps.pas.pas.vo.BankDataRelevance;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@RestController
@Api(value = "BankController", description = "银行管理")
@RequestMapping("/bank")
public class BankController {
    @Autowired
    private BankServiceImpl bankService;

    @RequestMapping(value = "/queryBank", method = RequestMethod.GET)
    @Logable(businessTag = "BankController.queryBank",outputResult = false)
    @Exceptionable
    @Validatable
    @ApiOperation(value = "银行管理列表", notes = "银行管理列表", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "beginTime", value = "开始时间yyyyMMddHHmmss", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "结束时间yyyyMMddHHmmss", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "updateBeginTime", value = "更新时间开始时间yyyyMMddHHmmss", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "updateEndTime", value = "更新时间结束时间yyyyMMddHHmmss", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "bankId", value = "银行ID", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "bankName", value = "银行名称", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageNum", value = "查询开始页", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "查询的页面大小不需要分页则把此值填大一点", required = true, dataType = "int", paramType = "query")
    })
    @DownloadAble
    public PageResult<Bank> queryBankInfo(
            @RequestHeader(value = "x-user-type", required = false) String userType,
            @RequestHeader(value = "x-userid", required = false) String userId,
            @RequestHeader(value = "x-customer-code", required = false) String customerCodeHead,
            @RequestParam(value = "fileSource", required = false) String fileSource,
            @RequestParam(value = "bankId", required = false) String bankId,
            @RequestParam(value = "bankName", required = false) String bankName,
            @RequestParam(value = "beginTime", required = false) String beginTime,
            @RequestParam(value = "endTime", required = false) String endTime,
            @RequestParam(value = "updateBeginTime", required = false) String updateBeginTime,
            @RequestParam(value = "updateEndTime", required = false) String updateEndTime,
            @RequestParam(value = "pageNum", required = true) Integer pageNum,
            @RequestParam(value = "pageSize", required = true) Integer pageSize,
            @RequestParam(value = "download", required = false) Boolean download,
            @RequestParam(value = "fileName", required = false) String fileName,
            @RequestParam(value = "type", required = false) String type) {
        PageResult<Bank> response = new PageResult<>();
        //该接口只支持运营门户
        if (!UserType.PAS_USER.code.equals(userType)) {
            throw new AppException(PasCode.CUSTOMER_TYPE_INVALID.code);
        }
        try {
            Map map = new HashMap();
            map.put("bankId", bankId);
            map.put("bankName", bankName);
            if (StringUtils.isNotBlank(beginTime)) {
                map.put("beginTime", DateUtils.parseDate(beginTime, "yyyyMMddHHmmss"));
            }
            if (StringUtils.isNotBlank(endTime)) {
                //+1S，原因是ORACLE中的时间精度是到毫秒的，所以查询范围应该为小于次日0点0分0面
                Date date = DateUtils.parseDate(endTime, "yyyyMMddHHmmss");
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(date);
                calendar.add(Calendar.SECOND, 1);
                date = calendar.getTime();
                //判断结束时间是否大于当前时间。如果大于则使用当前时间作为结束时间。
                //避免因结束时间大于当前时间导致数据排序不准问题
                Date now = new Date();
                if(com.epaylinks.efps.common.util.DateUtils.compareTo(date, now) > 0){
                    map.put("endTime", now);
                }else {
                    map.put("endTime", date);
                }
            }

            if (StringUtils.isNotBlank(updateBeginTime)) {
                map.put("updateBeginTime", DateUtils.parseDate(updateBeginTime, "yyyyMMddHHmmss"));
            }
            if (StringUtils.isNotBlank(updateEndTime)) {
                //+1S，原因是ORACLE中的时间精度是到毫秒的，所以查询范围应该为小于次日0点0分0面
                Date date = DateUtils.parseDate(updateEndTime, "yyyyMMddHHmmss");
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(date);
                calendar.add(Calendar.SECOND, 1);
                date = calendar.getTime();
                //判断结束时间是否大于当前时间。如果大于则使用当前时间作为结束时间。
                //避免因结束时间大于当前时间导致数据排序不准问题
                Date now = new Date();
                if(com.epaylinks.efps.common.util.DateUtils.compareTo(date, now) > 0){
                    map.put("updateEndTime", now);
                }else {
                    map.put("updateEndTime", date);
                }
            }
            int total = bankService.queryCount(map);
            response.setTotal(total);
            if (download != null && download) {
                pageSize = total;
                pageNum = 1;
            }
            int endRowNo = pageNum * pageSize;
            int beginRowNo = (pageNum - 1) * pageSize + 1;
            map.put("beginRowNo", beginRowNo);
            map.put("endRowNo", endRowNo);
            response.setRows(bankService.bankPageQuery(map));
            response.setResult(Constants.detailReturnCode.RETURN_SUCCESS.code);
            response.setCode(Constants.detailReturnCode.RETURN_SUCCESS.code);
            return response;
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof AppException) {
                response.setCode(((AppException) e).getErrorCode());
                response.setMessage(((AppException) e).getErrorMsg());
            } else {
                response.setCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setMessage(PasCode.SYSTEM_EXCEPTION.message);
            }
            return response;
        }
    }

    /**
     * 保存银行信息
     */
    @RequestMapping(value = "/saveBankInfo", method = RequestMethod.POST)
    @Exceptionable
    @Validatable
    @ApiOperation(value = "保存银行信息", notes = "保存银行信息", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "bankId", value = "银行ID", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "bankName", value = "银行名称", required = true, dataType = "String", paramType = "query"),
    })
    @Logable(businessTag = "BankController.saveBankInfo")
    public CommonOuterResponse saveBankInfo(
            @RequestHeader(value = "x-user-type", required = false) String userType,
            @RequestHeader(value = "x-userid", required = false) String userId,
            @RequestHeader(value = "x-customer-code", required = false) String customerCodeHead,
            @RequestParam(value = "bankName") String bankName,
            @RequestParam(value = "bankId") String bankId,
            @RequestParam(value = "method") String method
    ){
        //该接口只支持运营门户
        if (!UserType.PAS_USER.code.equals(userType)) {
            throw new AppException(PasCode.CUSTOMER_TYPE_INVALID.code);
        }
        try {
            Bank bank = new Bank();
            bank.setOperatorId(userId);
            bank.setLbnkCd(bankId);
            bank.setBnkNm(bankName);
            bankService.saveBank(bank,method);
            return CommonOuterResponse.success();
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof AppException) {
                return CommonOuterResponse.fail(((AppException) e).getErrorCode(), ((AppException) e).getErrorMsg());
            } else {
                return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
            }
        }
    }

    /**
     * 保存参数值
     */
    @RequestMapping(value = "/enableBankInfo", method = RequestMethod.POST)
    @Exceptionable
    @Validatable
    @ApiOperation(value = "银行信息启用/停用", notes = "银行信息启用/停用", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "bankIds", value = "银行编码（批量逗号分隔）", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "flag", value = "是否启用 0：停用 1：启用", required = true, dataType = "String", paramType = "query"),
    })
    @Logable(businessTag = "BankController.enableBankInfo")
    public CommonOuterResponse enableBankInfo(
            @RequestHeader(value = "x-user-type", required = false) String userType,
            @RequestHeader(value = "x-userid", required = false) String userId,
            @RequestHeader(value = "x-customer-code", required = false) String customerCodeHead,
            @RequestParam(value = "bankIds") String bankIds,
            @RequestParam(value = "flag") String flag
    ){
        //该接口只支持运营门户
        if (!UserType.PAS_USER.code.equals(userType)) {
            throw new AppException(PasCode.CUSTOMER_TYPE_INVALID.code);
        }
        try {
            for (String bankId : bankIds.split(",")){
                bankService.enableBank(bankId,flag,userId);
            }
            return CommonOuterResponse.success();
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof AppException) {
                return CommonOuterResponse.fail(((AppException) e).getErrorCode(), ((AppException) e).getErrorMsg());
            } else {
                return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
            }
        }
    }

}
