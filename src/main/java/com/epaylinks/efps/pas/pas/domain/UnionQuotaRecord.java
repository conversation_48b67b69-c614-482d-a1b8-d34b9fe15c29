package com.epaylinks.efps.pas.pas.domain;

import java.util.Date;

public class UnionQuotaRecord {

    private Long id;

    private String transactionNo;

    private String issridAccount;

    private Long amount;

    private String trxCategory;

    /**
     * 状态，0未处理，1已处理
     */
    private String state;

    private String userName;

    private Date createTime;

    private Long userId;

    private String channelTradeNo;

    private String channelReturnCode;

    private String channelReturnMessage;

    private String channelState;

    private Date channelReturnDate;

    private String remark;

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTransactionNo() {
        return transactionNo;
    }

    public void setTransactionNo(String transactionNo) {
        this.transactionNo = transactionNo;
    }

    public String getIssridAccount() {
        return issridAccount;
    }

    public void setIssridAccount(String issridAccount) {
        this.issridAccount = issridAccount;
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public String getTrxCategory() {
        return trxCategory;
    }

    public void setTrxCategory(String trxCategory) {
        this.trxCategory = trxCategory;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getChannelTradeNo() {
        return channelTradeNo;
    }

    public void setChannelTradeNo(String channelTradeNo) {
        this.channelTradeNo = channelTradeNo;
    }

    public String getChannelReturnCode() {
        return channelReturnCode;
    }

    public void setChannelReturnCode(String channelReturnCode) {
        this.channelReturnCode = channelReturnCode;
    }

    public String getChannelReturnMessage() {
        return channelReturnMessage;
    }

    public void setChannelReturnMessage(String channelReturnMessage) {
        this.channelReturnMessage = channelReturnMessage;
    }

    public String getChannelState() {
        return channelState;
    }

    public void setChannelState(String channelState) {
        this.channelState = channelState;
    }

    public Date getChannelReturnDate() {
        return channelReturnDate;
    }

    public void setChannelReturnDate(Date channelReturnDate) {
        this.channelReturnDate = channelReturnDate;
    }
}