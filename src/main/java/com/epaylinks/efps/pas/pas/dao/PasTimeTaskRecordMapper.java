package com.epaylinks.efps.pas.pas.dao;

import com.epaylinks.efps.pas.pas.domain.PasTimeTaskRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

@Mapper
public interface PasTimeTaskRecordMapper {
    int deleteByPrimaryKey(Long id);

    int insert(PasTimeTaskRecord record);

    int insertSelective(PasTimeTaskRecord record);

    PasTimeTaskRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PasTimeTaskRecord record);

    int updateByPrimaryKey(PasTimeTaskRecord record);

    PasTimeTaskRecord selectLastTimeByJobName(@Param("name") String name);

    int deleteByCreateTimeLessThanAndJobName(@Param("date") Date date, @Param("jobName") String jobName);

    int countJobDone(@Param("createTimeStart") Date createTimeStart, @Param("createTimeEnd") Date createTimeEnd, @Param("jobName") String jobName);
}