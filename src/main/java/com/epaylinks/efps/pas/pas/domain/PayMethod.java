package com.epaylinks.efps.pas.pas.domain;

import java.util.Date;

public class PayMethod {
    /**
     * 主键
     */
    private Long id;

    /**
     * 支付方式编码
     */
    private String payMethod;

    /**
     * 支付方式名称
     */
    private String payMethodName;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改者
     */
    private String updator;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 是否支持信用卡:1-支付;0-不支持
     */
    private String creditPay;

    /**
     * 是否支持借记卡:1-支付;0-不支持
     */
    private String debitPay;

    /**
     * 是否支持对公账户:1-支付;0-不支持
     */
    private String publicAccount;

    /**
     * 是否支持对私账户:1-支付;0-不支持
     */
    private String privateAccount;

    /**
     * 状态:0-不正常.1-正常
     */
    private String state;

    /**
     * 业务录入时是否允许配置关注公众号的appId，微信系部分支付方式需要:0-否.1-是
     */
    private String allowConfigAppId;

    /**
     * 该支付方式是否需要为商户在上游进件:0-否.1-是
     */
    private String needApplyInInstitution;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(String payMethod) {
        this.payMethod = payMethod;
    }

    public String getPayMethodName() {
        return payMethodName;
    }

    public void setPayMethodName(String payMethodName) {
        this.payMethodName = payMethodName;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdator() {
        return updator;
    }

    public void setUpdator(String updator) {
        this.updator = updator;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreditPay() {
        return creditPay;
    }

    public void setCreditPay(String creditPay) {
        this.creditPay = creditPay;
    }

    public String getDebitPay() {
        return debitPay;
    }

    public void setDebitPay(String debitPay) {
        this.debitPay = debitPay;
    }

    public String getPublicAccount() {
        return publicAccount;
    }

    public void setPublicAccount(String publicAccount) {
        this.publicAccount = publicAccount;
    }

    public String getPrivateAccount() {
        return privateAccount;
    }

    public void setPrivateAccount(String privateAccount) {
        this.privateAccount = privateAccount;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getAllowConfigAppId() {
        return allowConfigAppId;
    }

    public void setAllowConfigAppId(String allowConfigAppId) {
        this.allowConfigAppId = allowConfigAppId;
    }

    public String getNeedApplyInInstitution() {
        return needApplyInInstitution;
    }

    public void setNeedApplyInInstitution(String needApplyInInstitution) {
        this.needApplyInInstitution = needApplyInInstitution;
    }
}