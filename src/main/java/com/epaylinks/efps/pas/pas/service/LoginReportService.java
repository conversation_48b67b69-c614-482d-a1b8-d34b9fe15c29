package com.epaylinks.efps.pas.pas.service;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.pas.controller.response.LoginReportResponse;
import com.epaylinks.efps.pas.pas.dao.LoginLogMapper;
import com.epaylinks.efps.pas.pas.dao.LoginReportMapper;
import com.epaylinks.efps.pas.pas.domain.LoginReport;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class LoginReportService {
    @Autowired
    private LoginReportMapper loginReportMapper;

    @Autowired
    private LoginLogMapper loginLogMapper;

    @Autowired
    private CommonLogger logger;

    @Value("${loginReport.loginNum:1}")
    private Integer loginNum;

    @Value("${loginReport.loginTime:00:00-12:00}")
    private String loginTime;

    public PageResult<LoginReportResponse> reportPage(Map<String,Object> param, Long userId) {
        Integer count = loginReportMapper.countReportPage(param);
        List<LoginReportResponse> reportResponseList = loginReportMapper.reportPage(param);
        PageResult pageResult = new PageResult();
        pageResult.setTotal(count);
        pageResult.setRows(reportResponseList);
        return pageResult;
    }

    public CommonOuterResponse exceptionHandle(Long reportId,String exceptionHandle,Long userId) {
        if (reportId == null) {
            return CommonOuterResponse.fail(PasCode.MISSING_PARAMETER.code,PasCode.MISSING_PARAMETER.message);
        }
        if (StringUtils.isNotBlank(exceptionHandle) && getByteLength(exceptionHandle) > 100) {
            return CommonOuterResponse.fail(PasCode.LENGTH_LIMIT.code, PasCode.LENGTH_LIMIT.message);
        }
        LoginReport loginReport = loginReportMapper.selectByPrimaryKey(reportId);
        if (loginReport == null) {
            return CommonOuterResponse.fail(PasCode.RECORD_NOT_EXIST.code,PasCode.RECORD_NOT_EXIST.message);
        }
        loginReport.setExceptionHandle(exceptionHandle);
        loginReport.setOperator(userId);
        loginReport.setHandleTime(new Date());
        loginReportMapper.updateByPrimaryKeySelective(loginReport);
        return CommonOuterResponse.success();
    }

    private Integer getByteLength(String param) {
        try {
            return param.getBytes("GBK").length;
        } catch (Exception e){
            logger.printLog(e);
        }
        return 255;
    }

    @Transactional
    public void saveBeforeDay() {
        List<LoginReport> reportResponseList = loginLogMapper.queryBeforeDayList();
        if (reportResponseList.isEmpty()) {
            return;
        }
        for (LoginReport report : reportResponseList) {
            report.setReportState(ReportState.NORMAL.code);
            report.setCreateTime(new Date());
            if (report.getLoginNum() > loginNum) {
                report.setReportDesc("超过最大登录次数（" + loginNum + "）");
                report.setReportState(ReportState.ABNORMAL.code);
            }
            Map<String,Object> param = new HashMap<>();
            param.put("loginAccount",report.getLoginAccount());
            param.put("customerCode",report.getCustomerCode());
            param.put("userType",report.getUserType());
            param.put("loginRole",report.getLoginRole());
            param.put("name",report.getName());
            param.put("loginTime",report.getLoginTime());
            param.put("loginIp",report.getLoginIp());
            param.put("startTime",splitLoginTime()[0]);
            param.put("endTime",splitLoginTime()[1]);
            if (loginLogMapper.checkLoginTime(param) > 0) {
                report.setReportDesc(StringUtils.isBlank(report.getReportDesc()) ? "非工作时间段（" + loginTime + "）登录" : report.getReportDesc() + "；非工作时间段（" + loginTime + "）登录");
                report.setReportState(ReportState.ABNORMAL.code);
            }
            report.setReportId(loginReportMapper.queryReportSeq());
            loginReportMapper.insert(report);
        }
    }

    public String[] splitLoginTime() {
        String[] loginTimes = loginTime.split("-");
        return loginTimes;
    }

    public enum ReportState {
        NORMAL("1","正常"),
        ABNORMAL("2","异常")
        ;
        private String code;
        private String message;

        ReportState(String code,String message) {
            this.code = code;
            this.message = message;
        }
    }
}
