package com.epaylinks.efps.pas.pas.domain;


public class Application {
    /**
     * 主键
     */
    private Long id;

    /**
     * 某个进件所需字段的efpskey:表名.列名
     */
    private String efpsKey;

    /**
     * 某个进件所需字段efps名称,展示使用
     */
    private String efpsName;

    /**
     * 某个进件所需字段上游合作机构进件时的参数名
     */
    private String institutionKey;

    /**
     * 上游合作机构编码
     */
    private String institutionCode;

    /**
     * 字段是否可空:1-是;0-否
     */
    private String allowNull;

    /**
     * 是否为文件:0-不是.1-是
     */
    private String isFile;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getEfpsKey() {
        return efpsKey;
    }

    public void setEfpsKey(String efpsKey) {
        this.efpsKey = efpsKey;
    }

    public String getEfpsName() {
        return efpsName;
    }

    public void setEfpsName(String efpsName) {
        this.efpsName = efpsName;
    }

    public String getInstitutionKey() {
        return institutionKey;
    }

    public void setInstitutionKey(String institutionKey) {
        this.institutionKey = institutionKey;
    }

    public String getInstitutionCode() {
        return institutionCode;
    }

    public void setInstitutionCode(String institutionCode) {
        this.institutionCode = institutionCode;
    }

    public String getAllowNull() {
        return allowNull;
    }

    public void setAllowNull(String allowNull) {
        this.allowNull = allowNull;
    }

    public String getIsFile() { return isFile; }

    public void setIsFile(String isFile) { this.isFile = isFile; }
}