package com.epaylinks.efps.pas.pas.controller;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.business.CommonResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.pas.controller.request.ManagementDataRequest;
import com.epaylinks.efps.pas.pas.controller.response.ManagementDataResponse;
import com.epaylinks.efps.pas.pas.controller.response.ManagementSendRecordResponse;
import com.epaylinks.efps.pas.pas.service.ManagementDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/managementData")
@Api(value = "ManagementDataController", description = "经管数据")
public class ManagementDataController {
    @Autowired
    private CommonLogger logger;

    @Autowired
    private ManagementDataService managementDataService;

    @PostMapping("/uploadDetail")
    @Logable(businessTag = "ManagementDataController.uploadDetail",outputResult = false)
    @ApiOperation(value = "上传图片和备注信息", notes = "上传图片和备注信息", httpMethod = "POST")
    public CommonOuterResponse uploadDetail(@RequestBody ManagementDataRequest request,
                                            @RequestHeader("x-userid") Long userId) {
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            managementDataService.uploadDetail(request,userId);
        } catch (AppException e) {
            response.setReturnCode(e.getErrorCode());
            response.setReturnMsg(e.getErrorMsg());
        } catch (Exception e) {
            logger.printMessage("上传经管图片错误:" + e.getMessage());
            logger.printLog(e);
            response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
            response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
        }
        return response;
    }

    @GetMapping("/query")
    @Logable(businessTag = "ManagementDataController.query",outputResult = false)
    @ApiOperation(value = "人员列表", notes = "人员列表", httpMethod = "GET")
    public CommonOuterResponse query(@RequestHeader("x-userid") Long userId) {
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            return CommonOuterResponse.success(managementDataService.query());
        } catch (AppException e) {
            response.setReturnCode(e.getErrorCode());
            response.setReturnMsg(e.getErrorMsg());
        } catch (Exception e) {
            logger.printMessage("人员管理查询错误:" + e.getMessage());
            logger.printLog(e);
            response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
            response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
        }
        return response;
    }

    @GetMapping("/pageQuery")
    @Logable(businessTag = "ManagementDataController.pageQuery",outputResult = false)
    @ApiOperation(value = "详情列表", notes = "详情列表", httpMethod = "GET")
    public PageResult<List<ManagementDataResponse>> pageQuery(@RequestParam(value = "startCreateTime", required = false) String startCreateTime,
                                                              @RequestParam(value = "endCreateTime", required = false) String endCreateTime,
                                                              @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                              @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                              @RequestHeader("x-userid") Long userId) {
        PageResult page = new PageResult<>();
        try {
            int endRowNo = pageNum * pageSize;
            int beginRowNo = (pageNum - 1) * pageSize + 1;
            Map<String, Object> paramsMap = new HashMap<>();
            paramsMap.put("startCreateTime", startCreateTime);
            paramsMap.put("endCreateTime", endCreateTime);
            paramsMap.put("beginRowNo", beginRowNo);
            paramsMap.put("endRowNo", endRowNo);
            page = managementDataService.pageQuery(paramsMap,userId);
            page.setCode(CommonResponse.SUCCEE);
        } catch (Exception e) {
            logger.printMessage("经管数据详情列表错误：" + e.getMessage());
            logger.printLog(e);
            if (e instanceof AppException) {
                page.setCode(((AppException) e).getErrorCode());
                page.setMessage(((AppException) e).getErrorMsg());
            } else {
                page.setCode(PasCode.SYSTEM_EXCEPTION.code);
                page.setMessage(PasCode.SYSTEM_EXCEPTION.message);
            }
        }
        return page;
    }

    @GetMapping("/recordQuery")
    @Logable(businessTag = "ManagementDataController.recordQuery",outputResult = false)
    @ApiOperation(value = "发送记录分页列表", notes = "发送记录分页列表", httpMethod = "GET")
    public PageResult<List<ManagementSendRecordResponse>> recordQuery(@RequestParam(value = "startCreateTime", required = false) String startCreateTime,
                                                                      @RequestParam(value = "endCreateTime", required = false) String endCreateTime,
                                                                      @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                                      @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                                      @RequestHeader("x-userid") Long userId) {
        PageResult page = new PageResult<>();
        try {
            int endRowNo = pageNum * pageSize;
            int beginRowNo = (pageNum - 1) * pageSize + 1;
            Map<String, Object> paramsMap = new HashMap<>();
            paramsMap.put("startCreateTime", startCreateTime);
            paramsMap.put("endCreateTime", endCreateTime);
            paramsMap.put("beginRowNo", beginRowNo);
            paramsMap.put("endRowNo", endRowNo);
            page = managementDataService.recordQuery(paramsMap,userId);
            page.setCode(CommonResponse.SUCCEE);
        } catch (Exception e) {
            logger.printMessage("发送记录列表查询错误：" + e.getMessage());
            logger.printLog(e);
            if (e instanceof AppException) {
                page.setCode(((AppException) e).getErrorCode());
                page.setMessage(((AppException) e).getErrorMsg());
            } else {
                page.setCode(PasCode.SYSTEM_EXCEPTION.code);
                page.setMessage(PasCode.SYSTEM_EXCEPTION.message);
            }
        }
        return page;
    }

    @PostMapping("/add")
    @Logable(businessTag = "ManagementDataController.add")
    @ApiOperation(value = "添加人员", notes = "添加人员", httpMethod = "POST")
    public CommonOuterResponse add(@RequestParam("receiverId") String receiverId,
                                   @RequestParam("name") String name,
                                   @RequestParam("phone") String phone,
                                   @RequestHeader("x-userid") Long userId) {
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            managementDataService.add(receiverId,name,phone,userId);
        } catch (AppException e) {
            response.setReturnCode(e.getErrorCode());
            response.setReturnMsg(e.getErrorMsg());
        } catch (Exception e) {
            logger.printMessage("添加人员错误：" + e.getMessage());
            logger.printLog(e);
            response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
            response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
        }
        return response;
    }

    @PostMapping("/delete")
    @Logable(businessTag = "ManagementDataController.delete")
    @ApiOperation(value = "删除人员", notes = "删除人员", httpMethod = "POST")
    public CommonOuterResponse delete(@RequestParam("id") Long id,
                                      @RequestHeader("x-userid") Long userId) {
        return managementDataService.delete(id);
    }

    @PostMapping("/send")
    @Logable(businessTag = "ManagementDataController.send")
    @ApiOperation(value = "发送", notes = "发送", httpMethod = "POST")
    public CommonOuterResponse send(@RequestParam("personIds") String personIds,
                                    @RequestParam("dataId") Long dataId,
                                    @RequestHeader("x-userid") Long userId) {
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            return managementDataService.send(personIds,dataId,userId);
        } catch (AppException e) {
            response.setReturnCode(e.getErrorCode());
            response.setReturnMsg(e.getErrorMsg());
        } catch (Exception e) {
            logger.printMessage("发送信息错误：" + e.getMessage());
            logger.printLog(e);
            response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
            response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
        }
        return response;
    }

    @GetMapping("/getSendPhone")
    @Logable(businessTag = "ManagementDataController.getSendPhone")
    @ApiOperation(value = "页面获取手机号码", notes = "页面获取手机号码", httpMethod = "GET")
    public CommonOuterResponse<String> getSendPhone(@RequestParam("uid") String uid) {
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            String phone = managementDataService.getSendPhone(uid);
            response.setData(phone);
        } catch (AppException e) {
            response.setReturnCode(e.getErrorCode());
            response.setReturnMsg(e.getErrorMsg());
        } catch (Exception e) {
            logger.printMessage("页面获取手机号码错误：" + e.getMessage());
            logger.printLog(e);
            response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
            response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
        }
        return response;
    }

    @PostMapping("/extractFile")
    @Logable(businessTag = "ManagementDataController.extractFile")
    @ApiOperation(value = "提取文件", notes = "提取文件", httpMethod = "POST")
    public CommonOuterResponse extractFile(@RequestParam("uid") String uid,
                                                         @RequestParam(value = "phone",required = false) String phone,
                                                         @RequestParam("code") String code,
                                                         HttpServletResponse httpServletResponse) {
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            return managementDataService.extract(uid,phone,code,httpServletResponse);
        } catch (AppException e) {
            response.setReturnCode(e.getErrorCode());
            response.setReturnMsg(e.getErrorMsg());
        } catch (Exception e) {
            logger.printMessage("提取文件错误：" + e.getMessage());
            logger.printLog(e);
            response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
            response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
        }
        return response;
    }

    @PostMapping("/sendMessage")
    @Logable(businessTag = "ManagementDataController.sendMessage")
    @ApiOperation(value = "发送验证码", notes = "发送验证码", httpMethod = "POST")
    public CommonOuterResponse sendMessage(@RequestParam("uid") String uid) {
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            return managementDataService.sendMessage(uid);
        } catch (AppException e) {
            response.setReturnCode(e.getErrorCode());
            response.setReturnMsg(e.getErrorMsg());
        } catch (Exception e) {
            logger.printMessage("提取文件发送短信错误：" + e.getMessage());
            logger.printLog(e);
            response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
            response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
        }
        return response;
    }

    @PostMapping("/deleteAttach")
    @Logable(businessTag = "ManagementDataController.deleteAttach")
    @ApiOperation(value = "删除详情附件", notes = "删除详情附件", httpMethod = "POST")
    public CommonOuterResponse deleteAttach(@RequestParam("dataId") Long dataId,
                                            @RequestHeader("x-userid") Long userId) {
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            return managementDataService.deleteAttach(dataId);
        } catch (AppException e) {
            response.setReturnCode(e.getErrorCode());
            response.setReturnMsg(e.getErrorMsg());
        } catch (Exception e) {
            logger.printMessage("删除记录附件错误：" + e.getMessage());
            logger.printLog(e);
            response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
            response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
        }
        return response;
    }
}
