package com.epaylinks.efps.pas.pas.service.impl;

import java.util.List;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import com.epaylinks.efps.pas.pas.dao.BusinessCategoryMapper;
import com.epaylinks.efps.pas.pas.domain.BusinessCategory;

@Service
public class BusinessCategoryImpl {
	
	private static final String BUSINESS_CATEGORY_CODE_KEY_PREFIX = "BUSINESS_CATEGORY_CODE:";
	@Autowired
	private BusinessCategoryMapper businessCategoryMapper;
    @Autowired
    private RedisTemplate<Object, Object> redisTemplate;
	
	public List<BusinessCategory> getAllBusinessCategory(){
		return businessCategoryMapper.queryAll();
	}

	public List<BusinessCategory> selectBySelective(BusinessCategory b) {
		return businessCategoryMapper.selectBySelective(b);
	}
	public List<BusinessCategory> getBusinessCategoryByCodes(List<String> list) {
		return businessCategoryMapper.getBusinessCategoryByCodes(list);
	}
	
	/**
	 * 通过code查询业务分类名称（支持缓存）
	 * @param code
	 * @return
	 */
	public String queryBusinessCategoryNameByCode(String code) {
		
    	String name = (String) redisTemplate.opsForValue().get(BUSINESS_CATEGORY_CODE_KEY_PREFIX + code);
		if( name == null ) {
			BusinessCategory obj = new BusinessCategory();
			obj.setCode(code);
			List<BusinessCategory> list = businessCategoryMapper.selectBySelective(obj);
			if(list == null || list.isEmpty() || list.get(0).getName() == null) {
				name = "";
			}else {
				name = list.get(0).getName();
			}
			redisTemplate.opsForValue().set(BUSINESS_CATEGORY_CODE_KEY_PREFIX + code, name, 5, TimeUnit.MINUTES);
		}
		return name;
	}


}
