package com.epaylinks.efps.pas.pas.common;

import com.epaylinks.efps.common.log.Logable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.sql.SQLException;

@Service
public class CommonService {

    private String exceptionStraceLength = "256";


    private String exceptionStraceLengthSQl = "1024";

    @Autowired
    private CommonService self;

    @Logable(businessTag = "PasLog", outputResult = false)
    public String pasLog(String message) {
        return message;
    }

    public void logException(Exception e) {
        logKeyExceptionStrace(e);

    }

    public void logKeyExceptionStrace(Exception e) {

        try {
            if (null == e) {
                return ;
            }

            int length = Integer.parseInt(exceptionStraceLength);

            if (e instanceof SQLException) {
                length = Integer.parseInt(exceptionStraceLengthSQl);
            }

            String exceptionDetail = getTrace(e);
            String logStr = exceptionDetail.substring(0,
                    exceptionDetail.length() <= length? exceptionDetail.length(): length);
            self.pasLog(logStr);
        }catch (Exception e1) {
            System.out.println("CommonService-logKeyExceptionStrace-有异常!");
        }

    }

    private String getTrace(Throwable t) {
        StringWriter stringWriter= new StringWriter();
        PrintWriter writer= new PrintWriter(stringWriter);
        t.printStackTrace(writer);
        StringBuffer buffer= stringWriter.getBuffer();
        return buffer.toString();
    }
}
