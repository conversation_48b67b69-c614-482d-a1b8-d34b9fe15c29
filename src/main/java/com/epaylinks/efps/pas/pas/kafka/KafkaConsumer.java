package com.epaylinks.efps.pas.pas.kafka;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.pas.pas.common.PasConstant;
import com.epaylinks.efps.pas.pas.domain.ApplicationRecord;
import com.epaylinks.efps.pas.pas.service.ApplicationRecordService;
import com.epaylinks.efps.pas.pas.service.UnionQuotaService;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.KafkaListener;

import java.util.Date;
import java.util.Optional;

@Configuration
public class KafkaConsumer {

    private static final String KAFKA_KEY = "inlet";
    private static final String UNION_FUNDSETT_QUOTA_KEY = "UnionFundsettQuotaChange";

    @Autowired
    private ApplicationRecordService applicationRecordService;
    @Autowired
    private UnionQuotaService unionQuotaService;

    @KafkaListener(topics = {"014PaymentNotice","CLR_PayGatewayResult"},containerFactory = "customContainerFactory")
    @Logable(businessTag = "kafka")
    public void consume(ConsumerRecord<?, ?> record) {
        Optional<?> kafkaMessage = Optional.ofNullable(record.value());
        if (kafkaMessage.isPresent()) {
            String message = (String) kafkaMessage.get();
            Object obj = JSONObject.parse(message);
            JSONObject jsonObj = JSON.parseObject(obj.toString());
            String key = (String) record.key();

            if (KAFKA_KEY.equals(key)) {
                String requestId = jsonObj.getString("requestId");
                String resultCode = jsonObj.getString("resultCode");
                String institutionMerchCode = jsonObj.getString("institutionMerchCode");
                String institutionMerchSecret = jsonObj.getString("institutionMerchSecret");
                String resultMsg = jsonObj.getString("resultMsg");

                ApplicationRecord applicationRecord = new ApplicationRecord();
                applicationRecord.setId(Long.parseLong(requestId));
                applicationRecord.setUpdateTime(new Date());
                if("S".equals(resultCode)){
                    applicationRecord.setState(PasConstant.InletState.Success.code);
                }else if("F".equals(resultCode)){
                    applicationRecord.setState(PasConstant.InletState.Fail.code);
                }
                applicationRecord.setResultMsg(resultMsg);
                applicationRecord.setInstitutionMerchCode(institutionMerchCode);
                applicationRecord.setInstitutionMerchSecret(institutionMerchSecret);
                applicationRecordService.updateByPrimaryKeySelective(applicationRecord);

            }
            // 银总额度调增的异步通知
            else if (UNION_FUNDSETT_QUOTA_KEY.equals(key)) {
                String transactionNo = jsonObj.getString("transactionNo");
                String channelState = jsonObj.getString("state");
                String channelRespCode = jsonObj.getString("channelRespCode");
                String channelRespMsg = jsonObj.getString("channelRespMsg");
                unionQuotaService.updateUnionFundsettQuotaChange(transactionNo, channelState, channelRespCode, channelRespMsg);
            }
        }
    }

}


