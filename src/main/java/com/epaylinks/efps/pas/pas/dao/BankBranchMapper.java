package com.epaylinks.efps.pas.pas.dao;

import com.epaylinks.efps.pas.pas.domain.BankBranch;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface BankBranchMapper {
    int deleteByPrimaryKey(String lbnkNo);

    int insert(BankBranch record);

    int insertSelective(BankBranch record);

    BankBranch selectByPrimaryKey(String lbnkNo);

    int updateByPrimaryKeySelective(BankBranch record);

    int updateByPrimaryKey(BankBranch record);


    //联行号查询
    List<BankBranch> pageQueryBankBranch(
            @Param("beginRowNo") Integer beginRowNo,
            @Param("endRowNo") Integer endRowNo,
            @Param("lbnkNo") String lbnkNo,
            @Param("lbnkNm") String lbnkNm,
            @Param("lbnkCd") String lbnkCd,
            @Param("corpOrg") String corpOrg,
            @Param("provCd") String provCd,
            @Param("cityCd") String cityCd
    );

    Integer countPageQueryBankBranch(
            @Param("beginRowNo") Integer beginRowNo,
            @Param("endRowNo") Integer endRowNo,
            @Param("lbnkNo") String lbnkNo,
            @Param("lbnkNm") String lbnkNm,
            @Param("lbnkCd") String lbnkCd,
            @Param("corpOrg") String corpOrg,
            @Param("provCd") String provCd,
            @Param("cityCd") String cityCd
    );

    List<BankBranch> queryBankBranchByOptions(@Param("provinceCode") String provinceCode,
    		@Param("cityCode") String cityCode, @Param("bankIcon") String bankIcon, @Param("bankBranchName") String bankBranchName);

    List<BankBranch> selectByPage(Map map);

    int selectCount(Map map);
}