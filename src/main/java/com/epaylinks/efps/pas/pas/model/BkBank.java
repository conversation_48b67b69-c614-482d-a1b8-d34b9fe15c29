package com.epaylinks.efps.pas.pas.model;

import com.epaylinks.efps.common.datadownload.annotation.FieldAnnotation;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

public class BkBank {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 银行编码
     */
    @ApiModelProperty(value="银行编码", dataType = "String")
    @FieldAnnotation(fieldName="银行编码")
    private String bankCode;

    /**
     * 新银行编码
     */
    private String newBankCode;

    /**
     * 银行名称
     */
    @ApiModelProperty(value="银行名称", dataType = "String")
    @FieldAnnotation(fieldName="银行名称")
    private String bankName;

    /**
     * 银行全称
     */
    @ApiModelProperty(value="银行全称", dataType = "String")
    @FieldAnnotation(fieldName="银行全称")
    private String fullName;

    /**
     * 金融机构号
     */
    @ApiModelProperty(value="金融机构号", dataType = "String")
    @FieldAnnotation(fieldName="金融机构号")
    private String institutionCode;

    /**
     * cardbin机构代码
     */
    @ApiModelProperty(value="cardbin机构代码", dataType = "String")
    @FieldAnnotation(fieldName="cardbin机构代码")
    private String issueBankNo;

    /**
     * 银行ID
     */
    @ApiModelProperty(value="联行号行别代码", dataType = "String")
    @FieldAnnotation(fieldName="联行号行别代码")
    private String bankId;


    /**
     * 是否区域银行
     */
    @ApiModelProperty(value="是否区域银行 0:否 1:是", dataType = "String")
    @FieldAnnotation(fieldName="是否区域银行",dictionaries="0:否,1:是")
    private Integer isAreaBank;

    /**
     * 状态 0：停用 1：启用
     */
    @ApiModelProperty(value="状态 0：停用 1：启用", dataType = "String")
    @FieldAnnotation(fieldName="状态",dictionaries="0:停用,1:启用")
    private String flag;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    @FieldAnnotation(fieldName="创建时间", dateFormat="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    @FieldAnnotation(fieldName="更新时间", dateFormat="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty(value="操作人", dataType = "String")
    @FieldAnnotation(fieldName="操作人")
    private String operatorId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBankId() {
        return bankId;
    }

    public void setBankId(String bankId) {
        this.bankId = bankId;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getNewBankCode() {
        return newBankCode;
    }

    public void setNewBankCode(String newBankCode) {
        this.newBankCode = newBankCode;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getInstitutionCode() {
        return institutionCode;
    }

    public void setInstitutionCode(String institutionCode) {
        this.institutionCode = institutionCode;
    }

    public String getIssueBankNo() {
        return issueBankNo;
    }

    public void setIssueBankNo(String issueBankNo) {
        this.issueBankNo = issueBankNo;
    }

    public Integer getIsAreaBank() {
        return isAreaBank;
    }

    public void setIsAreaBank(Integer isAreaBank) {
        this.isAreaBank = isAreaBank;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }
}