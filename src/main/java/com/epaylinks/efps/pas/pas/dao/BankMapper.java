package com.epaylinks.efps.pas.pas.dao;

import com.epaylinks.efps.pas.pas.domain.Bank;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface BankMapper {

    int deleteByPrimaryKey(String lbnkCd);

    int insertSelective(Bank record);

    Bank selectByPrimaryKey(String lbnkCd);

    int updateByPrimaryKeySelective(Bank record);

    int updateByPrimaryKey(Bank record);

    List<Bank> selectAll();

    List<Bank> selectBankPage(Map map);

    int selectCountByMap(Map map);
}