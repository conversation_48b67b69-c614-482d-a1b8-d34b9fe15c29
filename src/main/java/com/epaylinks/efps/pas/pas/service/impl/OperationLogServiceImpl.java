package com.epaylinks.efps.pas.pas.service.impl;

import com.epaylinks.efps.pas.common.PageResult;
import com.epaylinks.efps.pas.pas.dao.OperationLogMapper;
import com.epaylinks.efps.pas.pas.domain.OperationLog;
import com.epaylinks.efps.pas.pas.domain.OperationLogReport;
import com.epaylinks.efps.pas.pas.service.OperationLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class OperationLogServiceImpl implements OperationLogService {

	@Autowired
	private OperationLogMapper operationLogMapper;


	@Override
	public void insert(OperationLog opLog) {
		operationLogMapper.insert(opLog);
	}

	@Override
	public PageResult<OperationLog> pageQuery(int pageSize, int pageNum, Map<String, String> map) {
		PageResult<OperationLog> pageResult = new PageResult<>();

		int endNum = pageSize*pageNum;
		int startNum = endNum - pageSize + 1;

		//根据条件查询总记录数
		int total = operationLogMapper.pageQueryTotal(map.get("startTime"),map.get("endTime"),map.get("userName"),map.get("realName"),map.get("module"));
		//根据条件查询当前页面数据
		List<OperationLog> logList = operationLogMapper.pageQuery(startNum,endNum,map.get("startTime"),map.get("endTime"),map.get("userName"),map.get("realName"),map.get("module"));


		pageResult.setRows(logList);
		pageResult.setTotal(total);
		return pageResult;
	}

    @Override
    public com.epaylinks.efps.common.util.page.PageResult<OperationLogReport> OperationLogReport(Map<String, Object> map) {
        
        com.epaylinks.efps.common.util.page.PageResult<OperationLogReport> pageResult = new com.epaylinks.efps.common.util.page.PageResult<>();

        //根据条件查询总记录数
        int total = operationLogMapper.countOperLogReport(map);
        if (total < 1) {
            pageResult.setTotal(total);
            return pageResult; 
        }
        
        //根据条件查询当前页面数据
        List<OperationLogReport> logList = operationLogMapper.pageQueryOperLogReport(map);

        pageResult.setRows(logList);
        pageResult.setTotal(total);
        return pageResult;
    }
}
