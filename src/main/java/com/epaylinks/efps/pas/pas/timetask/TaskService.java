package com.epaylinks.efps.pas.pas.timetask;

import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.common.tool.error.prediction.EpAssert;
import com.epaylinks.efps.common.tool.time.Timex;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.common.PasConstants;
import com.epaylinks.efps.pas.pas.dao.PasTimeTaskMapper;
import com.epaylinks.efps.pas.pas.dao.PasTimeTaskRecordMapper;
import com.epaylinks.efps.pas.pas.domain.PasTimeTask;
import com.epaylinks.efps.pas.pas.domain.PasTimeTaskRecord;
import org.quartz.JobExecutionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.net.InetAddress;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/3/24 14:17
 */
@Service
public class TaskService {
    private static final Logger logger = LoggerFactory.getLogger(TaskService.class);

    @Value("${partition}")
    private String partition;

    @Autowired
    private PasTimeTaskMapper taskMapper;

    @Autowired
    private SequenceService sequenceService;

    @Autowired
    private PasTimeTaskRecordMapper taskRecordMapper;

    @Autowired
    TaskService self;

    /**
     * 准备执行任务
     *
     * @param context 任务上下文
     * @return 如果任务不满足调度条件，则返回null
     */
    public PasTimeTask prepare(JobExecutionContext context) {
        String jobName = context.getTrigger().getKey().getName();
        Date fireTime = context.getScheduledFireTime(); //本次触发时间
        return self.prepare(jobName, fireTime);
    }

    public int updateRecord(PasTimeTaskRecord record) {
        return taskRecordMapper.updateByPrimaryKeySelective(record);
    }

    /**
     * 准备执行任务
     *
     * @param jobName  任务名称
     * @param fireTime 触发时间
     * @return 如果任务不满足调度条件，则返回null
     */
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRES_NEW)
    public PasTimeTask prepare(String jobName, Date fireTime) {
        PasTimeTask task = taskMapper.findByJobNameForUpdate(jobName);
        Date lastFireTime = task.getLastFireTime();

        //本次触发时间与上次触发时间相差不超过1秒
        //则认为是重复任务或者过时任务，直接跳过
        if (lastFireTime != null &&
                fireTime.getTime() - lastFireTime.getTime() < 1000) {
            return null;
        }

        //串行调度，判断是否满足执行条件
        if (!task.isExecuteConcurrent() && task.getLastExecuteRecordId() != null) {
            PasTimeTaskRecord last = taskRecordMapper.selectByPrimaryKey(task.getLastExecuteRecordId());
            if (last != null && last.isProcessing() &&
                    new Date().getTime() - last.getCreateTime().getTime() < Optional.ofNullable(task.getMaxExecuteDuration()).orElse(5 * 60) * 1000) {
                logger.info("串行任务[{}]处理中，不能重复调度[{}]", jobName, Timex.ofDate(fireTime).to(Timex.Format.yyyy$MM$dd$HH$mm$ss));
                return null;
            }
        }

        Long recordId = sequenceService.nextValue("pasTimeTaskRecord");
        //插入记录
        PasTimeTaskRecord record = new PasTimeTaskRecord();
        record.setJobName(task.getJobName());
        record.setCreateTime(new Date());
        record.setId(recordId);
        record.setPartition(partition);
        record.setStatus("2"); //处理中
        record.setRecordKey(record.getJobName() + Timex.ofDate(fireTime).to(Timex.Format.yyyyMMddHHmmss));
        try {
            InetAddress address = InetAddress.getLocalHost();
            record.setCallerIpAddress(address.getHostAddress());
        } catch (Exception ignored) {
        }
        taskRecordMapper.insertSelective(record);

        //更新执行记录ID
        task.setLastFireTime(fireTime);
        task.setLastExecuteRecordId(recordId);
        taskMapper.updateByPrimaryKeySelective(task);

        task.setLastExecuteRecord(record);
        return task;
    }

    public PasTimeTask getReExecuteTask(String jobName) {
        PasTimeTask task = EpAssert.notNull(taskMapper.selectByJobName(jobName), PasCode.TASK_STATE_EXCEPTION.msg("任务不存在"));
        Date lastFireTime = task.getLastFireTime();

        EpAssert.notNull(lastFireTime, PasCode.TASK_STATE_EXCEPTION.msg("任务执行记录不存在"));

        EpAssert.state(Timex.now().to(Timex.Format.yyyyMMdd).equals(Timex.ofDate(lastFireTime).to(Timex.Format.yyyyMMdd)),
                PasCode.TASK_STATE_EXCEPTION.msg("任务当日未执行"));

        PasTimeTaskRecord last = EpAssert.notNull(taskRecordMapper.selectByPrimaryKey(task.getLastExecuteRecordId()),
                PasCode.TASK_STATE_EXCEPTION.msg("任务执行记录不存在"));

        EpAssert.state(PasConstants.timeTaskRecordStatus.FAIL.code.equals(last.getStatus()) ||
                        PasConstants.timeTaskRecordStatus.PROCESSING.code.equals(last.getStatus()),
                PasCode.TASK_STATE_EXCEPTION.msg("已经执行成功的任务不允许重新调度"));

        task.setLastExecuteRecord(last);

        return task;
    }

    public Integer switchJobPartition(String partition,String jobName) {
        if (StringUtils.isEmpty(partition)) {
            throw new AppException(PasCode.MISSING_PARAMETER.code,PasCode.MISSING_PARAMETER.message);
        }
        if (!"A".equals(partition) && !"B".equals(partition)) {
            throw new AppException(PasCode.DATA_ERROR.code,"分区需传A或B");
        }
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("partition",partition);
        if (!StringUtils.isEmpty(jobName)) {
            List<String> jobNames = Arrays.asList(jobName.split(","));
            paramMap.put("jobName",jobNames);
        }
        return taskMapper.switchJobPartition(paramMap);
    }

    public Map<String, List<String>> queryJobsPartition() {
        List<PasTimeTask> taskList = taskMapper.selectAll();
        return taskList.stream().filter(t -> t.getPartition()!= null && "0".equals(t.getJobStatus()) )
                .collect(Collectors.groupingBy(PasTimeTask::getPartition,
                        Collectors.mapping(PasTimeTask::getJobName, Collectors.toList())));
    }
}
