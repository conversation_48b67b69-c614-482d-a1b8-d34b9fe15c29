package com.epaylinks.efps.pas.pas.service;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.Map;

public interface DueDiligenceService {

    /**
     * 生成和保存尽职调查表
     * @param merchantName
     * @param parentName
     * @param busiNo
     * @param regDate
     * @param remark
     * @param type
     * @return
     */
    String createTableByData(String merchantName, String parentName, String busiNo, String regDate, String remark, String type) throws Exception;

    /**
     * 填充网页模板
     * @param emailTemplateName
     * @param map
     * @return
     */
    String readTemplateAndSetValues(String emailTemplateName, Map<String, String> map);

    /**
     * 封装文件
     * @param file
     * @return
     * @throws Exception
     */
    MultipartFile getMultipartFile(File file) throws Exception;
}
