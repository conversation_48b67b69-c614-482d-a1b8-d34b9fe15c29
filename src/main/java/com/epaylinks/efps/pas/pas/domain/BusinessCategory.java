package com.epaylinks.efps.pas.pas.domain;

import java.util.Date;

import com.alibaba.fastjson.JSON;

public class BusinessCategory {
    private String code;

    private String name;

    private Date createTime;

    private Date updateTime;

    private String status;
    /**
     * 业务类型的结算粒度
     */
    private String settGrained;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
    public String getSettGrained() {
		return settGrained;
	}

	public void setSettGrained(String settGrained) {
		this.settGrained = settGrained;
	}

	@Override
    public String toString() {
    	// TODO Auto-generated method stub
    	return JSON.toJSONString(this);
    }
}