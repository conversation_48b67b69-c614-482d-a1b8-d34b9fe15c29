package com.epaylinks.efps.pas.pas.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.log.Logable.Level;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.pas.common.PasConstant;
import com.epaylinks.efps.pas.pas.dao.PayMethodMapper;
import com.epaylinks.efps.pas.pas.domain.PayMethod;
import com.epaylinks.efps.pas.pas.service.PayMethodService;

/**
 * <AUTHOR> 5m
 * @Date : 2017/12/27 16:19
 * @Description :
 */
@Service
public class PayMethodServiceImpl implements PayMethodService {

    @Autowired
    PayMethodMapper payMethodMapper;
    
    @Autowired
	private KafkaTemplate<String, String> kafkaTemplate;
	
	public static final String PAS_TOPIC = "PAS_BasicData";
	
	public static final String ALL_PAYMETHODS_KEY = "AllPayMethods";

	@Logable(businessTag = "deleteByPrimaryKey" , level = Level.DEBUG)
    @Override
    public int deleteByPrimaryKey(Long id) {
        return payMethodMapper.deleteByPrimaryKey(id);
    }

	@Logable(businessTag = "insert" , level = Level.DEBUG)
    @Override
    public int insert(PayMethod record) {
        return payMethodMapper.insert(record);
    }

	@Logable(businessTag = "insertSelective" , level = Level.DEBUG)
    @Override
    public int insertSelective(PayMethod record) {
        return payMethodMapper.insertSelective(record);
    }

	@Logable(businessTag = "selectByPrimaryKey" , level = Level.DEBUG)
    @Override
    public PayMethod selectByPrimaryKey(Long id) {
        return payMethodMapper.selectByPrimaryKey(id);
    }

	@Logable(businessTag = "updateByPrimaryKeySelective" , level = Level.DEBUG)
    @Override
    public int updateByPrimaryKeySelective(PayMethod record) {
        return payMethodMapper.updateByPrimaryKeySelective(record);
    }

	@Logable(businessTag = "updateByPrimaryKey" , level = Level.DEBUG)
    @Override
    public int updateByPrimaryKey(PayMethod record) {
        return payMethodMapper.updateByPrimaryKey(record);
    }

	@Logable(businessTag = "selectBySelective" , level = Level.DEBUG)
    @Override
    public List<PayMethod> selectBySelective(PayMethod record) {
        return payMethodMapper.selectBySelective(record);
    }

	@Logable(businessTag = "selectByBusinessCode" , level = Level.DEBUG)
    @Override
    public List<PayMethod> selectByBusinessCode(String businessCode) { return payMethodMapper.selectByBusinessCode(businessCode); }

	@Logable(businessTag = "getInstitutionCodeByPayMethods" , level = Level.DEBUG)
    @Override
    public List<String> getInstitutionCodeByPayMethods(List<String> payMethodList) {
        return payMethodMapper.getInstitutionCodeByPayMethods(payMethodList);
    }

    @Logable(businessTag = "syncPayMethod")
	@Override
	public void syncPayMethod() {
		// TODO Auto-generated method stub
		List<PayMethod> payMethods = payMethodMapper.selectByState(PasConstant.State.Normal.code);
		String data = JSON.toJSONString(payMethods);
		try {
			kafkaTemplate.send(PAS_TOPIC, ALL_PAYMETHODS_KEY, data);
		} catch (Exception e) {
			// TODO: handle exception
			throw new AppException(PasCode.SEND_KAFKA_EXCEPTION.code, e);
		}
	}

    /**
     *  查询支付方式
     *  pay,withdraw
     */
    @Override
    @Logable(businessTag = "queryPayMethodByType")
    public List<PayMethod> queryPayMethodByType(String type) {
        List<PayMethod> payMethodList = new ArrayList<>();
        if ("pay".equals(type)) {
        	payMethodList = payMethodMapper.queryPayMethodByBusinessCategory(Constants.BusinessCategory.EFPS_BASIC_PAY_SERVICE.code);
        	List<PayMethod> payMethodList2 = payMethodMapper.queryPayMethodByBusinessCategory(Constants.BusinessCategory.EFPS_NOCARD_SERVICE.code);
            payMethodList.addAll(payMethodList2);
        } else if ("withdraw".equals(type)) {
            payMethodList = payMethodMapper.queryPayMethodByBusinessCategory(Constants.BusinessCategory.EFPS_ACCOUNT_SERVICE.code);
        }else if("all".equals(type)){
            payMethodList = payMethodMapper.selectAll();
        }
        return payMethodList;
    }
}
