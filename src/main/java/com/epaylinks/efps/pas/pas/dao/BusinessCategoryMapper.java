package com.epaylinks.efps.pas.pas.dao;

import java.util.List;

import com.epaylinks.efps.pas.cum.domain.CumCustomerInfo;
import org.apache.ibatis.annotations.Mapper;

import com.epaylinks.efps.pas.pas.domain.BusinessCategory;
@Mapper
public interface BusinessCategoryMapper {
    int deleteByPrimaryKey(String code);

    int insert(BusinessCategory record);

    int insertSelective(BusinessCategory record);

    BusinessCategory selectByPrimaryKey(String code);

    int updateByPrimaryKeySelective(BusinessCategory record);

    int updateByPrimaryKey(BusinessCategory record);
    
    List<BusinessCategory> queryAll();
    List<BusinessCategory> selectBySelective(BusinessCategory b);


    /*
        * 查询范围内的商户号对应类别
        */
       List<BusinessCategory> getBusinessCategoryByCodes(List<String> codes);
}