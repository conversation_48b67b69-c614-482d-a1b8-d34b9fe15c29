package com.epaylinks.efps.pas.pas.controller;

import com.epaylinks.efps.common.business.CommonResponse;
import com.epaylinks.efps.common.datadownload.annotation.DownloadAble;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.oplog.OpLog;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.pas.common.PageResult;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.pas.common.PasConstant;
import com.epaylinks.efps.pas.pas.controller.response.QueryQuotaResponse;
import com.epaylinks.efps.pas.pas.controller.response.UnionQuotaPayMentsResp;
import com.epaylinks.efps.pas.pas.controller.response.UnionQuotaResponse;
import com.epaylinks.efps.pas.pas.domain.UnionQuotaRecord;
import com.epaylinks.efps.pas.pas.service.UnionQuotaService;
import com.github.pagehelper.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/unionquota")
@Api(value = "UnionQuotaController", description = "银联备款调额")
public class UnionQuotaController {

    @Autowired
    private UnionQuotaService unionQuotaService;

    @PostMapping("/add")
    @Logable(businessTag = "add")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "额度调增", notes = "额度调增", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "amount", value = "调增金额(分)", required = true, dataType = "String", length = 50, paramType = "query"),
    })
    @OpLog(opMethod = "额度调增", opModule = "出金管理-银联出金管理-额度调增")
    public UnionQuotaResponse add(
            @RequestParam Long amount,
            @RequestHeader(value = "x-userid") Long userId) {
        UnionQuotaResponse response = new UnionQuotaResponse();
        try {
            unionQuotaService.changeQuota(PasConstant.UnionFundsettQuotaChangeType.ADD.code, amount, userId);
        } catch (Exception e) {
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
            return response;
        }
        return response;
    }

    @PostMapping("/reduce")
    @Logable(businessTag = "reduce")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "额度调减", notes = "额度调减", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "amount", value = "调增金额(分)", required = true, dataType = "String", length = 50, paramType = "query"),
    })
    @OpLog(opMethod = "额度调减", opModule = "出金管理-银联出金管理-额度调减")
    public UnionQuotaResponse reduce(
            @RequestParam Long amount,
            @RequestHeader(value = "x-userid") Long userId) {
        UnionQuotaResponse response = new UnionQuotaResponse();
        try {
            unionQuotaService.changeQuota(PasConstant.UnionFundsettQuotaChangeType.REDUCE.code, amount, userId);
        } catch (Exception e) {
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
            return response;
        }
        return response;
    }

    @RequestMapping(value = "/queryUnionQuotaRecord", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @Logable(businessTag = "UnionQuotaController.pageQuery")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "额度调增，调减查询", notes = "额度调增，调减查询", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "transactionNo", value = "流水号", required = false, dataType = "String", length = 50, paramType = "query"),
            @ApiImplicitParam(name = "trxCategory", value = "交易类别(调整类型，REDUCE:调减, ADD:调增", required = false, dataType = "String", length = 50, paramType = "query"),
//            @ApiImplicitParam(name = "state", value = "状态: 0：未处理 1：已处理 ", required = false, dataType = "int", length = 1, paramType = "query", valueRange = "{0,1,''}"),
            @ApiImplicitParam(name = "channelState", value = "状态(00:成功,01:失败,02:处理中)", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "beginCreateTime", value = "创建时间起始（示例：20180905）", required = false, dataType = "String", paramType = "query", length = 10),
            @ApiImplicitParam(name = "endCreateTime", value = "创建时间截止（示例：20180905）", required = false, dataType = "String", paramType = "query", length = 10),
            @ApiImplicitParam(name = "pageNum", value = "查询开始页", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "查询的页面大小", required = true, dataType = "int", paramType = "query")})
    public PageResult<UnionQuotaRecord> pageQuery(@RequestParam(value = "transactionNo", required = false) String transactionNo,
                                                  @RequestParam(value = "trxCategory", required = false) String trxCategory,
                                                  @RequestParam(value = "state", required = false) Integer state,
                                                  @RequestParam(value = "channelState", required = false) String channelState,
                                                  @RequestParam(value = "beginCreateTime", required = false) String beginCreateTime,
                                                  @RequestParam(value = "endCreateTime", required = false) String endCreateTime,
                                                  @RequestParam(value = "pageNum", required = true) Integer pageNum,
                                                  @RequestParam(value = "pageSize", required = true) Integer pageSize,
                                                  @RequestHeader(value = "x-userid", required = true) Long userId) {
        PageResult<UnionQuotaRecord> response = new PageResult<UnionQuotaRecord>();
        try {
            Date beginDate = null;
            Date endDate = null;
            if (StringUtil.isNotEmpty(beginCreateTime)) {
                if(beginCreateTime.length() == 8){
                    beginDate = DateUtils.parseDate(beginCreateTime, "yyyyMMdd");
                } else {
                    beginDate = DateUtils.parseDate(beginCreateTime, "yyyyMMddHHmmss");
                }
            }
            if (StringUtil.isNotEmpty(endCreateTime)) {
                endDate = DateUtils.parseDate(endCreateTime + " 23:59:59", "yyyyMMdd HH:mm:ss");
            }
            int endRowNo = pageNum * pageSize;
            int beginRowNo = (pageNum - 1) * pageSize + 1;
            Map map = new HashMap();
            map.put("transactionNo", transactionNo);
            map.put("trxCategory", trxCategory);
            map.put("channelState", StringUtil.isNotEmpty(channelState)?channelState:null);
//            map.put("state", state);
            map.put("beginCreateTime", beginDate);
            map.put("endCreateTime", endDate);
            map.put("beginRowNo", beginRowNo);
            map.put("endRowNo", endRowNo);
            response = unionQuotaService.pageQuery(map);
            return response;
        } catch (ParseException e) {
            response.setReturnCode(PasCode.DATE_FORMAT_ERROR.code);
            response.setReturnMsg(PasCode.DATE_FORMAT_ERROR.message);
            return response;
        } catch (Exception e) {
            response = new PageResult<UnionQuotaRecord>();
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
            return response;
        }
    }



    @RequestMapping(value = "/querUnionQuota", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @Logable(businessTag = "UnionQuotaController.querUnionQuota")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "额度查询", notes = "额度查询", httpMethod = "GET")
    public QueryQuotaResponse querUnionQuota() {
        QueryQuotaResponse response = new QueryQuotaResponse();
        try {
            response = unionQuotaService.querUnionQuota();
            return response;
        }  catch (Exception e) {
            response = new QueryQuotaResponse();
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
            return response;
        }
    }

    @RequestMapping(value = "/payMentsUnionQuota", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @Logable(businessTag = "huikuan")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "回款新增", notes = "回款新增", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "amount", value = "回款金額", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "insSeqHK", value = "头寸", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "cfdPurpose", value = "结转用途，01：结转利息收入、02：结转手续费收入、03：结转预付费卡现金赎回资金、04备付金交存、09：其他", required = false, dataType = "String", paramType = "query", valueRange = "{01,02,03,04,09}"),
            @ApiImplicitParam(name = "remark", value = "备注", required = false, dataType = "String", paramType = "query")
    })
    @OpLog(opMethod = "回款", opModule = "出金管理-银联出金管理-回款")
    public CommonResponse huikuan(
            @RequestParam(value = "amount", required = true) String amount,
            @RequestParam(value = "insSeqHK", required = true) String insSeqHK,
            @RequestParam(value = "cfdPurpose", required = false) String cfdPurpose,
            @RequestParam(value = "remark", required = false) String remark,
            @RequestHeader(value = "x-userid", required = true) String curUserId
    ) {
        CommonResponse response = null;
        try {
            Map map = new HashMap<>();
            map.put("amount",amount);
            map.put("insSeq",insSeqHK);
            map.put("cfdPurpose",cfdPurpose);
            map.put("remark",remark);
            map.put("curUserId",curUserId);
            return unionQuotaService.payMentsCreate(map);
        }  catch (Exception e) {
            response = new CommonResponse();
            if (e instanceof AppException) {
                response.setCode(((AppException) e).getErrorCode());
                response.setResult(((AppException) e).getErrorMsg());
            } else {
                response.setCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setResult(PasCode.SYSTEM_EXCEPTION.message);
            }
            return response;
        }
    }

    @RequestMapping(value = "/payMentsUnionQuotaList", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @Logable(businessTag = "UnionQuotaController.payMentsUnionQuotaList")
    @Exceptionable
    @Validatable
    @DownloadAble
    @ApiOperation(value = "回款列表", notes = "回款列表", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startTime", value = "回款開始時間（示例：20181105）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "回款結束始時間（示例：20181106）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "channel", value = "回款渠道", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "transactionNo", value = "回款流水號", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "state", value = "回款状态", required = false, dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "pageNum", value = "查询开始页", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "查询的页面大小", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "download", value = "是否导出，true/false", required = false, dataType = "Boolean", paramType = "query"),
            @ApiImplicitParam(name = "fileName", value = "导出文件名称", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "type", value = "导出文件类型，例如.csv", required = false, dataType = "String", paramType = "query")
    })
    public com.epaylinks.efps.common.util.page.PageResult<UnionQuotaPayMentsResp> payMentsUnionQuotaList(
            @RequestParam(value = "startTime", required = false) String startTime,
            @RequestParam(value = "endTime", required = false) String endTime,
            @RequestParam(value = "channel", required = false) String channel,
            @RequestParam(value = "transactionNo", required = false) String transactionNo,
            @RequestParam(value = "state", required = false) Integer state,
            @RequestParam(value = "pageNum", required = true) Integer pageNum,
            @RequestParam(value = "pageSize", required = true) Integer pageSize,
            @RequestParam(required = false) boolean download,
            @RequestParam(required = false) String fileName,
            @RequestParam(required = false) String type) {
        com.epaylinks.efps.common.util.page.PageResult<UnionQuotaPayMentsResp> pageResult = null;
        try {
            Map map = new HashMap<>();
            Date beginDate = null;
            Date endDate = null;
            int endRowNo = pageNum * pageSize;
            int beginRowNo = (pageNum - 1) * pageSize + 1;
            try{
                if (startTime != null && !startTime.equals("")) {
                    if (startTime.length() == 8) {
                        beginDate = DateUtils.parseDate(startTime+"000000", "yyyyMMddHHmmss");
                    } else if (endTime.length() == 14) {
                        beginDate = DateUtils.parseDate(startTime, "yyyyMMddHHmmss");
                    } else {
                        pageResult.setCode(PageResult.SUCCEE);
                        pageResult.setMessage("开始时间格式有误");
                        return pageResult;
                    }
                }
                if (endTime != null && !endTime.equals("")) {
                    if(endTime.length() == 8){
                        endDate = DateUtils.parseDate(endTime+"235959", "yyyyMMddHHmmss");
                    }else if(endTime.length() == 14){
                        endDate = DateUtils.parseDate(endTime, "yyyyMMddHHmmss");
                    }else{
                        pageResult.setCode(PageResult.SUCCEE);
                        pageResult.setMessage("结束时间格式有误");
                        return pageResult;
                    }
                }
            }catch (Exception e){
                //拋出異常
                pageResult.setCode(PasCode.SYSTEM_EXCEPTION.code);
                pageResult.setMessage(PasCode.SYSTEM_EXCEPTION.message);
                return pageResult;
            }
            map.put("beginDate",beginDate);
            map.put("endDate",endDate);
            map.put("channel",channel);
            map.put("transactionNo",transactionNo);
            map.put("state",state);
            map.put("beginRowNo",beginRowNo);
            map.put("endRowNo",endRowNo);
            map.put("download",download);
            pageResult =  unionQuotaService.queryPayMentsList(map);
        }  catch (Exception e) {
            pageResult = new com.epaylinks.efps.common.util.page.PageResult<UnionQuotaPayMentsResp>();
            if (e instanceof AppException) {
                pageResult.setCode(((AppException) e).getErrorCode());
                pageResult.setMessage(((AppException) e).getErrorMsg());
            } else {
                pageResult.setCode(PasCode.SYSTEM_EXCEPTION.code);
                pageResult.setMessage(PasCode.SYSTEM_EXCEPTION.message);
            }
        }
        return pageResult;
    }
}
