package com.epaylinks.efps.pas.pas.domain;

import java.util.Date;

public class ManagementData {
    /**
     * ID
     */
    private Long id;

    /**
     */
    private String uniqueId1;

    /**
     */
    private String uniqueId2;

    /**
     * 序列号
     */
    private String serialNum;

    /**
     * 备注
     */
    private String remark;

    /**
     * 操作人
     */
    private Long operator;

    /**
     * 发送状态【0：未发送；1：已发送；】
     */
    private String sendStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 发送时间
     */
    private Date sendTime;

    /**
     */
    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUniqueId1() {
        return uniqueId1;
    }

    public void setUniqueId1(String uniqueId1) {
        this.uniqueId1 = uniqueId1;
    }

    public String getUniqueId2() {
        return uniqueId2;
    }

    public void setUniqueId2(String uniqueId2) {
        this.uniqueId2 = uniqueId2;
    }

    public String getSerialNum() {
        return serialNum;
    }

    public void setSerialNum(String serialNum) {
        this.serialNum = serialNum;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Long getOperator() {
        return operator;
    }

    public void setOperator(Long operator) {
        this.operator = operator;
    }

    public String getSendStatus() {
        return sendStatus;
    }

    public void setSendStatus(String sendStatus) {
        this.sendStatus = sendStatus;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getSendTime() {
        return sendTime;
    }

    public void setSendTime(Date sendTime) {
        this.sendTime = sendTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}