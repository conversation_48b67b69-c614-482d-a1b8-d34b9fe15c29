package com.epaylinks.efps.pas.pas.service;

import com.epaylinks.efps.pas.pas.domain.OpLogReport;

import java.util.List;
import java.util.Map;

public interface OpLogReportService {

    /**
     * 审计指定日期的操作日志
     * @param dateStr
     * @return
     */
    String checkOpLogReportByDate(String dateStr);

    /**
     * 分页查询审计的操作记录
     * @param paramMap
     * @return
     */
    com.epaylinks.efps.common.util.page.PageResult<OpLogReport> OperationLogReport(Map<String, Object> paramMap);

    /**
     * 操作日志异常处理
     * @param opId
     * @param auditMsg
     * @param auditUser
     * @return
     */
    String logAudit(Long opId,String auditMsg,String auditUser);
}
