package com.epaylinks.efps.pas.pas.domain;

import com.alibaba.fastjson.JSON;

public class BusinessParamInstKey {
    private String code;

    
    
    private Long businessInstDBId;

    public BusinessParamInstKey() {
		super();
	}

	public BusinessParamInstKey(String code,Long businessInstDBId) {
		super();
		this.code = code;
		
		this.businessInstDBId=businessInstDBId;
	}

	public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    
    
    @Override
    public String toString() {
    	// TODO Auto-generated method stub
    	return JSON.toJSONString(this);
    }

	public Long getBusinessInstDBId() {
		return businessInstDBId;
	}

	public void setBusinessInstDBId(Long businessInstDBId) {
		this.businessInstDBId = businessInstDBId;
	}
}