package com.epaylinks.efps.pas.pas.service.impl;

import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.pas.common.PageResult;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.mch.dao.PasHolidayMapper;
import com.epaylinks.efps.pas.mch.domain.PasHoliday;
import com.epaylinks.efps.pas.pas.dao.HolidayMapper;
import com.epaylinks.efps.pas.pas.domain.Holiday;
import com.epaylinks.efps.pas.pas.service.HolidayService;
import com.epaylinks.efps.pas.pas.vo.HolidayRequest;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by adm on 2018/8/23.
 */
@Service
public class HolidayServiceImpl implements HolidayService {
    @Autowired
    private HolidayMapper holidayMapper;

    @Logable(businessTag = "HolidayServiceImpl.insert")
    public int insert(HolidayRequest vo) {
        if (vo.getHolidayList().size() < 0) {
            throw new AppException(PasCode.USER_NAME_EXIST.code, PasCode.USER_NAME_EXIST.message);
        }
        List<Holiday> req = checkHoliday(vo);
        //检查是否重复录入当年节假日
        try {
            SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
            SimpleDateFormat formatterYear = new SimpleDateFormat("yyyy");
            String year = formatterYear.format(formatter.parse(req.get(0).getDateStr()));
            Map paramMap = new HashMap();
            paramMap.put("dateYear", year);    //(String) map.get("year")
            paramMap.put("beginRowNo", 1);
            paramMap.put("endRowNo", 10);
            PageResult<Holiday> holidayPage = selectByPage(paramMap);
            if (holidayPage != null && holidayPage.getTotal() > 0) {
                throw new AppException(PasCode.DATE_RANGE_EXIST_ERROR.code, PasCode.DATE_RANGE_EXIST_ERROR.message);
            }
        } catch (ParseException e) {
            // TODO Auto-generated catch block
            throw new AppException(PasCode.INVALID_DATE_FORMAT.code, PasCode.INVALID_DATE_FORMAT.message);
        }
        return holidayMapper.batchSave(req);
    }

    @Logable(businessTag = "checkHoliday")
    private List<Holiday> checkHoliday(HolidayRequest request) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat formatterYear = new SimpleDateFormat("yyyy");
        List<Holiday> newHoliday = new ArrayList<Holiday>();
        Date currDate = new Date();
        Map map = new HashMap();
        for (String req : request.getHolidayList()) {
            String date = "";
            Holiday vo = new Holiday();
            String year = StringUtils.trimToEmpty((String) map.get("year"));
            try {
                if(req.length()!=8) {
                    throw new AppException(PasCode.DATE_FORMAT_ERROR.code,PasCode.DATE_FORMAT_ERROR.message);
                }
                if (!year.equals("") && !year.equals(formatterYear.format(formatter.parse(req)))) {
                    throw new AppException(PasCode.DATE_RANGE_ERROR.code,PasCode.DATE_RANGE_ERROR.message);
                }
                if (year.equals("")) {
                    map.put("year", formatterYear.format(formatter.parse(req)));
                }
                date = formatter.format(formatter.parse(req));
                vo.setDateStr(date);
                vo.setType("Festival");
                vo.setCreateTime(currDate);
                vo.setCreator(request.getCreator());
                newHoliday.add(vo);
            } catch (ParseException e) {
                // TODO Auto-generated catch block
                throw new AppException(PasCode.INVALID_DATE_FORMAT.code,PasCode.INVALID_DATE_FORMAT.message);
            }
        }

        return newHoliday;
    }

    @Logable(businessTag = "HolidayServiceImpl.update")
    public int update(HolidayRequest vo) {
        if (vo.getHolidayList().size() < 0) {
            throw new AppException(PasCode.USER_NAME_EXIST.code, PasCode.USER_NAME_EXIST.message);
        }
        SimpleDateFormat formatterYear = new SimpleDateFormat("yyyy");
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
        List<Holiday> req = checkHoliday(vo);
        try {
            String year = formatterYear.format(formatter.parse( req.get(0).getDateStr())) ;
            delete(year);
        } catch (ParseException e) {
            // TODO Auto-generated catch block
            throw new AppException(PasCode.INVALID_DATE_FORMAT.code,PasCode.INVALID_DATE_FORMAT.message);
        }
        return holidayMapper.batchSave(req);
    }

    @Logable(businessTag = "HolidayServiceImpl.delete")
    public int delete(String dateYear) {
        return holidayMapper.delete(dateYear);
    }

    @Logable(businessTag = "HolidayServiceImpl.selectByPage")
    public PageResult<Holiday> selectByPage(Map map) {
        int total = holidayMapper.selectCountHoliday(map);
        List list = holidayMapper.selectByPage(map);
        PageResult<Holiday> pagingResult = new PageResult<Holiday>();
        pagingResult.setTotal(total);
        pagingResult.setRows(list);
        return pagingResult;
    }


    @Logable(businessTag = "HolidayServiceImpl.selectByPage2")
    public PageResult<Holiday> selectByPage2(Map map) {
        int total = holidayMapper.selectCountHoliday(map);
        List list = holidayMapper.selectByPage2(map);
        PageResult<Holiday> pagingResult = new PageResult<Holiday>();
        pagingResult.setTotal(total);
        pagingResult.setRows(list);
        return pagingResult;
    }

    @Override
    public PasHoliday selectByDate(String date) {
        return holidayMapper.selectByDate(date);
    }
}
