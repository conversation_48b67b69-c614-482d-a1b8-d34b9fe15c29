package com.epaylinks.efps.pas.pas.service;

import com.epaylinks.efps.pas.pas.vo.UnionCardBinFtpVo;

import java.util.List;

public interface UnionCardBinFtpService {

    String UNION_BRAND = "1";   //银联品牌

    String NOT_UNION_BRAND = "2";   //非银联品牌

    String EXP_BRAND = "3";   //非银联品牌

    String NOT_EXP_BRAND = "4";   //非银联品牌

    String PARENT_TYPE_LY = "1";    //路由卡表大类

    String PARENT_TYPE_YW = "2";    //业务卡表大类

    String ALLBIN = "ALLBIN";   //银联标准卡表

    String NONBIN = "NONBIN";   //非标卡卡表

    String YWBIN = "YWBIN";   //业务卡表

    String TFRBIN = "TFRBIN";   //转账卡表

    String NMGBIN = "NMGBIN";   //农民工卡表

    String DWBIN = "DWBIN";   //单位结算卡卡表

    String OCBIN = "OCBIN";   //境外发行银联卡卡表

    /**
     * 从银联ftp下载指定日期的卡bin信息
     * @param loadDate
     */
    List<UnionCardBinFtpVo> downCardBinFromFtp(String loadDate,String loadFrom);

    /**
     * 将ftp下载的文件加载到系统中
     * @param ftpFileList
     * @param loadFrom
     */
    void loadCardBinIntoSystem(List<UnionCardBinFtpVo> ftpFileList,String loadFrom);

    /**
     * 定时加载银联卡bin
     * @param loadDate
     */
    void loadCardBinForJob(String loadDate);
}
