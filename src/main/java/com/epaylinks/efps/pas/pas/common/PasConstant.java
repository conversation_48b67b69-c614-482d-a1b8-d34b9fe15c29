package com.epaylinks.efps.pas.pas.common;

import com.epaylinks.efps.common.util.Constants;

/**
 * <AUTHOR> 5m
 * @Date : 2017/12/27 18:16
 * @Description :
 */
public interface PasConstant extends Constants {

    public static final String SYSTEMID = "pas";

    public enum RatioMode {

        Single("1", "单笔固定费率"),
        Rate("2", "按交易金额比例");

        public final String code;
        public final String comment;

        RatioMode(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }
    }

     enum WarnLevel {

        NOMAL("01", "一般"),
        IMPORTANT("02", "重要"),
        URGENT("03","紧急");
        public final String code;
        public final String comment;

         WarnLevel(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }
    }


    public enum State {

        Abnormal("0", "不正常"),
        Normal("1", "正常"),
        Deleted("2", "已删除");

        public final String code;
        public final String comment;

        State(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }
    }

    /**
     * 告警状态
     */
     enum WranStatus {

        FINISH("00", "已处理"),
        PROCESSING("01", "未处理");

        public final String code;
        public final String comment;

        WranStatus(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }
    }


    public enum AllowNull {

        Yes("1", "是"),
        No("0", "否");

        public final String code;
        public final String comment;

        AllowNull(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }
    }

    //进件记录状态
    public enum InletState {
        Failure("0", "数据校验失败"),
        Success("1", "成功"),
        Processing("2", "处理中"),
        Fail("3", "失败");

        public final String code;
        public final String comment;

        InletState(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }
    }

    public enum IsFile {

        Yes("1", "是"),
        No("0", "否");

        public final String code;
        public final String comment;

        IsFile(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }
    }

    /**
     * 额度调整类型（银联资金结算）
     */
    enum UnionFundsettQuotaChangeType {
        REDUCE("REDUCE", "调减"),
        ADD("ADD", "调增");
        public final String code;
        public final String comment;
        UnionFundsettQuotaChangeType(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }
    }


    enum openStautsEnum{

        STOP("0","关闭"),
        OPEN("1","开启");

        public String code;
        public String comment;
        openStautsEnum(String code,String comment){
            this.code = code;
            this.comment = comment;
        }
    }

    enum executeStautsEnum{

        DONGING("0","未执行"),
        FINISHED("1","已执行");

        public String code;
        public String comment;
        executeStautsEnum(String code,String comment){
            this.code = code;
            this.comment = comment;
        }
    }


    enum warnTypeEnum{

        TIMING("0","定时"),
        LOOP("1","循环");

        public String code;
        public String comment;
        warnTypeEnum(String code,String comment){
            this.code = code;
            this.comment = comment;
        }
    }
    
    /**
     * 用户类型
     * <AUTHOR>
     *
     */
    enum UserType{
        NORMAL(new Short("0"),"内部管理员"),
        COMPANY(new Short("1"),"分公司管理员"),
        SALES(new Short("2"),"业务员");
    	
        public Short code;
        public String comment;
        UserType(Short code,String comment){
            this.code = code;
            this.comment = comment;
        }
    }
    
    /**
     * 营销团队角色
     * <AUTHOR>
     *
     */
    enum MarketRole{

        COMPANY("10","分公司管理员"),
        SALES("11","业务员");

        public String code;
        public String comment;
        MarketRole(String code,String comment){
            this.code = code;
            this.comment = comment;
        }
    }
}
