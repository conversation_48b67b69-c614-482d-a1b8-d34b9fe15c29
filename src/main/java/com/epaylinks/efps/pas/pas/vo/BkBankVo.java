package com.epaylinks.efps.pas.pas.vo;

import com.epaylinks.efps.pas.pas.model.BkBranch;

import java.util.List;

public class BkBankVo {
    private String bankName;
    private String institutionCode;
    private List<BkBranch> bkBranchList;

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getInstitutionCode() {
        return institutionCode;
    }

    public void setInstitutionCode(String institutionCode) {
        this.institutionCode = institutionCode;
    }

    public List<BkBranch> getBkBranchList() {
        return bkBranchList;
    }

    public void setBkBranchList(List<BkBranch> bkBranchList) {
        this.bkBranchList = bkBranchList;
    }
}
