package com.epaylinks.efps.pas.pas.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;

@Data
public class DkteRecordVo {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", dataType = "Long")
    private Long dkteId;

    /**
     * 商户编号
     */
    @ApiModelProperty(value = "商户编号", dataType = "String")
    private String customerCode;

    /**
     * 商户名称
     */
    @ApiModelProperty(value = "商户名称", dataType = "String")
    private String customerName;

    /**
     * 调额类型：1增额，2减额
     */
    @ApiModelProperty(value = "调额类型：1增额，2减额", dataType = "String")
    private String adjustType;

    /**
     * 调额事由：BZFR，补增分润差额；KJSD，扣减发票税点；KJFR，扣减分润差额；
     */
    @ApiModelProperty(value = "调额事由：BZFR，补增分润差额；KJSD，扣减发票税点；KJFR，扣减分润差额；", dataType = "String")
    private String adjustReason;

    /**
     * 调整金额，单位分
     */
    @ApiModelProperty(value = "调整金额，单位分", dataType = "Long")
    private Long adjustAmout;

    /**
     * 附件ID
     */
    @ApiModelProperty(value = "附件ID", dataType = "String")
    private String attachmentUid;

    /**
     * 附件名称
     */
    @ApiModelProperty(value = "附件名称", dataType = "String")
    private String attachmentName;

    /**
     * 附件url
     */
    @ApiModelProperty(value = "附件url", dataType = "String")
    private String attachmentUrl;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", dataType = "String")
    @Size(max = 200,message = "备注长度超过限制")
    private String remark;

    private String origFreezeNo;

    public Long getDkteId() {
        return dkteId;
    }

    public void setDkteId(Long dkteId) {
        this.dkteId = dkteId;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getAdjustType() {
        return adjustType;
    }

    public void setAdjustType(String adjustType) {
        this.adjustType = adjustType;
    }

    public String getAdjustReason() {
        return adjustReason;
    }

    public void setAdjustReason(String adjustReason) {
        this.adjustReason = adjustReason;
    }

    public Long getAdjustAmout() {
        return adjustAmout;
    }

    public void setAdjustAmout(Long adjustAmout) {
        this.adjustAmout = adjustAmout;
    }

    public String getAttachmentUid() {
        return attachmentUid;
    }

    public void setAttachmentUid(String attachmentUid) {
        this.attachmentUid = attachmentUid;
    }

    public String getAttachmentName() {
        return attachmentName;
    }

    public void setAttachmentName(String attachmentName) {
        this.attachmentName = attachmentName;
    }

    public String getAttachmentUrl() {
        return attachmentUrl;
    }

    public void setAttachmentUrl(String attachmentUrl) {
        this.attachmentUrl = attachmentUrl;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }


}
