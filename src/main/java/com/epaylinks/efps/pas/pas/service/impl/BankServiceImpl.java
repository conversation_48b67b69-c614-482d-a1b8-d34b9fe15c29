package com.epaylinks.efps.pas.pas.service.impl;

import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.util.StringUtils;
import com.epaylinks.efps.pas.acl.dao.UserMapper;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.pas.dao.BankMapper;
import com.epaylinks.efps.pas.pas.domain.Bank;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
public class BankServiceImpl {
    @Autowired
    private BankMapper bankMapper;
    @Autowired
    private UserMapper userMapper;


    public void saveBank(Bank bank,String method) {
        switch (method){
            case "add": insertRecord(bank); break;
            case "edit": editRecord(bank); break;
            default: throw new AppException(PasCode.SYSTEM_EXCEPTION.code,PasCode.SYSTEM_EXCEPTION.message);
        }
    }

    private void insertRecord(Bank bank){
        Bank record = bankMapper.selectByPrimaryKey(bank.getLbnkCd());
        if(record == null) {
            bank.setCreateTime(new Date());
            bankMapper.insertSelective(bank);
        }else {
            throw new AppException(PasCode.RECORD_EXIST.code, PasCode.RECORD_EXIST.message);
        }
    }

    private void editRecord(Bank bank){
        Bank record = bankMapper.selectByPrimaryKey(bank.getLbnkCd());
        if(record != null){
            bank.setUpdateTime(new Date());
            bankMapper.updateByPrimaryKeySelective(bank);
        }else {
            throw new AppException(PasCode.RECORD_NOT_EXIST.code, PasCode.RECORD_NOT_EXIST.message);
        }
    }

    public void enableBank(String bankId,String flag, String userId) {
        Bank record = bankMapper.selectByPrimaryKey(bankId);
        if(record == null) {
            throw new AppException(PasCode.RECORD_NOT_EXIST.code,PasCode.RECORD_NOT_EXIST.message);
        }else {
            record.setUpdateTime(new Date());
            record.setFlag(flag);
            record.setOperatorId(userId);
            bankMapper.updateByPrimaryKeySelective(record);
        }
    }

    public int queryCount(Map map){
        return bankMapper.selectCountByMap(map);
    }

    public List<Bank> bankPageQuery(Map map){
        List<Bank> bankList = bankMapper.selectBankPage(map);
        bankList.forEach(bank -> {
            if(!StringUtils.isBlank(bank.getOperatorId())){
                bank.setOperatorId(userMapper.selectByPrimaryKey(Long.valueOf(bank.getOperatorId())).getName());
            }
        });
        return bankList;
    }
}
