package com.epaylinks.efps.pas.pas.dao;

import com.epaylinks.efps.pas.pas.domain.Application;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@Mapper
@Transactional
public interface ApplicationMapper {
    int deleteByPrimaryKey(Long id);

    int insert(Application record);

    int insertSelective(Application record);

    @Transactional(readOnly = true)
    Application selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(Application record);

    int updateByPrimaryKey(Application record);

    @Transactional(readOnly = true)
    List<Application> selectBySelective(Application record);

    String checkUpValueIsNotNull(Map map);

    String getFileUrlId(Map map);

    String getInstitutionMCC(Map map);
}