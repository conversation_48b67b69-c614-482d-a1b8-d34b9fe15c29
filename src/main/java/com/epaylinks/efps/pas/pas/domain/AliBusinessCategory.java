package com.epaylinks.efps.pas.pas.domain;

public class AliBusinessCategory {
    /**
     * MCC
     */
    private String mcc;

    /**
     * 一级类目编码
     */
    private String level1Code;

    /**
     * 一级类目名称
     */
    private String level1Name;

    /**
     * 一级类目编码
     */
    private String level2Code;

    /**
     * 二级类目名称
     */
    private String level2Name;

    /**
     * 三级类目编码
     */
    private String level3Code;

    /**
     * 三级类目名称
     */
    private String level3Name;

    /**
     * 经营类目编码，用于上游进件
     */
    private String categoryCode;
    
    /**
     * 映射银联MCC
     */
    private String unionMcc; 

    public String getMcc() {
        return mcc;
    }

    public void setMcc(String mcc) {
        this.mcc = mcc;
    }

    public String getLevel1Code() {
        return level1Code;
    }

    public void setLevel1Code(String level1Code) {
        this.level1Code = level1Code;
    }

    public String getLevel1Name() {
        return level1Name;
    }

    public void setLevel1Name(String level1Name) {
        this.level1Name = level1Name;
    }

    public String getLevel2Code() {
        return level2Code;
    }

    public void setLevel2Code(String level2Code) {
        this.level2Code = level2Code;
    }

    public String getLevel2Name() {
        return level2Name;
    }

    public void setLevel2Name(String level2Name) {
        this.level2Name = level2Name;
    }

    public String getLevel3Code() {
        return level3Code;
    }

    public void setLevel3Code(String level3Code) {
        this.level3Code = level3Code;
    }

    public String getLevel3Name() {
        return level3Name;
    }

    public void setLevel3Name(String level3Name) {
        this.level3Name = level3Name;
    }

    public String getCategoryCode() {
        return categoryCode;
    }

    public void setCategoryCode(String categoryCode) {
        this.categoryCode = categoryCode;
    }

    public String getUnionMcc() {
        return unionMcc;
    }

    public void setUnionMcc(String unionMcc) {
        this.unionMcc = unionMcc;
    }
    
    
}