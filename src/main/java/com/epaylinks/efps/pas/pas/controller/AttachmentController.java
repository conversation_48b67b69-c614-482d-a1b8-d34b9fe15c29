package com.epaylinks.efps.pas.pas.controller;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.pas.common.FsCommonService;
import com.epaylinks.efps.pas.common.PasCode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;
import java.util.Objects;

@Api(value = "AttachmentController", description = "附件上传控制器")
@RestController
@RequestMapping(value = "/attachment")
public class AttachmentController {

    @Autowired
    private CommonLogger logger;

    @Autowired
    private FsCommonService fsCommonService;

    @Validatable
    @Exceptionable
    @Logable(businessTag = "AttachmentController.upload")
    @ApiOperation(value ="上传附件文件")
    @PostMapping("/upload")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "businessType", value = "业务类型编码（acct_quota:账户调账附件, 01:身份证正面, 02:身份证背面, 03:组织机构代码, 04:营业执照, 05:店铺招牌, 0601-0605:店铺内景, 0701:合作协议1, 0702:合作协议2, " +
                    "08:授权委托书, 09:签约银行卡图片, 10:手持证件照, 11:入境证明, 12:银行账户信息, 13:其它证件/文件(或1301-1305)）",
                    required = true, dataType = "String", paramType = "query"),
    })
    public CommonOuterResponse uploadFile(
            @RequestPart("file")MultipartFile file,
            @RequestParam("businessType")String businessType){
        try{
            if(Objects.isNull(file)){
                throw new AppException(PasCode.DATA_ERROR.code,  "上传附件不能为空");
            }
            if(Objects.isNull(file.getOriginalFilename()) || file.getOriginalFilename().length()>100){
                throw new AppException(PasCode.DATA_ERROR.code,  "上传附件名称长度过长");
            }
            logger.printMessage("pas上传附件，业务类型："+businessType+"，文件类型"+file.getContentType());
            String remark;
            if("acct_quota".equals(businessType)){
                remark = "调额附件";
            }else if("01".equals(businessType) || "02".equals(businessType) ){
                remark = "身份证件";
            }else if("03".equals(businessType) || "04".equals(businessType)){
                remark = "营业执照";
            }else if("05".equals(businessType) || businessType.startsWith("06")){
                remark = "店铺信息";
            }else if("0701".equals(businessType) || "0702".equals(businessType)){
                remark = "合作协议";
            }else if("08".equals(businessType) ){
                remark = "授权委托书";
            }else if("09".equals(businessType) || "12".equals(businessType) ){
                remark = "银行账户信息";
            }else if("10".equals(businessType)){
                remark = "手持证件照";
            }else if("11".equals(businessType)){
                remark = "入境证明";
            }else if(businessType.startsWith("13")){
                remark = "其它证件/文件";
            }else if("management_data".equals(businessType)){
                remark = "经管数据";
            }else {
                throw new AppException(PasCode.DATA_ERROR.code,  "上传附件业务类型有误");
            }
            String uid = fsCommonService.uploadFile(file,businessType,remark);
            Map<String, String> dataMap = fsCommonService.queryFileUrlForDownload(uid,true);
            return CommonOuterResponse.success(dataMap);
        }catch(Exception e) {
            logger.printMessage("上传附件文件错误：" + e.getMessage());
            logger.printLog(e);
            if (e instanceof AppException) {
                return CommonOuterResponse.fail(((AppException) e).getErrorCode(), ((AppException) e).getErrorMsg());
            } else {
                logger.printLog(e);
                return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
            }
        }
    }

    @Logable(businessTag = "getUrl")
    @ApiOperation(value ="获取附件Url")
    @GetMapping("/getUrl")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uniqueId", value = "下载附件uniqueId", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pathForDownload", value = "用于下载还是展示，show展示，download下载", required = true, dataType = "String", paramType = "query"),
    })
    public CommonOuterResponse getUrl(@RequestParam("uniqueId")String uniqueId,@RequestParam("pathForDownload")String pathForDownload ){
        try{
            //文件路径
            boolean isDownload = true;
            if("show".equals(pathForDownload)){
                isDownload = false;
            }
            Map<String, String> dataMap = fsCommonService.queryFileUrlForDownload(uniqueId,isDownload);
            return CommonOuterResponse.success(dataMap);
        }catch(Exception e) {
            if (e instanceof AppException) {
                return CommonOuterResponse.fail(((AppException) e).getErrorCode(), ((AppException) e).getErrorMsg());
            } else {
                logger.printLog(e);
                return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
            }
        }
    }
}
