package com.epaylinks.efps.pas.pas.dao;

import com.epaylinks.efps.pas.mch.domain.CustomerBusinessInfo;
import com.epaylinks.efps.pas.pas.domain.PayMethod;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;


@Mapper
@Transactional
public interface PayMethodMapper {

    int deleteByPrimaryKey(Long id);

    int insert(PayMethod record);

    int insertSelective(PayMethod record);

    @Transactional
    PayMethod selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PayMethod record);

    int updateByPrimaryKey(PayMethod record);

    @Transactional(readOnly = true)
    List<PayMethod> selectBySelective(PayMethod record);

    @Transactional(readOnly = true)
    List<PayMethod> selectByBusinessCode(String businessCode);

    @Transactional(readOnly = true)
    List<Map<String, Object>> selectByBusinessCodes(List<CustomerBusinessInfo> customerBusinessInfos);

    List<String> getInstitutionCodeByPayMethods(List<String> payMethodList);

    List<PayMethod> selectByState(@Param("state") String state);
    
    List<String> selectByPayMethodCodes(@Param("payMethodCodes")List<String> payMethodCodes);

    List<PayMethod> queryPayMethodByBusinessCategory(@Param("businessCategory") String businessCategory);

    List<PayMethod> selectAll();
}