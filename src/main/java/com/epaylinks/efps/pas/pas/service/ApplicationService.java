package com.epaylinks.efps.pas.pas.service;

import com.epaylinks.efps.pas.pas.domain.Application;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 5m
 * @Date : 2017/12/28 17:03
 * @Description :
 */
public interface ApplicationService {

    int deleteByPrimaryKey(Long id);

    int insert(Application record);

    int insertSelective(Application record);

    Application selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(Application record);

    int updateByPrimaryKey(Application record);

    List<Application> selectBySelective(Application record);

    String checkUpValueIsNotNull(Map map);

    String getFileUrlId(Map map);

    String getInstitutionMCC(Map map);
}
