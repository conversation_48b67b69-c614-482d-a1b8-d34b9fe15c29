package com.epaylinks.efps.pas.pas.dao;

import com.epaylinks.efps.pas.pas.controller.response.LoginReportResponse;
import com.epaylinks.efps.pas.pas.domain.LoginReport;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;
import java.util.Map;

@Mapper
public interface LoginReportMapper {
    int deleteByPrimaryKey(Long reportId);

    int insert(LoginReport record);

    int insertSelective(LoginReport record);

    LoginReport selectByPrimaryKey(Long reportId);

    int updateByPrimaryKeySelective(LoginReport record);

    int updateByPrimaryKey(LoginReport record);

    List<LoginReportResponse> reportPage(Map map);

    Integer countReportPage(Map map);

    Long queryReportSeq();
}