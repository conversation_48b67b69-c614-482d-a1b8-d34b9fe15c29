package com.epaylinks.efps.pas.pas.vo;

public class BkBranchRequest {
    private String lbnkNo;
    private String lbnkNm;
    private String lbnkCd;
    private String flag;
    private String admProv;
    private String admCity;
    private String beginTime;
    private String endTime;

    public String getLbnkNo() {
        return lbnkNo;
    }

    public void setLbnkNo(String lbnkNo) {
        this.lbnkNo = lbnkNo;
    }

    public String getLbnkNm() {
        return lbnkNm;
    }

    public void setLbnkNm(String lbnkNm) {
        this.lbnkNm = lbnkNm;
    }

    public String getLbnkCd() {
        return lbnkCd;
    }

    public void setLbnkCd(String lbnkCd) {
        this.lbnkCd = lbnkCd;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }

    public String getAdmProv() {
        return admProv;
    }

    public void setAdmProv(String admProv) {
        this.admProv = admProv;
    }

    public String getAdmCity() {
        return admCity;
    }

    public void setAdmCity(String admCity) {
        this.admCity = admCity;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
}
