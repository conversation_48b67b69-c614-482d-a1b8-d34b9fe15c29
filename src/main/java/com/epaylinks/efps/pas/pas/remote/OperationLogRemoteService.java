package com.epaylinks.efps.pas.pas.remote;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.epaylinks.efps.common.business.CommonResponse;

@FeignClient(value = "pas")
public interface OperationLogRemoteService {

	@RequestMapping(value= "/logInsert", method = RequestMethod.POST)
	public CommonResponse logInsert(
			@RequestParam("userId") Long userId,
			@RequestParam("opModule") String opModule,
			@RequestParam("opMethod") String opMethod,
			@RequestParam("opContent") String opContent,
			String opTime);
	
}
