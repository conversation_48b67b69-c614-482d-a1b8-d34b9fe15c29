package com.epaylinks.efps.pas.pas.service;

import com.epaylinks.efps.pas.pas.domain.AppliAddPara;

import java.util.List;

/**
 * <AUTHOR> 5m
 * @Date : 2017/12/28 17:03
 * @Description :
 */
public interface AppliAddParaService {

    public int deleteByPrimaryKey(Long id);

    public int insert(AppliAddPara record);

    public int insertSelective(AppliAddPara record);

    public AppliAddPara selectByPrimaryKey(Long id);

    public int updateByPrimaryKeySelective(AppliAddPara record);

    public int updateByPrimaryKey(AppliAddPara record);

    public List<AppliAddPara> selectBySelective(AppliAddPara record);

}
