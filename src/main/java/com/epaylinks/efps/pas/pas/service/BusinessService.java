package com.epaylinks.efps.pas.pas.service;

import java.util.List;
import java.util.Map;

import com.epaylinks.efps.common.business.CodeNameResponse;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.pas.pas.controller.dto.CustBusinessPriceDTO;
import com.epaylinks.efps.pas.pas.domain.ApplyBusiness;
import com.epaylinks.efps.pas.pas.domain.Business;
import com.epaylinks.efps.pas.pas.domain.BusinessTemplate;

/**
 * <AUTHOR> 5m
 * @Date : 2017/12/27 14:36
 * @Description :
 */
public interface BusinessService {

    public int deleteByPrimaryKey(Long id);

    public int insert(Business record, String payMethod);

    public int insertSelective(Business record);

    public Business selectByPrimaryKey(Long id);

    public int updateByPrimaryKeySelective(Business record, String payMethod);

    public int updateByPrimaryKey(Business record);

    public List<Business> selectBySelective(Business record);

    public List<Business> selectByPage(Map map);

    public List<Business> getBusinessByCodes(List<String> codes);

    public Business optimal(String payMethodCode, String amount);

    public void syncAllNormalBusiness();

    public List<Business> getBusinessByCategory(String businessCategory);

    /**
     * 通过业务标签查询业务编码
     *
     * @param businessLabel
     * @return
     */
    public List<String> getBusinessCodeByLabel(String businessLabel);

    public Business selectByCodeOrName(Map map);

    public int modifyBusinessCategory();

    //根据业务查所属业务类别
    public List<Business> getBusinessCatetoryByBusiness(String businessCode);

    public List<Business> selectBusinessByGroupOrTemplate(Map map);

    public List<Business> selectByState();

    List<ApplyBusiness> selectApplyBusinessByDisplayCode(String displayCode);

    List<ApplyBusiness> selectApplyBusinessByCode(String code);

    List<Business> getBuseinssByScope(String scope);

    PageResult<CodeNameResponse> fuzzyQuery(String keyword);

    List<Business> selectByCodeList(List<String> codes);

    List<CustBusinessPriceDTO> selectCustBusinessPrice(Long customerId);

    List<Business> selectAll();
}
