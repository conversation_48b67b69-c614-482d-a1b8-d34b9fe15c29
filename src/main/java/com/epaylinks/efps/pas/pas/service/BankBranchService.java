package com.epaylinks.efps.pas.pas.service;


import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.pas.pas.controller.dto.BankBranchDTO;
import com.epaylinks.efps.pas.pas.domain.BankBranch;

import java.util.List;
import java.util.Map;

/**
 * 联行号管理接口
 *
 * <AUTHOR>
 */
public interface BankBranchService {


//    public Long selectRefundAmountByCustomerOrgiTransactionNo(String customerCodeFromHeader, String orgiOutTradeNo, String orgiTransactionNo);

//    public PageResult<Cnaps> pageQueryRefundApply(Integer pageNum, Integer pageSize, String beginCreateTime, String endCreateTime,
//                                                        String customerCode, String customerName, Long userId, String refundTransactionNo,
//                                                        String orgiOutTradeNo, String orgiTransactionNo,
//                                                        String auditState, String refundState, String refundApplyNo);

//    public Cnaps refundBatchApprove(RefundApply refundApply, long userId, String customerCodeFromHeader);


    public int insert(BankBranch vo);

    public int updateByPrimaryKey(BankBranch vo);

    public int deleteByPrimaryKey(String lbnkNo) ;


    public PageResult<BankBranch> queryBranch(Integer pageNum, Integer pageSize, String lbnkNo, String lbnkNm,
                                                String lbnkCd, String corpOrg, String provCd, String cityCd
       ) ;

    List<BankBranchDTO> queryBankBranchByOptions(String provinceCode, String cityCode, String bankIcon, String bankBranchName);
    
    String queryBankBranchNameByKey(String lbnkNo);

    List<BankBranch> pageQuery(Map map) ;

    int queryCount(Map map);

    void enableBankBranch(String lbnkNo,String flag, String userId);

    void saveBankBranch(BankBranch bankBranch, String method);
}
