package com.epaylinks.efps.pas.pas.service.impl;

import com.alibaba.fastjson.JSON;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.pas.pas.common.PasConstant;
import com.epaylinks.efps.pas.pas.domain.AppliAddPara;
import com.epaylinks.efps.pas.pas.domain.Application;
import com.epaylinks.efps.pas.pas.domain.ApplicationRecord;
import com.epaylinks.efps.pas.pas.domain.BizPayMethod;
import com.epaylinks.efps.pas.pas.service.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.util.*;

/**
 * <AUTHOR> 5m
 * @Date : 2018/1/2 14:30
 * @Description :
 */
@Service
public class MchinletServiceImpl implements MchinletService{

    @Autowired
    ApplicationService applicationService;

    @Autowired
    AppliAddParaService appliAddParaService;

    @Autowired
    ApplicationRecordService applicationRecordService;

    @Autowired
    BizPayMethodService bizPayMethodService;

    @Autowired
    PayMethodService payMethodService;

    @Autowired
    MchinletInletService mchinletInletService;

    @Autowired
    FSService fsService;

    @Value("${downFile.effectiveMinute}")
    private String effectiveMinute;

    @Value("${downFile.frequency}")
    private String frequency;

    @Autowired
    private SequenceService sequenceService;

    /**
     * 进件数据检查: true-通过 false-检查不通过
     */
    boolean inLetCheck = false;

    /**
     * 进件数据
     */
    Map inletMsg = new HashMap();

    static final String FS_TYPE = "download";

    public static Logger logger = Logger.getLogger(MchinletServiceImpl.class);


    /**
     * 进件数据检查
     * @param institutionCode
     * @param customerCode
     * @return
     */
    @Override
    @Logable(businessTag = "judge")
    public String judge(String institutionCode, String customerCode){

        inLetCheck = true;

        //获取改上游机构需要的进件字段
        Application application = new Application();
        application.setInstitutionCode(institutionCode);
        application.setAllowNull(PasConstant.AllowNull.No.code);
        List<Application> applicationList = applicationService.selectBySelective(application);

//        Map result = new HashMap();
//        result.put("resultCode","Success");
//        Map errorHap = new HashMap();


        //遍历每个商户号
//        String[] customerCodeArray = customerCodes.split(",");
//        for (int i = 0; i < customerCodeArray.length ; i++) {
//            String customerCode = customerCodeArray[i];
            List<String> errors = new ArrayList<String>();

            //遍历每个商户号检查所需字段
            for (Application appli : applicationList) {

                String[] efpsKey = appli.getEfpsKey().split("\\.");
                String tableName = efpsKey[0];
                String columnName = efpsKey[1];

                //查PAS_APPLI_ADD_PARA 表
                if(tableName.toUpperCase().indexOf("PAS_APPLI_ADD_PARA")>-1){
                    AppliAddPara appliAddPara = new AppliAddPara();
                    appliAddPara.setCustomerCode(customerCode);
                    appliAddPara.setEfpsKey(columnName);

                    List<AppliAddPara> appliList = appliAddParaService.selectBySelective(appliAddPara);
                    if(appliList.size()>0){
                        if(StringUtils.isBlank(appliList.get(0).getValue())){
                            errors.add(MessageFormat.format("{0}表{1}字段无值", tableName , columnName));
                        }else{

                            inletMsg.put( appli.getInstitutionKey() ,appliList.get(0).getValue());
                        }
                    }else{
                        errors.add(MessageFormat.format("{0}表{1}字段不存在", tableName , columnName));
                    }

                //查PAS_CUSTOMER_ATTACHMENT_INFO表
                /**ATTACHMENT_CODE：附件编码（01:身份证正面, 02:身份证背面, 03:组织机构代码, 04:营业执照, 05:店铺招牌, 06:店铺内景,
                   07:合作协议, 08:授权委托书, 09:签约银行卡图片, 10:手持证件照, 11:入境证明, 12:银行账户信息, 13:其它证件/文件）
                 **/
                }else if (tableName.toUpperCase().indexOf("PAS_CUSTOMER_ATTACHMENT_INFO")>-1){
                    String attachmentCode  ;
                    if(efpsKey.length>=3){
                        attachmentCode = efpsKey[2];
                    }else {
                        errors.add(MessageFormat.format("{0}表{1}字段未设置下载URL的附件编码", tableName , columnName));
                        continue;
                    }

                    Map map = new HashMap();
                    map.put("attachmentCode", attachmentCode);
                    map.put("customerCode", customerCode);
                    String uniqueId = applicationService.getFileUrlId(map);
                    if(StringUtils.isBlank(uniqueId)){
                        errors.add(MessageFormat.format("{0}表{1}字段未设置下载URL的附件编码", tableName , columnName));
                        continue;
                    }
                    Map mapResult = fsService.filePath(uniqueId, effectiveMinute, frequency, FS_TYPE);
                    /*{
                        "resultCode": "0000",
                        "resultMsg": "成功",
                        "filePath": "http://172.20.4.80:8170/File/2b68b26357234bcba36209add81d8b7d?fileAccessToken=1dd73c2f949e4078b9973035dc7ae2db"
                    }*/
                    if(mapResult!= null && mapResult.get("resultCode").equals("0000")&& StringUtils.isNotBlank((String)mapResult.get("filePath"))){
                        inletMsg.put( appli.getInstitutionKey() , mapResult.get("filePath") );
                    }else{
                        errors.add(MessageFormat.format("{0}表{1}字段获取不到下载URL", tableName , columnName));
                    }

                 //查 PAS_TRADE_CATEGORY 表
                }else if (tableName.toUpperCase().indexOf("PAS_TRADE_CATEGORY")>-1){

                    Map map = new HashMap();
                    map.put("columnName", columnName);
                    map.put("customerCode", "'"+customerCode+"'");
                    String institutionMCC = applicationService.getInstitutionMCC(map);
                    if(StringUtils.isBlank(institutionMCC)){
                        errors.add(MessageFormat.format("{0}表{1}字段字段无值", tableName , columnName));
                        continue;
                    }else{
                        inletMsg.put(appli.getInstitutionKey() , institutionMCC );
                    }
                }else{//查其他表
                    Map<String, String> map = new HashMap<String, String>();
                    map.put("tableName", tableName);
                    map.put("columnName", columnName);
                    map.put("customerCode", "'"+customerCode+"'");

                    if(StringUtils.isBlank(applicationService.checkUpValueIsNotNull(map))){
                        errors.add(MessageFormat.format("{0}表{1}字段无值", tableName , columnName));
                    }else{
                        //判断数据类型

                        inletMsg.put( appli.getInstitutionKey() ,applicationService.checkUpValueIsNotNull(map));
                    }
                }
            }

        if(errors.size()>0){
            inLetCheck = false;
        }

        return StringUtils.join(errors.toArray(), ",");//JSON.toJSONString(result);
    }

    @Override
    @Logable(businessTag = "inlet")
    public String inlet( String institutionCode, String customerCode, String businessCode){

        List<String> institutionCodeList = new ArrayList<String>();
        if (StringUtils.isNotBlank(businessCode) ) {

            List<String> payMethodList = new ArrayList();

            //根据businessCode获取所有支付方式
            BizPayMethod bizPayMethod = new BizPayMethod();
            bizPayMethod.setBusinessCode(businessCode);
            List<BizPayMethod> bizPayMethodList = bizPayMethodService.selectBySelective(bizPayMethod);
            for (BizPayMethod biz : bizPayMethodList) {
                payMethodList.add(biz.getPayMethodCode());
            }

            //根据支付方式,获取到所有上游
            institutionCodeList = payMethodService.getInstitutionCodeByPayMethods(payMethodList);
        }else if (StringUtils.isNotBlank(institutionCode) ) {
            institutionCodeList.add(institutionCode);
        }

        Map result = new HashMap();

        for (String ins : institutionCodeList) {
            //检查进件数据
            String resultMsgString = this.judge(ins, customerCode);

            Long id = sequenceService.nextValue("pas");
            ApplicationRecord applicationRecord = new ApplicationRecord();
            applicationRecord.setId(id);
            applicationRecord.setCustomerCode(customerCode);
            applicationRecord.setInstitutionCode(ins);
            applicationRecord.setInputParams(JSON.toJSONString(inletMsg));
            applicationRecord.setCreateTime(new Date());

            if(inLetCheck){
                // TODO 返回值后续会带上校验数据结果
                Map resultMsg = mchinletInletService.inlet(PasConstant.SYSTEMID , customerCode , String.valueOf(id) , ins , JSON.toJSONString(inletMsg));

                logger.info("clr上游进件返回结果:" + resultMsg);
                boolean clrInletResult = false ;
                if(null!=resultMsg){
                    String resultCode = (String)resultMsg.get("resultCode");
                    if(StringUtils.isNotBlank(resultCode) && "0000".equals(resultCode)){
                        clrInletResult = true;
                    }else {
                        //获取clr返回的失败信息
                        if(StringUtils.isNotBlank((String)resultMsg.get("resultMsg"))){
                            resultMsgString = (String)resultMsg.get("resultMsg");
                        }else{
                            resultMsgString = "清算系统未返回错误信息";
                        }
                    }
                }else{
                    resultMsgString = "清算系统未返回错误信息";
                }

                if(clrInletResult){
                    applicationRecord.setState(PasConstant.InletState.Processing.code);

                    result.put("resultCode","Success");
                    result.put("resultMsg","清算系统保存进件成功");

                }else{
                    applicationRecord.setState(PasConstant.InletState.Fail.code);
                    applicationRecord.setResultMsg(resultMsgString);

                    result.put("resultCode","Fail");
                    result.put("resultMsg","清算系统保存进件失败");
                }
            }else{

                //数据校验失败
                applicationRecord.setState(PasConstant.InletState.Failure.code);
                applicationRecord.setResultMsg(resultMsgString);

                result.put("resultCode","Fail");
                result.put("resultMsg","数据校验失败");
            }
            applicationRecordService.insert(applicationRecord);
        }

        return null;//JSON.toJSONString(result);
    }

    @Override
    @Logable(businessTag = "reInlet")
    public String reInlet(Long id){

        //获取进件数据
        ApplicationRecord ar = applicationRecordService.selectByPrimaryKey(id);
        if(null == ar ){
            throw new AppException(Constants.ReturnCode.INVALID_PARAM.code);
        }
        this.inlet(ar.getInstitutionCode(), ar.getCustomerCode(), null);

        return null;
    }

}
