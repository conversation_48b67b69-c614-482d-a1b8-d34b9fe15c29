package com.epaylinks.efps.pas.pas.service.impl;

import com.alibaba.fastjson.JSON;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.common.PasConstants;
import com.epaylinks.efps.pas.pas.common.PasConstant;
import com.epaylinks.efps.pas.pas.dao.BizPayMethodMapper;
import com.epaylinks.efps.pas.pas.domain.BizPayMethod;
import com.epaylinks.efps.pas.pas.domain.Business;
import com.epaylinks.efps.pas.pas.service.BizPayMethodService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> 5m
 * @Date : 2017/12/27 16:18
 * @Description :
 */
@Service
public class BizPayMethodServiceImpl implements BizPayMethodService {

	
    @Autowired
    BizPayMethodMapper bizPayMethodMapper;
    
    @Autowired
	private KafkaTemplate<String, String> kafkaTemplate;
    
    public static final String BUSINESSES_PAYMETHOD_KEY = "BusinessesPayMethod";
    
    public static final String PAS_TOPIC = "PAS_BasicData";
    
    @Override
    public int deleteByPrimaryKey(Long id) {
        return bizPayMethodMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(BizPayMethod record) {
        return bizPayMethodMapper.insert(record);
    }

    @Override
    public int insertSelective(BizPayMethod record) {
        return bizPayMethodMapper.insertSelective(record);
    }

    @Override
    public BizPayMethod selectByPrimaryKey(Long id) {
        return bizPayMethodMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(BizPayMethod record) {
        return bizPayMethodMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(BizPayMethod record) {
        return bizPayMethodMapper.updateByPrimaryKey(record);
    }

    @Override
    public List<BizPayMethod> selectBySelective(BizPayMethod record) {
        return bizPayMethodMapper.selectBySelective(record);
    }

    @Override
    public int deleteBySelective(BizPayMethod record){
        return bizPayMethodMapper.deleteBySelective(record);
    }

    @Override
    public int addBizPayMethods(List<BizPayMethod> list){ return bizPayMethodMapper.addBizPayMethods(list); }

	@Override
	public void syncAllBizPayMethod() {
		// TODO Auto-generated method stub
		List<BizPayMethod> bizPayMethods = bizPayMethodMapper.selectAll();
    	String data = JSON.toJSONString(bizPayMethods);
		try {
			kafkaTemplate.send(PAS_TOPIC, BUSINESSES_PAYMETHOD_KEY, data);
		} catch (Exception e) {
			// TODO: handle exception
			throw new AppException(PasCode.SEND_KAFKA_EXCEPTION.code, e);
		}
	}
}
