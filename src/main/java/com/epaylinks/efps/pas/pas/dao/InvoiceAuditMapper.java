package com.epaylinks.efps.pas.pas.dao;

import com.epaylinks.efps.pas.pas.controller.response.InvoiceAuditResponse;
import com.epaylinks.efps.pas.pas.domain.InvoiceAudit;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface InvoiceAuditMapper {
    int deleteByPrimaryKey(Long invoiceId);

    int insert(InvoiceAudit record);

    int insertSelective(InvoiceAudit record);

    InvoiceAudit selectByPrimaryKey(Long invoiceId);

    int updateByPrimaryKeySelective(InvoiceAudit record);

    int updateByPrimaryKey(InvoiceAudit record);

    Long selectSeqInvoiceAudit();

    List<InvoiceAudit> pageQuery(Map paramMap);

    List<InvoiceAudit> pageQueryInvoice(Map paramMap);

    Integer count(Map paramMap);

    String selectCustUser(@Param("userId") Long userId);
}