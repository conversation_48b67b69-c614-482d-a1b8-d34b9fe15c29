package com.epaylinks.efps.pas.pas.service;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

/**
 * <AUTHOR> 5m
 * @Date : 2018/1/2 14:30
 * @Description : 文件服务器
 */
@FeignClient("fs")
public interface FSService {

    @RequestMapping(value = "/FilePath", method= RequestMethod.POST)
    public Map<String, String> filePath(@RequestParam(value = "uniqueId", required = true) String uniqueId,
                                     @RequestParam(value = "expiredTime", required = true) String expiredTime,
                                     @RequestParam(value = "maxAccessCount", required = false) String maxAccessCount,
                                     @RequestParam(value = "type", required = true) String type);


}
