package com.epaylinks.efps.pas.pas.controller;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.business.CommonResponse;
import com.epaylinks.efps.common.datadownload.annotation.DownloadAble;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.pas.common.LogService;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.pas.controller.request.InvoiceAuditRequest;
import com.epaylinks.efps.pas.pas.controller.response.InvoiceAuditResponse;
import com.epaylinks.efps.pas.pas.service.InvoiceAuditService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/invoiceAudit")
@Api(value = "InvoiceAuditController", description = "开票审核管理")
public class InvoiceAuditController {
    @Autowired
    private InvoiceAuditService invoiceAuditService;

    @Autowired
    private LogService logService;

    @PostMapping("/addInvoiceAudit")
    @ApiOperation(value = "新增开票审核", notes = "新增开票审核", httpMethod = "POST")
    @Logable(businessTag = "InvoiceAuditController.addInvoiceAudit")
    public CommonOuterResponse addInvoiceAudit(@RequestBody InvoiceAuditRequest request,
                                               @RequestHeader(value = "x-userid",required = false) Long userId,
                                               @RequestHeader(value = "x-customer-code",required = false) String headCustomerNo,
                                               @RequestHeader(value = "x-user-type",required = false) String userType) {
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            invoiceAuditService.checkParam(request,userId,headCustomerNo,"add",userType);
            invoiceAuditService.add(request,userId,headCustomerNo,userType);
            response = CommonOuterResponse.success();
        } catch (AppException e) {
            response.setReturnCode(e.getErrorCode());
            response.setReturnMsg(e.getErrorMsg());
        } catch (Exception e) {
            System.out.println("新增发票error:" + e.getMessage());
            logService.printLog("新增发票error:" + e.getMessage());
            response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
            response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
        }
        return response;
    }

    @PostMapping("/editInvoiceAudit")
    @ApiOperation(value = "修改开票审核", notes = "修改开票审核", httpMethod = "POST")
    @Logable(businessTag = "InvoiceAuditController.editInvoiceAudit")
    public CommonOuterResponse editInvoiceAudit(@RequestBody InvoiceAuditRequest request,
                                                @RequestHeader(value = "x-userid",required = false) Long userId,
                                                @RequestHeader(value = "x-customer-code",required = false) String headCustomerNo,
                                                @RequestHeader(value = "x-user-type",required = false) String userType) {
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            invoiceAuditService.checkParam(request,userId,headCustomerNo,"edit",userType);
            invoiceAuditService.edit(request,userId,headCustomerNo,userType);
            response = CommonOuterResponse.success();
        } catch (AppException e) {
            response.setReturnCode(e.getErrorCode());
            response.setReturnMsg(e.getErrorMsg());
        } catch (Exception e) {
            System.out.println("修改发票error:" + e.getMessage());
            logService.printLog("修改发票error:" + e.getMessage());
            response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
            response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
        }
        return response;
    }

    @PostMapping("/auditInvoiceAudit")
    @ApiOperation(value = "审核开票记录", notes = "审核开票记录", httpMethod = "POST")
    @Logable(businessTag = "InvoiceAuditController.auditInvoiceAudit")
    public CommonOuterResponse auditInvoiceAudit(@RequestParam("invoiceId") Long invoiceId,
                                                @RequestParam("auditResult") String auditResult,
                                                @RequestParam(value = "auditComment",required = false) String auditComment,
                                                @RequestHeader(value = "x-userid",required = false) Long userId) {
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            invoiceAuditService.audit(invoiceId,userId,auditResult,auditComment);
            response = CommonOuterResponse.success();
        } catch (AppException e) {
            response.setReturnCode(e.getErrorCode());
            response.setReturnMsg(e.getErrorMsg());
        } catch (Exception e) {
            System.out.println("审核发票error:" + e.getMessage());
            logService.printLog("审核发票error:" + e.getMessage());
            response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
            response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
        }
        return response;
    }

    @GetMapping("/pageQuery")
    @DownloadAble
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startCreateTime", value = "开始时间yyyyMMddHHmmss", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endCreateTime", value = "结束时间yyyyMMddHHmmss", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "customerNo", value = "商户编号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "status", value = "状态[0：待审核；1：审核通过；2：审核不通过]", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "invoiceMedium", value = "发票介质[1：电子发票；2：纸质发票]", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "invoiceNo", value = "发票编号", required = false, dataType = "String", paramType = "query")
    })
    @ApiOperation(value = "分页列表", notes = "分页列表", httpMethod = "GET")
    @Logable(businessTag = "InvoiceAuditController.pageQuery")
    public PageResult<List<InvoiceAuditResponse>> pageQuery(@RequestParam(value = "startCreateTime", required = false) String startCreateTime,
                                                            @RequestParam(value = "endCreateTime", required = false) String endCreateTime,
                                                            @RequestParam(value = "customerNo",required = false) String customerNo,
                                                            @RequestParam(value = "status",required = false) String status,
                                                            @RequestParam(value = "invoiceMedium",required = false) String invoiceMedium,
                                                            @RequestParam(value = "invoiceNo",required = false) String invoiceNo,
                                                            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                            @RequestParam(required = false,defaultValue = "false") boolean download,
                                                            @RequestParam(required = false) String fileName,
                                                            @RequestParam(required = false,defaultValue = "csv") String type,
                                                            @RequestParam(required = false) String fileSource,
                                                            @RequestHeader(value = "x-userid") Long userId) {
        PageResult<List<InvoiceAuditResponse>> page = new PageResult<>();
        try {
            int endRowNo = pageNum * pageSize;
            int beginRowNo = (pageNum - 1) * pageSize + 1;
            Map<String, Object> paramsMap = new HashMap<>();
            paramsMap.put("startCreateTime", startCreateTime);
            paramsMap.put("endCreateTime", endCreateTime);
            paramsMap.put("invoiceNo",invoiceNo);
            paramsMap.put("customerNo",customerNo);
            paramsMap.put("invoiceMedium",invoiceMedium);
            paramsMap.put("auditStatus",status);
            paramsMap.put("beginRowNo", beginRowNo);
            paramsMap.put("endRowNo", endRowNo);
            page = invoiceAuditService.pageQuery(paramsMap,userId,null,download);
            page.setCode(CommonResponse.SUCCEE);
        } catch (Exception e) {
            System.out.println("错误信息：" + e.getMessage());
            logService.printLog(e);
            if (e instanceof AppException) {
                page.setCode(((AppException) e).getErrorCode());
                page.setMessage(((AppException) e).getErrorMsg());
            } else {
                page.setCode(PasCode.SYSTEM_EXCEPTION.code);
                page.setMessage(PasCode.SYSTEM_EXCEPTION.message);
            }
        }
        return page;
    }

    @GetMapping("/merchantPageQuery")
    @DownloadAble
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startCreateTime", value = "开始时间yyyyMMddHHmmss", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endCreateTime", value = "结束时间yyyyMMddHHmmss", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "customerNo", value = "商户编号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "status", value = "状态[0：待审核；1：审核通过；2：审核不通过]", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "invoiceMedium", value = "发票介质[1：电子发票；2：纸质发票]", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "invoiceNo", value = "发票编号", required = false, dataType = "String", paramType = "query")
    })
    @ApiOperation(value = "商户门户分页列表", notes = "商户门户分页列表", httpMethod = "GET")
    @Logable(businessTag = "InvoiceAuditController.merchantPageQuery")
    public PageResult<List<InvoiceAuditResponse>> merchantPageQuery(@RequestParam(value = "startCreateTime", required = false) String startCreateTime,
                                                                    @RequestParam(value = "endCreateTime", required = false) String endCreateTime,
                                                                    @RequestParam(value = "customerNo",required = false) String customerNo,
                                                                    @RequestParam(value = "status",required = false) String status,
                                                                    @RequestParam(value = "invoiceMedium",required = false) String invoiceMedium,
                                                                    @RequestParam(value = "invoiceNo",required = false) String invoiceNo,
                                                                    @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                                    @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                                    @RequestParam(required = false,defaultValue = "false") boolean download,
                                                                    @RequestParam(required = false) String fileName,
                                                                    @RequestParam(required = false,defaultValue = "csv") String type,
                                                                    @RequestParam(required = false) String fileSource,
                                                                    @RequestHeader(value = "x-customer-code") String headCustomerNo) {
        PageResult<List<InvoiceAuditResponse>> page = new PageResult<>();
        try {
            int endRowNo = pageNum * pageSize;
            int beginRowNo = (pageNum - 1) * pageSize + 1;
            Map<String, Object> paramsMap = new HashMap<>();
            paramsMap.put("startCreateTime", startCreateTime);
            paramsMap.put("endCreateTime", endCreateTime);
            paramsMap.put("invoiceNo",invoiceNo);
            paramsMap.put("customerNo",customerNo);
            paramsMap.put("invoiceMedium",invoiceMedium);
            paramsMap.put("auditStatus",status);
            paramsMap.put("beginRowNo", beginRowNo);
            paramsMap.put("endRowNo", endRowNo);
            page = invoiceAuditService.pageQuery(paramsMap,null,headCustomerNo,download);
            page.setCode(CommonResponse.SUCCEE);
        } catch (Exception e) {
            System.out.println("错误信息：" + e.getMessage());
            logService.printLog(e);
            if (e instanceof AppException) {
                page.setCode(((AppException) e).getErrorCode());
                page.setMessage(((AppException) e).getErrorMsg());
            } else {
                page.setCode(PasCode.SYSTEM_EXCEPTION.code);
                page.setMessage(PasCode.SYSTEM_EXCEPTION.message);
            }
        }
        return page;
    }
}
