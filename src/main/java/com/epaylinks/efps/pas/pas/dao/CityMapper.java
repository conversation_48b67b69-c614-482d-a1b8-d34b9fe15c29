package com.epaylinks.efps.pas.pas.dao;

import com.epaylinks.efps.pas.pas.domain.City;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CityMapper {

    int deleteByPrimaryKey(String cityCd);

    int insert(City record);

    int insertSelective(City record);

    City selectByPrimaryKey(String cityCd);

    int updateByPrimaryKeySelective(City record);

    int updateByPrimaryKey(City record);

    List<City> selectByProvinceCode(@Param("provinceCode") String provinceCode);
    
    /**
     * 查询地市编码
     * @param cityName
     * @return
     */
    String queryCityCodeByNameAndProvinceCode(@Param("cityName") String cityName, @Param("provinceCode") String provinceCode);

}