package com.epaylinks.efps.pas.pas.util;

import com.epaylinks.efps.pas.pas.vo.UnionCardBinFtpVo;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.net.ProtocolCommandEvent;
import org.apache.commons.net.ProtocolCommandListener;
import org.apache.commons.net.ftp.FTP;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPReply;

import java.io.*;
import java.util.ArrayList;
import java.util.List;

public class FtpUtils extends FTPClient {

    private String serverIp;
    private int serverPort;
    private String username;
    private String password;
    private String workDir; // 服务器上的工作目录,注,在linux下/代表系统根目录,而win下则代表ftp所在的目录

    private void debug(String msg){
        System.out.println(msg);
    }

    public FtpUtils() {

    }

    public FtpUtils(String serverIp, int serverPort, String username,
                    String password, String workDir) {
        super();
        this.serverIp = serverIp;
        this.serverPort = serverPort;
        this.username = username;
        this.password = password;
        try {
            this.workDir = new String(workDir.getBytes(), "ISO-8859-1");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        // 添加命令监听器
        addProtocolCommandListener(new ProtocolCommandListener() {
            String message = "";

            public void protocolCommandSent(ProtocolCommandEvent event) {
                message = event.getMessage();
                if ("pass".equalsIgnoreCase(event.getCommand())) {
                    // 隐藏密码
                    message = message.substring(0, message.indexOf(" "))
                            + " ******";
                }
                debug(message);
            }

            public void protocolReplyReceived(ProtocolCommandEvent event) {
                debug(event.getMessage());
            }
        });
    }

    /**
     * 判断文件是否存在ftp目录上
     *
     * @param fileName
     *            ftp上的文件所在所在路径
     * @param fileName
     *            所判断文件名称
     * @return
     */
    public boolean exsitsFile(String fileName) {
        debug("正在判断文件是否存在ftp目录上，workDir=" + workDir + ", fileName="
                + fileName + " ......");

        try {
            ftpPrepare();

            InputStream is = null;
            is = super.retrieveFileStream(fileName);// 获取远程ftp上指定文件的InputStream
            if (null != is) {
                is.close();
                return true;
            }

        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("判断文件是否存在ftp目录上时报错,workDir=" + workDir
                    + ", fileName=" + fileName);
        }
        return false;
    }

    public String downloadFile(String ftpFilename, String localPath)
            throws Exception {
        return downloadFile(ftpFilename, localPath, false);
    }



    /**
     * 下载指定文件
     *
     * @param ftpFilename
     * @param localPath
     *            本地文件的存放目录
     * @param isDelete
     * @throws Exception
     */
    public String downloadFile(String ftpFilename, String localPath,
                               boolean isDelete) throws Exception {
        String localFilename = "";
        FileOutputStream fos = null;
        try {

            ftpPrepare();

            // 下载文件
            localFilename = localPath + ftpFilename;
            ftpFilename = new String(ftpFilename.getBytes(), "ISO-8859-1");
            fos = new FileOutputStream(localFilename);
            if (!super.retrieveFile(ftpFilename, fos)) {
                throw new Exception("下载远程文件失败:" + ftpFilename);
            }
            fos.close();
            if (isDelete) {
                super.deleteFile(ftpFilename);
            }
        } finally {
            if (isConnected()) {
                try {
                    logout();
                    disconnect();
                } catch (IOException e) {
                }
            }
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e) {
                }
            }
        }
        return localFilename;
    }

    /**
     * 下载ftp工作目录下的所有文件到本地指定目录
     *
     * @param localPath
     *            本地路径,须为/结尾的完整路径
     * @param isDelete
     * @return
     * @throws Exception
     */
    public List<String> downloadAllFile(String localPath, boolean isDelete)
            throws Exception {
        List<String> filenames = new ArrayList<String>();
        try {
            ftpPrepare();
            FTPFile[] files = super.listFiles(workDir);
            if (files != null && files.length > 0) {
                FileOutputStream fos = null;
                String localname = "";
                for (int i = 0; i < files.length; i++) {
                    if (files[i].isDirectory()) {
                        continue;
                    }
                    try {
                        localname = new String(files[i].getName().getBytes(
                                "ISO-8859-1"), "GBK");
                        File f = new File(localPath + localname);
                        if (f.exists()) {
                            f.delete();
                        }
                        fos = new FileOutputStream(f);
                        // 下载文件
                        super.retrieveFile(files[i].getName(), fos);

                        if (isDelete) {
                            super.deleteFile(files[i].getName());
                        }
                        filenames.add(f.getAbsolutePath());
                    } catch (Exception e) {
                        debug("下载ftp文件异常："+e.getMessage());
                        e.printStackTrace();
                    } finally {
                        if (fos != null) {
                            fos.close();
                            fos = null;
                        }
                    }
                }
            }
        } finally {
            if (isConnected()) {
                try {
                    logout();
                    disconnect();
                } catch (IOException e) {
                }
            }
        }
        return filenames;
    }

    public List<UnionCardBinFtpVo> downloadAllFile(String localPath)
            throws Exception {
        List<UnionCardBinFtpVo> filenames = new ArrayList<>();
        try {
            ftpPrepare();
            FTPFile[] files = super.listFiles(workDir);
            if (files != null && files.length > 0) {
                FileOutputStream fos = null;
                String localname = "";
                for (int i = 0; i < files.length; i++) {
                    if (files[i].isDirectory()) {
                        continue;
                    }
                    try {
                        localname = new String(files[i].getName().getBytes(
                                "ISO-8859-1"), "GBK");
                        File f = new File(localPath + localname);
                        if (f.exists()) {
                            f.delete();
                        }
                        fos = new FileOutputStream(f);
                        // 下载文件
                        super.retrieveFile(files[i].getName(), fos);

                        UnionCardBinFtpVo fileVo = new UnionCardBinFtpVo();
                        fileVo.setFileName(localname);
                        fileVo.setFilePath(localPath + localname);
                        filenames.add(fileVo);
                    } catch (Exception e) {
                        debug("下载ftp文件异常："+e.getMessage());
                        e.printStackTrace();
                    } finally {
                        if (fos != null) {
                            fos.close();
                            fos = null;
                        }
                    }
                }
            }
        } finally {
            if (isConnected()) {
                try {
                    logout();
                    disconnect();
                } catch (IOException e) {
                }
            }
        }
        return filenames;
    }

    /**
     * 上传文件到ftp服务器
     *
     * @param localFilename
     * @throws Exception
     */
    public boolean uploadFile(String localFilename) throws Exception {
        return uploadFile(localFilename, null);
    }
    /**
     * 上传文件到ftp服务器
     *
     * @param localFilename
     * @param fileName
     * @throws Exception
     */
    public boolean uploadFile(String localFilename,String fileName) throws Exception {
        FileInputStream fis = null;
        try {

            ftpPrepare();
            String remote = fileName;
            if(fileName==null || fileName.isEmpty()) {
                int p = localFilename.lastIndexOf("/");
//				int p = localFilename.lastIndexOf(File.separator);
                remote = localFilename.substring(p + 1);
            }
            // 上传文件
            fis = new FileInputStream(localFilename);
            return super.storeFile(remote, fis);
        } finally {
            if (isConnected()) {
                try {
                    logout();
                    disconnect();
                } catch (IOException e) {
                }
            }
            if (fis != null)
                fis.close();
        }
    }

    /**
     * 搜索以指定参数开头的文件
     *
     * @param startName
     * @return
     * @throws Exception
     */
    public List<String> findFile(String startName) throws Exception {
        List<String> list = new ArrayList<String>();
        try {
            ftpPrepare();
            FTPFile[] file = super.listFiles();
            String localName = "";
            for (FTPFile f : file) {
                if (f.isDirectory()) {
                    continue;
                }
                localName = new String(f.getName().getBytes("ISO-8859-1"),
                        "GBK");
                if (localName.startsWith(startName)) {
                    list.add(localName);
                }
            }
        } finally {
            if (isConnected()) {
                try {
                    logout();
                    disconnect();
                } catch (IOException e) {
                }
            }
        }
        return list;
    }

    /**
     * 远程文件移动,使用rename方法实现,注,路径需为相对路径
     *
     * @param srcPath
     *            源文件所在相对目录
     * @param destPath
     *            目标相对目录
     * @param srcFilename
     *            文件名
     * @throws Exception
     */
    public void remoteMoveFile(String srcPath, String destPath, String srcFilename, String destFileName )
            throws Exception {

        destFileName = destFileName==null? srcFilename : destFileName;

        try {
            ftpPrepare();
            super.rename(srcPath + srcFilename, destPath + destFileName);
        } finally {
            if (isConnected()) {
                try {
                    logout();
                    disconnect();
                } catch (IOException e) {
                }
            }
        }
    }

    /**
     * 远程文件移动,将源文件夹下的所有文件全部移动到目标文件夹,使用rename方法实现,注,路径需为相对路径
     *
     * @param srcPath
     *            源文件所在相对目录
     * @param destPath
     *            目标相对目录
     * @throws Exception
     */
    public void remoteMoveFiles(String srcPath, String destPath)
            throws Exception {
        try {

            ftpPrepare();

            FTPFile[] files = super.listFiles();
            String filename = "";
            for (FTPFile f : files) {
                if (f.isDirectory()) {
                    continue;
                }
                filename = f.getName();
                super.rename(srcPath + filename, destPath + filename);
            }
        } finally {
            if (isConnected()) {
                try {
                    logout();
                    disconnect();
                } catch (IOException e) {
                }
            }
        }
    }

    /**
     * 远程文件拷贝,使用rename方法实现,注,路径需为相对路径
     *
     * @param srcPath
     *            源文件所在相对目录
     * @param destPath
     *            目标相对目录
     * @param filename
     *            文件名
     * @throws Exception
     */
    public void remoteCopyFile(String srcPath, String destPath, String filename)
            throws Exception {
        try {
            if (!StringUtils.isEmpty(srcPath)
                    && srcPath.charAt(srcPath.length() - 1) != '/'
                    && srcPath.charAt(srcPath.length() - 1) != '\\') {
                srcPath += "/";
            }
            if (!StringUtils.isEmpty(destPath)
                    && destPath.charAt(destPath.length() - 1) != '/'
                    && destPath.charAt(destPath.length() - 1) != '\\') {
                destPath += "/";
            }
            ftpPrepare();
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            if (!super.retrieveFile(srcPath + filename, baos)) {
                throw new Exception("下载远程文件到缓冲区失败:" + srcPath + filename);
            }
            ByteArrayInputStream bais = new ByteArrayInputStream(baos
                    .toByteArray());
            if (!super.storeFile(destPath + filename, bais)) {
                throw new Exception("从缓冲区上传文件失败:" + destPath + filename);
            }
            debug("FTP远程文件复制成功:" + srcPath + filename + " -> " + destPath
                    + filename);
        } finally {
            if (isConnected()) {
                try {
                    logout();
                    disconnect();
                } catch (IOException e) {
                }
            }
        }
    }

    /**
     * 远程文件复制,将源文件夹下的所有文件全部复制到目标文件夹,注,路径需为相对路径
     *
     * @param srcPath
     *            源文件所在相对目录
     * @param destPath
     *            目标相对目录
     * @throws Exception
     */
    public void remoteCopyFiles(String srcPath, String destPath)
            throws Exception {
        try {
            long start = System.currentTimeMillis();
            ftpPrepare();
            FTPFile[] files = super.listFiles();
            String filename = "";
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ByteArrayInputStream bais = null;
            int byteCount = 0, total = 0, fileCount = 0;
            for (FTPFile f : files) {
                filename = f.getName();
                if (f.isDirectory() || filename.equalsIgnoreCase("Thumbs.db")) {
                    continue;
                }
                baos.reset();
                if (!super.retrieveFile(srcPath + filename, baos)) {
                    throw new Exception("下载远程文件到缓冲区失败:" + srcPath + filename);
                }
                bais = new ByteArrayInputStream(baos.toByteArray());
                if (!super.storeFile(destPath + filename, bais)) {
                    throw new Exception("从缓冲区上传文件失败:" + destPath + filename);
                }
                byteCount = baos.size();
                total += byteCount;
                fileCount++;
                bais.close();
                debug("FTP远程文件复制成功(" + srcPath + filename + " -> "
                        + destPath + filename + ")共" + byteCount + " B");
            }
            long useTime = System.currentTimeMillis() - start;
            long speed = (long) ((total / 1024.00) / (useTime / 1000.00));
            debug("FTP远程文件复制完成,共" + fileCount + "个文件,用时:" + useTime
                    + "ms,速度:" + speed + " KB/S");
        } finally {
            if (isConnected()) {
                try {
                    logout();
                    disconnect();
                } catch (IOException e) {
                }
            }
        }
    }




    /**
     * 相对于当前工作目录来创建新目录
     * @param path 需要创建的新目录。相对于workdir的路径,可以为多级目录。
     * 例子：mkdirs("demo") ，mkdirs("a/b/c)
     */
    public void mkdirs(String path) throws Exception{

        if(path==null || "".equals(path.trim())){
            return;
        }
        try{
            ftpPrepare();

            String tmpDirPath = workDir;
            String[]dirs = path.split("[\\\\,/]");
            for(String dir :dirs){
                dir = dir.trim();
                if(!"".equals(dir)){
                    super.mkd(dir);
                    tmpDirPath = tmpDirPath + dir + "/";
                    super.changeWorkingDirectory(tmpDirPath);
                }
            }
        }finally{
            if (isConnected()) {
                try {
                    logout();
                    disconnect();
                } catch (IOException e) {
                }
            }
        }
    }



    private void ftpPrepare() throws Exception {
        int reply = -1;
        try {
            super.connect(serverIp, serverPort);
            super.setConnectTimeout(60000);
        } catch (Exception e) {
            debug("无法连接到ftp服务器: " + serverIp + ":" + serverPort + ".");
            debug("无法连接到ftp服务器: " +e.getMessage());
            throw new Exception("无法连接到ftp服务器: " + serverIp + ":" + serverPort
                    + ".");
        }
        reply = super.getReplyCode();
        if (!FTPReply.isPositiveCompletion(reply)) {
            logout();
            disconnect();
            debug("ftp服务器拒绝本次连接!");
            throw new Exception("ftp服务器拒绝本次连接!");
        }
        if (!login(username, password)) {
            logout();
            debug("登陆失败,请检查用户名或密码!");
            throw new Exception("登陆失败,请检查用户名或密码!");
        }
        // 转到工作文件夹
        if (super.changeWorkingDirectory(workDir) == false) {
            throw new Exception("切换工作目录失败:" + super.getReplyString());
        }
        if (super.setFileType(FTP.BINARY_FILE_TYPE) == false) {
            throw new Exception("设置二进制传输方式失败:" + super.getReplyString());
        }
        super.enterLocalPassiveMode();
    }


    /**
     * 列出指定目录下的文件列表
     * @param path
     * @return
     * @throws Exception
     */
    public String[] listFileNames(String path) throws Exception{
        if(path==null || "".equals(path.trim())){
            return null;
        }

        List<String> fileNames = new ArrayList<String>();
        try{
            ftpPrepare();
            FTPFile[]files = this.listFiles(path);
            for(FTPFile f:files){
                if(f.isFile()){
                    fileNames.add(f.getName());
                }
            }
            return fileNames.toArray(new String[]{});
        }finally{
            if (isConnected()) {
                try {
                    logout();
                    disconnect();
                } catch (IOException e) {
                }
            }
        }
    }



    /**
     * 删除FTP服务器上的文件
     */
    public void delFile(String path) throws Exception{
        if(path==null || "".equals(path.trim())){
            return ;
        }
        try{
            ftpPrepare();
            dele(path);
        }finally{
            if (isConnected()) {
                try {
                    logout();
                    disconnect();
                } catch (IOException e) {
                }
            }
        }
    }



    public String getServerIp() {
        return serverIp;
    }

    public void setServerIp(String serverIp) {
        this.serverIp = serverIp;
    }

    public int getServerPort() {
        return serverPort;
    }

    public void setServerPort(int serverPort) {
        this.serverPort = serverPort;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getWorkDir() {
        return workDir;
    }

    public void setWorkDir(String workDir) {
        this.workDir = workDir;
    }

    // public static void main(String[] args) {
    // String ip = "***************";
    // int port = 21;
    // String username = "umps";
    // String password = "umps123";
    // String workDir = "/Data/数据处理/20110317/";
    // ip = "*************";
    // password = "123456";
    // username = "zmj";
    // workDir = "/home/<USER>/";
    // FtpUtils ftp = new FtpUtils(ip, port, username, password,
    // workDir);
    // String localPath = "D:\\temp\\ftpTest\\";
    // try {
    // // ftp.uploadFile("D:\\temp\\ftpTest\\b.txt" );
    // // String filename =
    // // ftp.downloadFile("卡0103000022244565解冻数据备份txt.txt", localPath);
    // // System.out.println(filename);
    // // List<String> files = ftp.downloadAllFile(localPath, false);
    // // for(String f : files){
    // // System.out.println(f);
    // // }
    // // List<String> ff = ftp.findFile("pbc");
    // // for(String f : ff){
    // // System.out.println(f);
    // // }
    // // ftp.remoteMoveFile("abc/", "", "a.txt");
    // // ftp.remoteCopyFile("abc", "./", "a2.txt");
    // ftp.remoteCopyFiles("./", "abc/");
    // } catch (Exception e) {
    // //
    // e.printStackTrace();
    // }
    // }

    /**
     * 文件名修改方法
     */
    public boolean changeFileName(String oldFile, String newFile)
            throws Exception {
        boolean flag = false;
        if (null != oldFile && !oldFile.trim().equals("")) {
            File f = new File(oldFile);// F:\\备份文件\\2014_06\\old.txt
            String c = f.getParent();
            File mm = new File(c + "\\" + newFile);
            if (f.renameTo(mm)) {
                flag = true;
            }
        }
        return flag;
    }

    public String changeFileNameAndReturn(String oldFile, String newFile)
            throws Exception {
        String file = null;
        if (null != oldFile && !oldFile.trim().equals("")) {
            File f = new File(oldFile);// F:\\备份文件\\2014_06\\old.txt
            String c = f.getParent();
            File mm = new File(c + "\\" + newFile);
            if (f.renameTo(mm)) {
                file = c + "\\" + newFile;
            }
        }
        return file;
    }
}
