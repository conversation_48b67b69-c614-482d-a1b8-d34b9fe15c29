package com.epaylinks.efps.pas.pas.controller;


import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.datadownload.annotation.DownloadAble;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.pas.acl.po.User;
import com.epaylinks.efps.pas.acl.service.UserService;
import com.epaylinks.efps.pas.common.PageResult;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.mch.model.CustomerResponse;
import com.epaylinks.efps.pas.pas.domain.OpLogReport;
import com.epaylinks.efps.pas.pas.domain.OperationLog;
import com.epaylinks.efps.pas.pas.domain.OperationLogReport;
import com.epaylinks.efps.pas.pas.service.OpLogReportService;
import com.epaylinks.efps.pas.pas.service.OperationLogService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 操作日志控制类
 */

@RestController
@RequestMapping("/opLog")
@Api(value = "OperationLogController", description = "操作日志控制类")
public class OperationLogController {

	@Autowired
	private UserService userService;

	@Autowired
	private SequenceService sequenceService;

	@Autowired
	private OperationLogService operationLogService;

	@Autowired
	private OpLogReportService opLogReportService;

	@Autowired
	private CommonLogger logger;


	@PostMapping("/logInsert")
	@Logable(businessTag = "opLog.insert")
	@Exceptionable
	@Validatable
	@ApiOperation(value = "记录操作日志", notes = "记录操作日志", httpMethod = "POST")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "userId", value = "操作用户ID", required = true, dataType = "Long", length = 20, paramType = "query"),
		@ApiImplicitParam(name = "opModule", value = "操作模块", required = true, dataType = "String", paramType = "query"),
		@ApiImplicitParam(name = "opMethod", value = "操作方式", required = true, dataType = "String", paramType = "query"),
		@ApiImplicitParam(name = "opContent", value = "操作内容", required = true, dataType = "String", paramType = "query"),
		@ApiImplicitParam(name = "opTime", value = "操作时间", required = false, dataType = "String", paramType = "query")
	})
	public CustomerResponse opLog(
			@RequestParam("userId") Long userId,
			@RequestParam("opModule") String opModule,
			@RequestParam("opMethod") String opMethod,
			@RequestParam("opContent") String opContent,
			String opTime
	){
		CustomerResponse response  = new CustomerResponse();
		OperationLog opLog = new OperationLog();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd HHmmss");
		Date now = new Date();

		//根据userId查询用户名和真实姓名
		User user = userService.setUserById(userId);
		if(user == null){
			response.setReturnCode(PasCode.USER_NOT_EXIST.code);
			response.setReturnMsg(PasCode.USER_NOT_EXIST.message);
			return response;
		}

		//封装数据
		opLog.setLogId(sequenceService.nextValue("pas"));
		opLog.setUserName(user.getName());
		opLog.setRealName(user.getRealName());
		opLog.setOpModule(opModule);
		opLog.setOpMethod(opMethod);
		opLog.setOpContent(opContent);
		if(StringUtils.isBlank(opTime)){
			opLog.setOpTime(sdf.format(now));
		}else{
			opLog.setOpTime(opTime);
		}
		opLog.setCreateTime(now);

		//插入数据库
		operationLogService.insert(opLog);

		response.setReturnCode("0000");
		response.setReturnMsg("记录成功！");

		return response;
	}


	@GetMapping("/pageQuery")
	@Logable(businessTag = "opLog.query")
	@Exceptionable
	@Validatable
	@ApiOperation(value = "分页查询操作日志", notes = "分页查询操作日志", httpMethod = "GET")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "pageSize", value = "页面大小", required = true, dataType = "int", paramType = "query"),
			@ApiImplicitParam(name = "pageNum", value = "页码", required = true, dataType = "int", paramType = "query"),
			@ApiImplicitParam(name = "startTime", value = "开始时间", required = false, dataType = "String", paramType = "query"),
			@ApiImplicitParam(name = "endTime", value = "结束时间", required = false, dataType = "String", paramType = "query"),
			@ApiImplicitParam(name = "userName", value = "用户名", required = false, dataType = "String", paramType = "query"),
			@ApiImplicitParam(name = "realName", value = "真实名称", required = false, dataType = "String", paramType = "query"),
	})
	public PageResult<OperationLog> pageQuery(
			@RequestParam(value = "pageSize" ) Integer pageSize,
			@RequestParam(value = "pageNum") Integer pageNum,
			String startTime,
			String endTime,
			String userName,
			String realName,
			String module
	){
		PageResult<OperationLog> pageResult = null;

		if(StringUtils.isNotBlank(startTime)){
			startTime = startTime +"000000";
		}

		if(StringUtils.isNotBlank(endTime)){
			endTime = endTime+"235959";
		}

		//用于封装查询条件
		Map<String,String> map = new HashMap<>();
		map.put("startTime",startTime);
		map.put("endTime",endTime);
		map.put("userName",userName);
		map.put("realName",realName);
		map.put("module",module);

		pageResult = operationLogService.pageQuery(pageSize,pageNum,map);

		return pageResult;
	}


    @GetMapping("/oldPageQueryReport")
    @Logable(businessTag = "OperationLogController.oldPageQueryReport")
    @Exceptionable
    @Validatable
    @DownloadAble
    @ApiOperation(value = "分页查询操作日志报表，旧版本", notes = "分页查询操作日志报表，旧版本", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageSize", value = "页面大小", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "pageNum", value = "页码", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "startTime", value = "开始时间", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "结束时间", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "userName", value = "用户名", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "realName", value = "真实名称", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "download", value = "是否导出，true/false", required = false, dataType = "Boolean", paramType = "query"),
            @ApiImplicitParam(name = "fileName", value = "导出文件名称(带文件类型后缀，例如.csv)", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "type", value = "导出文件类型，例如 csv", required = false, dataType = "String", paramType = "query")
    })
    public com.epaylinks.efps.common.util.page.PageResult<OperationLogReport> oldPageQueryReport(
            @RequestParam(value = "pageSize" ) Integer pageSize,
            @RequestParam(value = "pageNum") Integer pageNum,
            String startTime,
            String endTime,
            String userName,
            String realName,
            @RequestParam(value = "download", required = false) Boolean download,
            @RequestParam(value = "fileName", required = false) String fileName,
            @RequestParam(value = "type", required = false) String type,
            @RequestHeader(value = "x-userid", required = true) Long userId
    ) {

        com.epaylinks.efps.common.util.page.PageResult<OperationLogReport> pageResult = null;
        try {
            if (StringUtils.isNotBlank(startTime)) {
                startTime = startTime + "000000";
            }

            if (StringUtils.isNotBlank(endTime)) {
                endTime = endTime + "235959";
            }

            //用于封装查询条件
            int endNum = pageSize * pageNum;
            int startNum = endNum - pageSize + 1;
            
            // 用于封装查询条件
            Map<String, Object> map = new HashMap<>();
            map.put("startTime", startTime);
            map.put("endTime", endTime);
            map.put("userName", userName);
            map.put("realName", realName);
            map.put("startNum", startNum);
            map.put("endNum", endNum);

            pageResult = operationLogService.OperationLogReport(map);

            return pageResult;

        } catch (Exception e) {
            pageResult = new com.epaylinks.efps.common.util.page.PageResult<OperationLogReport>();
            if (e instanceof AppException) {
                pageResult.setResult(((AppException) e).getErrorCode());
                pageResult.setErrorMsg(((AppException) e).getErrorMsg());
            } else {
                pageResult.setResult(PasCode.SYSTEM_EXCEPTION.code);
                pageResult.setErrorMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
            return pageResult;
        }
    }

	@GetMapping("/pageQueryReport")
	@Logable(businessTag = "OperationLogController.pageQueryReport")
	@Exceptionable
	@Validatable
	@DownloadAble
	@ApiOperation(value = "分页查询每日操作审计报告", notes = "分页查询每日操作审计报告", httpMethod = "GET")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "pageSize", value = "页面大小", required = true, dataType = "int", paramType = "query"),
			@ApiImplicitParam(name = "pageNum", value = "页码", required = true, dataType = "int", paramType = "query"),
			@ApiImplicitParam(name = "startTime", value = "开始时间", required = false, dataType = "String", paramType = "query"),
			@ApiImplicitParam(name = "endTime", value = "结束时间", required = false, dataType = "String", paramType = "query"),
			@ApiImplicitParam(name = "userName", value = "用户名", required = false, dataType = "String", paramType = "query"),
			@ApiImplicitParam(name = "realName", value = "真实名称", required = false, dataType = "String", paramType = "query"),
			@ApiImplicitParam(name = "checkState", value = "审计状态 1：正常，2：异常", required = false, dataType = "String", paramType = "query"),
			@ApiImplicitParam(name = "download", value = "是否导出，true/false", required = false, dataType = "Boolean", paramType = "query"),
			@ApiImplicitParam(name = "fileName", value = "导出文件名称(带文件类型后缀，例如.csv)", required = false, dataType = "String", paramType = "query"),
			@ApiImplicitParam(name = "type", value = "导出文件类型，例如 csv", required = false, dataType = "String", paramType = "query")
	})
	public com.epaylinks.efps.common.util.page.PageResult<OpLogReport> pageQueryReport(
			@RequestParam(value = "pageSize" ) Integer pageSize,
			@RequestParam(value = "pageNum") Integer pageNum,
			String startTime,
			String endTime,
			String userName,
			String realName,
			String checkState,
			@RequestParam(value = "download", required = false) Boolean download,
			@RequestParam(value = "fileName", required = false) String fileName,
			@RequestParam(value = "type", required = false) String type,
			@RequestHeader(value = "x-userid", required = true) Long userId
	) {

		com.epaylinks.efps.common.util.page.PageResult<OpLogReport> pageResult = null;
		try {
			if (StringUtils.isNotBlank(startTime)) {
				startTime = startTime.replaceAll("(\\d{4})(\\d{2})(\\d{2})","$1-$2-$3");
			}

			if (StringUtils.isNotBlank(endTime)) {
				endTime = endTime.replaceAll("(\\d{4})(\\d{2})(\\d{2})","$1-$2-$3");
			}

			//用于封装查询条件
			int endNum = pageSize * pageNum;
			int startNum = endNum - pageSize + 1;

			// 用于封装查询条件
			Map<String, Object> map = new HashMap<>();
			map.put("startTime", startTime);
			map.put("endTime", endTime);
			map.put("userName", userName);
			map.put("realName", realName);
			map.put("checkState", checkState);
			map.put("startNum", startNum);
			map.put("endNum", endNum);
			logger.printMessage("分页查询每日操作审计报告，查询条件："+map);
			pageResult = opLogReportService.OperationLogReport(map);

			return pageResult;

		} catch (Exception e) {
			logger.printMessage("分页查询每日操作审计报告，异常："+e.getMessage());
			logger.printLog(e);
			pageResult = new com.epaylinks.efps.common.util.page.PageResult<OpLogReport>();
			if (e instanceof AppException) {
				pageResult.setResult(((AppException) e).getErrorCode());
				pageResult.setErrorMsg(((AppException) e).getErrorMsg());
			} else {
				pageResult.setResult(PasCode.SYSTEM_EXCEPTION.code);
				pageResult.setErrorMsg(PasCode.SYSTEM_EXCEPTION.message);
			}
			return pageResult;
		}
	}


	@PostMapping("/logCheck")
	@Logable(businessTag = "opLog.logCheck")
	@Exceptionable
	@ApiOperation(value = "指定日期生成操作审计报告", notes = "指定日期生成操作审计报告", httpMethod = "POST")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "opTime", value = "操作时间，格式：yyyy-mm-dd", required = false, dataType = "String", paramType = "query")
	})
	public CommonOuterResponse logCheck(
			@RequestParam(value = "opTime",required = false) String opTime
	){
		CommonOuterResponse response  = new CommonOuterResponse();
		try {
			String res = opLogReportService.checkOpLogReportByDate(opTime);
			response.setReturnMsg(res);
		} catch (Exception e) {
			logger.printMessage("指定日期生成操作审计报告，异常："+e.getMessage());
			logger.printLog(e);
			if (e instanceof AppException) {
				response.setReturnCode(((AppException) e).getErrorCode());
				response.setReturnMsg(((AppException) e).getErrorMsg());
			} else {
				response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
				response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
			}
		}
		return response;
	}


	@PostMapping("/logAudit")
	@Logable(businessTag = "opLog.logAudit")
	@Exceptionable
	@ApiOperation(value = "操作日志异常处理", notes = "操作日志异常处理", httpMethod = "POST")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "opId", value = "操作日志ID", required = false, dataType = "long", paramType = "query"),
			@ApiImplicitParam(name = "auditMsg", value = "异常处理情况，限制最多输入50中文字符", required = false, dataType = "String", paramType = "query"),
	})
	public CommonOuterResponse logAudit(
			@RequestParam(value = "opId",required = false) Long opId,
			@RequestParam(value = "auditMsg",required = false) String auditMsg,
			@RequestHeader(value = "x-userid", required = true) Long userId
	){
		CommonOuterResponse response  = new CommonOuterResponse();
		try {
			//根据userId查询用户名和真实姓名
			User user = userService.setUserById(userId);
			if(Objects.isNull(user)){
				response.setReturnCode(PasCode.USER_NOT_EXIST.code);
				response.setReturnMsg(PasCode.USER_NOT_EXIST.message);
				return response;
			}
			if(StringUtils.isNotBlank(auditMsg) && auditMsg.length()>50){
				response.setReturnCode(PasCode.ERROR_PARAM.code);
				response.setReturnMsg("异常处理情况，最多输入50字符");
				return response;
			}
			String res = opLogReportService.logAudit(opId, auditMsg, user.getName());
			response.setReturnMsg(res);
		} catch (Exception e) {
			logger.printMessage("操作日志异常处理，异常："+e.getMessage());
			logger.printLog(e);
			if (e instanceof AppException) {
				response.setReturnCode(((AppException) e).getErrorCode());
				response.setReturnMsg(((AppException) e).getErrorMsg());
			} else {
				response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
				response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
			}
		}
		return response;
	}

}
