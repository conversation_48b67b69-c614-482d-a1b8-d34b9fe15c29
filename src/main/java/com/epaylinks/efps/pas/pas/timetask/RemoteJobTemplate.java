package com.epaylinks.efps.pas.pas.timetask;

import org.quartz.JobExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component("RemoteJobTemplate")
public class RemoteJobTemplate implements BaseTaskJob {
    @Autowired
    RemoteJobService remoteJobService;

    @Override
    public void execute(JobExecutionContext context) {
        remoteJobService.executeJob(context);
    }

}
