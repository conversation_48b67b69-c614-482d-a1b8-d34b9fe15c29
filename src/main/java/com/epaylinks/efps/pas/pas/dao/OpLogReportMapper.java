package com.epaylinks.efps.pas.pas.dao;

import com.epaylinks.efps.pas.pas.domain.OpLogReport;
import com.epaylinks.efps.pas.pas.domain.OperationLogReport;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface OpLogReportMapper {
    /**
     * 获取主键ID
     * @return
     */
    Long selectIdFromSeq();

    int deleteByPrimaryKey(Long reportId);

    int insert(OpLogReport record);

    int insertSelective(OpLogReport record);

    OpLogReport selectByPrimaryKey(Long reportId);

    int updateByPrimaryKeySelective(OpLogReport record);

    int updateByPrimaryKey(OpLogReport record);

    List<OperationLogReport> queryOperationByDate(@Param("operDate")String operDate);

    boolean queryOpAuditExist(@Param("operDate") String operDate);

    String selectPermByModule(@Param("opModule") String opModule);

    int countOperLogReport(Map<String, Object> map);

    List<OpLogReport> pageOperLogReport(Map<String, Object> map);
}