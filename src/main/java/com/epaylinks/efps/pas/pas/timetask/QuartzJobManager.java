package com.epaylinks.efps.pas.pas.timetask;


import com.alibaba.fastjson.JSON;
import com.epaylinks.efps.common.util.SpringContextUtils;
import com.epaylinks.efps.pas.pas.domain.PasTimeTask;
import org.quartz.*;
import org.quartz.impl.matchers.GroupMatcher;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
public class QuartzJobManager {
    private static final Logger logger = LoggerFactory.getLogger(QuartzJobManager.class);
    private static final String TASK_JSON = "taskJson";

    private static QuartzJobManager jobUtil;

    @Autowired
    private Scheduler scheduler;

    public QuartzJobManager() {
        jobUtil = this;
    }

    public static QuartzJobManager getInstance() {
        return QuartzJobManager.jobUtil;
    }


    public void addJob(PasTimeTask task) throws Exception {
        // 启动调度器
        if (!scheduler.isStarted()) {
            scheduler.start();
        }

        Class clazz = SpringContextUtils.getBean(task.getBeanName()).getClass();

        //构建job信息
        JobDetail jobDetail =
                JobBuilder.newJob(clazz).withIdentity(task.getJobName(), task.getJobName()).build();


        //表达式调度构建器(即任务执行的时间)
        CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule(task.getCron());

        //按新的cronExpression表达式构建一个新的trigger
        CronTrigger trigger = TriggerBuilder.newTrigger().withIdentity(task.getJobName(), task.getJobName())
                .withSchedule(scheduleBuilder).build();

        //保存任务JSON，用来判断是否需要更新任务调度
        trigger.getJobDataMap().put(TASK_JSON, JSON.toJSONString(task));
        logger.info("新增任务[{}]调度，[{}][{}]", task.getJobName(), task.getBeanName(), task.getCron());
        scheduler.scheduleJob(jobDetail, trigger);
    }

    public void pauseJob(PasTimeTask task) throws SchedulerException {
        scheduler.pauseJob(JobKey.jobKey(task.getJobName(), task.getJobName()));
    }

    public void resumeJob(PasTimeTask task) throws SchedulerException {
        scheduler.resumeJob(JobKey.jobKey(task.getJobName(), task.getJobName()));
    }

    /**
     * 更新
     *
     * @param task
     */
    public void updateJob(PasTimeTask task) throws Exception {
        TriggerKey triggerKey = TriggerKey.triggerKey(task.getJobName(), task.getJobName());
        Trigger trigger = scheduler.getTrigger(triggerKey);
        JobDataMap jobDataMap = trigger.getJobDataMap();

        //取保存的taskJson
        String taskJson = jobDataMap.getString(TASK_JSON);
        PasTimeTask oldTask = JSON.parseObject(taskJson, PasTimeTask.class);
        if (!Objects.equals(oldTask.getCron(), task.getCron()) ||
                !Objects.equals(oldTask.getBeanName(), task.getBeanName())) { //任务有更新
            logger.info("更新任务[{}]调度，旧[{}][{}]新[{}][{}]",
                    task.getJobName(),
                    oldTask.getBeanName(),
                    oldTask.getCron(),
                    task.getBeanName(),
                    task.getCron());
            deleteJob(task);
            addJob(task);
        }
    }

    /**
     * 删除一个job
     *
     * @param task
     * @throws SchedulerException
     */
    public void deleteJob(PasTimeTask task) throws SchedulerException {
        String jobName = task.getJobName();
        deleteJobByName(jobName);
    }

    /**
     * 通过jobName删除一个调度任务
     *
     * @param jobName
     * @throws SchedulerException
     */
    public void deleteJobByName(String jobName) throws SchedulerException {
        logger.info("删除任务[{}]调度", jobName);
        scheduler.pauseTrigger(TriggerKey.triggerKey(jobName, jobName));
        scheduler.unscheduleJob(TriggerKey.triggerKey(jobName, jobName));
        scheduler.deleteJob(JobKey.jobKey(jobName, jobName));
    }

    /**
     * 启动全部JOBS
     */
    public void startAllJobs() {
        try {
            scheduler.start();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 停掉所有的JOBS
     */
    public void shutdownAllJobs() {
        try {
            if (!scheduler.isShutdown()) {
                scheduler.shutdown();
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }


    }

    /**
     * 获取所有任务列表
     *
     * @return
     * @throws SchedulerException
     */
    public List<Map<String, Object>> getAllJob() throws SchedulerException {
        GroupMatcher<JobKey> matcher = GroupMatcher.anyJobGroup();
        Set<JobKey> jobKeys = scheduler.getJobKeys(matcher);
        List<Map<String, Object>> jobList = new ArrayList<>();
        for (JobKey jobKey : jobKeys) {
            List<? extends Trigger> triggers = scheduler.getTriggersOfJob(jobKey);
            for (Trigger trigger : triggers) {
                Map<String, Object> job = new HashMap<>();
                job.put("jobName", jobKey.getName());
                job.put("jobGroupName", jobKey.getGroup());
                job.put("trigger", trigger.getKey());
                Trigger.TriggerState triggerState = scheduler.getTriggerState(trigger.getKey());
                job.put("jobStatus", triggerState.name());
                if (trigger instanceof CronTrigger) {
                    CronTrigger cronTrigger = (CronTrigger) trigger;
                    String cronExpression = cronTrigger.getCronExpression();
                    job.put("cronExpression", cronExpression);
                }
                jobList.add(job);
            }
        }
        return jobList;
    }

    /**
     * 获取定时任务状态
     */
    public String getTriggerState(TriggerKey triggerKey) throws SchedulerException {
        Trigger.TriggerState triggerState = scheduler.getTriggerState(triggerKey);
        return triggerState.name();
    }

    /**
     * 获取所有原生定时任务
     *
     * @return
     * @throws SchedulerException
     */
    public List<Trigger> getAllTrigger() throws SchedulerException {
        GroupMatcher<JobKey> matcher = GroupMatcher.anyJobGroup();
        Set<JobKey> jobKeys = scheduler.getJobKeys(matcher);
        List<Trigger> allTriggers = new ArrayList<>();
        for (JobKey jobKey : jobKeys) {
            List<? extends Trigger> triggers = scheduler.getTriggersOfJob(jobKey);
            allTriggers.addAll(triggers);
        }
        return allTriggers;
    }

    public Trigger.TriggerState queryTrggerState(PasTimeTask task) throws Exception {
        TriggerKey triggerKey = TriggerKey.triggerKey(task.getJobName(), task.getJobName());
        Trigger trigger = scheduler.getTrigger(triggerKey);
        if (trigger == null) {
            return null;
        }
        return scheduler.getTriggerState(trigger.getKey());
    }


    public Trigger queryOneJob(String jobName, String jobGroup) throws SchedulerException {
        TriggerKey triggerKey = TriggerKey.triggerKey(jobName, jobGroup);
        return scheduler.getTrigger(triggerKey);
    }

}
