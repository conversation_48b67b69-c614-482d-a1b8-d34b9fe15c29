package com.epaylinks.efps.pas.pas.service.impl;

import com.epaylinks.efps.common.dataimport.BatchService;
import com.epaylinks.efps.common.dataimport.model.BatchDetail;
import com.epaylinks.efps.common.dataimport.util.CustReturnCode;
import com.epaylinks.efps.common.dataimport.util.DataImportConstants;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.common.util.StringUtils;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.pas.common.LogService;
import com.epaylinks.efps.pas.pas.dao.ComplainTradeMapper;
import com.epaylinks.efps.pas.pas.domain.ComplainCustVo;
import com.epaylinks.efps.pas.pas.domain.ComplainTrade;
import com.epaylinks.efps.pas.pas.service.ComplainTradeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service("ComplainTradeServiceImpl")
public class ComplainTradeServiceImpl implements ComplainTradeService, BatchService {

    private static final DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private static final DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

    //private static ExecutorService fixedThreadPool = Executors.newFixedThreadPool(8); // 创建一个线程池


    @Autowired
    private ComplainTradeMapper complainTradeMapper;

    @Autowired
    private LogService pasLogService;

    @Autowired
    private CommonLogger logger;

    @Override
    public Map<Integer, BatchDetail> importData(List<String> titleList, Map<Integer, List<String>> dataMap, Map<String, Object> extraData) {
        SortedMap<Integer, BatchDetail> result = new TreeMap<>();
        String batchNo = extraData.get("batchNo").toString();
        int colCount = titleList.size(); // 以表头列数为数据列数校验
        if (titleList == null || dataMap == null) {
            throw new AppException(CustReturnCode.EXCEL_DATA_ERROR.code, CustReturnCode.EXCEL_DATA_ERROR.message);
        }
        pasLogService.printLog("准备进行批量投诉解析..");
        // 解析来自批量导入的数据
        for (Iterator iterator = dataMap.keySet().iterator(); iterator.hasNext();) {
            Integer rowNo = (Integer) iterator.next();
            try{
                List<String> dataList = dataMap.get(rowNo);
                // 校验非空
                if (dataList.size() != colCount) {
                    throw new AppException(CustReturnCode.EXCEL_DATA_ERROR.code,
                            CustReturnCode.EXCEL_DATA_ERROR.message + ":" + (rowNo + 1) + "行缺少数据");
                }
                ComplainTrade complainTrade = buildComplainTradeImport(dataList,batchNo);
                //校验非空项
                checkEmptyParam(complainTrade.getChannelType(),"上游机构");
                checkEmptyParam(complainTrade.getComplainContent(),"投诉内容");
                checkEmptyParam(complainTrade.getChannelMchId(),"上游商户号");
                if(!"微信".equals(complainTrade.getChannelType()) && !"支付宝".equals(complainTrade.getChannelType())
                        && !"银联".equals(complainTrade.getChannelType()) && !"网联".equals(complainTrade.getChannelType())){
                    throw new AppException(PasCode.SYSTEM_EXCEPTION.code,  "上游机构有误");
                }
                if(!StringUtils.isBlank(complainTrade.getOrderNo()) && complainTrade.getOrderNo().length()>64){
                    throw new AppException(PasCode.SYSTEM_EXCEPTION.code,  "订单号长度过长");
                }
                if(!StringUtils.isBlank(complainTrade.getAmount()) && complainTrade.getAmount().length()>32){
                    throw new AppException(PasCode.SYSTEM_EXCEPTION.code,  "金额长度过长");
                }
                if(!StringUtils.isBlank(complainTrade.getAmount()) && !complainTrade.getAmount().matches("[+-]?[0-9]+(\\.[0-9]{1,4})?")){
                    throw new AppException(PasCode.SYSTEM_EXCEPTION.code,  "金额有误");
                }
                if(!StringUtils.isBlank(complainTrade.getRiskDesc()) && complainTrade.getRiskDesc().length()>500){
                    throw new AppException(PasCode.SYSTEM_EXCEPTION.code,  "风险描述长度过长");
                }
                if(complainTrade.getComplainContent().length()>500){
                    throw new AppException(PasCode.SYSTEM_EXCEPTION.code,  "投诉内容长度过长");
                }
                if(!StringUtils.isBlank(complainTrade.getUserMobile()) && complainTrade.getUserMobile().length()>32){
                    throw new AppException(PasCode.SYSTEM_EXCEPTION.code,  "用户手机长度过长");
                }
                if(complainTrade.getChannelMchId().length()>64){
                    throw new AppException(PasCode.SYSTEM_EXCEPTION.code,  "上游商户号长度过长");
                }
                /*if("微信".equals(complainTrade.getChannelType()) && complainTrade.getChannelMchId().length()>10){
                    throw new AppException(PasCode.SYSTEM_EXCEPTION.code,  "上游商户号与上游渠道不匹配!");
                }else if("支付宝".equals(complainTrade.getChannelType()) && complainTrade.getChannelMchId().length()<16){
                    throw new AppException(PasCode.SYSTEM_EXCEPTION.code,  "上游商户号与上游渠道不匹配!!");
                }*/
                if(!StringUtils.isBlank(complainTrade.getOrderNo()) && !StringUtils.isBlank(complainTrade.getAmount())
                        && complainTradeMapper.isComplainExist(complainTrade.getOrderNo(),complainTrade.getAmount(),complainTrade.getComplainContent())){
                    throw new AppException(PasCode.SYSTEM_EXCEPTION.code,  "投诉记录重复");
                }
                insertRecord(complainTrade);
                result.put(rowNo, buildDetail(rowNo, dataList.get(0), DataImportConstants.SuccessFail.SUCCESS.code, complainTrade.getChannelMchId(), null));
            }catch (Exception e){
                logger.printMessage("导入投诉记录失败："+e.getMessage());
                logger.printLog(e);
                if (e instanceof AppException) {
                    result.put(rowNo, buildDetail(rowNo, null, DataImportConstants.SuccessFail.FAIL.code, null, ((AppException) e).getErrorMsg()));
                }else {
                    result.put(rowNo, buildDetail(rowNo, null, DataImportConstants.SuccessFail.FAIL.code, null, CustReturnCode.SYSTEM_EXCEPTION.message));
                }
            }
        }

        return result;
    }

    private void insertRecord(ComplainTrade complainTrade){
        //fixedThreadPool.execute(new Runnable() {
         //   public void run() {
          //      try {
                    Long id = complainTradeMapper.selectIdFromSeq();
                    complainTrade.setCtId(id);
                    List<ComplainCustVo> list = complainTradeMapper.selectCustByChannel(complainTrade.getChannelMchId());
                    if(list!=null && list.size()>0){
                        ComplainCustVo custVo = list.get(0);
                        if("微信".equals(complainTrade.getChannelType()) && "alipay".equals(custVo.getInletType())){
                            throw new AppException(PasCode.SYSTEM_EXCEPTION.code,  "上游商户号与上游渠道不匹配!");
                        }else if("支付宝".equals(complainTrade.getChannelType()) && "wechat".equals(custVo.getInletType())){
                            throw new AppException(PasCode.SYSTEM_EXCEPTION.code,  "上游商户号与上游渠道不匹配!!");
                        }
                        complainTrade.setCustomerCode(custVo.getCustomerCode());
                    }else {
                        if ("银联".equals(complainTrade.getChannelType())) {
                            List<String> strs = complainTradeMapper.selectCustomerNoByMchtNo(complainTrade.getChannelMchId());
                            if (strs != null && strs.size() > 0) {
                                complainTrade.setCustomerCode(strs.get(0));
                            } else {
                                throw new AppException(PasCode.SYSTEM_EXCEPTION.code,  "上游商户号没有匹配商户");
                            }
                        }
                        if ("网联".equals(complainTrade.getChannelType())) {
                            List<String> strs = complainTradeMapper.selectCustomerNoByCustNo(complainTrade.getChannelMchId());
                            if (strs != null && strs.size() > 0) {
                                complainTrade.setCustomerCode(strs.get(0));
                            } else {
                                throw new AppException(PasCode.SYSTEM_EXCEPTION.code,  "上游商户号没有匹配商户");
                            }
                        }
                    }
                    if(StringUtils.isBlank(complainTrade.getCustomerCode())){
                        throw new AppException(PasCode.SYSTEM_EXCEPTION.code,  "上游商户号没有匹配商户");
                    }
                    ComplainTrade custInfo = complainTradeMapper.selectByCust(complainTrade.getCustomerCode());
                    if(Objects.isNull(custInfo)){
                        throw new AppException(PasCode.SYSTEM_EXCEPTION.code,  "上游商户号没有匹配商户");
                    }
                    complainTrade.setCustName(custInfo.getCustName());
                    complainTrade.setAgentCustomerCode(custInfo.getAgentCustomerCode());
                    complainTrade.setAgentCustName(custInfo.getAgentCustName());
                    complainTrade.setPlatCustomerCode(custInfo.getPlatCustomerCode());
                    complainTrade.setPlatCustName(custInfo.getPlatCustName());
                    complainTrade.setBusinessMan(custInfo.getBusinessMan());
                    complainTrade.setCreateTime(new Date());
                    complainTradeMapper.insert(complainTrade);
         //       }catch (Exception e){
         //           pasLogService.printLog(e);
         //       }
         //   }
       // });
    }

    private BatchDetail buildDetail(Integer rowNo, String rowName, short status, String relateId, String remarks) {
        BatchDetail detail = new BatchDetail();
        detail.setRowNo((long) rowNo);
        detail.setRowName(rowName);
        detail.setStatus(status);
        detail.setRelateId(relateId);
        detail.setRemarks(remarks);
        return detail;
    }

    private ComplainTrade buildComplainTradeImport(List<String> dataList,String batchNo){
        if(dataList==null){
            return null;
        }
        //新建导入数据对象
        ComplainTrade complainTrade = new ComplainTrade();
        //将excel数据设置到对象中
        complainTrade.setBatchNo(batchNo);
        complainTrade.setChannelType(dataList.get(0));//上游机构
        if(StringUtils.isBlank(dataList.get(1))){
            throw new AppException(PasCode.SYSTEM_EXCEPTION.code,  "风险识别时间不能为空");
        }
        try{
            String riskTime = dataList.get(1).trim();
            Boolean flag = checkDateParam(riskTime);
            if (!flag) {
                throw new AppException(CustReturnCode.EXCEL_DATA_ERROR.code,
                        CustReturnCode.EXCEL_DATA_ERROR.message + ":风险识别时间格式错误");
            }
            if (riskTime.length() > 10) {
                complainTrade.setRiskTime(dateFormat.parse(riskTime));//风险识别时间
            } else {
                complainTrade.setRiskTime(sdf.parse(riskTime));
            }
        }catch (Exception e){
            throw new AppException(CustReturnCode.EXCEL_DATA_ERROR.code,
                    CustReturnCode.EXCEL_DATA_ERROR.message + ":风险识别时间格式错误");
        }
        try{
            String transTime = dataList.get(2);
            if(!StringUtils.isBlank(transTime)){
                transTime = transTime.trim();
                Boolean flag = checkDateParam(transTime);
                if (!flag) {
                    throw new AppException(CustReturnCode.EXCEL_DATA_ERROR.code,
                            CustReturnCode.EXCEL_DATA_ERROR.message + ":交易时间格式错误");
                }
                if (transTime.length() > 10) {
                    complainTrade.setTradeTime(dateFormat.parse(transTime));//交易时间
                } else {
                    complainTrade.setTradeTime(sdf.parse(transTime));
                }
            }
        }catch (Exception e){
            throw new AppException(CustReturnCode.EXCEL_DATA_ERROR.code,
                    CustReturnCode.EXCEL_DATA_ERROR.message + ":交易时间格式错误");
        }
        if(!StringUtils.isBlank(dataList.get(3))) {
            complainTrade.setOrderNo(dataList.get(3));//订单号
        }
        if(!StringUtils.isBlank(dataList.get(4))) {
            complainTrade.setAmount(dataList.get(4));//金额
        }
        if(!StringUtils.isBlank(dataList.get(5))) {
            complainTrade.setRiskDesc(dataList.get(5));//风险描述
        }
        complainTrade.setComplainContent(dataList.get(6));//投诉内容
        if(!StringUtils.isBlank(dataList.get(7))) {
            complainTrade.setUserMobile(dataList.get(7));//用户手机
        }
        complainTrade.setChannelMchId(dataList.get(8));//上游商户号
        return complainTrade;
    }

    /**
     * 校验空字符串
     * @param value
     * @param name
     */
    private void checkEmptyParam(String value, String name) {
        if(StringUtils.isBlank(value)) {
            throw new AppException(PasCode.SYSTEM_EXCEPTION.code,  name + "不能为空");
        }
    }

    @Override
    public List<ComplainTrade> pageQuery(Map map) {
        return complainTradeMapper.selectByPage(map);
    }

    @Override
    public int queryCount(Map map) {
        return complainTradeMapper.selectCount(map);
    }

    private Boolean checkDateParam(String param) {
        // yyyy-MM-dd || yyyy-MM-dd HH:mm:ss
        String regex = "^[123]\\d{3}-([1-9]|([0][1-9])|([1][0-2]))-([1-9]|([012]\\d)|([3][01]))" +
                "(\\s" +
                "([0-9]|([0][0-9])|([1]\\d)|([2][0-4]))" +
                ":([0-9]|([0][0-9])|([12345][0-9]))" +
                ":([0-9]|([0][0-9])|([12345][0-9]))" +
                ")?";
        Pattern pattern= Pattern.compile(regex);
        Matcher matcher = pattern.matcher(param);
        return matcher.matches();
    }

    public static void main(String[] args) {
//        String param = "2022-08-12 23:00:01";
//        ComplainTradeServiceImpl test = new ComplainTradeServiceImpl();
//        System.out.println("结果：" + test.checkDateParam(param));


        String regex = "^[123]\\d{3}([1-9]|([0][1-9])|([1][0-2]))([1-9]|([012]\\d)|([3][01]))\\d{2}";
        Pattern pattern= Pattern.compile(regex);
        Matcher matcher = pattern.matcher("2022131201");
        System.out.println("结果：" + matcher.matches());
    }
}
