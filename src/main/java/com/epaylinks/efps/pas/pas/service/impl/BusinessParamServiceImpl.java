package com.epaylinks.efps.pas.pas.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.epaylinks.efps.pas.pas.dao.BusinessMapper;
import com.epaylinks.efps.pas.pas.dao.BusinessParamMapper;
import com.epaylinks.efps.pas.pas.domain.Business;
import com.epaylinks.efps.pas.pas.domain.BusinessParam;
import com.epaylinks.efps.pas.pas.service.BusinessParamService;
@Service
public class BusinessParamServiceImpl implements BusinessParamService{
	@Autowired
	private BusinessMapper businessMapper;
	@Autowired
	private BusinessParamMapper businessParamMapper;
	
	@Override
	public List<BusinessParam> getBusinessParamByBusinessCode(String businessCode) {
		// TODO Auto-generated method stub
		Business business = businessMapper.selectByCode(businessCode);
		if (business == null) {
			return null;
		}
		List<BusinessParam> businessParams = businessParamMapper.selectByBusinessId(business.getId());
		return businessParams;
	}


}
