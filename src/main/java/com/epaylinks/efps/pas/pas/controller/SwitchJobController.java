package com.epaylinks.efps.pas.pas.controller;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.pas.common.LogService;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.pas.timetask.TaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@Api(value = "SwitchJobController", description = "")
public class SwitchJobController {
    @Autowired
    private TaskService taskService;

    @Autowired
    private LogService logService;

    @PostMapping("/SwitchJobPartition")
    @Logable(businessTag = "SwitchJobController.SwitchJobPartition")
    @ApiOperation(value = "切换定时任务分区", notes = "切换定时任务分区", httpMethod = "POST")
    public CommonOuterResponse<Integer> switchJob(@RequestParam("partition") String partition,
                                                  @RequestParam(value = "job_name",required = false) String jobName) {
        CommonOuterResponse response = new CommonOuterResponse<>();
        try {
            Integer count = taskService.switchJobPartition(partition,jobName);
            response = CommonOuterResponse.success(count);
        } catch (AppException e) {
            response.setReturnCode(e.getErrorCode());
            response.setReturnMsg(e.getErrorMsg());
        } catch (Exception e) {
            logService.printLog("定时任务分区切换error:" + e.getMessage());
            response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
            response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
        }
        return response;
    }

    @GetMapping("/queryJobsPartition")
    @Logable(businessTag = "queryJobsPartition")
    @ApiOperation(value = "切换定时任务分区", notes = "切换定时任务分区", httpMethod = "GET")
    public CommonOuterResponse<Map<String, List<String>>> queryJobsPartition() {
        CommonOuterResponse response = new CommonOuterResponse<>();
        Map<String, List<String>> result = taskService.queryJobsPartition();
        response = CommonOuterResponse.success(result);
        return response;
    }

}
