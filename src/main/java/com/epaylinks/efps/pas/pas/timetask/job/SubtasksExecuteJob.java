package com.epaylinks.efps.pas.pas.timetask.job;

import com.epaylinks.efps.common.timetask.TaskJob;
import com.epaylinks.efps.common.timetask.TaskRequest;
import com.epaylinks.efps.common.tool.time.Timex;
import com.epaylinks.efps.pas.common.PasConstants;
import com.epaylinks.efps.pas.pas.dao.PasTimeTaskMapper;
import com.epaylinks.efps.pas.pas.dao.PasTimeTaskRecordMapper;
import com.epaylinks.efps.pas.pas.domain.PasTimeTask;
import com.epaylinks.efps.pas.pas.timetask.RemoteJobService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class SubtasksExecuteJob implements TaskJob {
    private static Logger logger = LoggerFactory.getLogger(SubtasksExecuteJob.class);

    @Autowired
    private PasTimeTaskRecordMapper taskRecordMapper;

    @Autowired
    private PasTimeTaskMapper taskMapper;

    @Autowired
    RemoteJobService remoteJobService;

    @Override
    public void execute(TaskRequest request) {
        long today = Timex.now().start().toMillis();
        //获取数据库中所有启用的并且今天未执行的子任务
        List<PasTimeTask> tasks = taskMapper.selectAll().stream()
                .filter(task ->
                        PasConstants.timeTaskStatus.OPEN.code.equalsIgnoreCase(task.getJobStatus()) &&
                                StringUtils.isNotBlank(task.getParentJobs()) &&
                                (task.getLastFireTime() == null ||
                                        Timex.ofDate(task.getLastFireTime()).toMillis() < today))
                .collect(Collectors.toList());
        for (PasTimeTask task : tasks) {
            try {
                logger.info("检查并启动子任务" + task.getJobName());
                checkAndExecute(task);
            } catch (Exception e) {
                logger.error("检查并启动子任务" + task.getJobName() + "失败", e);
            }
        }
    }

    private void checkAndExecute(PasTimeTask task) {
        Date start = Timex.now().start().toDate();
        Date end = Timex.now().start().plus(Duration.ofDays(1)).toDate();
        String[] parentJobs = task.getParentJobs().split(",");
        //检查所有上级任务是否都已经完成
        for (String parentJob : parentJobs) {
            if (taskRecordMapper.countJobDone(start, end, parentJob) == 0) {
                logger.info("{}的父任务{}未完成", task.getJobName(), parentJob);
                return;
            } else {
                logger.info("{}的父任务{}完成", task.getJobName(), parentJob);
            }
        }

        logger.info("启动子任务" + task.getJobName());
        remoteJobService.executeJob(task.getJobName(), start);

    }

}
