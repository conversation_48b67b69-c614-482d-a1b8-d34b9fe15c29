package com.epaylinks.efps.pas.pas.service;

import com.epaylinks.efps.pas.common.PageResult;
import com.epaylinks.efps.pas.pas.domain.OperationLog;
import com.epaylinks.efps.pas.pas.domain.OperationLogReport;

import java.util.Map;

public interface OperationLogService {

	/**
	 * 插入操作日志记录
	 */
	void insert(OperationLog opLog);

	/**
	 * 分页查询
	 */
	PageResult<OperationLog> pageQuery(int pageSize, int pageNum, Map<String,String>map);

	/**
	 * 分页查询操作日志报表
	 * @param pageSize
	 * @param pageNum
	 * @param map
	 * @return
	 */
    com.epaylinks.efps.common.util.page.PageResult<OperationLogReport> OperationLogReport(Map<String, Object> paramMap);

}
