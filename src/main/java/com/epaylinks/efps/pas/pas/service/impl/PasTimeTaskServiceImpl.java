package com.epaylinks.efps.pas.pas.service.impl;

import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.pas.pas.dao.PasTimeTaskMapper;
import com.epaylinks.efps.pas.pas.domain.PasTimeTask;
import com.epaylinks.efps.pas.pas.service.PasTimeTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class PasTimeTaskServiceImpl implements PasTimeTaskService {

    @Autowired
    private PasTimeTaskMapper pasTimeTaskMapper;

    @Override
    public List<PasTimeTask> selectAll() {
        return pasTimeTaskMapper.selectAll();
    }

    @Override
    public PasTimeTask selectByJobName(String jobName) {
        return pasTimeTaskMapper.selectByJobName(jobName);
    }


    @Logable(businessTag = "timtaskLog")
    @Override
    public void timtaskLog(String message){

    }
}
