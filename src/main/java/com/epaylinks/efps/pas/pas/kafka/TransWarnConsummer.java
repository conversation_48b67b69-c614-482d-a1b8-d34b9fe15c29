package com.epaylinks.efps.pas.pas.kafka;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.pas.pas.common.PasConstant;
import com.epaylinks.efps.pas.pas.dao.PasTransWarnMapper;
import com.epaylinks.efps.pas.pas.vo.PasTransWarn;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.KafkaListener;

import java.sql.SQLIntegrityConstraintViolationException;
import java.util.Date;
import java.util.Optional;

@Configuration
public class TransWarnConsummer {

    @Autowired
    private PasTransWarnMapper pasTransWarnMapper;

    @Autowired
    private SequenceService sequenceService;

    @Autowired
    private CommonLogger logger;

    @Logable(businessTag = "TRANS_WARN")
    @KafkaListener(topics = {"TRANS_WARN"},containerFactory = "customContainerFactory")
    public void consumeMsg(ConsumerRecord<?, ?> record) {
        Optional<?> kafkaMessage = Optional.ofNullable(record.value());
        if (kafkaMessage.isPresent()) {
            String message = (String) kafkaMessage.get();
            Object obj = JSONObject.parse(message);
            JSONObject jsonObj = JSON.parseObject(obj.toString());
            String key = (String) record.key();

            //查询是否已经存在了
//            PasTransWarn transWarn = pasTransWarnMapper.selectByTransactionNo(jsonObj.get("transactionNo"));
//            if(transWarn != null){
//                transWarn.setUpdateTime(new Date());
//                transWarn.setWarnTimes(transWarn.getWarnTimes() + 1);
//                pasTransWarnMapper.updateByPrimaryKey(transWarn);
//            }else {
//                transWarn = new PasTransWarn();
//                transWarn.setId(sequenceService.nextValue("TRANS_WARN"));
//                transWarn.setTransactionNo(jsonObj.getString("transactionNo"));
//                transWarn.setTransactionMsg(jsonObj.getString("transactionMsg"));
//                transWarn.setType(jsonObj.getString("transactionType"));
//                transWarn.setCreateTime(new Date());
//                transWarn.setUpdateTime(new Date());
//                transWarn.setWarnTimes(1);
//                transWarn.setSource(key);
//                transWarn.setReason(jsonObj.getString("reason"));
//                transWarn.setStatus(PasConstant.WranStatus.PROCESSING.code);
//                transWarn.setWarnLevel(PasConstant.WarnLevel.NOMAL.code);
//                pasTransWarnMapper.insertSelective(transWarn);
//            }
            //
            PasTransWarn transWarn = new PasTransWarn();
            transWarn.setTransactionNo(jsonObj.getString("transactionNo"));
            transWarn.setTransactionMsg(jsonObj.getString("transactionMsg"));
            transWarn.setType(jsonObj.getString("transactionType"));
            transWarn.setUpdateTime(new Date());
            transWarn.setWarnTimes(1);
            transWarn.setSource(key);
            transWarn.setReason(jsonObj.getString("reason"));
            transWarn.setStatus(PasConstant.WranStatus.PROCESSING.code);
            transWarn.setWarnLevel(PasConstant.WarnLevel.NOMAL.code);
            int c = -1;
            try {
                c = pasTransWarnMapper.updateByTransactionNo(transWarn);
            } catch(Exception e){
                logger.printMessage(transWarn.getTransactionNo()+"订单号，告警更新错误："+e.getLocalizedMessage());
                return;
            }
            if(c == 0) {
                transWarn.setId(sequenceService.nextValue("TRANS_WARN"));
                transWarn.setCreateTime(new Date());
                try {
                    pasTransWarnMapper.insertSelective(transWarn);
                } catch(Exception e){
                    if(e instanceof SQLIntegrityConstraintViolationException){
                        logger.printMessage(transWarn.getTransactionNo()+"订单号，违反唯一约束："+e.getLocalizedMessage());
                    }
                }
            }
        }
    }






}
