package com.epaylinks.efps.pas.pas.domain;

import com.epaylinks.efps.common.datadownload.annotation.FieldAnnotation;
import com.epaylinks.efps.pas.common.PasConstants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

@Data
@ApiModel(value = "待开调额记录")
public class DkteRecord {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", dataType = "Long")
    private Long dkteId;

    /**
     * 易票联流水号
     */
    @ApiModelProperty(value = "易票联流水号", dataType = "String")
    @FieldAnnotation(fieldName="易票联流水号")
    private String transactionNo;

    /**
     * 记账凭证号
     */
    @ApiModelProperty(value = "记账凭证号", dataType = "String")
    private String accVoucherNo;

    /**
     * 商户编号
     */
    @ApiModelProperty(value = "商户编号", dataType = "String")
    private String customerCode;

    /**
     * 商户名称
     */
    @ApiModelProperty(value = "商户名称", dataType = "String")
    private String customerName;

    @FieldAnnotation(fieldName="商户")
    private String customer;

    /**
     * 调额类型：1增额，2减额
     */
    @ApiModelProperty(value = "调额类型：1增额，2减额,3:冻结,4:解冻", dataType = "String")
    @FieldAnnotation(fieldName="调额类型",dictionaries="1:增额,2:减额,3:冻结,4:解冻")
    private String adjustType;

    @ApiModelProperty(value = "调额类型展示", dataType = "String")
    private String adjustTypeShow;

    /**
     * 调额事由：BZFR，补增分润差额；KJSD，扣减发票税点；KJFR，扣减分润差额；
     */
    @ApiModelProperty(value = "调额事由：BZFR，补增分润差额；KJSD，扣减发票税点；KJFR，扣减分润差额；,KCFR:扣除分润,DJFR:冻结分润,JDFR:解冻分润", dataType = "String")
    @FieldAnnotation(fieldName="调额事由",dictionaries="BZFR:补增分润差额,KJSD:扣减发票税点,KJFR:扣减分润差额,KCFR:扣除分润,DJFR:冻结分润,JDFR:解冻分润")
    private String adjustReason;

    @ApiModelProperty(value = "调额事由展示", dataType = "String")
    private String adjustReasonShow;

    /**
     * 调整金额，单位分
     */
    @ApiModelProperty(value = "调整金额，单位分", dataType = "Long")
    private Long adjustAmout;

    @FieldAnnotation(fieldName="调整金额（元）")
    private String adjustAmoutShow;

    /**
     * 附件ID
     */
    @ApiModelProperty(value = "附件ID", dataType = "String")
    private String attachmentUid;

    /**
     * 附件名称
     */
    @ApiModelProperty(value = "附件名称", dataType = "String")
    private String attachmentName;

    /**
     * 附件url
     */
    @ApiModelProperty(value = "附件url", dataType = "String")
    private String attachmentUrl;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", dataType = "String")
    private String remark;

    /**
     * 状态，1：审核通过；2：审核不通过；3：待审核
     */
    @ApiModelProperty(value = "状态，1：审核通过；2：审核不通过；3：待审核", dataType = "String")
    @FieldAnnotation(fieldName="状态",dictionaries="1:审核通过,2:审核不通过,3:待审核")
    private String recordState;

    @ApiModelProperty(value = "状态展示", dataType = "String")
    private String recordStateShow;

    /**
     * 创建人ID
     */
    @ApiModelProperty(value = "创建人ID", dataType = "String")
    private String createPersonId;

    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间 yyyy-MM-dd HH:mm:ss", dataType = "String")
    @FieldAnnotation(fieldName="创建时间", dateFormat="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 创建人名称
     */
    @ApiModelProperty(value = "创建人名称", dataType = "String")
    @FieldAnnotation(fieldName="创建人")
    private String createPersonName;

    /**
     * 修改时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "修改时间 yyyy-MM-dd HH:mm:ss", dataType = "String")
    private Date updateTime;

    /**
     * 审核时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "审核时间 yyyy-MM-dd HH:mm:ss", dataType = "String")
    private Date auditTime;

    /**
     * 审核人ID
     */
    @ApiModelProperty(value = "审核人ID", dataType = "String")
    private String auditPersonId;

    /**
     * 审核人名称
     */
    @ApiModelProperty(value = "审核人名称", dataType = "String")
    private String auditPersonName;

    /**
     * 审核结果
     */
    @ApiModelProperty(value = "审核结果", dataType = "String")
    private String auditResult;

    /**
     * 审核意见
     */
    @ApiModelProperty(value = "审核意见", dataType = "String")
    private String auditReason;

    @ApiModelProperty(value = "原冻结流水", dataType = "String")
    private String origFreezeNo;

    public Long getDkteId() {
        return dkteId;
    }

    public void setDkteId(Long dkteId) {
        this.dkteId = dkteId;
    }

    public String getTransactionNo() {
        return transactionNo;
    }

    public void setTransactionNo(String transactionNo) {
        this.transactionNo = transactionNo;
    }

    public String getAccVoucherNo() {
        return accVoucherNo;
    }

    public void setAccVoucherNo(String accVoucherNo) {
        this.accVoucherNo = accVoucherNo;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getCustomer() {
        if(Objects.nonNull(customerCode)){
            String tempCust = customerCode;
            if(Objects.nonNull(customerName)){
                tempCust = customerCode+" "+customerName;
            }
            return tempCust;
        }
        return customer;
    }

    public void setCustomer(String customer) {
        this.customer = customer;
    }

    public String getAdjustType() {
        return adjustType;
    }

    public void setAdjustType(String adjustType) {
        this.adjustType = adjustType;
    }

    public String getAdjustTypeShow() {
        return PasConstants.AdjustmentType.getCommentByCode(adjustType);
    }

    public void setAdjustTypeShow(String adjustTypeShow) {
        this.adjustTypeShow = adjustTypeShow;
    }

    public String getAdjustReason() {
        return adjustReason;
    }

    public void setAdjustReason(String adjustReason) {
        this.adjustReason = adjustReason;
    }

    public String getAdjustReasonShow() {
        return PasConstants.AdjustSubjectMatter.getCommentByCode(adjustReason);
    }

    public void setAdjustReasonShow(String adjustReasonShow) {
        this.adjustReasonShow = adjustReasonShow;
    }

    public Long getAdjustAmout() {
        return adjustAmout;
    }

    public void setAdjustAmout(Long adjustAmout) {
        this.adjustAmout = adjustAmout;
    }

    public String getAdjustAmoutShow() {
        if(Objects.nonNull(adjustAmout)){
            Long tempAmoutFen = adjustAmout;
            if(PasConstants.AdjustmentType.DERATE.code.equals(adjustType) || PasConstants.AdjustmentType.FREEZE.code.equals(adjustType)){
                tempAmoutFen = adjustAmout*(-1);
            }
            BigDecimal tempAmoutYuan = new BigDecimal(tempAmoutFen).divide(new BigDecimal(100),2,BigDecimal.ROUND_HALF_UP);
            return tempAmoutYuan.toString();
        }
        return adjustAmoutShow;
    }

    public void setAdjustAmoutShow(String adjustAmoutShow) {
        this.adjustAmoutShow = adjustAmoutShow;
    }

    public String getAttachmentUid() {
        return attachmentUid;
    }

    public void setAttachmentUid(String attachmentUid) {
        this.attachmentUid = attachmentUid;
    }

    public String getAttachmentName() {
        return attachmentName;
    }

    public void setAttachmentName(String attachmentName) {
        this.attachmentName = attachmentName;
    }

    public String getAttachmentUrl() {
        return attachmentUrl;
    }

    public void setAttachmentUrl(String attachmentUrl) {
        this.attachmentUrl = attachmentUrl;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getRecordState() {
        return recordState;
    }

    public void setRecordState(String recordState) {
        this.recordState = recordState;
    }

    public String getRecordStateShow() {
        //1:审核通过,2:审核不通过,3:待审核
        if("1".equals(recordState)){
            return "审核通过";
        }else if("2".equals(recordState)){
            return "审核不通过";
        }else if("3".equals(recordState)){
            return "待审核";
        }

        return recordStateShow;
    }

    public void setRecordStateShow(String recordStateShow) {
        this.recordStateShow = recordStateShow;
    }

    public String getCreatePersonId() {
        return createPersonId;
    }

    public void setCreatePersonId(String createPersonId) {
        this.createPersonId = createPersonId;
    }

    public String getCreatePersonName() {
        return createPersonName;
    }

    public void setCreatePersonName(String createPersonName) {
        this.createPersonName = createPersonName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(Date auditTime) {
        this.auditTime = auditTime;
    }

    public String getAuditPersonId() {
        return auditPersonId;
    }

    public void setAuditPersonId(String auditPersonId) {
        this.auditPersonId = auditPersonId;
    }

    public String getAuditPersonName() {
        return auditPersonName;
    }

    public void setAuditPersonName(String auditPersonName) {
        this.auditPersonName = auditPersonName;
    }

    public String getAuditResult() {
        return auditResult;
    }

    public void setAuditResult(String auditResult) {
        this.auditResult = auditResult;
    }

    public String getAuditReason() {
        return auditReason;
    }

    public void setAuditReason(String auditReason) {
        this.auditReason = auditReason;
    }
}