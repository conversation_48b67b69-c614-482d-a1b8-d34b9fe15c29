package com.epaylinks.efps.pas.pas.service;

import com.epaylinks.efps.pas.pas.domain.PasLoginInfo;

public interface LoginInfoService {
	
	public void updateErrorRecordCount();
	
	Integer checkPasLoginInfoExist(PasLoginInfo pasLoginInfo);
	
	void insertLoginInfo(PasLoginInfo pasLoginInfo);
	
	void updateLoginInfo(PasLoginInfo pasLoginInfo);
	
	PasLoginInfo searchLoginInfo(PasLoginInfo pasLoginInfo);

	int recoveryErrorCountByUsernameAndType(String username, String usertype);

}
