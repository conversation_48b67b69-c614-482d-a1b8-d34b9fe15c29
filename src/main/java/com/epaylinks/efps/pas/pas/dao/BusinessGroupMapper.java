package com.epaylinks.efps.pas.pas.dao;

import com.epaylinks.efps.pas.pas.domain.BusinessGroup;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BusinessGroupMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table PAS_BUSINESS_GROUP
     *
     * @mbggenerated Mon May 06 13:18:13 CST 2019
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table PAS_BUSINESS_GROUP
     *
     * @mbggenerated Mon May 06 13:18:13 CST 2019
     */
    int insert(BusinessGroup record);


    int insertSelective(BusinessGroup record);


    BusinessGroup selectByPrimaryKey(Long id);


    int updateByPrimaryKeySelective(BusinessGroup record);


    int updateByPrimaryKey(BusinessGroup record);

    List<BusinessGroup> selectAllBusinssGroup(String isTemplate);

    List<BusinessGroup> selectAllTemplateBusinssGroup(BusinessGroup record);


//    List<BusinessGroupNode> selectBusinessGroupTree(Map map);

    BusinessGroup selectByCode(@Param("code") String code);

}