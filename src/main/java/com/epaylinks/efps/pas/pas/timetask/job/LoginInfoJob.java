package com.epaylinks.efps.pas.pas.timetask.job;

import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.pas.pas.service.OpLogReportService;
import com.epaylinks.efps.pas.pas.service.impl.UnionCardBinFtpServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.epaylinks.efps.common.timetask.TaskJob;
import com.epaylinks.efps.common.timetask.TaskRequest;
import com.epaylinks.efps.pas.pas.service.LoginInfoService;

/**
* 定时任务将登录错误次数清零
* <AUTHOR>
* @date：2020年5月13日 上午10:23:53
*/
@Component("LoginInfoJob")
public class LoginInfoJob implements TaskJob {
    
    @Autowired
    LoginInfoService loginInfoService;

	@Autowired
	private UnionCardBinFtpServiceImpl unionCardBinFtpService;

	@Autowired
	private CommonLogger logger;
    
    
	@Override
	public void execute(TaskRequest request) throws Exception {
		try{
			loginInfoService.updateErrorRecordCount();
		}catch (Exception e){
			logger.printMessage("定时任务更新登录错误次数，异常："+e.getMessage());
			logger.printLog(e);
		}
		try{
			unionCardBinFtpService.loadCardBinForJob(null);	//新增一个定时任务每月10或25号加载银联卡bin
		}catch (Exception e){
			logger.printMessage("加载银联卡Bin，异常："+e.getMessage());
			logger.printLog(e);
		}
	}

}
