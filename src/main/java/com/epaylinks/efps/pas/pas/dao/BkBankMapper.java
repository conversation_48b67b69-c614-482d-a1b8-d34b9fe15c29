package com.epaylinks.efps.pas.pas.dao;

import com.epaylinks.efps.pas.pas.model.BkBank;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface BkBankMapper {
    int insert(BkBank record);

    int insertSelective(BkBank record);

    BkBank selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(BkBank record);

    List<BkBank> selectBankPage(Map map);

    List<BkBank> selectBankNotPage(Map map);

    int selectCountByMap(Map map);

    List<BkBank> selectByBankId(Long bankId);

    List<BkBank> selectByBankCodeOrBankName(@Param("bankCode") String bankCode,@Param("bankName") String bankName);
    List<BkBank> selectByIssueBankNo(@Param("issueBankNo") String issueBankNo);
}