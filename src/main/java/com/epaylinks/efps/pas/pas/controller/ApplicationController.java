package com.epaylinks.efps.pas.pas.controller;

import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.pas.pas.domain.Application;
import com.epaylinks.efps.pas.pas.service.ApplicationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 * <AUTHOR> 5m
 * @Date : 2017/12/28 17:16
 * @Description :
 */
@RestController
@RequestMapping("/Application")
@Api(value = "ApplicationController", description = "商户进件EFPS-上游KEY映射关系表")
public class ApplicationController {

    @Autowired
    private SequenceService sequenceService;

    @Autowired
    private ApplicationService applicatiaonService;

    @RequestMapping(value ="/create", method = RequestMethod.POST)
    @Logable(businessTag = "create")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "新增商户进件上游映射关系表", notes = "新增商户进件上游映射关系表", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "efpsKey", value = "进件所需字段的efpskey:表名.列名", required = true, dataType = "String", length =50, paramType = "query"),
            @ApiImplicitParam(name = "efpsName", value = "某个进件所需字段efps名称,展示使用", required = true, dataType = "String", length =50, paramType = "query"),
            @ApiImplicitParam(name = "institutionKey", value = "某个进件所需字段上游合作机构进件时的参数名", required = true, dataType = "String", length =20, paramType = "query"),
            @ApiImplicitParam(name = "institutionCode", value = "上游合作机构编码", required = true, dataType = "String", length =20, paramType = "query"),
            @ApiImplicitParam(name = "allowNull", value = "字段是否可空:1-是;0-否", required = true, dataType = "String", length =1, paramType = "query",valueRange="{0,1}") })
    public String create(@ApiIgnore Application record) {
        record.setId(sequenceService.nextValue("pas"));
        return applicatiaonService.insert(record) == 1? Constants.SUCCESS: Constants.FAIL;

    }

    @RequestMapping(value ="/modify", method = RequestMethod.POST)
    @Logable(businessTag = "modify")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "修改商户进件上游映射关系表", notes = "修改商户进件上游映射关系表", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "id", required = true, dataType = "long", length =50, paramType = "query"),
            @ApiImplicitParam(name = "efpsKey", value = "进件所需字段的efpskey:表名.列名", required = false, dataType = "String", length =50, paramType = "query"),
            @ApiImplicitParam(name = "efpsName", value = "某个进件所需字段efps名称,展示使用", required = false, dataType = "String", length =50, paramType = "query"),
            @ApiImplicitParam(name = "institutionKey", value = "某个进件所需字段上游合作机构进件时的参数名", required = false, dataType = "String", length =20, paramType = "query"),
            @ApiImplicitParam(name = "institutionCode", value = "上游合作机构编码", required = false, dataType = "String", length =20, paramType = "query"),
            @ApiImplicitParam(name = "allowNull", value = "字段是否可空:1-是;0-否", required = false, dataType = "String", length =1, paramType = "query",valueRange="{0,1}")  })
    public String modify(@ApiIgnore Application record ) {
        return applicatiaonService.updateByPrimaryKeySelective(record) == 1? Constants.SUCCESS: Constants.FAIL;

    }


    @RequestMapping(value ="/delete", method = RequestMethod.GET)
    @Logable(businessTag = "delete")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "删除商户进件上游映射关系表", notes = "删除商户进件上游映射关系表", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "id", required = true, dataType = "long", length =50, paramType = "query")})
    public String delete(@RequestParam Long id) {
        return applicatiaonService.deleteByPrimaryKey(id) == 1? Constants.SUCCESS: Constants.FAIL;

    }


    @RequestMapping(value ="/select", method = RequestMethod.GET)
    @Logable(businessTag = "select")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "搜索商户进件上游映射关系表", notes = "搜索商户进件上游映射关系表", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "name", value = "商户进件上游映射关系表名称", required = false, dataType = "String", length =50, paramType = "query"),
            @ApiImplicitParam(name = "efpsKey", value = "进件所需字段的efpskey:表名.列名", required = false, dataType = "String", length =50, paramType = "query"),
            @ApiImplicitParam(name = "efpsName", value = "某个进件所需字段efps名称,展示使用", required = false, dataType = "String", length =50, paramType = "query"),
            @ApiImplicitParam(name = "institutionKey", value = "某个进件所需字段上游合作机构进件时的参数名", required = false, dataType = "String", length =20, paramType = "query"),
            @ApiImplicitParam(name = "institutionCode", value = "上游合作机构编码", required = false, dataType = "String", length =20, paramType = "query"),
            @ApiImplicitParam(name = "allowNull", value = "字段是否可空:1-是;0-否", required = false, dataType = "String", length =1, paramType = "query",valueRange="{0,1}") })
    public List<Application> select(@ApiIgnore Application record ) {
        return applicatiaonService.selectBySelective(record) ;

    }

    @RequestMapping(value ="/selectById", method = RequestMethod.GET)
    @Logable(businessTag = "selectById")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "根据ID搜索单条商户进件上游映射关系表", notes = "根据ID搜索单条商户进件上游映射关系表", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "id", required = true, dataType = "long", length =50, paramType = "query")})
    public Application selectById(@RequestParam Long id) {
        return applicatiaonService.selectByPrimaryKey(id) ;
    }

}
