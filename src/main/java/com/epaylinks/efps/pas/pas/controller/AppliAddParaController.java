package com.epaylinks.efps.pas.pas.controller;

import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.pas.pas.domain.AppliAddPara;
import com.epaylinks.efps.pas.pas.service.AppliAddParaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 * <AUTHOR> 5m
 * @Date : 2017/12/28 17:15
 * @Description :
 */
@RestController
@RequestMapping("/AppliAddPara")
@Api(value = "AppliAddParaController", description = "商户进件额外数据")
public class AppliAddParaController {

    @Autowired
    private SequenceService sequenceService;

    @Autowired
    private AppliAddParaService appliAddParaService;

    @RequestMapping(value ="/create", method = RequestMethod.POST)
    @Logable(businessTag = "create")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "新增商户进件额外数据", notes = "新增商户进件额外数据", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "customerCode", value = "商户的客户编码", required = true, dataType = "String", length =20, paramType = "query"),
            @ApiImplicitParam(name = "efpsKey", value = "进件所需字段的efpskey", required = true, dataType = "String", length =50, paramType = "query"),
            @ApiImplicitParam(name = "value", value = "指定商户的某个进件所需字段的value", required = true, dataType = "String", length =100, paramType = "query")})
    public String create(@ApiIgnore AppliAddPara record) {
        record.setId(sequenceService.nextValue("pas"));
        return appliAddParaService.insert(record) == 1? Constants.SUCCESS: Constants.FAIL;

    }

    @RequestMapping(value ="/modify", method = RequestMethod.POST)
    @Logable(businessTag = "modify")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "修改商户进件额外数据", notes = "修改商户进件额外数据", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "id", required = true, dataType = "long", length =50, paramType = "query"),
            @ApiImplicitParam(name = "customerCode", value = "商户的客户编码", required = false, dataType = "String", length =20, paramType = "query"),
            @ApiImplicitParam(name = "efpsKey", value = "进件所需字段的efpskey", required = false, dataType = "String", length =50, paramType = "query"),
            @ApiImplicitParam(name = "value", value = "指定商户的某个进件所需字段的value", required = false, dataType = "String", length =100, paramType = "query")})
    public String modify(@ApiIgnore AppliAddPara record ) {
        return appliAddParaService.updateByPrimaryKeySelective(record) == 1? Constants.SUCCESS: Constants.FAIL;

    }


    @RequestMapping(value ="/delete", method = RequestMethod.GET)
    @Logable(businessTag = "delete")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "删除商户进件额外数据", notes = "删除商户进件额外数据", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "id", required = true, dataType = "long", length =50, paramType = "query")})
    public String delete(@RequestParam Long id) {
        return appliAddParaService.deleteByPrimaryKey(id) == 1? Constants.SUCCESS: Constants.FAIL;

    }


    @RequestMapping(value ="/select", method = RequestMethod.GET)
    @Logable(businessTag = "select")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "搜索商户进件额外数据", notes = "搜索商户进件额外数据", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "name", value = "商户进件额外数据名称", required = false, dataType = "String", length =50, paramType = "query"),
            @ApiImplicitParam(name = "customerCode", value = "商户的客户编码", required = false, dataType = "String", length =20, paramType = "query"),
            @ApiImplicitParam(name = "efpsKey", value = "进件所需字段的efpskey", required = false, dataType = "String", length =50, paramType = "query"),
            @ApiImplicitParam(name = "value", value = "指定商户的某个进件所需字段的value", required = false, dataType = "String", length =100, paramType = "query")})
    public List<AppliAddPara> select(@ApiIgnore AppliAddPara record ) {
        return appliAddParaService.selectBySelective(record) ;

    }

    @RequestMapping(value ="/selectById", method = RequestMethod.GET)
    @Logable(businessTag = "selectById")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "根据ID搜索单条商户进件额外数据", notes = "根据ID搜索单条商户进件额外数据", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "id", required = true, dataType = "long", length =50, paramType = "query")})
    public AppliAddPara selectById(@RequestParam Long id) {
        return appliAddParaService.selectByPrimaryKey(id) ;
    }

}
