package com.epaylinks.efps.pas.pas.util;

import java.io.*;

import java.util.ArrayList;
import java.util.List;
import java.util.zip.GZIPInputStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

import org.apache.commons.compress.archivers.sevenz.SevenZArchiveEntry;
import org.apache.commons.compress.archivers.sevenz.SevenZFile;
import org.apache.commons.compress.archivers.tar.TarArchiveEntry;
import org.apache.commons.compress.archivers.tar.TarArchiveInputStream;
import org.apache.commons.compress.compressors.z.ZCompressorInputStream;

public class ChkZipUtils {

    private static final int BUFFER = 512;
    private static String FILE_PATH_SEPARATOR = "/";

    /**
     * 获取目录下所有文件
     *
     * @param srcFile
     * @return
     */
    private static List<File> getAllFiles(File srcFile) {
        List<File> fileList = new ArrayList<File>();
        File[] tmp = srcFile.listFiles();

        for (int i = 0; i < tmp.length; i++) {

            if (tmp[i].isFile()) {
                fileList.add(tmp[i]);
            }

            if (tmp[i].isDirectory()) {
                if (tmp[i].listFiles().length != 0) {
                    fileList.addAll(getAllFiles(tmp[i]));
                } else {
                    fileList.add(tmp[i]);
                }
            }
        }

        return fileList;
    }

    /**
     * 获取相对路径
     *
     * @param dirPath
     * @param file
     * @return
     */
    private static String getRelativePath(String dirPath, File file) {
        File dir = new File(dirPath);
        String relativePath = file.getName();

        while (true) {
            file = file.getParentFile();

            if (file == null) {
                break;
            }

            if (file.equals(dir)) {
                break;
            } else {
                relativePath = file.getName() + "/" + relativePath;
            }
        }

        return relativePath;
    }

    /**
     * 创建文件 根据压缩包内文件名和解压缩目的路径，创建解压缩目标文件， 生成中间目录
     *
     * @param dstPath
     * @param fileName
     * @return
     * @throws IOException
     */
    private static File createFile(String dstPath, String fileName) throws IOException {
        String[] dirs = fileName.split("/");
        File file = new File(dstPath);

        if (dirs.length > 1) {
            for (int i = 0; i < dirs.length - 1; i++) {
                file = new File(file, dirs[i]);
            }

            if (!file.exists()) {
                file.mkdirs();
            }

            file = new File(file, dirs[dirs.length - 1]);

            return file;
        } else {
            if (!file.exists()) {
                file.mkdirs();
            }

            file = new File(file, dirs[0]);
            return file;
        }
    }

    /**
     * 解压缩
     *
     * @param zipFileName
     * @param dstPath
     * @return
     */
    public static boolean unzip(String zipFileName, String dstPath) {

        try {
            ZipInputStream zipInputStream = new ZipInputStream(new FileInputStream(zipFileName));
            ZipEntry zipEntry = null;
            byte[] buffer = new byte[BUFFER];// 缓冲器
            int readLength = 0;// 每次读出来的长度

            while ((zipEntry = zipInputStream.getNextEntry()) != null) {
                if (zipEntry.isDirectory()) {// 若是zip条目目录，则需创建这个目录
                    File dir = new File(dstPath + "/" + zipEntry.getName());

                    if (!dir.exists()) {
                        dir.mkdirs();

                        continue;// 跳出
                    }
                }

                File file = createFile(dstPath, zipEntry.getName());// 若是文件，则需创建该文件

                OutputStream outputStream = new FileOutputStream(file);

                while ((readLength = zipInputStream.read(buffer, 0, BUFFER)) != -1) {
                    outputStream.write(buffer, 0, readLength);
                }

                outputStream.close();
            }
            zipInputStream.close();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
            return false;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }

        return true;
    }

    /**
     * 压缩
     *
     * @param srcPath
     * @param zipFileName
     * @return
     */
    public static boolean zip(String srcPath, String zipFileName) {
        File srcFile = new File(srcPath);
        List<File> fileList = getAllFiles(srcFile);
        byte[] buffer = new byte[BUFFER];
        ZipEntry zipEntry = null;
        int readLength = 0;

        try {
            ZipOutputStream zipOutputStream = new ZipOutputStream(new FileOutputStream(zipFileName));

            for (File file : fileList) {
                if (file.isFile()) {
                    zipEntry = new ZipEntry(getRelativePath(srcPath, file));
                    zipEntry.setSize(file.length());
                    zipEntry.setTime(file.lastModified());
                    zipOutputStream.putNextEntry(zipEntry);

                    InputStream inputStream = new BufferedInputStream(new FileInputStream(file));

                    while ((readLength = inputStream.read(buffer, 0, BUFFER)) != -1) {
                        zipOutputStream.write(buffer, 0, readLength);
                    }

                    inputStream.close();
                } else {
                    zipEntry = new ZipEntry(getRelativePath(srcPath, file) + "/");
                    zipOutputStream.putNextEntry(zipEntry);
                }

            }

            zipOutputStream.close();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
            return false;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }

        return true;
    }

    /**
     * Tar文件解压方法
     *
     * @param tarGzFile
     *            要解压的压缩文件名称（绝对路径名称）
     * @param destDir
     *            解压后文件放置的路径名（绝对路径名称）
     * @return 解压出的文件列表
     */
    public static List<String> deCompressGZipFile(String tarGzFile, String destDir) {

        List<String> fileList = new ArrayList<String>();

        OutputStream out = null; // 建立输出流，用于将从压缩文件中读出的文件流写入到磁盘
        FileInputStream fis = null; // 建立输入流，用于从压缩文件中读出文件
        GZIPInputStream gis = null;

        TarArchiveInputStream taris = null;
        TarArchiveEntry entry = null;
        TarArchiveEntry[] subEntries = null;

        File entryFile = null;
        File subEntryFile = null;
        String entryFileName = null;

        int entryNum = 0;
        try {
            fis = new FileInputStream(tarGzFile);
            gis = new GZIPInputStream(fis);
            taris = new TarArchiveInputStream(gis);

            while ((entry = taris.getNextTarEntry()) != null) {
                entryFileName = destDir + FILE_PATH_SEPARATOR + entry.getName();
                entryFile = new File(entryFileName);
                entryNum++;
                if (entry.isDirectory()) {
                    if (!entryFile.exists()) {
                        entryFile.mkdir();
                    }
                    subEntries = entry.getDirectoryEntries();
                    for (int i = 0; i < subEntries.length; i++) {
                        try {
                            subEntryFile = new File(entryFileName + FILE_PATH_SEPARATOR + subEntries[i].getName());
                            fileList.add(entryFileName + FILE_PATH_SEPARATOR + subEntries[i].getName());
                            out = new FileOutputStream(subEntryFile);
                            byte[] buf = new byte[1024];
                            int len = 0;
                            while ((len = taris.read(buf)) != -1) {
                                out.write(buf, 0, len);
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        } finally {
                            out.close();
                            out = null;
                        }
                    }
                } else {
                    fileList.add(entryFileName);
                    out = new FileOutputStream(entryFile);
                    try {
                        byte[] buf = new byte[1024];
                        int len = 0;
                        while ((len = taris.read(buf)) != -1) {
                            out.write(buf, 0, len);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    } finally {
                        out.close();
                        out = null;
                    }
                }
            }

            if (entryNum == 0) {
                System.out.println("0 entrys");
            }

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (taris != null) {
                try {
                    taris.close();
                } catch (Exception ce) {
                    taris = null;
                }
            }
            if (gis != null) {
                try {
                    gis.close();
                } catch (Exception ce) {
                    gis = null;
                }
            }
            if (fis != null) {
                try {
                    fis.close();
                } catch (Exception ce) {
                    fis = null;
                }
            }
            if (out != null) {
                try {
                    out.close();
                } catch (Exception ce) {
                    out = null;
                }
            }
        }
        return fileList;

    }

    /**
     * 解压*.z文件 * @param file z包文件
     * @param outPath z包下解压后文件存放路径
     * @return 解压后文件
     */
    public static File unZFile(File file,String outPath) {
        int buffersize = 2048;
        FileOutputStream out = null;
        ZCompressorInputStream zIn = null;
        try {
            FileInputStream fin = new FileInputStream(file);
            BufferedInputStream in = new BufferedInputStream(fin);
            // 若获取z文件名，最好使用lastIndexOf，不要使用indexOf，比如paid_orderlist_20170920.test.z
            String name = file.getName() .substring(0, file.getName().lastIndexOf("."));
            File outFile = new File(outPath + "/" + name);
            if (outFile.exists()) {
                outFile.delete();
            }
            out = new FileOutputStream(outFile);
            zIn = new ZCompressorInputStream(in);
            final byte[] buffer = new byte[buffersize];
            int n = 0;
            while (-1 != (n = zIn.read(buffer))) {
                out.write(buffer, 0, n);
            }
            return outFile;
        }
        catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        finally {
            try {
                out.close();
                zIn.close();
            }
            catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * <AUTHOR>
     * @param orgPath 源压缩文件地址
     * @param tarpath 解压后存放的目录地址
     */
    public static void apache7ZDecomp(String orgPath, String tarpath) {

        try {
            SevenZFile sevenZFile = new SevenZFile(new File(orgPath));
            SevenZArchiveEntry entry = sevenZFile.getNextEntry();
            while (entry != null) {

                // System.out.println(entry.getName());
                if (entry.isDirectory()) {

                    new File(tarpath + entry.getName()).mkdirs();
                    entry = sevenZFile.getNextEntry();
                    continue;
                }
                FileOutputStream out = new FileOutputStream(tarpath
                        + File.separator + entry.getName());
                byte[] content = new byte[(int) entry.getSize()];
                sevenZFile.read(content, 0, content.length);
                out.write(content);
                out.close();
                entry = sevenZFile.getNextEntry();
            }
            sevenZFile.close();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    public static void main(String[] args) {
        String srcPath1 = "E:\\EPLINKS\\Source\\010-ReqDoc\\三期-EPSP\\02-核算管理\\上游对账单\\银联微信\\1507610861All2018-11-27.csv.zip";
        String srcPath2 = "E:\\EPLINKS\\Source\\010-ReqDoc\\三期-EPSP\\02-核算管理\\上游对账单\\银联微信\\1507489761All2018-11-11.csv.zip";
        String srcPath3 = "E:\\EPLINKS\\Source\\010-ReqDoc\\三期-EPSP\\02-核算管理\\上游对账单\\银联支付宝\\20880015769646550156_20181128.csv.zip";
        String srcPath4 = "E:\\EPLINKS\\Source\\010-ReqDoc\\三期-EPSP\\02-核算管理\\上游对账单\\银联二维码\\P_48453320_20181113.tar.gz";
        String srcPath9 = "E:\\EPLINKS\\Source\\010-ReqDoc\\三期-EPSP\\02-核算管理\\上游对账单\\渤海代付\\97001181128ACOM.zip";
        String srcPath10 = "E:\\home\\efps\\efps\\chk\\GuiJi\\BANKINLET\\33\\********\\YPL_********.csv.7z";
        String dstPath = "E:\\home\\unzip";
        String dstPath1 = "E:\\home\\unzip\\task1";
        String dstPath2 = "E:\\home\\unzip\\task2";
        String dstPath3 = "E:\\home\\unzip\\task3";
        String dstPath4 = "E:\\home\\unzip\\task4";
        String dstPath9 = "E:\\home\\unzip\\task9";
        String dstPath10 = "E:\\home\\efps\\efps\\chk\\GuiJi\\BANKINLET\\33\\********\\";


        //unzip(srcPath9, dstPath9);
        //List <String> liststr = deCompressGZipFile(srcPath10, dstPath10 );
        apache7ZDecomp(srcPath10, dstPath10 );
        //System.out.println(liststr);
    }
}
