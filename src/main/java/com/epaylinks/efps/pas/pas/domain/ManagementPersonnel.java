package com.epaylinks.efps.pas.pas.domain;

import java.util.Date;

public class ManagementPersonnel {
    /**
     * ID
     */
    private Long id;

    /**
     */
    private String receiverId;

    /**
     * 接收人姓名
     */
    private String receiver;

    /**
     * 手机号码
     */
    private String phone;

    /**
     */
    private String phoneEncrypt;

    /**
     * 操作人
     */
    private Long operator;

    /**
     * 创建时间
     */
    private Date createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getReceiverId() {
        return receiverId;
    }

    public void setReceiverId(String receiverId) {
        this.receiverId = receiverId;
    }

    public String getReceiver() {
        return receiver;
    }

    public void setReceiver(String receiver) {
        this.receiver = receiver;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getPhoneEncrypt() {
        return phoneEncrypt;
    }

    public void setPhoneEncrypt(String phoneEncrypt) {
        this.phoneEncrypt = phoneEncrypt;
    }

    public Long getOperator() {
        return operator;
    }

    public void setOperator(Long operator) {
        this.operator = operator;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}