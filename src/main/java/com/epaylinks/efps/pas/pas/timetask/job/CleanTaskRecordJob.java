package com.epaylinks.efps.pas.pas.timetask.job;

import com.epaylinks.efps.common.timetask.TaskJob;
import com.epaylinks.efps.common.timetask.TaskRequest;
import com.epaylinks.efps.pas.pas.dao.PasTimeTaskMapper;
import com.epaylinks.efps.pas.pas.dao.PasTimeTaskRecordMapper;
import com.epaylinks.efps.pas.pas.domain.PasTimeTask;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Component
public class CleanTaskRecordJob implements TaskJob {
    private static Logger logger = LoggerFactory.getLogger(CleanTaskRecordJob.class);

    @Autowired
    private PasTimeTaskRecordMapper taskRecordMapper;

    @Autowired
    private PasTimeTaskMapper taskMapper;

    @Override
    public void execute(TaskRequest request) {
        List<PasTimeTask> tasks = taskMapper.selectAll();
        for (PasTimeTask task : tasks) {
            try {
                cleanJobRecord(task);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private void cleanJobRecord(PasTimeTask task) {
        if (task.getRecordSaveDays() != null) {
            DateTime dateTime = new DateTime().withTimeAtStartOfDay().minusDays(task.getRecordSaveDays());
            logger.info("删除任务[{}]在[{}]前的执行记录", task.getJobName(), dateTime.toString("yyyy-MM-dd HH:mm:ss"));
            taskRecordMapper.deleteByCreateTimeLessThanAndJobName(new Date(dateTime.getMillis()), task.getJobName());
        }
    }
}
