package com.epaylinks.efps.pas.pas.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.epaylinks.efps.common.business.pay.request.UserType;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.pas.domain.BusinessParam;
import com.epaylinks.efps.pas.pas.service.BusinessParamService;

@RestController
@RequestMapping(value = "/BusinessParam")
public class BusinessParamController {
	@Autowired
	private BusinessParamService businessParamService;
	
	/**
	 * 根据业务编码来获取业务可选参数
	 * @param businessCode 业务编码
	 * @return 业务对应的参数
	 */
	@RequestMapping(value = "getByBueinessCode" , method = RequestMethod.GET)
	public List<BusinessParam> getBuesinessParamByBusinessId(
			@RequestParam(name = "businessCode" , required = true) String businessCode,
			@RequestHeader(value = "x-user-type") String userType){
		if (UserType.PAS_USER.code.equals(userType)) {
			return businessParamService.getBusinessParamByBusinessCode(businessCode);
		}
		else {
			throw new AppException(PasCode.USERTYPE_NOTEXIST.code);
		}
		
	}
}
