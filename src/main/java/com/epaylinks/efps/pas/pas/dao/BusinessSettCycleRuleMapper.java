package com.epaylinks.efps.pas.pas.dao;

import com.epaylinks.efps.pas.pas.domain.BusinessSettCycleRule;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface BusinessSettCycleRuleMapper {

    int deleteByPrimaryKey(Long id);

    int insert(BusinessSettCycleRule record);

    int insertSelective(BusinessSettCycleRule record);

    BusinessSettCycleRule selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(BusinessSettCycleRule record);

    int updateByPrimaryKey(BusinessSettCycleRule record);

    BusinessSettCycleRule querySupportSettCycleByBusinessCode(@Param("businessCode") String businessCode);
}