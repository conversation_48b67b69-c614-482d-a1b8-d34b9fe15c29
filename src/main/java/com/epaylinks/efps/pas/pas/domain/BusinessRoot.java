package com.epaylinks.efps.pas.pas.domain;

import com.epaylinks.efps.common.business.CommonOuterResponse;

import java.util.List;

public class BusinessRoot extends CommonOuterResponse {

    private String code;
    private String name;
    private List<BusinessGroup> groupList;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<BusinessGroup> getGroupList() {
        return groupList;
    }

    public void setGroupList(List<BusinessGroup> groupList) {
        this.groupList = groupList;
    }
}
