package com.epaylinks.efps.pas.pas.vo;


public class PasLoginLogJson {
    /**
     * 登录名
     */
    private String userName;

    /**
     * 用户类型(1:运营管理系统用户; 2:商户门户用户)
     */
    private String userType;

    /**
     * 角色
     */
    private String role;

    /**
     * 姓名/商户名称
     */
    private String realName;

    /**
     * 商户编号
     */
    private String customerCode;

    /**
     * 所属平台商(按照epsp规则，所属平台商编号(所属平台商名称))例：5651200003067369(第一个服务商001)
     */
    private String platCustomer;

    /**
     * 所属代理商(按照epsp规则，所属代理商编号(所属代理商名称))例：5651200003067369(第一个服务商001)
     */
    private String serviceCustomer;

    /**
     * 最近登陆时间(yyyy-MM-dd HH:mm:ss)
     */
    private String loginTime;

    /**
     * 最近登陆IP
     */
    private String loginIp;

    /**
     * 登录状态：0失败 1成功
     */
    private String state;
    
    /**
     * 是否密码错误
     */
    private boolean pwdError;

    /**
     * 日志类型：1：登录日志；2：操作日志
     */
    private Short logType;

    /**
     * 备注(密码错误、账号锁定等)
     */
    private String remark;
    
    private String targetCustomercode;
    
    
    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getPlatCustomer() {
        return platCustomer;
    }

    public void setPlatCustomer(String platCustomer) {
        this.platCustomer = platCustomer;
    }

    public String getServiceCustomer() {
        return serviceCustomer;
    }

    public void setServiceCustomer(String serviceCustomer) {
        this.serviceCustomer = serviceCustomer;
    }

    public String getLoginTime() {
        return loginTime;
    }

    public void setLoginTime(String loginTime) {
        this.loginTime = loginTime;
    }

    public String getLoginIp() {
        return loginIp;
    }

    public void setLoginIp(String loginIp) {
        this.loginIp = loginIp;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public boolean isPwdError() {
        return pwdError;
    }

    public void setPwdError(boolean pwdError) {
        this.pwdError = pwdError;
    }

    public Short getLogType() {
        return logType;
    }

    public void setLogType(Short logType) {
        this.logType = logType;
    }

    public String getTargetCustomercode() {
        return targetCustomercode;
    }

    public void setTargetCustomercode(String targetCustomercode) {
        this.targetCustomercode = targetCustomercode;
    }

}
