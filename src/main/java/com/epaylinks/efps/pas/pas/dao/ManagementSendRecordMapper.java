package com.epaylinks.efps.pas.pas.dao;

import com.epaylinks.efps.pas.pas.controller.response.ManagementSendRecordResponse;
import com.epaylinks.efps.pas.pas.domain.ManagementSendRecord;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface ManagementSendRecordMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ManagementSendRecord record);

    int insertSelective(ManagementSendRecord record);

    ManagementSendRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ManagementSendRecord record);

    int updateByPrimaryKey(ManagementSendRecord record);

    Long queryMSRNextSeq();

    Integer countManageSendRecord(Map paramMap);

    List<ManagementSendRecordResponse> pageQuerySendRecord(Map paramMap);
}