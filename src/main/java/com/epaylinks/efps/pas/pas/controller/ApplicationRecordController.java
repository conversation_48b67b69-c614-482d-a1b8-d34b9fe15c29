package com.epaylinks.efps.pas.pas.controller;

import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.pas.pas.domain.ApplicationRecord;
import com.epaylinks.efps.pas.pas.service.ApplicationRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 5m
 * @Date : 2017/12/28 17:16
 * @Description : 商户上游合作机构进件记录
 */
@RestController
@RequestMapping("/ApplicationRecord")
@Api(value = "ApplicationRecordController", description = "商户上游合作机构进件记录")
public class ApplicationRecordController {

    @Autowired
    private SequenceService sequenceService;

    @Autowired
    private ApplicationRecordService applicationRecordService;

    @RequestMapping(value ="/create", method = RequestMethod.POST)
    @Logable(businessTag = "create")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "新增商户上游合作机构进件记录", notes = "新增商户上游合作机构进件记录", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "customerCode", value = "客户编码", required = true, dataType = "String", length =20, paramType = "query"),
            @ApiImplicitParam(name = "customerInfoId", value = "进件使用的商户基本信息ID", required = false, dataType = "long", length =20, paramType = "query"),
            @ApiImplicitParam(name = "institutionCode", value = "进件目标上游合作机构", required = true, dataType = "long", length =20, paramType = "query"),
            @ApiImplicitParam(name = "inputParams", value = "进件时使用的参数Json序列化后的结果", required = true, dataType = "String", length =1000, paramType = "query"),
            @ApiImplicitParam(name = "state", value = "进件处理状态:0-数据校验失败.1-成功.2-处理中.3-失败", required = true, dataType = "String", length =1, paramType = "query",valueRange="{0,1,2,3}")  })
    public String create(@ApiIgnore ApplicationRecord record) {
        record.setId(sequenceService.nextValue("pas"));
        record.setCreateTime(new Date());
        return applicationRecordService.insert(record) == 1? Constants.SUCCESS: Constants.FAIL;

    }

    @RequestMapping(value ="/modify", method = RequestMethod.POST)
    @Logable(businessTag = "modify")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "修改商户上游合作机构进件记录", notes = "修改商户上游合作机构进件记录", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "id", required = true, dataType = "long", length =50, paramType = "query"),
            @ApiImplicitParam(name = "customerCode", value = "客户编码", required = false, dataType = "String", length =20, paramType = "query"),
            @ApiImplicitParam(name = "customerInfoId", value = "进件使用的商户基本信息ID", required = false, dataType = "long", length =20, paramType = "query"),
            @ApiImplicitParam(name = "institutionCode", value = "进件目标上游合作机构", required = false, dataType = "long", length =20, paramType = "query"),
            @ApiImplicitParam(name = "inputParams", value = "进件时使用的参数Json序列化后的结果", required = false, dataType = "String", length =1000, paramType = "query"),
            @ApiImplicitParam(name = "state", value = "进件处理状态:0-数据校验失败.1-成功.2-处理中.3-失败", required = false, dataType = "String", length =1, paramType = "query",valueRange="{0,1,2,3}"),
            @ApiImplicitParam(name = "resultMsg", value = "进件处理结果描述", required = false, dataType = "String", length =200, paramType = "query"),
            @ApiImplicitParam(name = "institutionMerchCode", value = "上游进件返回的商户号", required = false, dataType = "String", length =20, paramType = "query"),
            @ApiImplicitParam(name = "institutionMerchSecret", value = "上游进件返回的商户交易秘钥", required = false, dataType = "String", length =100, paramType = "query")})
    public String modify(@ApiIgnore ApplicationRecord record ) {
        record.setUpdateTime(new Date());
        return applicationRecordService.updateByPrimaryKeySelective(record) == 1? Constants.SUCCESS: Constants.FAIL;

    }


    @RequestMapping(value ="/delete", method = RequestMethod.GET)
    @Logable(businessTag = "delete")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "删除商户上游合作机构进件记录", notes = "删除商户上游合作机构进件记录", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "id", required = true, dataType = "long", length =50, paramType = "query")})
    public String delete(@RequestParam Long id) {
        return applicationRecordService.deleteByPrimaryKey(id) == 1? Constants.SUCCESS: Constants.FAIL;

    }


    @RequestMapping(value ="/select", method = RequestMethod.GET)
    @Logable(businessTag = "select")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "搜索商户上游合作机构进件记录", notes = "搜索商户上游合作机构进件记录", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "customerCode", value = "客户编码", required = false, dataType = "String", length =20, paramType = "query"),
            @ApiImplicitParam(name = "customerName", value = "商户名称", required = false, dataType = "String", length =50, paramType = "query"),
            @ApiImplicitParam(name = "customerInfoId", value = "进件使用的商户基本信息ID", required = false, dataType = "long", length =20, paramType = "query"),
            @ApiImplicitParam(name = "institutionCode", value = "进件目标上游合作机构", required = false, dataType = "long", length =20, paramType = "query"),
            @ApiImplicitParam(name = "inputParams", value = "进件时使用的参数Json序列化后的结果", required = false, dataType = "String", length =1000, paramType = "query"),
            @ApiImplicitParam(name = "state", value = "进件处理状态:0-数据校验失败.1-成功.2-处理中.3-失败", required = false, dataType = "String", length =1, paramType = "query",valueRange="{0,1,2,3}"),
            @ApiImplicitParam(name = "resultMsg", value = "进件处理结果描述", required = false, dataType = "String", length =200, paramType = "query"),
            @ApiImplicitParam(name = "institutionMerchCode", value = "上游进件返回的商户号", required = false, dataType = "String", length =20, paramType = "query"),
            @ApiImplicitParam(name = "institutionMerchSecret", value = "上游进件返回的商户交易秘钥", required = false, dataType = "String", length =100, paramType = "query"),
            @ApiImplicitParam(name = "beginTime", value = "开始时间yyyyMMddHHmmss", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "结束时间yyyyMMddHHmmss", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageNum", value = "查询开始页", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "查询的页面大小", required = true, dataType = "int", paramType = "query")})
    public PageResult<ApplicationRecord> select(@ApiIgnore ApplicationRecord record,
                                   @RequestParam(value = "customerName", required = false) String customerName,
                                   @RequestParam(value = "beginTime", required = false) String beginTime,
                                   @RequestParam(value = "endTime", required = false) String endTime,
                                   @RequestParam(value = "pageNum", required = false) Integer pageNum,
                                   @RequestParam(value = "pageSize", required = false) Integer pageSize ) {

        List<ApplicationRecord> noPage = applicationRecordService.selectBySelective(record);

        if (pageNum == null) {
            pageNum = 1;
        }
        if (pageSize == null) {
            pageSize = noPage.size();
        }

        int endRowNo = pageNum * pageSize;
        int beginRowNo = (pageNum-1)*pageSize+1;

        Map map = new HashMap();
        try{
            map.put("customerName", customerName);
            map.put("customerCode", record.getCustomerCode());
            map.put("customerInfoId", record.getCustomerInfoId());
            map.put("institutionCode", record.getInstitutionCode() );
            map.put("inputParams", record.getInputParams());
            map.put("state", record.getState());
            map.put("resultMsg", record.getResultMsg());
            map.put("institutionMerchCode", record.getInstitutionMerchCode());
            map.put("institutionMerchSecret", record.getInstitutionMerchSecret());
            if (StringUtils.isNotBlank(beginTime)) {
                map.put("beginTime", DateUtils.parseDate(beginTime, "yyyyMMddHHmmss"));
            }
            if (StringUtils.isNotBlank(endTime)) {
                map.put("endTime", DateUtils.parseDate(endTime, "yyyyMMddHHmmss"));
            }
            map.put("beginRowNo", beginRowNo);
            map.put("endRowNo", endRowNo);
        } catch (ParseException e) {
            throw new AppException(Constants.ReturnCode.INVALID_PARAM.code);
        }

        List<ApplicationRecord> pageList = applicationRecordService.selectByPage(map);

        PageResult<ApplicationRecord> page = new PageResult<>();
        page.setRows(pageList);
        page.setTotal(noPage.size());
        return page;

    }

    @RequestMapping(value ="/selectBySelective", method = RequestMethod.GET)
    @Logable(businessTag = "selectBySelective")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "搜索商户上游合作机构进件List", notes = "搜索商户上游合作机构进件List", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "customerCode", value = "客户编码", required = false, dataType = "String", length =20, paramType = "query"),
            @ApiImplicitParam(name = "customerInfoId", value = "进件使用的商户基本信息ID", required = false, dataType = "long", length =20, paramType = "query"),
            @ApiImplicitParam(name = "institutionCode", value = "进件目标上游合作机构", required = false, dataType = "long", length =20, paramType = "query"),
            @ApiImplicitParam(name = "inputParams", value = "进件时使用的参数Json序列化后的结果", required = false, dataType = "String", length =1000, paramType = "query"),
            @ApiImplicitParam(name = "state", value = "进件处理状态:0-数据校验失败.1-成功.2-处理中.3-失败", required = false, dataType = "String", length =1, paramType = "query",valueRange="{0,1,2,3}"),
            @ApiImplicitParam(name = "resultMsg", value = "进件处理结果描述", required = false, dataType = "String", length =200, paramType = "query"),
            @ApiImplicitParam(name = "institutionMerchCode", value = "上游进件返回的商户号", required = false, dataType = "String", length =20, paramType = "query"),
            @ApiImplicitParam(name = "institutionMerchSecret", value = "上游进件返回的商户交易秘钥", required = false, dataType = "String", length =100, paramType = "query")
 })
    public List<ApplicationRecord> selectBySelective(@ApiIgnore ApplicationRecord record  ) {

        return applicationRecordService.selectBySelective(record);

    }

    @RequestMapping(value ="/selectById", method = RequestMethod.GET)
    @Logable(businessTag = "selectById")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "根据ID搜索单条商户上游合作机构进件记录", notes = "根据ID搜索单条商户上游合作机构进件记录", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "id", required = true, dataType = "long", length =50, paramType = "query")})
    public ApplicationRecord selectById(@RequestParam Long id) {
        return applicationRecordService.selectByPrimaryKey(id) ;
    }

}
