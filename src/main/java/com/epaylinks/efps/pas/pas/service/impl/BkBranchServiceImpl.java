package com.epaylinks.efps.pas.pas.service.impl;

import com.epaylinks.efps.common.dataimport.BatchService;
import com.epaylinks.efps.common.dataimport.model.BatchDetail;
import com.epaylinks.efps.common.dataimport.util.CustReturnCode;
import com.epaylinks.efps.common.dataimport.util.DataImportConstants;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.util.StringUtils;
import com.epaylinks.efps.pas.acl.dao.UserMapper;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.pas.dao.BkBankMapper;
import com.epaylinks.efps.pas.pas.dao.BkBranchMapper;
import com.epaylinks.efps.pas.pas.model.BkBranch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service("bkBranchServiceImpl")
public class BkBranchServiceImpl implements BatchService {
    @Autowired
    private BkBranchMapper bkBranchMapper;

    @Autowired
    private BkBankMapper bkBankMapper;

    @Autowired
    private UserMapper userMapper;


    public List<BkBranch> pageQuery(Map map) {
        List<BkBranch> bankBranchList = bkBranchMapper.selectByPage(map);
        bankBranchList.forEach(bankBranch -> {
            if (!StringUtils.isBlank(bankBranch.getOperatorId())) {
                bankBranch.setOperatorId(userMapper.selectByPrimaryKey(Long.valueOf(bankBranch.getOperatorId())).getName());
            }
        });
        return bankBranchList;
    }

    public int queryCount(Map map) {
        return bkBranchMapper.selectCount(map);
    }

    public void enableBkBranch(String lbnkNo, String flag, String userId) {
        BkBranch record = bkBranchMapper.selectByPrimaryKey(lbnkNo);
        if (record == null) {
            throw new AppException(PasCode.RECORD_NOT_EXIST.code, PasCode.RECORD_NOT_EXIST.message);
        } else {
            record.setUpdateTime(new Date());
            record.setFlag(flag);
            record.setOperatorId(userId);
            bkBranchMapper.updateByPrimaryKeySelective(record);
        }
    }

    public void saveBkBranch(BkBranch bankBranch, String method) {
        switch (method){
            case "add": insertRecord(bankBranch); break;
            case "edit": editRecord(bankBranch); break;
            default: throw new AppException(PasCode.SYSTEM_EXCEPTION.code,PasCode.SYSTEM_EXCEPTION.message);
        }
    }

    private void insertRecord(BkBranch bankBranch){
        if (bkBankMapper.selectByBankId(Long.valueOf(bankBranch.getLbnkCd())).size() == 0) {
            throw new AppException(PasCode.BANK_NAME_ERROR.code, PasCode.BANK_NAME_ERROR.message);
        }
        BkBranch record = bkBranchMapper.selectByPrimaryKey(bankBranch.getLbnkNo());
        if (record == null) {
            bankBranch.setFlag("1");
            bankBranch.setCreateTime(new Date());
            bkBranchMapper.insertSelective(bankBranch);
        } else {
            throw new AppException(PasCode.RECORD_EXIST.code, PasCode.RECORD_EXIST.message);
        }
    }

    private void editRecord(BkBranch bankBranch){
        if (bkBankMapper.selectByBankId(Long.valueOf(bankBranch.getLbnkCd())).size() == 0) {
            throw new AppException(PasCode.BANK_NAME_ERROR.code, PasCode.BANK_NAME_ERROR.message);
        }
        BkBranch record = bkBranchMapper.selectByPrimaryKey(bankBranch.getLbnkNo());
        if (record != null) {
            bankBranch.setUpdateTime(new Date());
            bkBranchMapper.updateByPrimaryKeySelective(bankBranch);
        } else {
            throw new AppException(PasCode.RECORD_NOT_EXIST.code, PasCode.RECORD_NOT_EXIST.message);
        }
    }

    public List<BkBranch> queryByLbnkCd(String bankId){
        return bkBranchMapper.selectByLbnkCd(bankId);
    }

    @Override
    public Map<Integer, BatchDetail> importData(List<String> titleList, Map<Integer, List<String>> dataMap, Map<String, Object> extraData) {
        SortedMap<Integer, BatchDetail> result = new TreeMap<>();
        String userId = extraData.get("userId").toString();
        int colCount = titleList.size(); // 以表头列数为数据列数校验
        if (titleList == null || dataMap == null) {
            throw new AppException(CustReturnCode.EXCEL_DATA_ERROR.code, CustReturnCode.EXCEL_DATA_ERROR.message);
        }
        // 解析来自批量导入的数据
        for (Iterator iterator = dataMap.keySet().iterator(); iterator.hasNext();) {
            Integer rowNo = (Integer) iterator.next();
            try{
                List<String> dataList = dataMap.get(rowNo);
                // 校验非空
                if (dataList.size() != colCount) {
                    throw new AppException(CustReturnCode.EXCEL_DATA_ERROR.code,
                            CustReturnCode.EXCEL_DATA_ERROR.message + ":" + (rowNo + 1) + "行缺少数据");
                }
                BkBranch bankBranch = buildBkBranchImport(dataList,userId);
                //校验非空项
                checkEmptyParam(bankBranch.getLbnkNo(),"联行号");
                checkEmptyParam(bankBranch.getLbnkNm(),"支行名称");
                String opFlag = dataList.get(5);
                if(StringUtils.isBlank(opFlag) || "01".equals(opFlag)){
                    insertRecord(bankBranch);
                }else if("02".equals(opFlag)){
                    editRecord(bankBranch);
                }

                result.put(rowNo, buildDetail(rowNo, dataList.get(0), DataImportConstants.SuccessFail.SUCCESS.code, String.valueOf(bankBranch.getLbnkNo()), null));
            }catch (Exception e){
                if (e instanceof AppException) {
                    result.put(rowNo, buildDetail(rowNo, null, DataImportConstants.SuccessFail.FAIL.code, null, ((AppException) e).getErrorMsg()));
                }else {
                    result.put(rowNo, buildDetail(rowNo, null, DataImportConstants.SuccessFail.FAIL.code, null, CustReturnCode.SYSTEM_EXCEPTION.message));
                }
            }

        }

        return result;
    }

    /**
     * 校验空字符串
     * @param value
     * @param name
     */
    private void checkEmptyParam(String value, String name) {
        if(StringUtils.isBlank(value)) {
            throw new AppException(PasCode.SYSTEM_EXCEPTION.code,  name + "不能为空");
        }
    }

    private BkBranch buildBkBranchImport(List<String> dataList, String userId){
        if(dataList==null){
            return null;
        }
        if(!StringUtils.isBlank(dataList.get(3)) && dataList.get(3).length() != 4){
            throw new AppException(PasCode.SYSTEM_EXCEPTION.code,  "省地区代码（选填）请输入4位数字的银联地区码");
        }
        if(!StringUtils.isBlank(dataList.get(4)) && dataList.get(4).length() != 4){
            throw new AppException(PasCode.SYSTEM_EXCEPTION.code,  "市地区代码（选填）请输入4位数字的银联地区码");
        }
        //新建导入数据对象
        BkBranch bankBranch = new BkBranch();
        //将excel数据设置到对象中
        bankBranch.setLbnkNo(dataList.get(0));//联行号
        bankBranch.setLbnkNm(dataList.get(1));//支行名称
        bankBranch.setLbnkCd(StringUtils.isBlank(dataList.get(2)) ? dataList.get(0).substring(0,3) : dataList.get(2));//银行ID
        bankBranch.setAdmProv(StringUtils.isBlank(dataList.get(3)) ? dataList.get(0).substring(3,5) + "00" : dataList.get(3));//省地区代码（选填）
        bankBranch.setAdmCity(StringUtils.isBlank(dataList.get(4)) ? dataList.get(0).substring(3,7) : dataList.get(4));//市地区代码（选填）
        bankBranch.setOperatorId(userId);
        bankBranch.setCreateTime(new Date());
        return bankBranch;
    }

    private BatchDetail buildDetail(Integer rowNo, String rowName, short status, String relateId, String remarks) {
        BatchDetail detail = new BatchDetail();
        detail.setRowNo((long) rowNo);
        detail.setRowName(rowName);
        detail.setStatus(status);
        detail.setRelateId(relateId);
        detail.setRemarks(remarks);
        return detail;
    }

    public List<BkBranch> queryBkBranchByOptions(String provinceCode, String cityCode, String lbnkCd, String bankBranchName, String bankBranchNo){
        List<String> bankBranchNameList = null;
        if(!StringUtils.isBlank(bankBranchName) && bankBranchName.contains(" ")){
            bankBranchNameList = new ArrayList<>();
            Collections.addAll(bankBranchNameList, bankBranchName.split(" "));
            bankBranchName = null;
        }
        return bkBranchMapper.queryBkBranchByOptions(provinceCode,cityCode,lbnkCd,bankBranchName,bankBranchNameList, bankBranchNo);
    }

    public BkBranch queryBkBranchByLbnkNo(String LbnkNo){
        if(StringUtils.isBlank(LbnkNo)){
            return null;
        }
        return bkBranchMapper.selectByPrimaryKey(LbnkNo);
    }
}
