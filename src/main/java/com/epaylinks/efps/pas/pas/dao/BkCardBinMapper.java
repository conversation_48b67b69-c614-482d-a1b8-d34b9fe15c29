package com.epaylinks.efps.pas.pas.dao;

import com.epaylinks.efps.pas.pas.model.BkCardBin;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface BkCardBinMapper {
    int deleteByPrimaryKey(Long id);

    int insert(BkCardBin record);

    int insertSelective(BkCardBin record);

    BkCardBin selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(BkCardBin record);

    int updateByPrimaryKey(BkCardBin record);

    int selectCountByMap(Map map);

    List<BkCardBin> selectBkCardBinPage(Map map);

    boolean checkCardBinTypeExist(@Param("parentType")String parentType, @Param("sonType")String sonType);

    boolean checkCardBinExist(@Param("parentType")String parentType, @Param("sonType")String sonType, @Param("cardNoRange")String cardNoRange, @Param("cardNoLen")Long cardNoLen, @Param("loadFrom")String loadFrom);

    boolean checkYWCardBinTypeExist(@Param("parentType")String parentType, @Param("sonType")String sonType);

    BkCardBin selectCardBinByType(@Param("parentType")String parentType, @Param("sonType")String sonType, @Param("cardNoRange")String cardNoRange, @Param("cardNoLen")Long cardNoLen, @Param("loadFrom")String loadFrom);

    List<BkCardBin> selectBkCardBinByCardNoRang(@Param("cardNoRange")String cardNoRang,@Param("parentType")String parentType, @Param("sonType")String sonType);

    List<BkCardBin> selectByCardNoAndCardNoLen(@Param("cardNo")String cardNo, @Param("cardNoLen") int cardNoLen,@Param("parentType")String parentType, @Param("sonType")String sonType);
}