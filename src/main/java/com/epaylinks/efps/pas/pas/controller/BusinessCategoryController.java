package com.epaylinks.efps.pas.pas.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.pas.pas.domain.BizPayMethod;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.pas.common.Result;
import com.epaylinks.efps.pas.pas.domain.BusinessCategory;
import com.epaylinks.efps.pas.pas.service.impl.BusinessCategoryImpl;
import com.epaylinks.efps.pas.pas.service.impl.BusinessCategoryServiceImpl;
import springfox.documentation.annotations.ApiIgnore;

@RestController
public class BusinessCategoryController {
	@Autowired
	private BusinessCategoryImpl businessCategory;
	@Autowired
	private BusinessCategoryServiceImpl businessCategoryService;
	@RequestMapping(value = "/businesscategories" , method = RequestMethod.GET)
	public List<BusinessCategory> getAllBusinessCategory(){
		return businessCategory.getAllBusinessCategory();
	}
	
	/**
     * 同步所有的业务信息（往kafka发送数据）
     * @return 成功 "1" ， 失败  "0"
     */
    @RequestMapping(value = "/sync" , method = RequestMethod.GET)
    public Result<String> syncBusiness() {
    	businessCategoryService.syncAllBusinessCategory();
    	Result<String> result = new Result<>();
    	result.setData(Constants.SUCCESS);
    	return result;
    }


	@RequestMapping(value = "/selectCategory", method = RequestMethod.GET)
	@Logable(businessTag = "select")
	@Exceptionable
	@Validatable
	@ApiOperation(value = "搜索业务类别", notes = "搜索业务类别", httpMethod = "GET")
		/*@ApiImplicitParams({
	            @ApiImplicitParam(name = "code", value = "编码", required = false, dataType = "String", length =50, paramType = "query"),
	            @ApiImplicitParam(name = "name", value = "名称", required = false, dataType = "String", length =50, paramType = "query")})*/
	public List<BusinessCategory> select(@ApiIgnore BusinessCategory record) {
		return businessCategory.selectBySelective(record);
	}

	@RequestMapping(value = "/selectCategoryByCodes", method = RequestMethod.GET)
	@Logable(businessTag = "selectCategoryByCodes----")
	@Exceptionable
	@Validatable
	@ApiOperation(value = "搜索业务类别", notes = "搜索业务类别", httpMethod = "GET")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "codes", value = "编码", required = false, dataType = "String", paramType = "query"),
//	            @ApiImplicitParam(name = "name", value = "名称", required = false, dataType = "String", length =50, paramType = "query")
	})
	public List<BusinessCategory> selectCategoryByCodes(
			@RequestParam(required = false) String codes
//			@ApiIgnore List<String> list
	) {
		List<String> list = new ArrayList<String>() ;
		if (codes!=null ) {
			String[] c = codes.split(",");
			 list = Arrays.asList(c);
		}
		return businessCategory.getBusinessCategoryByCodes(list);
	}
	
	@RequestMapping(value = "/businessCategory/queryCategoryNameByCode", method = RequestMethod.GET)
	@Logable(businessTag = "queryCategoryNameByCode")
	@Exceptionable
	@Validatable
	@ApiOperation(value = "查询业务类别名称", notes = "查询业务类别名称", httpMethod = "GET")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "code", value = "编码", required = false, dataType = "String", paramType = "query"),
	})
	public String queryBusinessCategoryNameByCode( @RequestParam(required = true) String code	) {

		return businessCategory.queryBusinessCategoryNameByCode(code);
	}
}
