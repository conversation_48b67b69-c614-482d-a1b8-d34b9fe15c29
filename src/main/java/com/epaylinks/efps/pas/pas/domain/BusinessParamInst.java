package com.epaylinks.efps.pas.pas.domain;

import java.util.Date;

import com.alibaba.fastjson.JSON;

public class BusinessParamInst extends BusinessParamInstKey {
    
    private String name;

    private String type;

    private String value;

    private Date createTime;

    private String creator;

    private Date updateTime;

    private String updator;
    
    private String businessExamId;

    private String extendedField;//用作展示用
    
    public BusinessParamInst(String code , String businessExamId , String name, String type, String value, Date createTime, String creator, Date updateTime,
			String updator,Long businessInstDBId) {
		super(code,businessInstDBId);
		this.name = name;
		this.type = type;
		this.value = value;
		this.createTime = createTime;
		this.creator = creator;
		this.updateTime = updateTime;
		this.updator = updator;
		this.businessExamId = businessExamId;
	}
    

	public BusinessParamInst() {
		super();
	}


	public String getBusinessExamId() {
        return businessExamId;
    }

    public void setBusinessExamId(String businessExamId) {
        this.businessExamId = businessExamId;
    }

	public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdator() {
        return updator;
    }

    public void setUpdator(String updator) {
        this.updator = updator;
    }
    
    @Override
    public String toString() {
    	// TODO Auto-generated method stub
    	return JSON.toJSONString(this);
    }


	public String getExtendedField() {
		return extendedField;
	}


	public void setExtendedField(String extendedField) {
		this.extendedField = extendedField;
	}
}