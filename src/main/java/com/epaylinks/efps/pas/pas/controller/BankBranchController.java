package com.epaylinks.efps.pas.pas.controller;


import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.business.pay.request.UserType;
import com.epaylinks.efps.common.datadownload.annotation.DownloadAble;
import com.epaylinks.efps.common.dataimport.BatchTaskService;
import com.epaylinks.efps.common.dataimport.response.BatchResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.pas.pas.controller.response.BankBranchResponse;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.pas.controller.dto.BankBranchDTO;
import com.epaylinks.efps.pas.pas.domain.Bank;
import com.epaylinks.efps.pas.pas.domain.BankBranch;
import com.epaylinks.efps.pas.pas.service.BankBranchService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;

/**
 * 联行号管理
 *
 * <AUTHOR>
 */
@Controller
@Api(value = "BankBranchController", description = "联行号管理")
public class BankBranchController {
    @Autowired
    private BankBranchService bankBranchService;

    @Autowired
    private BatchTaskService batchTaskService;

    @RequestMapping(value = "/branch/insert", method = RequestMethod.POST)
    @ResponseBody
    @Logable(businessTag = "/branch/insert")
    @Exceptionable
    @ApiImplicitParams({
    })
    @ApiOperation(value = "新增联行号", notes = "新增联行号", httpMethod = "POST")
    public String insertBankBranch(
            @RequestBody BankBranch bankBranch
    ) {
        return bankBranchService.insert(bankBranch) == 1 ? Constants.SUCCESS : Constants.FAIL;
    }

    @RequestMapping(value = "/branch/update", method = RequestMethod.POST)
    @ResponseBody
    @Logable(businessTag = "/branch/update")
    @Exceptionable
    @ApiImplicitParams({
    })
    @ApiOperation(value = "修改联行号", notes = "修改联行号", httpMethod = "POST")
    public String updateBankBranch(
            @RequestBody BankBranch bankBranch
    ) {
        return bankBranchService.updateByPrimaryKey(bankBranch) == 1 ? Constants.SUCCESS : Constants.FAIL;
    }

    @RequestMapping(value = "/branch/delete", method = RequestMethod.POST)
    @ResponseBody
    @Logable(businessTag = "/branch/delete")
    @Exceptionable
    @ApiImplicitParams({
    })
    @ApiOperation(value = "删除联行号", notes = "删除联行号", httpMethod = "POST")
    public String deleteBankBranch(
            @RequestBody BankBranch bankBranch
    ) {
        return bankBranchService.deleteByPrimaryKey(bankBranch.getLbnkNo()) == 1 ? Constants.SUCCESS : Constants.FAIL;
    }

    @GetMapping("/branch/query")
    @ResponseBody
    @Logable(businessTag = "bankBranchPageQuery")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "联行号查询", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页码", required = true, dataType = "Integer", paramType = "query", digit = true),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", required = true, dataType = "Integer", paramType = "query", digit = true),
            @ApiImplicitParam(name = "lbnkNo", value = "联行行号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "lbnkNm", value = "联行名称", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "lbnkCd", value = "行别代码", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "corpOrg", value = "合作机构编号", required = false, dataType = "String", paramType = "query"),

            @ApiImplicitParam(name = "provCd", value = "省公司编号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "cityCd", value = "移动市公司", required = false, dataType = "String", paramType = "query"),
    })
    public PageResult<BankBranch> queryBranch(
            @RequestParam Integer pageNum,
            @RequestParam Integer pageSize,
            @RequestParam(required = false) String lbnkNo,
            @RequestParam(required = false) String lbnkNm,
            @RequestHeader(required = false) String lbnkCd,
            @RequestParam(required = false) String corpOrg,
            @RequestParam(required = false) String provCd,
            @RequestParam(required = false) String cityCd) {
        return bankBranchService.queryBranch(pageNum, pageSize, lbnkNo, lbnkNm,
                lbnkCd, corpOrg, provCd, cityCd);
    }

    /**
     * 查询某省份的所有支行(表pas_ccop)
     */
    @GetMapping("/branch/queryBankBranchByOptions")
    @ResponseBody
    @Logable(businessTag = "queryBankBranchByOptions")
    public BankBranchResponse queryBankBranchByOptions(
            @RequestParam(required = false) String provinceCode,
            @RequestParam(required = false) String cityCode,
            @RequestParam String bankIcon,
            @RequestParam(required = false) String bankBranchName) {
        BankBranchResponse response = new BankBranchResponse();
        try {
            List<BankBranchDTO> list = bankBranchService.queryBankBranchByOptions(provinceCode, cityCode, bankIcon, bankBranchName);
            response.setList(list);
        } catch (Exception e) {
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
            return response;
        }
        return response;
    }

    @RequestMapping(value = "/branch/queryBankBranchNameByKey", method = RequestMethod.GET)
    @ResponseBody
    @Logable(businessTag = "/branch/queryBankBranchNameByKey")
    @Exceptionable
    @ApiImplicitParams({
            @ApiImplicitParam(name = "lbnkNo", value = "联行行号", required = true, dataType = "String", paramType = "query")
    })
    @ApiOperation(value = "联行号查询支行名称", notes = "联行号查询支行名称", httpMethod = "GET")
    public String queryBankBranchNameByKey(
            @RequestParam("lbnkNo") String lbnkNo
    ) {
        return bankBranchService.queryBankBranchNameByKey(lbnkNo);
    }

    @GetMapping("/branch/pageQuery")
    @ResponseBody
    @Logable(businessTag = "pageQuery",outputResult = false)
    @Exceptionable
    @Validatable
    @ApiOperation(value = "联行号查询", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "beginTime", value = "开始时间yyyyMMddHHmmss", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "结束时间yyyyMMddHHmmss", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "updateBeginTime", value = "更新时间开始时间yyyyMMddHHmmss", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "updateEndTime", value = "更新时间结束时间yyyyMMddHHmmss", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "lbnkNo", value = "联行号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "lbnkNm", value = "支行名称", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageNum", value = "查询开始页", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "查询的页面大小不需要分页则把此值填大一点", required = true, dataType = "int", paramType = "query")
    })
    @DownloadAble
    public PageResult<BankBranch> pageQuery(
            @RequestHeader(value = "x-user-type", required = false) String userType,
            @RequestParam(value = "lbnkNo", required = false) String lbnkNo,
            @RequestParam(value = "lbnkNm", required = false) String lbnkNm,
            @RequestParam(value = "beginTime", required = false) String beginTime,
            @RequestParam(value = "endTime", required = false) String endTime,
            @RequestParam(value = "updateBeginTime", required = false) String updateBeginTime,
            @RequestParam(value = "updateEndTime", required = false) String updateEndTime,
            @RequestParam(value = "pageNum", required = true) Integer pageNum,
            @RequestParam(value = "pageSize", required = true) Integer pageSize,
            @RequestParam(value = "download", required = false) Boolean download,
            @RequestParam(value = "fileName", required = false) String fileName,
            @RequestParam(value = "type", required = false) String type) {
        PageResult<BankBranch> response = new PageResult<>();
        //该接口只支持运营门户
        if (!UserType.PAS_USER.code.equals(userType)) {
            throw new AppException(PasCode.CUSTOMER_TYPE_INVALID.code);
        }
        try {
            Map map = new HashMap();
            map.put("lbnkNo", lbnkNo);
            map.put("lbnkNm", lbnkNm);
            if (StringUtils.isNotBlank(beginTime)) {
                map.put("beginTime", DateUtils.parseDate(beginTime, "yyyyMMddHHmmss"));
            }
            if (StringUtils.isNotBlank(endTime)) {
                //+1S，原因是ORACLE中的时间精度是到毫秒的，所以查询范围应该为小于次日0点0分0面
                Date date = DateUtils.parseDate(endTime, "yyyyMMddHHmmss");
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(date);
                calendar.add(Calendar.SECOND, 1);
                date = calendar.getTime();
                //判断结束时间是否大于当前时间。如果大于则使用当前时间作为结束时间。
                //避免因结束时间大于当前时间导致数据排序不准问题
                Date now = new Date();
                if(com.epaylinks.efps.common.util.DateUtils.compareTo(date, now) > 0){
                    map.put("endTime", now);
                }else {
                    map.put("endTime", date);
                }
            }

            if (StringUtils.isNotBlank(updateBeginTime)) {
                map.put("updateBeginTime", DateUtils.parseDate(updateBeginTime, "yyyyMMddHHmmss"));
            }
            if (StringUtils.isNotBlank(updateEndTime)) {
                //+1S，原因是ORACLE中的时间精度是到毫秒的，所以查询范围应该为小于次日0点0分0面
                Date date = DateUtils.parseDate(updateEndTime, "yyyyMMddHHmmss");
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(date);
                calendar.add(Calendar.SECOND, 1);
                date = calendar.getTime();
                //判断结束时间是否大于当前时间。如果大于则使用当前时间作为结束时间。
                //避免因结束时间大于当前时间导致数据排序不准问题
                Date now = new Date();
                if(com.epaylinks.efps.common.util.DateUtils.compareTo(date, now) > 0){
                    map.put("updateEndTime", now);
                }else {
                    map.put("updateEndTime", date);
                }
            }
            int total = bankBranchService.queryCount(map);
            response.setTotal(total);
            if (download != null && download) {
                pageSize = total;
                pageNum = 1;
            }
            int endRowNo = pageNum * pageSize;
            int beginRowNo = (pageNum - 1) * pageSize + 1;
            map.put("beginRowNo", beginRowNo);
            map.put("endRowNo", endRowNo);
            response.setRows(bankBranchService.pageQuery(map));
            response.setResult(Constants.detailReturnCode.RETURN_SUCCESS.code);
            response.setCode(Constants.detailReturnCode.RETURN_SUCCESS.code);
            return response;
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof AppException) {
                response.setCode(((AppException) e).getErrorCode());
                response.setMessage(((AppException) e).getErrorMsg());
            } else {
                response.setCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setMessage(PasCode.SYSTEM_EXCEPTION.message);
            }
            return response;
        }
    }

    /**
     * 保存银行信息
     */
    @RequestMapping(value = "/branch/saveBankBranch", method = RequestMethod.POST)
    @Exceptionable
    @Validatable
    @ApiOperation(value = "保存联行号信息", notes = "保存联行号信息", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "lbnkNo", value = "联行号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "lbnkNm", value = "支行名称", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "lbnkCd", value = "银行ID", required = false, dataType = "String", paramType = "query"),
    })
    @Logable(businessTag = "saveBankBranch")
    @ResponseBody
    public CommonOuterResponse saveBankBranch(
            @RequestHeader(value = "x-user-type", required = false) String userType,
            @RequestHeader(value = "x-userid", required = false) String userId,
            @RequestHeader(value = "x-customer-code", required = false) String customerCodeHead,
            @RequestParam(value = "lbnkNo") String lbnkNo,
            @RequestParam(value = "lbnkNm") String lbnkNm,
            @RequestParam(value = "lbnkCd") String lbnkCd,
            @RequestParam(value = "method") String method
    ){
        //该接口只支持运营门户
        if (!UserType.PAS_USER.code.equals(userType)) {
            throw new AppException(PasCode.CUSTOMER_TYPE_INVALID.code);
        }
        try {
            BankBranch bankBranch = new BankBranch();
            bankBranch.setOperatorId(userId);
            bankBranch.setLbnkCd(lbnkCd);
            bankBranch.setLbnkNo(lbnkNo);
            bankBranch.setLbnkNm(lbnkNm);
            bankBranchService.saveBankBranch(bankBranch,method);
            return CommonOuterResponse.success();
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof AppException) {
                return CommonOuterResponse.fail(((AppException) e).getErrorCode(), ((AppException) e).getErrorMsg());
            } else {
                return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
            }
        }
    }

    /**
     * 保存参数值
     */
    @RequestMapping(value = "/branch/enableBankBranch", method = RequestMethod.POST)
    @Exceptionable
    @Validatable
    @ApiOperation(value = "联行号启用/停用", notes = "银行信息启用/停用", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "lbnkNos", value = "联行号（批量逗号分隔）", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "flag", value = "是否启用 0：停用 1：启用", required = true, dataType = "String", paramType = "query"),
    })
    @Logable(businessTag = "enableBankBranch")
    @ResponseBody
    public CommonOuterResponse enableBankBranch(
            @RequestHeader(value = "x-user-type", required = false) String userType,
            @RequestHeader(value = "x-userid", required = false) String userId,
            @RequestHeader(value = "x-customer-code", required = false) String customerCodeHead,
            @RequestParam(value = "lbnkNos") String lbnkNos,
            @RequestParam(value = "flag") String flag
    ){
        //该接口只支持运营门户
        if (!UserType.PAS_USER.code.equals(userType)) {
            throw new AppException(PasCode.CUSTOMER_TYPE_INVALID.code);
        }
        try {
            for (String lbnkNo : lbnkNos.split(",")){
                bankBranchService.enableBankBranch(lbnkNo,flag,userId);
            }
            return CommonOuterResponse.success();
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof AppException) {
                return CommonOuterResponse.fail(((AppException) e).getErrorCode(), ((AppException) e).getErrorMsg());
            } else {
                return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
            }
        }
    }

    /**
     * 批量任务列表
     */
    @RequestMapping(value = "/branch/batchSave", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @Logable(businessTag = "BankBranchController.batchSave")
    @ResponseBody
    @Exceptionable
    @Validatable
    @ApiOperation(value = "联行号批量保存", notes = "联行号批量保存", httpMethod = "POST")
    public BatchResponse batchSave(
            @RequestPart(value = "file", required = false) MultipartFile file,
            @RequestHeader(value = "x-userid", required = true) Long userId
    ){
        BatchResponse response = new BatchResponse();
        try {
            response = batchTaskService.batchSaveByFile(file, (short) 17, null, userId);
        } catch (Exception e) {
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                e.printStackTrace();
                response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
        }
        return response;
    }
}
