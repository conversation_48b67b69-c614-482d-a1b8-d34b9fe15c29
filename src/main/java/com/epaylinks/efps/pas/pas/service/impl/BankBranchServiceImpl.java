package com.epaylinks.efps.pas.pas.service.impl;

import com.epaylinks.efps.common.dataimport.BatchService;
import com.epaylinks.efps.common.dataimport.model.BatchDetail;
import com.epaylinks.efps.common.dataimport.util.CustReturnCode;
import com.epaylinks.efps.common.dataimport.util.DataImportConstants;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.util.StringUtils;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.pas.acl.dao.UserMapper;
import com.epaylinks.efps.pas.acl.po.User;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.pas.controller.dto.BankBranchDTO;
import com.epaylinks.efps.pas.pas.dao.BankBranchMapper;
import com.epaylinks.efps.pas.pas.dao.BankMapper;
import com.epaylinks.efps.pas.pas.domain.Bank;
import com.epaylinks.efps.pas.pas.domain.BankBranch;
import com.epaylinks.efps.pas.pas.service.BankBranchService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;

@Service("BankBranchServiceImpl")
public class BankBranchServiceImpl implements BankBranchService, BatchService {

    @Autowired
    private BankBranchMapper bankBranchMapper;

    @Autowired
    private BankMapper bankMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private RedisTemplate<Object, Object> redisTemplate;

    private static final String BANK_BRANCH_LBNK_NO_KEY_PREFIX = "PAS_BANK_BRANCH_LBNK_NO:";

    private static final Logger log = LoggerFactory.getLogger(BankBranchServiceImpl.class);

    //联行号记录查询
    public PageResult<BankBranch> queryBranch(Integer pageNum, Integer pageSize, String lbnkNo, String lbnkNm,
                                              String lbnkCd, String corpOrg, String provCd, String cityCd
    ) {

        int total = bankBranchMapper.countPageQueryBankBranch(pageNum, pageSize, lbnkNo, lbnkNm,
                lbnkCd, corpOrg, provCd, cityCd);
        int beginRowNo = (pageNum - 1) * pageSize + 1;
        int endRowNo = pageNum * pageSize;
        List<BankBranch> list = bankBranchMapper.pageQueryBankBranch(beginRowNo, endRowNo, lbnkNo, lbnkNm,
                lbnkCd, corpOrg, provCd, cityCd);
        PageResult<BankBranch> pagingResult = new PageResult<>();
        pagingResult.setTotal(total);
        pagingResult.setRows(list);
        return pagingResult;
    }

    @Override
    @Logable(businessTag = "queryBankBranchByOptions")
    public List<BankBranchDTO> queryBankBranchByOptions(String provinceCode, String cityCode, String bankIcon, String bankBranchName) {
        List<BankBranchDTO> dtoList = new ArrayList<>();
        List<BankBranch> list = bankBranchMapper.queryBankBranchByOptions(provinceCode, cityCode, bankIcon, bankBranchName);
        for (BankBranch x : list) {
            BankBranchDTO dto = new BankBranchDTO(x.getLbnkNo(), x.getLbnkNm());
            dtoList.add(dto);
        }
        return dtoList;
    }

    @Override
    @Transactional(isolation = Isolation.DEFAULT, rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public int insert(BankBranch reqVo) {
        return bankBranchMapper.insert(reqVo);
    }


    @Override
    @Transactional(isolation = Isolation.DEFAULT, rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public int updateByPrimaryKey(BankBranch reqVo) {
        return bankBranchMapper.updateByPrimaryKey(reqVo);
    }

    @Override
    @Transactional(isolation = Isolation.DEFAULT, rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public int deleteByPrimaryKey(String lbnkNo) {
        return bankBranchMapper.deleteByPrimaryKey(lbnkNo);
    }

    @Override
    public String queryBankBranchNameByKey(String lbnkNo) {

        String branchName = (String) redisTemplate.opsForValue().get(BANK_BRANCH_LBNK_NO_KEY_PREFIX + lbnkNo);
        if (branchName == null) {
            BankBranch branch = bankBranchMapper.selectByPrimaryKey(lbnkNo);
            if (branch != null) {
                branchName = branch.getLbnkNm();
                redisTemplate.opsForValue().set(BANK_BRANCH_LBNK_NO_KEY_PREFIX + lbnkNo, branchName, 5, TimeUnit.MINUTES);
            }
        }
        return branchName;
    }

    @Override
    public List<BankBranch> pageQuery(Map map) {
        List<BankBranch> bankBranchList = bankBranchMapper.selectByPage(map);
        bankBranchList.forEach(bankBranch -> {
            Bank bank = bankMapper.selectByPrimaryKey(bankBranch.getLbnkCd());
            bankBranch.setLbnkCd(bank.getBnkNm());
            if (!StringUtils.isBlank(bankBranch.getOperatorId())) {
                User user = userMapper.selectByPrimaryKey(Long.valueOf(bankBranch.getOperatorId()));
                if(user != null){
                    bankBranch.setOperatorId(userMapper.selectByPrimaryKey(Long.valueOf(bankBranch.getOperatorId())).getName());
                }

            }
        });
        return bankBranchList;
    }

    @Override
    public int queryCount(Map map) {
        return bankBranchMapper.selectCount(map);
    }

    public void enableBankBranch(String lbnkNo, String flag, String userId) {
        BankBranch record = bankBranchMapper.selectByPrimaryKey(lbnkNo);
        if (record == null) {
            throw new AppException(PasCode.RECORD_NOT_EXIST.code, PasCode.RECORD_NOT_EXIST.message);
        } else {
            record.setUpdateTime(new Date());
            record.setFlag(flag);
            record.setOperatorId(userId);
            bankBranchMapper.updateByPrimaryKeySelective(record);
        }
    }

    @Override
    public void saveBankBranch(BankBranch bankBranch, String method) {
        switch (method){
            case "add": insertRecord(bankBranch); break;
            case "edit": editRecord(bankBranch); break;
            default: throw new AppException(PasCode.SYSTEM_EXCEPTION.code,PasCode.SYSTEM_EXCEPTION.message);
        }
    }

    private void insertRecord(BankBranch bankBranch){
        if (bankMapper.selectByPrimaryKey(bankBranch.getLbnkCd()) == null) {
            throw new AppException(PasCode.BANK_NAME_ERROR.code, PasCode.BANK_NAME_ERROR.message);
        }
        BankBranch record = bankBranchMapper.selectByPrimaryKey(bankBranch.getLbnkNo());
        if (record == null) {
            bankBranch.setCreateTime(new Date());
            bankBranchMapper.insertSelective(bankBranch);
        } else {
            throw new AppException(PasCode.RECORD_EXIST.code, PasCode.RECORD_EXIST.message);
        }
    }

    private void editRecord(BankBranch bankBranch){
        BankBranch record = bankBranchMapper.selectByPrimaryKey(bankBranch.getLbnkNo());
        if (bankMapper.selectByPrimaryKey(bankBranch.getLbnkCd()) == null) {
            throw new AppException(PasCode.BANK_NAME_ERROR.code, PasCode.BANK_NAME_ERROR.message);
        }
        if (record != null) {
            bankBranch.setUpdateTime(new Date());
            bankBranchMapper.updateByPrimaryKeySelective(bankBranch);
        } else {
            throw new AppException(PasCode.RECORD_NOT_EXIST.code, PasCode.RECORD_NOT_EXIST.message);
        }
    }

    @Override
    public Map<Integer, BatchDetail> importData(List<String> titleList, Map<Integer, List<String>> dataMap, Map<String, Object> extraData) {
        SortedMap<Integer, BatchDetail> result = new TreeMap<>();
        String userId = extraData.get("userId").toString();
        int colCount = titleList.size(); // 以表头列数为数据列数校验
        if (titleList == null || dataMap == null) {
            throw new AppException(CustReturnCode.EXCEL_DATA_ERROR.code, CustReturnCode.EXCEL_DATA_ERROR.message);
        }
        // 解析来自批量导入的数据
        for (Iterator iterator = dataMap.keySet().iterator(); iterator.hasNext();) {
            Integer rowNo = (Integer) iterator.next();
            try{
                List<String> dataList = dataMap.get(rowNo);
                // 校验非空
                if (dataList.size() != colCount) {
                    throw new AppException(CustReturnCode.EXCEL_DATA_ERROR.code,
                            CustReturnCode.EXCEL_DATA_ERROR.message + ":" + (rowNo + 1) + "行缺少数据");
                }
                BankBranch bankBranch = buildBankBranchImport(dataList,userId);
                //校验非空项
                checkEmptyParam(bankBranch.getLbnkNo(),"联行号");
                checkEmptyParam(bankBranch.getLbnkNm(),"支行名称");
                checkEmptyParam(bankBranch.getLbnkCd(),"银行ID");
                insertRecord(bankBranch);
                result.put(rowNo, buildDetail(rowNo, dataList.get(0), DataImportConstants.SuccessFail.SUCCESS.code, String.valueOf(bankBranch.getLbnkNo()), null));
            }catch (Exception e){
                if (e instanceof AppException) {
                    result.put(rowNo, buildDetail(rowNo, null, DataImportConstants.SuccessFail.FAIL.code, null, ((AppException) e).getErrorMsg()));
                }else {
                    result.put(rowNo, buildDetail(rowNo, null, DataImportConstants.SuccessFail.FAIL.code, null, CustReturnCode.SYSTEM_EXCEPTION.message));
                }
            }

        }

        return result;
    }

    /**
     * 校验空字符串
     * @param value
     * @param name
     */
    private void checkEmptyParam(String value, String name) {
        if(StringUtils.isBlank(value)) {
            throw new AppException(PasCode.SYSTEM_EXCEPTION.code,  name + "不能为空");
        }
    }

    private BankBranch buildBankBranchImport(List<String> dataList,String userId){
        if(dataList==null){
            return null;
        }
        //新建导入数据对象
        BankBranch bankBranch = new BankBranch();
        //将excel数据设置到对象中
        bankBranch.setLbnkNo(dataList.get(0));//联行号
        bankBranch.setLbnkNm(dataList.get(1));//支行名称
        bankBranch.setLbnkCd(dataList.get(2));//银行ID
        bankBranch.setOperatorId(userId);
        bankBranch.setCreateTime(new Date());
        return bankBranch;
    }

    private BatchDetail buildDetail(Integer rowNo, String rowName, short status, String relateId, String remarks) {
        BatchDetail detail = new BatchDetail();
        detail.setRowNo((long) rowNo);
        detail.setRowName(rowName);
        detail.setStatus(status);
        detail.setRelateId(relateId);
        detail.setRemarks(remarks);
        return detail;
    }
}



