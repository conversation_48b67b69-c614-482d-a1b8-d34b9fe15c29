package com.epaylinks.efps.pas.pas.controller;

import com.alibaba.fastjson.JSON;
import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.common.timetask.TimeTaskRequest;
import com.epaylinks.efps.common.timetask.TimeTaskResponse;
import com.epaylinks.efps.common.timetask.TimeTaskSend;
import com.epaylinks.efps.common.tool.response.EpResponse;
import com.epaylinks.efps.common.tool.time.Timex;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.common.PasConstants;
import com.epaylinks.efps.pas.pas.dao.PasTimeTaskMapper;
import com.epaylinks.efps.pas.pas.dao.PasTimeTaskRecordMapper;
import com.epaylinks.efps.pas.pas.domain.PasTimeTask;
import com.epaylinks.efps.pas.pas.domain.PasTimeTaskRecord;
import com.epaylinks.efps.pas.pas.service.PasTimeTaskService;
import com.epaylinks.efps.pas.pas.timetask.QuartzJobManager;
import com.epaylinks.efps.pas.pas.timetask.RemoteJobService;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.quartz.CronExpression;
import org.quartz.SchedulerException;
import org.quartz.Trigger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.net.InetAddress;
import java.text.ParseException;
import java.time.Duration;
import java.util.*;

@RestController
@RequestMapping("/timeTask")
@Api(value = "TimeTaskController", description = "动态定时任务相关")
public class TimeTaskController {
    private static final Logger logger = LoggerFactory.getLogger(TimeTaskController.class);

    @Autowired
    private PasTimeTaskService pasTimeTaskService;

    @Autowired
    private TimeTaskController self;


    @Autowired
    private QuartzJobManager quartzJobManager;

    @Autowired
    private TimeTaskSend timeTaskSend;


    @Autowired
    private PasTimeTaskMapper pasTimeTaskMapper;

    @Autowired
    private SequenceService sequenceService;

    @Autowired
    private PasTimeTaskRecordMapper pasTimeTaskRecordMapper;

    @Autowired
    private RemoteJobService remoteJobService;

    @PostMapping("/reExecuteJob")
    @ApiOperation(value = "重新执行当日执行失败或处理中的任务，对于处理中的任务要先确认任务已经停止")
    public EpResponse<Long> reExecuteJob(String jobName) {
        return EpResponse.success(remoteJobService.reExecuteJob(jobName));
    }

    @PostMapping("/executeJob")
    @Logable(businessTag = "timeTask.executeJob")
    @ApiOperation(value = "执行当日过了触发时间还没执行的任务，当日已经执行的任务（无论是否成功）不能调用该接口")
    public CommonOuterResponse executeJob(String jobName) {
        remoteJobService.executeJob(jobName, new Date());
        return CommonOuterResponse.success();
    }

    @PostMapping("/pauseJob")
    @Logable(businessTag = "timeTask.pauseJob")
    @ApiOperation(value = "暂停一个定时任务")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "jobName", value = "定时任务名称", dataType = "String", required = false, paramType = "query")
            })
    public CommonOuterResponse pauseJob(String jobName) {
        PasTimeTask task = pasTimeTaskService.selectByJobName(jobName);
        try {
            //如果定时任务不是出于正常状态
            if (!Trigger.TriggerState.NORMAL.equals(quartzJobManager.queryTrggerState(task))) {
                return CommonOuterResponse.fail(PasCode.TASK_STATE_EXCEPTION.code, PasCode.TASK_STATE_EXCEPTION.message);
            }
            quartzJobManager.pauseJob(task);
        } catch (Exception e) {
            self.timeTaskLog(e.getMessage());
            return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
        }

        return CommonOuterResponse.success();
    }


    @PostMapping("/resumeJob")
    @Logable(businessTag = "timeTask.resumeJob")
    @ApiOperation(value = "启动一个暂停的一个定时任务")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "jobName", value = "定时任务名称", dataType = "String", required = false, paramType = "query")
            })
    public CommonOuterResponse resumeJob(String jobName) {

        PasTimeTask task = pasTimeTaskService.selectByJobName(jobName);
        try {
            //如果该定时任务不是处于暂停的状态
            if (!Trigger.TriggerState.PAUSED.equals(quartzJobManager.queryTrggerState(task))) {
                return CommonOuterResponse.fail(PasCode.TASK_STATE_EXCEPTION.code, PasCode.TASK_STATE_EXCEPTION.message);
            }
            quartzJobManager.resumeJob(task);
        } catch (Exception e) {
            self.timeTaskLog(e.getMessage());
            return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
        }
        return CommonOuterResponse.success();
    }

    @PostMapping("/pauseAllJob")
    @Logable(businessTag = "timeTask.pauseAllJob")
    @ApiOperation(value = "暂停所有定时任务")
    public CommonOuterResponse pauseAllJob() {
        quartzJobManager.shutdownAllJobs();
        return CommonOuterResponse.success();
    }

    @PostMapping("/startAllJobs")
    @Logable(businessTag = "timeTask.startAllJobs")
    @ApiOperation(value = "启动所有定时任务")
    public CommonOuterResponse startAllJobs() {
        quartzJobManager.shutdownAllJobs();
        return CommonOuterResponse.success();
    }

    @PostMapping("/queryAllJobs")
    @Logable(businessTag = "timeTask.startAllJobs")
    @ApiOperation(value = "查看所有定时任务")
    public CommonOuterResponse queryAllJobs() throws SchedulerException {
        List<Map<String, Object>> allJob = quartzJobManager.getAllJob();
        return CommonOuterResponse.success(allJob);
    }

    @Logable(businessTag = "timeTaskLog")
    public String timeTaskLog(String message) {
        return message;
    }


    public static class MonitorRecord {
        String jobName;
        String jobRemark;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        Date lastFireTime;
        Long lastExecuteRecordId;
        String errMsg;

        public String getJobName() {
            return jobName;
        }

        public void setJobName(String jobName) {
            this.jobName = jobName;
        }

        public String getJobRemark() {
            return jobRemark;
        }

        public void setJobRemark(String jobRemark) {
            this.jobRemark = jobRemark;
        }

        public Date getLastFireTime() {
            return lastFireTime;
        }

        public void setLastFireTime(Date lastFireTime) {
            this.lastFireTime = lastFireTime;
        }

        public Long getLastExecuteRecordId() {
            return lastExecuteRecordId;
        }

        public void setLastExecuteRecordId(Long lastExecuteRecordId) {
            this.lastExecuteRecordId = lastExecuteRecordId;
        }

        public String getErrMsg() {
            return errMsg;
        }

        public void setErrMsg(String errMsg) {
            this.errMsg = errMsg;
        }
    }

    @GetMapping("monitor")
    public List<MonitorRecord> monitor() throws ParseException {
        List<MonitorRecord> monitorRecords = new ArrayList<>();
        Timex now = Timex.now();

        List<PasTimeTask> timeTasks = pasTimeTaskMapper.selectAll();
        for (PasTimeTask timeTask : timeTasks) {
            if (timeTask.getJobStatus().equals("1")) //跳过不调度的任务
                continue;

            if (StringUtils.isNotBlank(timeTask.getDeadline())) { //设置了最后期限的任务检查是否执行完成
                Timex deadline = Timex.of(now.to(Timex.Format.yyyyMMdd) + timeTask.getDeadline(), Timex.Format.yyyyMMddHHmmss);
                if (deadline.toDateTime().isBeforeNow()) {
                    //检查当日任务是否已经完成
                    if (pasTimeTaskRecordMapper.countJobDone(now.start().toDate(), deadline.toDate(), timeTask.getJobName()) == 0) {
                        MonitorRecord monitorRecord = new MonitorRecord();
                        monitorRecord.setJobName(timeTask.getJobName());
                        monitorRecord.setJobRemark(timeTask.getRemark());
                        monitorRecord.setLastFireTime(timeTask.getLastFireTime());
                        monitorRecord.setLastExecuteRecordId(timeTask.getLastExecuteRecordId());
                        monitorRecord.setErrMsg("任务在最后期限[" + deadline.to(Timex.Format.yyyy$MM$dd$HH$mm$ss) + "]前未执行或执行出错");
                        monitorRecords.add(monitorRecord);
                        logger.info("任务[{}]在最后期限[{}]前未执行或执行出错", timeTask.getJobName(), deadline.to(Timex.Format.yyyy$MM$dd$HH$mm$ss));
                    }
                }
            } else {
                CronExpression cronExpression = new CronExpression(timeTask.getCron());
                if (timeTask.getLastFireTime() != null) {
                    Timex lastFireTime = Timex.ofDate(timeTask.getLastFireTime());
                    //检查执行是否已经超时
                    Timex nextTime = Timex.ofDate(cronExpression.getNextValidTimeAfter(lastFireTime.toDate()));
                    if (nextTime.toDateTime().isBeforeNow()) {
                        //如果是串行的任务再判断是否超过最大执行时间
                        if ("1".equals(timeTask.getConcurrent())) {
                            nextTime = Timex.ofDate(cronExpression.getNextValidTimeAfter(
                                    lastFireTime
                                            .plus(Duration.ofSeconds(Optional.ofNullable(timeTask.getMaxExecuteDuration()).orElse(300)))
                                            .toDate()));
                            if (nextTime.toDateTime().isAfterNow()) {
                                continue;
                            }
                        }

                        MonitorRecord monitorRecord = new MonitorRecord();
                        monitorRecord.setJobName(timeTask.getJobName());
                        monitorRecord.setJobRemark(timeTask.getRemark());
                        monitorRecord.setLastFireTime(timeTask.getLastFireTime());
                        monitorRecord.setLastExecuteRecordId(timeTask.getLastExecuteRecordId());
                        monitorRecord.setErrMsg("任务执行超时，下一触发时间[" + nextTime.to(Timex.Format.yyyy$MM$dd$HH$mm$ss) + "]的任务未执行");
                        monitorRecords.add(monitorRecord);

                        logger.info("任务[{}]RECORD_ID[{}]执行超时，[{}]触发的任务未执行",
                                timeTask.getJobName(),
                                timeTask.getLastExecuteRecordId(),
                                nextTime.to(Timex.Format.yyyy$MM$dd$HH$mm$ss));
                    }
                }
            }
        }
        return monitorRecords;
    }
}
