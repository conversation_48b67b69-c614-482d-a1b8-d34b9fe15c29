package com.epaylinks.efps.pas.pas.timetask.job;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.epaylinks.efps.common.business.pay.request.UserType;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.timetask.TaskJob;
import com.epaylinks.efps.common.timetask.TaskRequest;
import com.epaylinks.efps.pas.acl.po.User;
import com.epaylinks.efps.pas.acl.service.UserService;
import com.epaylinks.efps.pas.common.PasConstants;
import com.epaylinks.efps.pas.pas.service.LoginInfoService;

/**
 * 锁定用户自动回复
 * 
 * <AUTHOR>
 * @date 2020-11-06
 *
 */
@Component
public class PasLockedUserRecoveryJob implements TaskJob {

    private static final Long DEFAULT_COUNT = 100L;

    @Autowired
    private UserService userService;

    @Autowired
    private LoginInfoService loginInfoService;

    @Override
    @Logable(businessTag = "execute")
    public void execute(TaskRequest request) throws Exception {

        List<User> list = userService.queryLockedUserList(getCount(request));
        list.forEach(record -> {
            try {
                // 锁定状态解锁
                record.setStatus(PasConstants.UserStatus.YES.code);
                record.setUpdateTime(new Date());
                record.setRemark("系统恢复");
                userService.modifyUser(record);

                // 登录错误次数清空
                loginInfoService.recoveryErrorCountByUsernameAndType(record.getName(), UserType.PAS_USER.code);
            } catch (Exception e) {
                e.printStackTrace();
            }
        });

    }

    Long getCount(TaskRequest taskRequest) {
        Long count = DEFAULT_COUNT;
        try {
            String data = taskRequest.getJobParams();
            if (StringUtils.isNotEmpty(data)) {
                JSONObject objectData = JSON.parseObject(data);
                count = objectData.getLong("count");
            }
        } catch (Exception e) {
        }
        return count;
    }

}
