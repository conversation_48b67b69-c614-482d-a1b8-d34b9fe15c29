package com.epaylinks.efps.pas.pas.dao;

import com.epaylinks.efps.pas.pas.controller.response.ManagementDataResponse;
import com.epaylinks.efps.pas.pas.domain.ManagementData;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;
import java.util.Map;

@Mapper
public interface ManagementDataMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ManagementData record);

    int insertSelective(ManagementData record);

    ManagementData selectByPrimaryKey(Long id);

    ManagementData selectByManagementData(Long id);

    int updateByPrimaryKeySelective(ManagementData record);

    int updateByPrimaryKey(ManagementData record);

    Long queryMDNextSeq();

    List<ManagementDataResponse> pageQueryManageData(Map map);

    Integer countManageData(Map map);
}