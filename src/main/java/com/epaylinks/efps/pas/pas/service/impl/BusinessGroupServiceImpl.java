package com.epaylinks.efps.pas.pas.service.impl;

import com.epaylinks.efps.common.log.Logable;

import com.epaylinks.efps.pas.pas.controller.dto.CustBusinessPriceDTO;
import com.epaylinks.efps.pas.pas.dao.BusinessGroupMapper;
import com.epaylinks.efps.pas.pas.domain.*;
import com.epaylinks.efps.pas.pas.service.BusinessGroupService;

import com.epaylinks.efps.pas.pas.service.BusinessService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class BusinessGroupServiceImpl implements BusinessGroupService {

    @Autowired
    private BusinessGroupMapper businessGroupMapper;

    @Autowired
    private BusinessService businessService;

    public List<BusinessGroup> selectAllBusinssGroup(String isTemplate) {
        return businessGroupMapper.selectAllBusinssGroup(isTemplate);
    }

    /**
     * 完整的基础权限树
     *
     * @return
     */
    @Logable(businessTag = "PermService.getBasicTree")
    public BusinessRoot getBasicTree() {
        BusinessRoot root = new BusinessRoot();
        List<BusinessGroup> allGroupList = businessGroupMapper.selectAllBusinssGroup("0");//查分组;
        List<Business> businessesList = businessService.selectByState(); //查业务
        root.setGroupList(allGroupList);
        for (BusinessGroup group : allGroupList) {
            BusinessGroup paramGroup = new BusinessGroup();
            paramGroup.setIsTemplate("1");
            paramGroup.setParentCode(group.getCode());
            List<BusinessGroup> templateList = this.selectAllTemplateBusinssGroup(paramGroup);
            group.setTemplateList(templateList);
            for (BusinessGroup template : templateList) {
                List<Business> templateBusiness = new ArrayList();
                for (Business record : businessesList) {
                    Business b = new Business();
                    BeanUtils.copyProperties(record, b);
                    if (b.getTemplateCode() != null && b.getBusinessGroup() != null && b.getTemplateCode().contains(template.getCode()) && b.getBusinessGroup().contains(template.getParentCode())) {//b.getBusinessGroup().equals(tem.getGroupCode()
                        //如果父级存在业务大类：取父级业务大类返回
                        if (StringUtils.isNotEmpty(group.getBusinessCategoryCode())) {
                            b.setBusinessCategory(group.getBusinessCategoryCode());
                        }
                        templateBusiness.add(b);
                    }
                }
                template.setBusinessList(templateBusiness);
            }
        }
        return root;
    }

    @Override
    public BusinessRoot getExcludeBasicTree(String source, String excludeList) {
        BusinessRoot root = new BusinessRoot();
        List<BusinessGroup> allGroupList = businessGroupMapper.selectAllBusinssGroup("0");//查分组;
        List<Business> businessesList = businessService.selectByState(); //查业务
        String[] excludes = excludeList.split(",");
        root.setGroupList(allGroupList);
        for (BusinessGroup group : allGroupList) {
            BusinessGroup paramGroup = new BusinessGroup();
            paramGroup.setIsTemplate("1");
            paramGroup.setParentCode(group.getCode());
            List<BusinessGroup> templateList = this.selectAllTemplateBusinssGroup(paramGroup);
            group.setTemplateList(templateList);
            for (BusinessGroup template : templateList) {
                List<Business> templateBusiness = new ArrayList();
                for (Business record : businessesList) {
                    Business b = new Business();
                    BeanUtils.copyProperties(record, b);
                    if (b.getTemplateCode() != null && b.getBusinessGroup() != null && b.getTemplateCode().contains(template.getCode()) && b.getBusinessGroup().contains(template.getParentCode())) {//b.getBusinessGroup().equals(tem.getGroupCode()
                        //如果父级存在业务大类：取父级业务大类返回
                        if (StringUtils.isNotEmpty(group.getBusinessCategoryCode())) {
                            b.setBusinessCategory(group.getBusinessCategoryCode());
                        }
                        if(StringUtils.isBlank(excludeList) || !checkExclude(b.getCode(),excludes)){
                            templateBusiness.add(b);
                        }
                    }
                }
                template.setBusinessList(templateBusiness);
            }
        }
        return root;
    }

    private boolean checkExclude(String businessCode,String[] excludeList){
        if(Objects.isNull(excludeList) || excludeList.length<1){
            return false;
        }
        for(String exclude : excludeList){
            if(businessCode.equals(exclude)){
                return true;
            }
        }
        return false;
    }

    @Override
    public BusinessRoot getCustomerTree(Long customerId) {
        BusinessRoot root = getBasicTree();
        List<BusinessGroup> groupList = root.getGroupList();
        List<CustBusinessPriceDTO> priceList = businessService.selectCustBusinessPrice(customerId);
        if (groupList == null || groupList.size() == 0) {
            return root;
        }
        // 20230106 H5业务员进件业务列表改为只展示提现
        for (int i = groupList.size() - 1; i >= 0; i--) {
            BusinessGroup outerBusinessGroup = groupList.get(i);
            if (!"1000014".equals(outerBusinessGroup.getCode())) {
                groupList.remove(i); // 过滤非出金支付服务
            }
        }
        // 将商户费率信息放入map，key值为业务编码
        Map<String,CustBusinessPriceDTO> priceDTOMap = new HashMap<>();
        if (priceList != null && priceList.size() > 0) {
            for (int i = 0; i < priceList.size(); i++) {
                CustBusinessPriceDTO custBusinessPriceDTO = priceList.get(i);
                priceDTOMap.put(custBusinessPriceDTO.getBusinessCode(),custBusinessPriceDTO);
            }
        }
        // 商户分组树添加费率信息
        // groupList
        for (int i = 0; i < groupList.size(); i++) {
            BusinessGroup outerBusinessGroup = groupList.get(i);
            List<BusinessGroup> businessGroups = outerBusinessGroup.getTemplateList();
            // templateList
            if (businessGroups != null && businessGroups.size() > 0) {
                for (int j = 0; j < businessGroups.size(); j++) {
                    BusinessGroup businessGroup = businessGroups.get(j);
                    List<Business> businessList = businessGroup.getBusinessList();
                    // businessList
                    if (businessList != null && businessList.size() > 0) {
//                        for (int z = 0; z < businessList.size(); z++) {
//                            Business business = businessList.get(z);
//                            CustBusinessPriceDTO custBusinessPriceDTO = priceDTOMap.get(business.getCode());
//                            business.setPrice(custBusinessPriceDTO);
//                            businessList.set(z,business);
//                        }
                        for (int z = businessList.size() - 1; z >= 0; z--) {
                            Business business = businessList.get(z);
                            CustBusinessPriceDTO custBusinessPriceDTO = priceDTOMap.get(business.getCode());
                            business.setPrice(custBusinessPriceDTO);
                            if (!"WithdrawToSettmentDebit".equals(business.getCode())) {
                                businessList.remove(z); // 过滤非提现
                            } else {
                                businessList.set(z,business);
                            }
                        }
                    }
                    businessGroup.setBusinessList(businessList);
                    businessGroups.set(j,businessGroup);
                }
            }
            outerBusinessGroup.setTemplateList(businessGroups);
            groupList.set(i,outerBusinessGroup);
        }
        root.setGroupList(groupList);
        return root;
    }

    //查分组下的模版   0510
    public List<BusinessGroup> selectAllTemplateBusinssGroup(BusinessGroup record) {
        return businessGroupMapper.selectAllTemplateBusinssGroup(record);
    }

    @Override
    public BusinessRoot getBasicTreeByBusinss(List<String> businssCodes) {
        BusinessRoot root = new BusinessRoot();
        root.setCode("TermBusinessGroup");
        root.setName("终端业务分组");
        root.setGroupList(new ArrayList<>());
        //1.获取到业务列表
        List<Business> businessList = businessService.selectByCodeList(businssCodes);
        //2.获取到业务分组
        Map<String, BusinessGroup> businessGroupMap = new HashMap<>();
        Map<String, BusinessGroup> businessTemplateMap = new HashMap<>();
        Map<String, BusinessGroup> termBusinessGroupMap = new HashMap<>();
        businessList.forEach(business -> {
            //TODO 一个业务在多个分组下如何处理？
            String businessGroup = business.getBusinessGroup().split(",")[0];
            BusinessGroup group = businessGroupMapper.selectByCode(businessGroup);
            if (null == businessGroupMap.get(group.getCode())) {
                businessGroupMap.put(group.getCode(), group);
                group.setTemplateList(new ArrayList<>());
            } else {
                group = businessGroupMap.get(group.getCode());
            }
            List<BusinessGroup> templateList = group.getTemplateList();

            //TODO 一个业务在多个模板下如何处理？
            String templateCode = business.getTemplateCode().split(",")[0];
            BusinessGroup template = businessGroupMapper.selectByCode(templateCode);
            if (null == businessTemplateMap.get(template.getCode())) {
                businessTemplateMap.put(template.getCode(), template);
                template.setBusinessList(new ArrayList<>());
            } else {
                template = businessTemplateMap.get(template.getCode());
            }
            List<Business> templateBusinessList = template.getBusinessList();

            templateBusinessList.add(business);
            template.setBusinessList(templateBusinessList);
            templateList.add(template);
            Map<String, BusinessGroup> templateListMap = templateList.stream().collect(Collectors.toMap(BusinessGroup::getCode, Function.identity(), (existing, replacement) -> existing));
            templateList = templateListMap.entrySet().stream().sorted(Map.Entry.<String, BusinessGroup>comparingByKey().reversed()).map(x -> x.getValue()).collect(Collectors.toList());
            group.setTemplateList(templateList);
            if (null == termBusinessGroupMap.get(group.getCode())) {
                root.getGroupList().add(group);
                termBusinessGroupMap.put(group.getCode(), group);
            }
        });
        return root;
    }


    /**
     * 查询所有模版下的业务
     *
     * @return
     */
    private List<Business> selectBusinessByTemplate(List<Business> templateList, List<String> groupCodeList) {

        List<String> deptIdList = new ArrayList<>();
        for (Business d : templateList) {
            deptIdList.add(d.getTemplateCode());
        }

        Map m = new HashMap();
        m.put("templateCodeList", deptIdList);
        m.put("groupCodeList", groupCodeList);
        List<Business> departmentUserAmountList = businessService.selectBusinessByGroupOrTemplate(m);
        return departmentUserAmountList;
    }


}
