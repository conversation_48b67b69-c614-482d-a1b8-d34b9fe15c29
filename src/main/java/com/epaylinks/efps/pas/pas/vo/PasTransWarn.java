package com.epaylinks.efps.pas.pas.vo;

import java.util.Date;

public class PasTransWarn {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 易票联订单号
     */
    private String transactionNo;

    /**
     * 订单类型
     */
    private String type;

    /**
     * 订单详情
     */
    private String transactionMsg;

    /**
     * 告警状态 01处理中 00处理完成
     */
    private String status;

    /**
     * 告警次数
     */
    private Integer warnTimes;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 操作员ID
     */
    private Long userId;

    /**
     * 操作名称
     */
    private String userName;

    /**
     * 告警原因
     */
    private String reason;

    /**
     * 告警来源
     */
    private String source;

    /**
     * 告警级别 01一般 02重要 03紧急
     */
    private String warnLevel;

    /**
     * 告警类别
     */
    private String category;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTransactionNo() {
        return transactionNo;
    }

    public void setTransactionNo(String transactionNo) {
        this.transactionNo = transactionNo;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTransactionMsg() {
        return transactionMsg;
    }

    public void setTransactionMsg(String transactionMsg) {
        this.transactionMsg = transactionMsg;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getWarnTimes() {
        return warnTimes;
    }

    public void setWarnTimes(Integer warnTimes) {
        this.warnTimes = warnTimes;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getWarnLevel() {
        return warnLevel;
    }

    public void setWarnLevel(String warnLevel) {
        this.warnLevel = warnLevel;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }
}