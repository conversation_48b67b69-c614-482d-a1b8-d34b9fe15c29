package com.epaylinks.efps.pas.pas.vo;

import org.springframework.stereotype.Component;

import com.epaylinks.efps.common.datadownload.annotation.FieldAnnotation;

@Component
public class PasLoginLogResp {
	 
	 @FieldAnnotation(fieldName="登录账号")
	 private String username;
	 @FieldAnnotation(fieldName="角色")
	 private String lrole;
	 @FieldAnnotation(fieldName="姓名/商户名称")
	 private String realname;
	 @FieldAnnotation(fieldName="商户编号")
	 private String customercode;
	 @FieldAnnotation(fieldName="所属平台商")
	 private String platcustomer;
	 @FieldAnnotation(fieldName="所属代理商")
	 private String servicecustomer;
	 @FieldAnnotation(fieldName="上次登录时间")
	 private String login_time;
	 @FieldAnnotation(fieldName="登录IP")
	 private String login_ip;
	 @FieldAnnotation(fieldName="登录状态")
	 private String state;
	 @FieldAnnotation(fieldName="备注")
	 private String remark;
	public String getUsername() {
		return username;
	}
	public void setUsername(String username) {
		this.username = username;
	}
	public String getLrole() {
		return lrole;
	}
	public void setLrole(String lrole) {
		this.lrole = lrole;
	}
	public String getRealname() {
		return realname;
	}
	public void setRealname(String realname) {
		this.realname = realname;
	}
	public String getCustomercode() {
		return customercode;
	}
	public void setCustomercode(String customercode) {
		this.customercode = customercode;
	}
	public String getPlatcustomer() {
		return platcustomer;
	}
	public void setPlatcustomer(String platcustomer) {
		this.platcustomer = platcustomer;
	}
	public String getServicecustomer() {
		return servicecustomer;
	}
	public void setServicecustomer(String servicecustomer) {
		this.servicecustomer = servicecustomer;
	}
	public String getLogin_time() {
		return login_time;
	}
	public void setLogin_time(String login_time) {
		this.login_time = login_time;
	}
	public String getLogin_ip() {
		return login_ip;
	}
	public void setLogin_ip(String login_ip) {
		this.login_ip = login_ip;
	}
	public String getState() {
		return state;
	}
	public void setState(String state) {
		this.state = state;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}

}
