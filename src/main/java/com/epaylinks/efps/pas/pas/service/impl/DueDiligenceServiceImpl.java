package com.epaylinks.efps.pas.pas.service.impl;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.pas.common.FsCommonService;
import com.epaylinks.efps.pas.pas.service.DueDiligenceService;

import com.itextpdf.text.pdf.BaseFont;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;
import org.xhtmlrenderer.pdf.ITextFontResolver;
import org.xhtmlrenderer.pdf.ITextRenderer;

import java.io.*;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

@Service
public class DueDiligenceServiceImpl implements DueDiligenceService {

    private static SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
    private static final Random random = new Random();

    @Autowired
    private CommonLogger logger;

    @Autowired
    private TemplateEngine templateEngine;

    @Autowired
    private FsCommonService fsCommonService;

    @Override
    public MultipartFile getMultipartFile(File file) throws Exception{
        FileItem item = new DiskFileItemFactory().createItem("file", MediaType.MULTIPART_FORM_DATA_VALUE, true, file.getName());
        InputStream input = new FileInputStream(file);
        OutputStream os = item.getOutputStream();
        IOUtils.copy(input, os);
        return new CommonsMultipartFile(item);
    }

    @Override
    public String createTableByData(String merchantName, String parentName, String busiNo, String regDate, String remark, String type)
        throws Exception{

        //处理超长数据
        if(StringUtils.isNotBlank(remark)){
            String split = "\n";
            StringBuilder tempStr = new StringBuilder();
            if(remark.length()>30 && remark.length()<=60){
                tempStr.append(remark.substring(0,30))
                        .append(split)
                        .append(remark.substring(30));
                remark = tempStr.toString();
            }else if(remark.length()>60 && remark.length()<=90){
                tempStr.append(remark.substring(0,30))
                        .append(split)
                        .append(remark.substring(30,60))
                        .append(split)
                        .append(remark.substring(60));
                remark = tempStr.toString();
            }else if(remark.length()>90){
                tempStr.append(remark.substring(0,30))
                        .append(split)
                        .append(remark.substring(30,60))
                        .append(split)
                        .append(remark.substring(60,90));
                remark = tempStr.toString();
            }
        }

        //封装参数
        Map<String, String> vals = null;
        vals = new HashMap<>(5);
        vals.put("merchantName", merchantName);
        vals.put("parentName", parentName);
        vals.put("busiNo", busiNo);
        vals.put("regDate", regDate);
        vals.put("remark", remark);
        String up = "1";
        if(StringUtils.isBlank(parentName)){
            up = "2";
        }
        vals.put("up", up);

        //使用的模板
        String emailTemplateName = "due_tpl_table_api";
        if("1".equals(type)){
            emailTemplateName = "due_tpl_table";
        }
        //组装html
        String html = readTemplateAndSetValues(emailTemplateName, vals);
        //生成本地文件路径
        String parentFilePath = "tmp/pdf/";
        File parentFile = new File(parentFilePath);
        if(!parentFile.exists()) {
            parentFile.mkdirs();
        }
        String filePath = parentFilePath + sdf.format(new Date()) + random.nextInt(9) + ".pdf";
        //转换pdf
        File file = new File(filePath);
        OutputStream os = new FileOutputStream(file);
        ITextRenderer renderer = new ITextRenderer();
        ITextFontResolver fontResolver = renderer.getFontResolver();
        //ClassPathResource regular = new ClassPathResource("templates/fonts/simsun.ttf");
        //regular.getURL().toString()
        fontResolver.addFont("templates/fonts/simsun.ttf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
        renderer.setDocumentFromString(html);
        renderer.layout();
        //ByteArrayOutputStream os = new ByteArrayOutputStream();
        //renderer.createPDF(os);
        //os.toByteArray();
        renderer.createPDF(os);
        os.close();
        MultipartFile multipartFile = getMultipartFile(file);
        String uid = fsCommonService.uploadFile(multipartFile,"CUST","尽职调查表");
        return uid;
    }

    @Override
    public String readTemplateAndSetValues(String emailTemplateName, Map<String, String> map) {
        Context context = new Context();
        for (String key : map.keySet()) {
            context.setVariable(key, map.get(key));
        }
        return templateEngine.process(emailTemplateName, context);
    }
}
