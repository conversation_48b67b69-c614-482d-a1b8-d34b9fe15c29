package com.epaylinks.efps.pas.pas.service;

import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.pas.pas.dao.WxBusinessCategoryMapper;
import com.epaylinks.efps.pas.pas.domain.WxBusinessCategory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class WxBusinessCategoryService {

	@Autowired
	private WxBusinessCategoryMapper wxBusinessCategoryMapper;

	public void batchInsert(List<WxBusinessCategory> list) {
		wxBusinessCategoryMapper.batchInsert(list);
	}

	@Logable(businessTag = "queryLevel1s")
	public List<String> queryLevel1s() {
		return wxBusinessCategoryMapper.selectLevel1s();
	}

	@Logable(businessTag = "queryLevel2s")
	public List<String> queryLevel2s(String level1) {
		return wxBusinessCategoryMapper.selectLevel2s(level1);
	}

	@Logable(businessTag = "queryLevel3s")
	public List<String> queryLevel3s(String level1, String level2) {
		return wxBusinessCategoryMapper.selectLevel3s(level1, level2);
	}

	@Logable(businessTag = "queryLevel4s")
	public List<WxBusinessCategory> queryLevel4s(String level1, String level2, String level3) {
		return wxBusinessCategoryMapper.selectLevel4s(level1, level2, level3);
	}

	@Logable(businessTag = "getBusinessCategoryById")
	public WxBusinessCategory getBusinessCategoryById(String id) {
		return wxBusinessCategoryMapper.selectByPrimaryKey(Long.valueOf(id));
	}

    @Logable(businessTag = "queryBusinessCategoryByUnionMcc")
    public List<WxBusinessCategory> queryBusinessCategoryByUnionMcc(String unionMcc, Long mccType) {
        return wxBusinessCategoryMapper.selectByUnionMcc(unionMcc, mccType);
    }
}
