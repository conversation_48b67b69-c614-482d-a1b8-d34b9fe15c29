package com.epaylinks.efps.pas.pas.service.impl;

import com.epaylinks.efps.pas.pas.dao.AppliAddParaMapper;
import com.epaylinks.efps.pas.pas.domain.AppliAddPara;
import com.epaylinks.efps.pas.pas.service.AppliAddParaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> 5m
 * @Date : 2017/12/28 17:05
 * @Description :
 */
@Service
public class AppliAddParaServiceImpl implements AppliAddParaService{

    @Autowired
    AppliAddParaMapper appliAddParaMapper;

    @Override
    public int deleteByPrimaryKey(Long id) {
        return appliAddParaMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(AppliAddPara record) {
        return appliAddParaMapper.insert(record);
    }

    @Override
    public int insertSelective(AppliAddPara record) {
        return appliAddParaMapper.insertSelective(record);
    }

    @Override
    public AppliAddPara selectByPrimaryKey(Long id) {
        return appliAddParaMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(AppliAddPara record) {
        return appliAddParaMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(AppliAddPara record) {
        return appliAddParaMapper.updateByPrimaryKey(record);
    }

    @Override
    public List<AppliAddPara> selectBySelective(AppliAddPara record) {
        return appliAddParaMapper.selectBySelective(record);
    }
}
