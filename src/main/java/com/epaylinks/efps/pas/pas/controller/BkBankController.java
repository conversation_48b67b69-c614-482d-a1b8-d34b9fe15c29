package com.epaylinks.efps.pas.pas.controller;


import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.business.pay.request.UserType;
import com.epaylinks.efps.common.datadownload.annotation.DownloadAble;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.util.BeanUtil;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.pas.model.BkBank;
import com.epaylinks.efps.pas.pas.service.impl.BkBankServiceImpl;
import com.epaylinks.efps.pas.pas.vo.BankDataRelevance;
import com.epaylinks.efps.pas.pas.vo.BkBankRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Date;
import java.util.Map;

@RestController
@Api(value = "BkBankController", description = "银行管理")
@RequestMapping("/bk/bank")
public class BkBankController {
    @Autowired
    private BkBankServiceImpl bkBankService;

    @RequestMapping(value = "/queryList", method = RequestMethod.GET)
    @Logable(businessTag = "BkBankController.queryList",outputResult = false)
    @Exceptionable
    @Validatable
    @ApiOperation(value = "银行管理列表", notes = "银行管理列表", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "bankName", value = "银行名称", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "institutionCode", value = "金融机构编码", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "issueBankNo", value = "cardbin机构代码", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "bankId", value = "联行号行别代码", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "flag", value = "状态 0：停用 1：启用", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "bankCode", value = "银行编码", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "beginTime", value = "开始时间yyyyMMddHHmmss", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "结束时间yyyyMMddHHmmss", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "isAreaBank", value = "是否区域银行 0:否 1:是", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "pageNum", value = "查询开始页", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "查询的页面大小不需要分页则把此值填大一点", required = true, dataType = "int", paramType = "query")
    })
    @DownloadAble
    public PageResult<BkBank> queryList(
            @RequestHeader(value = "x-user-type", required = false) String userType,
            @RequestHeader(value = "x-userid", required = false) String userId,
            @RequestHeader(value = "x-customer-code", required = false) String customerCodeHead,
            @RequestParam(value = "fileSource", required = false) String fileSource,
            @ApiIgnore BkBankRequest request,
            @RequestParam(value = "pageNum", required = true) Integer pageNum,
            @RequestParam(value = "pageSize", required = true) Integer pageSize,
            @RequestParam(value = "download", required = false) Boolean download,
            @RequestParam(value = "fileName", required = false) String fileName,
            @RequestParam(value = "type", required = false) String type) {
        PageResult<BkBank> response = new PageResult<>();
        //该接口只支持运营门户
       /* if (!UserType.PAS_USER.code.equals(userType)) {
            throw new AppException(PasCode.CUSTOMER_TYPE_INVALID.code);
        }*/
        try {
            Map<String, Object> map = BeanUtil.transBean2Map(request);
            map.entrySet().removeIf(x -> x.getValue() == null || x.getValue() == "");
            if (StringUtils.isNotBlank(request.getBeginTime())) {
                map.put("beginTime", DateUtils.parseDate(request.getBeginTime(), "yyyyMMddHHmmss"));
            }
            if (StringUtils.isNotBlank(request.getEndTime())) {
                //+1S，原因是ORACLE中的时间精度是到毫秒的，所以查询范围应该为小于次日0点0分0面
                Date date = DateUtils.parseDate(request.getEndTime(), "yyyyMMddHHmmss");
                //判断结束时间是否大于当前时间。如果大于则使用当前时间作为结束时间。
                //避免因结束时间大于当前时间导致数据排序不准问题
                Date now = new Date();
                if(com.epaylinks.efps.common.util.DateUtils.compareTo(date, now) > 0){
                    map.put("endTime", now);
                }else {
                    map.put("endTime", date);
                }
            }
            int total = bkBankService.queryCount(map);
            response.setTotal(total);
            if (download != null && download) {
                pageSize = total;
                pageNum = 1;
            }
            int endRowNo = pageNum * pageSize;
            int beginRowNo = (pageNum - 1) * pageSize + 1;
            map.put("beginRowNo", beginRowNo);
            map.put("endRowNo", endRowNo);
            response.setRows(bkBankService.bankPageQuery(map));
            response.setResult(Constants.detailReturnCode.RETURN_SUCCESS.code);
            response.setCode(Constants.detailReturnCode.RETURN_SUCCESS.code);
            return response;
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof AppException) {
                response.setCode(((AppException) e).getErrorCode());
                response.setMessage(((AppException) e).getErrorMsg());
            } else {
                response.setCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setMessage(PasCode.SYSTEM_EXCEPTION.message);
            }
            return response;
        }
    }

    @RequestMapping(value = "/saveBankInfo", method = RequestMethod.POST)
    @Exceptionable
    @Validatable
    @ApiOperation(value = "保存银行信息", notes = "保存银行信息", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "bankCode", value = "银行编码", length = 20, required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "bankName", value = "银行名称",length = 25, required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "fullName", value = "全称", length = 100,required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "institutionCode", value = "金融机构编码", length = 14, required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "issueBankNo", value = "cardbin机构代码", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "bankId", value = "联行号行别代码", length = 3, required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "isAreaBank", value = "是否区域银行 0:否 1:是", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "id", value = "银行主键ID", required = false, dataType = "int", paramType = "query"),
    })
    @Logable(businessTag = "BankController.saveBankInfo")
    public CommonOuterResponse saveBankInfo(
            @RequestHeader(value = "x-user-type", required = false) String userType,
            @RequestHeader(value = "x-userid", required = false) String userId,
            @RequestHeader(value = "x-customer-code", required = false) String customerCodeHead,
            @ApiIgnore BkBank bkBank,
            @RequestParam(value = "method") String method
    ){
        //该接口只支持运营门户
        if (!UserType.PAS_USER.code.equals(userType)) {
            throw new AppException(PasCode.CUSTOMER_TYPE_INVALID.code);
        }
        try {
            bkBank.setOperatorId(userId);
            bkBankService.saveBank(bkBank,method);
            return CommonOuterResponse.success();
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof AppException) {
                return CommonOuterResponse.fail(((AppException) e).getErrorCode(), ((AppException) e).getErrorMsg());
            } else {
                return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
            }
        }
    }


    @RequestMapping(value = "/enableBankInfo", method = RequestMethod.POST)
    @Exceptionable
    @Validatable
    @ApiOperation(value = "银行信息启用/停用", notes = "银行信息启用/停用", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "银行信息表id（批量逗号分隔）", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "flag", value = "是否启用 0：停用 1：启用", required = true, dataType = "String", paramType = "query"),
    })
    @Logable(businessTag = "BankController.enableBankInfo")
    public CommonOuterResponse enableBankInfo(
            @RequestHeader(value = "x-user-type", required = false) String userType,
            @RequestHeader(value = "x-userid", required = false) String userId,
            @RequestHeader(value = "x-customer-code", required = false) String customerCodeHead,
            @RequestParam(value = "ids") String ids,
            @RequestParam(value = "flag") String flag
    ){
        //该接口只支持运营门户
        if (!UserType.PAS_USER.code.equals(userType)) {
            throw new AppException(PasCode.CUSTOMER_TYPE_INVALID.code);
        }
        try {
            for (String id : ids.split(",")){
                bkBankService.enableBank(Long.valueOf(id),flag,userId);
            }
            return CommonOuterResponse.success();
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof AppException) {
                return CommonOuterResponse.fail(((AppException) e).getErrorCode(), ((AppException) e).getErrorMsg());
            } else {
                return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
            }
        }
    }


    /**
     * 数据关联
     */
    @RequestMapping(value = "/dataRelevance", method = RequestMethod.POST)
    @Exceptionable
    @Validatable
    @ApiOperation(value = "数据关联/数据关联", notes = "数据关联/数据关联", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "cardNoRang", value = "银行卡号", required = true, dataType = "String", paramType = "query"),
    })
    @Logable(businessTag = "BankController.dataRelevance")
    public CommonOuterResponse<BankDataRelevance> dataRelevance(@RequestHeader(value = "x-user-type", required = false) String userType,
                                                                @RequestHeader(value = "x-userid", required = false) String userId,
                                                                @RequestHeader(value = "x-customer-code", required = false) String customerCodeHead,
                                                                String cardNoRang){
        //该接口只支持运营门户
        if (!UserType.PAS_USER.code.equals(userType)) {
            throw new AppException(PasCode.CUSTOMER_TYPE_INVALID.code);
        }
        try {
            return CommonOuterResponse.success(bkBankService.dataRelevance(cardNoRang));
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof AppException) {
                return CommonOuterResponse.fail(((AppException) e).getErrorCode(), ((AppException) e).getErrorMsg());
            } else {
                return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
            }
        }
    }

    @RequestMapping(value = "/queryBkBank", method = RequestMethod.POST)
    @Exceptionable
    @Validatable
    @ApiOperation(value = "通过卡号或银行名称查询银行信息", notes = "通过卡号或银行名称查询银行信息", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "查询类型（1:银行名称 2:银行卡号(完整卡号) 3:联行号 4:银行卡号（前几位匹配）", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "cardNoRang", value = "银行卡号（前几位）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "cardNo", value = "银行卡号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "bankBranchNo", value = "联行号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "bankName", value = "银行名称", required = false, dataType = "String", paramType = "query"),
    })
    public CommonOuterResponse queryBkBank(@RequestParam(value = "cardNoRang",required = false) String cardNoRang,
                                           @RequestParam(value = "cardNo",required = false) String cardNo,
                                           @RequestParam(value = "bankName",required = false) String bankName,
                                           @RequestParam(value = "bankBranchNo",required = false) String bankBranchNo,
                                           @RequestParam(value = "type",required = true)  String type
                                           ){
        try {
            if(StringUtils.isBlank(type)){
                return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, "未指定查询类型");
            }
            return CommonOuterResponse.success(bkBankService.queryBkBank(cardNoRang,bankName,cardNo,bankBranchNo,type));
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof AppException) {
                return CommonOuterResponse.fail(((AppException) e).getErrorCode(), ((AppException) e).getErrorMsg());
            } else {
                return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
            }
        }
    }
}
