package com.epaylinks.efps.pas.pas.controller;

import com.epaylinks.efps.common.business.pay.request.UserType;
import com.epaylinks.efps.common.datadownload.annotation.DownloadAble;
import com.epaylinks.efps.common.dataimport.BatchTaskService;
import com.epaylinks.efps.common.dataimport.response.BatchResponse;
import com.epaylinks.efps.common.dataimport.util.CustReturnCode;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.pas.acl.service.DataAuthService;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.pas.common.LogService;
import com.epaylinks.efps.pas.pas.domain.ComplainTrade;
import com.epaylinks.efps.pas.pas.service.ComplainTradeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/complainTrade")
@Api(value = "ComplainTradeController", description = "投诉交易记录管理")
public class ComplainTradeController {

    @Autowired
    private ComplainTradeService complainTradeService;

    @Autowired
    private BatchTaskService batchTaskService;

    @Autowired
    private LogService pasLogService;

    @Autowired
    private CommonLogger logger;

    @Autowired
    private DataAuthService dataAuthService;

    @GetMapping("/page")
    @Logable(businessTag = "ComplainTradeController.pageQuery",outputResult = false)
    @Exceptionable
    @ApiOperation(value = "投诉交易记录查询", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dtStartTime", value = "开始时间yyyyMMddHHmmss", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "dtEndTime", value = "结束时间yyyyMMddHHmmss", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "startCreateTime", value = "开始时间yyyyMMddHHmmss", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endCreateTime", value = "结束时间yyyyMMddHHmmss", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "customerCode", value = "商户编号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "channelMchtNo", value = "上游商户号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "platCustomerCode", value = "所属平台商编号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "agentCustomerCode", value = "所属代理商编号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "businessMan", value = "业务员", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orderNo", value = "订单号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "channelType", value = "上游机构：微信、支付宝", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "batchNo", value = "导入批次号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageNum", value = "查询开始页", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "查询的页面大小不需要分页则把此值填大一点", required = true, dataType = "int", paramType = "query")
    })
    @DownloadAble
    public PageResult<ComplainTrade> pageQuery(
            @RequestHeader(value = "x-user-type", required = false) String userType,
            @RequestParam(value = "dtStartTime", required = false) String dtStartTime,
            @RequestParam(value = "dtEndTime", required = false) String dtEndTime,
            @RequestParam(value = "startCreateTime", required = false) String startCreateTime,
            @RequestParam(value = "endCreateTime", required = false) String endCreateTime,
            @RequestParam(value = "customerCode", required = false) String customerCode,
            @RequestParam(value = "channelMchtNo", required = false) String channelMchtNo,
            @RequestParam(value = "platCustomerCode", required = false) String platCustomerCode,
            @RequestParam(value = "agentCustomerCode", required = false) String agentCustomerCode,
            @RequestParam(value = "businessMan", required = false) String businessMan,
            @RequestParam(value = "orderNo", required = false) String orderNo,
            @RequestParam(value = "channelType", required = false) String channelType,
            @RequestParam(value = "batchNo", required = false) String batchNo,
            @RequestParam(value = "pageNum", required = true) Integer pageNum,
            @RequestParam(value = "pageSize", required = true) Integer pageSize,
            @RequestParam(value = "download", required = false) Boolean download,
            @RequestParam(value = "fileName", required = false) String fileName,
            @RequestParam(value = "fileSource", required = false) String fileSource,
            @RequestParam(value = "type", required = false) String type,
            @RequestHeader(value = "x-userid",required = false) Long userId) {
        PageResult<ComplainTrade> response = new PageResult<>();
        try {
            //该接口只支持运营门户
            if (!UserType.PAS_USER.code.equals(userType)) {
                throw new AppException(PasCode.CUSTOMER_TYPE_INVALID.code);
            }
            Map map = new HashMap();
            if (StringUtils.isNotBlank(dtStartTime)) {
                map.put("dtStartTime", dtStartTime);
            }
            if (StringUtils.isNotBlank(dtEndTime)) {
                map.put("dtEndTime", dtEndTime);
            }
            if (StringUtils.isNotBlank(startCreateTime)) {
                map.put("startCreateTime", startCreateTime);
            }
            if (StringUtils.isNotBlank(endCreateTime)) {
                map.put("endCreateTime", endCreateTime);
            }
            if (StringUtils.isNotBlank(customerCode)) {
                map.put("customerCode", customerCode);
            }
            if (StringUtils.isNotBlank(channelMchtNo)) {
                map.put("channelMchId", channelMchtNo);
            }
            if (StringUtils.isNotBlank(platCustomerCode)) {
                map.put("platCustomerCode", platCustomerCode);
            }
            if (StringUtils.isNotBlank(agentCustomerCode)) {
                map.put("agentCustomerCode", agentCustomerCode);
            }
            if (StringUtils.isNotBlank(businessMan)) {
                map.put("businessMan", businessMan);
            }
            if (StringUtils.isNotBlank(orderNo)) {
                map.put("orderNo", orderNo);
            }
            if (StringUtils.isNotBlank(channelType)) {
                map.put("channelType", channelType);
            }
            if (StringUtils.isNotBlank(batchNo)) {
                map.put("batchNo", batchNo);
            }
            dataAuthService.setMapParam(map,userId);

            int total = complainTradeService.queryCount(map);
            response.setTotal(total);
            if (download != null && download) {
                pageSize = total;
                pageNum = 1;
            }
            int endRowNo = pageNum * pageSize;
            int beginRowNo = (pageNum - 1) * pageSize + 1;
            map.put("beginRowNo", beginRowNo);
            map.put("endRowNo", endRowNo);
            response.setRows(complainTradeService.pageQuery(map));
            response.setCode(Constants.detailReturnCode.RETURN_SUCCESS.code);
            return response;
        } catch (Exception e) {
            logger.printMessage("投诉交易记录查询异常："+e.getMessage());
            logger.printLog(e);
            if (e instanceof AppException) {
                response.setCode(((AppException) e).getErrorCode());
                response.setMessage(((AppException) e).getErrorMsg());
                response.setErrorMsg(((AppException) e).getErrorMsg());
            } else {
                response.setCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setMessage(PasCode.SYSTEM_EXCEPTION.message);
                response.setErrorMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
            return response;
        }
    }

    /**
     * 批量任务列表
     */
    @PostMapping(value = "/batchSave")
    @Logable(businessTag = "ComplainTradeController.batchSave",outputResult = false)
    @Exceptionable
    @ApiOperation(value = "投诉交易记录批量保存", notes = "投诉交易记录批量保存", httpMethod = "POST")
    public BatchResponse batchSave(
            @RequestPart(value = "file", required = false) MultipartFile file,
            @RequestHeader(value = "x-userid", required = true) Long userId
    ){
        BatchResponse response = new BatchResponse();
        try {
            pasLogService.printLog("准备进行批量投诉导入..");
            response = batchTaskService.batchSaveByFile(file, (short) 18, null, userId);
            if(response!=null){
                response.setDetailMessage(null);
            }
            if(response!=null &&
                    (CustReturnCode.BATCH_PART_ERROR.code.equals(response.getReturnCode()) || CustReturnCode.BATCH_ERROR.code.equals(response.getReturnCode()))){
                response.setReturnCode("0000");
                response.setReturnMsg("文件导入成功，请在导入文件管理中查看处理情况!");
            }
        } catch (Exception e) {
            logger.printMessage("投诉交易记录文件导入异常："+e.getMessage());
            logger.printLog(e);
            if (e instanceof AppException) {
                if(response!=null &&
                        (CustReturnCode.BATCH_PART_ERROR.code.equals(response.getReturnCode()) || CustReturnCode.BATCH_ERROR.code.equals(response.getReturnCode()))){
                    response.setReturnCode("0000");
                    response.setReturnMsg("文件导入成功，请在导入文件管理中查看处理情况!");
                }else {
                    response.setReturnCode(((AppException) e).getErrorCode());
                    response.setReturnMsg(((AppException) e).getErrorMsg());
                }
            } else {
                response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
        }
        return response;
    }
}
