package com.epaylinks.efps.pas.pas.dao;

import com.epaylinks.efps.pas.pas.domain.AppliAddPara;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@Mapper
@Transactional
public interface AppliAddParaMapper {
    int deleteByPrimaryKey(Long id);

    int insert(AppliAddPara record);

    int insertSelective(AppliAddPara record);

    @Transactional(readOnly = true)
    AppliAddPara selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(AppliAddPara record);

    int updateByPrimaryKey(AppliAddPara record);

    @Transactional(readOnly = true)
    List<AppliAddPara> selectBySelective(AppliAddPara record);

}