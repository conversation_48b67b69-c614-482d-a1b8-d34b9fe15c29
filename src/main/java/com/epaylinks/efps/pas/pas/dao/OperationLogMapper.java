package com.epaylinks.efps.pas.pas.dao;

import com.epaylinks.efps.pas.pas.domain.OperationLog;
import com.epaylinks.efps.pas.pas.domain.OperationLogReport;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@Mapper
@Transactional
public interface OperationLogMapper {

	void insert(OperationLog opLog);

	int pageQueryTotal(@Param("startTime")String startTime,@Param("endTime")String endTime,@Param("userName")String userName,@Param("realName")String realName,@Param("module")String module);

	List<OperationLog> pageQuery(@Param("startNum")int startNum ,@Param("endNum")int endNum, @Param("startTime")String startTime,@Param("endTime")String endTime,@Param("userName")String userName,@Param("realName")String realName,@Param("module")String module);

	/**
	 * 查询操作日志报表总数
	 * @param map
	 * @return
	 */
    int countOperLogReport(Map<String, Object> map);

    /**
     * 分页查询操作日志报表
     * @param map
     * @return
     */
    List<OperationLogReport> pageQueryOperLogReport(Map<String, Object> map);
    
    
}
