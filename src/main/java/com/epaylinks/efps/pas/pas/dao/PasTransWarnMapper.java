package com.epaylinks.efps.pas.pas.dao;

import com.epaylinks.efps.pas.pas.vo.PasTransWarn;
import org.apache.ibatis.annotations.Mapper;

import java.math.BigDecimal;

@Mapper
public interface PasTransWarnMapper {
    int deleteByPrimaryKey(BigDecimal id);

    int insert(PasTransWarn record);

    int insertSelective(PasTransWarn record);

    PasTransWarn selectByPrimaryKey(BigDecimal id);

    int updateByPrimaryKeySelective(PasTransWarn record);

    int updateByPrimaryKey(PasTransWarn record);

    int updateByTransactionNo(PasTransWarn record);

    PasTransWarn selectByTransactionNo(Object transactionNo);
}