package com.epaylinks.efps.pas.pas.service;

import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.pas.pas.vo.PasLoginLogJson;
import com.epaylinks.efps.pas.pas.vo.PasLoginLogReportResp;
import com.epaylinks.efps.pas.pas.vo.PasLoginLogReq;
import com.epaylinks.efps.pas.pas.vo.PasLoginLogResp;
import com.epaylinks.efps.pas.pas.vo.PasOperLogResp;

public interface LoginLogService {


    public void syncAllBizPayMethod(String value);
	/**
	 * 分页查询
	 */
	PageResult<PasLoginLogResp> pageQuery(PasLoginLogReq pasLoginLogReq);

	/**
	 * 保存登录日志
	 * @param pasLoginLogJson
	 */
	public void saveLoginLog(PasLoginLogJson pasLoginLogJson);
	
	/**
	 * 保存pas登录日志（pas系统登录，不需用pas订阅kafka消息处理）
	 * @param loginName
	 * @param state  登录状态：0失败 1成功
	 * @param loginIp
	 * @param remark
	 */
    void savePasLoginLog(String loginName, String state, String loginIp, String remark, boolean pwdError);
    
    /**
     * 保存操作日志
     * @param pasLoginLogJson
     */
    public void saveOperLog(PasLoginLogJson pasLoginLogJson);
    
    /**
     * 操作记录查询接口
     * @param pasLoginLogReq
     * @return
     */
    public PageResult<PasOperLogResp> pageQueryOperLog(PasLoginLogReq pasLoginLogReq);
    
    /**
     * 分页查询登录日志报表
     * @param pasLoginLogReq
     * @return
     */
    public PageResult<PasLoginLogReportResp> pageQueryReport(PasLoginLogReq pasLoginLogReq);

}
