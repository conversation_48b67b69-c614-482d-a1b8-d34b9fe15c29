package com.epaylinks.efps.pas.pas.service;

import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.pas.pas.controller.request.InvoiceAuditRequest;
import com.epaylinks.efps.pas.pas.controller.response.InvoiceAuditResponse;

import java.text.ParseException;
import java.util.List;
import java.util.Map;

public interface InvoiceAuditService {
    /**
     * 参数校验
     * @param request
     * @param userId
     * @param customerNo
     * @param source
     * @return
     */
    InvoiceAuditRequest checkParam(InvoiceAuditRequest request,Long userId,String customerNo,String source,String userType);

    /**
     * 新增发票
     * @param request
     * @param userId
     * @param customerNo
     */
    void add(InvoiceAuditRequest request,Long userId,String customerNo,String userType) throws ParseException;

    /**
     * 修改发票
     * @param request
     * @param userId
     * @param customerNo
     */
    void edit(InvoiceAuditRequest request,Long userId,String customerNo,String userType) throws ParseException;

    /**
     * 审核发票
     * @param invoiceId
     * @param userId
     * @param auditResult
     * @param auditComment
     */
    void audit(Long invoiceId,Long userId,String auditResult,String auditComment);

    /**
     * 分页列表
     * @param paramMap
     * @param userId
     * @param headCustomerNo
     * @param download
     * @return
     */
    PageResult<List<InvoiceAuditResponse>> pageQuery(Map paramMap, Long userId, String headCustomerNo,boolean download);
}
