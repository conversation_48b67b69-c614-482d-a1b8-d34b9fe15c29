package com.epaylinks.efps.pas.pas.controller.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
public class LoginReportResponse {
    @ApiModelProperty(value = "ID",dataType = "Long")
    private Long reportId;

    @ApiModelProperty(value = "登录账号",dataType = "String")
    private String loginAccount;

    @ApiModelProperty(value = "角色",dataType = "String")
    private String loginRole;

    @ApiModelProperty(value = "姓名/商户名称",dataType = "String")
    private String name;

    @ApiModelProperty(value = "商户编号",dataType = "String")
    private String customerCode;

    @ApiModelProperty(value = "登录IP",dataType = "String")
    private String loginIp;

    @ApiModelProperty(value = "用户类型(1:运营管理系统用户; 2:商户门户用户; 3:钱包APP商户门户用户)",dataType = "String")
    private String userType;

    @ApiModelProperty(value = "登录次数",dataType = "String")
    private Integer loginNum;

    @ApiModelProperty(value = "登录日期",dataType = "String")
    private String loginTime;

    @ApiModelProperty(value = "审计状态(1:正常；2:异常)",dataType = "String")
    private String reportState;

    @ApiModelProperty(value = "审计说明",dataType = "String")
    private String reportDesc;

    @ApiModelProperty(value = "异常处理",dataType = "String")
    private String exceptionHandle;

    @ApiModelProperty(value = "处理人",dataType = "String")
    private String operator;

    @ApiModelProperty(value = "处理时间",dataType = "String")
    private String handleTime;
}
