package com.epaylinks.efps.pas.pas.timetask;

import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.common.util.DateUtils;
import com.epaylinks.efps.pas.pas.common.PasConstant;
import com.epaylinks.efps.pas.pas.dao.PasTimeTaskRecordMapper;
import com.epaylinks.efps.pas.pas.dao.PasTransWarnMapper;
import com.epaylinks.efps.pas.pas.domain.PasTimeTaskRecord;
import com.epaylinks.efps.pas.pas.domain.TimeTaskHeart;
import com.epaylinks.efps.pas.pas.service.PasTimeTaskService;
import com.epaylinks.efps.pas.pas.vo.PasTransWarn;
import org.quartz.CronTrigger;
import org.quartz.SchedulerException;
import org.quartz.Trigger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.sql.SQLIntegrityConstraintViolationException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 心跳定时任务
 * 因为插表时间不一定会等于执行时间，告警误差1分钟
 *
 */
@Component
@EnableScheduling
public class HeartJob {
    @Autowired
    private PasTimeTaskRecordMapper taskRecordMapper;

    @Autowired
    private PasTransWarnMapper pasTransWarnMapper;

    @Autowired
    private CommonLogger commonLogger;

    @Autowired
    private PasTimeTaskService service;

    @Autowired
    private SequenceService sequenceService;

    @Value("${erroTime}")
    private long erroTime;

    @Autowired
    private QuartzJobManager quartzJobManager;
    
    @Scheduled(cron = "0 0/1 * * * ?")
    public void execute() throws SchedulerException {
        List<TimeTaskHeart> normalTasks = new ArrayList<>();
        List<TimeTaskHeart> errorTasks = new ArrayList<>();

        //获取到所有的定时任务
        List<Trigger> triggers = quartzJobManager.getAllTrigger();
        for (Trigger trigger : triggers){
            TimeTaskHeart timeTaskHeart = new TimeTaskHeart();
            timeTaskHeart.setJobName(trigger.getKey().getName());
            CronTrigger cronTrigger = (CronTrigger) trigger;
            timeTaskHeart.setCron(cronTrigger.getCronExpression());
            timeTaskHeart.setNextTime(DateUtils.formatDate(trigger.getNextFireTime(),"yyyy-MM-dd hh:mm:ss"));
            try {
                try {
                    String state = quartzJobManager.getTriggerState(trigger.getKey());
                    timeTaskHeart.setJobState(state);
                    normalTasks.add(timeTaskHeart);
                }catch (SchedulerException sc){
                    timeTaskHeart.setJobState("error");
                    errorTasks.add(timeTaskHeart);
                }

                /**
                 * 分析出间隔时间
                 * 这个方法只能分析出间隔时间一样的定时任务
                 */
                long nextTime = caculateTime(trigger);
                //分析出上次执行定时任务时间与下次定时任务执行时间是否间隔时间过长
                PasTimeTaskRecord pasTimeTaskRecord = taskRecordMapper.selectLastTimeByJobName(trigger.getKey().getName());
                Long lastTime = 0L;
                if (pasTimeTaskRecord == null){
                    //如果一次都没执行，有可能是刚刚创建的
                    timeTaskHeart.setLastTime(DateUtils.formatDate(trigger.getStartTime(),"yyyy-MM-dd hh:mm:ss"));
                    lastTime = trigger.getStartTime().getTime();
                }else{
                        timeTaskHeart.setLastTime(DateUtils.formatDate(pasTimeTaskRecord.getCreateTime(),"yyyy-MM-dd hh:mm:ss"));
                        lastTime = pasTimeTaskRecord.getCreateTime().getTime();
                }
                long spaceTime = trigger.getNextFireTime().getTime() - lastTime;

                if (spaceTime > nextTime ){
                    //如果两次定时任务之间间隔大于两次定时任务时间间隔，也有可能是执行超时，等待Hystrix的超时时间再试一次
                    Thread.sleep(erroTime);
                    pasTimeTaskRecord = taskRecordMapper.selectLastTimeByJobName(trigger.getKey().getName());
                    lastTime = pasTimeTaskRecord.getCreateTime().getTime();
                    spaceTime = trigger.getNextFireTime().getTime() - lastTime;
                    if (spaceTime > nextTime ){
                        //查询是否已经存在了
                        String transactionNo = DateUtils.formatDate(new Date(),"yyyyMMdd hhmmss")+pasTimeTaskRecord.getJobName();
                        PasTransWarn transWarn = pasTransWarnMapper.selectByTransactionNo(transactionNo);
                        if(transWarn != null){
                            transWarn.setUpdateTime(new Date());
                            transWarn.setWarnTimes(transWarn.getWarnTimes() + 1);
                            pasTransWarnMapper.updateByPrimaryKey(transWarn);
                        }else {
                            transWarn = new PasTransWarn();
                            transWarn.setId(sequenceService.nextValue("TRANS_WARN"));
                            transWarn.setTransactionNo(transactionNo);
                            transWarn.setTransactionMsg("定时任务执行间隔时间异常");
                            transWarn.setType("QUARZT");
                            transWarn.setCreateTime(new Date());
                            transWarn.setUpdateTime(new Date());
                            transWarn.setWarnTimes(1);
                            transWarn.setSource("QUARZT");
                            transWarn.setReason("两次定时任务超出预定时间,上一次定时任务时间为："+DateUtils.formatDate(pasTimeTaskRecord.getCreateTime(),"yyyyMMdd hhmmss")+",下一次定时任务时间为："+DateUtils.formatDate(trigger.getNextFireTime(),"yyyyMMdd hhmmss"));
                            transWarn.setStatus(PasConstant.WranStatus.PROCESSING.code);
                            transWarn.setWarnLevel(PasConstant.WarnLevel.NOMAL.code);
                            try {
                                pasTransWarnMapper.insertSelective(transWarn);
                            } catch(Exception e){
                                if(e instanceof SQLIntegrityConstraintViolationException){
                                    System.out.println(e.getMessage());
                                }
                            }
                        }
                    }
                }
            }catch (Exception e){
                commonLogger.printLog(e.getMessage());
                timeTaskHeart.setJobState("error");
                errorTasks.add(timeTaskHeart);
            }
        }
        StringBuilder builder = new StringBuilder();
        builder.append("正在正常执行的定时任务：").append(normalTasks.toString()).append(",不正常执行的定时任务为：").append(errorTasks);
        service.timtaskLog(builder.toString());
    }

    public long caculateTime(Trigger trigger){
        Long next2Time =  trigger.getFireTimeAfter(trigger.getNextFireTime()).getTime();
        Long nextTime = trigger.getNextFireTime().getTime();
        return  next2Time - nextTime ;
    }
}
