package com.epaylinks.efps.pas.pas.domain;

import com.epaylinks.efps.common.datadownload.annotation.FieldAnnotation;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

@ApiModel
public class OpLogReport {
    /**
     * 主键
     */
    private Long reportId;

    /**
     * 用户名
     */
    @ApiModelProperty( value = "用户名" , dataType = "String")
    @FieldAnnotation(fieldName = "用户名")
    private String userName;

    /**
     * 真实名称
     */
    @ApiModelProperty( value = "真实名称" , dataType = "String")
    @FieldAnnotation(fieldName = "真实名称")
    private String realName;

    /**
     * 操作模块
     */
    @ApiModelProperty( value = "操作模块" , dataType = "String")
    @FieldAnnotation(fieldName = "操作模块")
    private String opModule;

    /**
     * 操作次数
     */
    @ApiModelProperty( value = "操作次数" , dataType = "String")
    @FieldAnnotation(fieldName = "操作次数")
    private Integer opNum;

    private Integer operCount; //操作次数

    /**
     * 操作日期
     */
    @ApiModelProperty( value = "操作日期" , dataType = "String")
    @FieldAnnotation(fieldName = "操作日期")
    private String opTime;

    private String operDate; //操作日期

    /**
     * 审计状态，1正常，2异常
     */
    @ApiModelProperty(value="审计状态 1：正常，2：异常", dataType = "String")
    @FieldAnnotation(fieldName="审计状态", dictionaries="1:正常,2:异常")
    private String checkState;

    /**
     * 审计说明
     */
    @ApiModelProperty( value = "审计说明" , dataType = "String")
    @FieldAnnotation(fieldName = "审计说明")
    private String checkMsg;

    /**
     * 异常处理
     */
    @ApiModelProperty( value = "异常处理" , dataType = "String")
    @FieldAnnotation(fieldName = "异常处理")
    private String auditMsg;

    /**
     * 处理人
     */
    @ApiModelProperty( value = "处理人" , dataType = "String")
    @FieldAnnotation(fieldName = "处理人")
    private String auditName;

    /**
     * 处理时间
     */
    @ApiModelProperty(value = "处理时间")
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    @FieldAnnotation(fieldName="处理时间", dateFormat="yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    public Long getReportId() {
        return reportId;
    }

    public void setReportId(Long reportId) {
        this.reportId = reportId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getOpModule() {
        return opModule;
    }

    public void setOpModule(String opModule) {
        this.opModule = opModule;
    }

    public Integer getOpNum() {
        return opNum;
    }

    public void setOpNum(Integer opNum) {
        this.opNum = opNum;
    }

    public Integer getOperCount() {
        if(opNum!=null){
            return opNum;
        }
        return operCount;
    }

    public void setOperCount(Integer operCount) {
        this.operCount = operCount;
    }

    public String getOpTime() {
        return opTime;
    }

    public void setOpTime(String opTime) {
        this.opTime = opTime;
    }

    public String getOperDate() {
        if(StringUtils.isNotBlank(opTime)){
            return opTime;
        }
        return operDate;
    }

    public void setOperDate(String operDate) {
        this.operDate = operDate;
    }

    public String getCheckState() {
        return checkState;
    }

    public void setCheckState(String checkState) {
        this.checkState = checkState;
    }

    public String getCheckMsg() {
        return checkMsg;
    }

    public void setCheckMsg(String checkMsg) {
        this.checkMsg = checkMsg;
    }

    public String getAuditMsg() {
        return auditMsg;
    }

    public void setAuditMsg(String auditMsg) {
        this.auditMsg = auditMsg;
    }

    public String getAuditName() {
        return auditName;
    }

    public void setAuditName(String auditName) {
        this.auditName = auditName;
    }

    public Date getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(Date auditTime) {
        this.auditTime = auditTime;
    }
}