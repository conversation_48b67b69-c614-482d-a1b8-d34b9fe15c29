package com.epaylinks.efps.pas.pas.dao;

import com.epaylinks.efps.pas.pas.controller.dto.CustBusinessPriceDTO;
import com.epaylinks.efps.pas.pas.domain.ApplyBusiness;
import com.epaylinks.efps.pas.pas.domain.Business;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Mapper
@Transactional
public interface BusinessMapper {
    int deleteByPrimaryKey(Long id);

    int insert(Business record);

    int insertSelective(Business record);

    @Transactional
    Business selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(Business record);

    int updateByPrimaryKey(Business record);

    @Transactional(readOnly = true)
    List<Business> selectBySelective(Business record);

    @Transactional(readOnly = true)
    List<Business> selectByPage(Map map);

    List<Business> getBusinessByCodes(List<String> codes);

    String selectMaxCode();

    List<Business> selectByState(@Param("state") String state);

    /**
     * 根据业务编码查询业务
     *
     * @param businessCode 业务编码
     * @return
     */
    Business selectByCode(String businessCode);

    public List<Business> getBusinessesByCatetory(@Param("list") List<String> list);

    String selectBusinessCategoryByCode(@Param("businessCode") String businessCode);

    List<Business> selectByBusinessLabel(@Param("businessLabel") String businessLabel);

    Business selectByCodeOrName(Map map);


    public List<Business> getBusinessCatetoryByBusiness(@Param("businessCode") String businessCode);

    public List<Business> selectBusinessByGroupOrTemplate(Map map);

    public List<Business> selectTemplateByGroup(@Param("groupIdList") List<String> deptIdList);

    List<ApplyBusiness> selectApplyBusinessByDisplayCode(@Param("displayCode") String displayCode);

    List<ApplyBusiness> selectApplyBusinessByCode(@Param("code") String code);

    List<Business> selectBusinessesByScope(@Param("scope") String scope);

    List<Business> queryKeywords(String keywords);

    /**
     * 注意仅适用于查询终端业务列表
     * @param codes
     * @return
     */
    List<Business> selectByCodeList(@Param("codes") List<String> codes);

    List<CustBusinessPriceDTO> selectCustBusinessPrice(@Param("customerId") Long customerId);

    List<Business> selectAll();
}