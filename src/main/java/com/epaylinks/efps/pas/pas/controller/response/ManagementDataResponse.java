package com.epaylinks.efps.pas.pas.controller.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
public class ManagementDataResponse {
    private Long id;

    @ApiModelProperty(value = "创建时间",dataType = "String")
    private String createTime;

    @ApiModelProperty(value = "备注",dataType = "String")
    private String remark;

    private String operator;

    @ApiModelProperty(value = "创建人",dataType = "String")
    private String operatorName;

    @ApiModelProperty(value = "附件详情【1：有附件；2：无附件】",dataType = "String")
    private String detail;

    @ApiModelProperty(value = "序号 ",dataType = "String")
    private String serialNum;

    @ApiModelProperty(value = "发送状态【0：未发送；1：已发送；】",dataType = "String")
    private String sendStatus;
}
