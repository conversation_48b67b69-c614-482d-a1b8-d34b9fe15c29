package com.epaylinks.efps.pas.pas.domain;

/**
 * @Author: <EMAIL>
 * @Date: 2019/5/10 18:23
 * @Description 针对业务对外服务时研发经理增加了DISPLAY_CODE、SCOPE 字段
 * @Version 1.0
 */
public class ApplyBusiness extends Business {

    //适用范围[ALL：所有场景；IF：接口]
    private String scope;

    //进件时使用的编码
    private String displayCode;

    //银行卡类型
    private Short cardType;

    @Override
    public Short getCardType() {
        return cardType;
    }

    @Override
    public void setCardType(Short cardType) {
        this.cardType = cardType;
    }

    public String getScope() {
        return scope;
    }

    public void setScope(String scope) {
        this.scope = scope;
    }

    public String getDisplayCode() {
        return displayCode;
    }

    public void setDisplayCode(String displayCode) {
        this.displayCode = displayCode;
    }
}
