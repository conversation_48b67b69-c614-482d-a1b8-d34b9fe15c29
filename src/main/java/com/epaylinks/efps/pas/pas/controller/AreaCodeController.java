package com.epaylinks.efps.pas.pas.controller;

import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.pas.pas.controller.response.BankResponse;
import com.epaylinks.efps.pas.pas.controller.response.CityResponse;
import com.epaylinks.efps.pas.pas.controller.response.ProvinceResponse;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.pas.controller.dto.BankDTO;
import com.epaylinks.efps.pas.pas.controller.dto.CityDTO;
import com.epaylinks.efps.pas.pas.controller.dto.ProvinceDTO;
import com.epaylinks.efps.pas.pas.service.AreaCodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 省市区管理（数据库里的基础数据）
 */
@RestController
@RequestMapping("/AreaCode")
public class AreaCodeController {

	@Autowired
	private AreaCodeService areaCodeService;

    /**
     * 查询所有省份(表pas_pcop)
     */
    @GetMapping("/queryProvince")
    @Logable(businessTag = "queryProvince")
    public ProvinceResponse queryProvince() {
        ProvinceResponse response = new ProvinceResponse();
        try {
            List<ProvinceDTO> list = areaCodeService.queryProvince();
            response.setList(list);
        } catch (Exception e) {
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
            return response;
        }
        return response;
    }

    /**
     * 查询某省份的所有区县(表pas_ccop)
     */
    @GetMapping("/queryCityByProvinceCode")
    @Logable(businessTag = "queryCityByProvinceCode")
    public CityResponse queryCityByProvinceCode(@RequestParam String provinceCode) {
        CityResponse response = new CityResponse();
        try {
            List<CityDTO> list = areaCodeService.queryCityByProvinceCode(provinceCode);
            response.setList(list);
        } catch (Exception e) {
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
            return response;
        }
        return response;
    }

    /**
     * 查询所有银行(表pas_bkcd)
     */
    @GetMapping("/queryBank")
    @Logable(businessTag = "queryBank")
    public BankResponse queryBank() {
        BankResponse response = new BankResponse();
        try {
            List<BankDTO> list = areaCodeService.queryBank();
            response.setList(list);
        } catch (Exception e) {
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
            return response;
        }
        return response;
    }
    
    /**
     * 查询省份名称
     */
    @GetMapping("/queryProvinceNameByKey")
    @Logable(businessTag = "queryProvinceNameByKey")
    public String queryProvinceNameByKey(@RequestParam String provinceCode) {
    	
        return areaCodeService.queryProvinceNameByKey(provinceCode);
    }

    /**
     * 查询地市名称
     */
    @GetMapping("/queryCityNameByKey")
    @Logable(businessTag = "queryCityNameByKey")
    public String queryCityNameByKey(@RequestParam String cityCode) {
    	
        return areaCodeService.queryCityNameByKey(cityCode);
    }
    
    /**
     * 查询省份代码
     */
    @GetMapping("/queryProviceCodeByName")
    @Logable(businessTag = "queryProviceCodeByName")
    public String queryProviceCodeByName(@RequestParam String province) {
    	
        return areaCodeService.queryProviceCodeByName(province);
    }
    
    /**
     * 查询地市代码
     */
    @GetMapping("/queryCityCodeByNameAndProvinceCode")
    @Logable(businessTag = "queryCityCodeByNameAndProvinceCode")
    public String queryCityCodeByNameAndProvinceCode(
    		@RequestParam String city,
    		@RequestParam String provinceCode) {
    	
        return areaCodeService.queryCityCodeByNameAndProvinceCode(city, provinceCode);
    }
}
