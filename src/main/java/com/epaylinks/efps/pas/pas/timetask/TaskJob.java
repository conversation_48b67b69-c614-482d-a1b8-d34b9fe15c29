package com.epaylinks.efps.pas.pas.timetask;

import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.pas.common.PasConstants;
import com.epaylinks.efps.pas.pas.common.PasConstant;
import com.epaylinks.efps.pas.pas.domain.PasTimeTask;
import com.epaylinks.efps.pas.pas.service.PasTimeTaskService;
import org.apache.commons.lang3.StringUtils;
import org.quartz.SchedulerException;
import org.quartz.Trigger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.*;
import java.util.stream.Collectors;

@Component
@EnableScheduling
public class TaskJob {

    @Autowired
    private PasTimeTaskService pasTimeTaskService;

    @Value("${noExecuteIp}")
    private String noExecuteIp;

    //配置在启动参数中 --taskJobNoExecute=true
    @Value("${taskJobNoExecute: false}")
    private Boolean taskJobNoExecute;

    @Value("${partition}")
    private String partition;

    @Value("${taskjobOpen}")
    private boolean taskjobOpen;

    @Autowired
    private QuartzJobManager quartzJobManager;

    public List<String> getLocalIp() {
        List<String> list = new ArrayList<String>();
        try {
            Enumeration<NetworkInterface> netInterfaces = NetworkInterface.getNetworkInterfaces();
            InetAddress ip;
            while (netInterfaces.hasMoreElements()) {
                NetworkInterface ni = netInterfaces.nextElement();
                Enumeration<InetAddress> nii = ni.getInetAddresses();
                while (nii.hasMoreElements()) {
                    ip = nii.nextElement();
                    if (ip.getHostAddress().indexOf(":") == -1) {
                        list.add(ip.getHostAddress());
                    }
                }
            }
        } catch (SocketException e) {
            throw new AppException(PasConstant.FAIL, e);
        }
        return list;
    }

    @Scheduled(fixedRate = 3000)
    public void execute() throws SchedulerException {
        if (taskJobNoExecute || "true".equals(System.getenv("taskJobNoExecute"))) {
            return;
        }

        String[] noExecuteIps = noExecuteIp.split(",");
        for (int i = 0; i < noExecuteIps.length; i++) {
            if (getLocalIp().contains(noExecuteIps[i])) {
                return;
            }
        }
        //获取数据库中所有启用的顶级（父任务列表为空）定时任务
        List<PasTimeTask> tasks = pasTimeTaskService.selectAll().stream()
                .filter(task ->
                        PasConstants.timeTaskStatus.OPEN.code.equalsIgnoreCase(task.getJobStatus()) &&
                                StringUtils.isBlank(task.getParentJobs()))
                .collect(Collectors.toList());
        List<String> timeTaskJobNames = tasks.stream().map(PasTimeTask::getJobName).collect(Collectors.toList());

        //获取所有正在调用的quartz任务，删除不在pas_time_task中的任务调度
        List<Map<String, Object>> quartzJobs = quartzJobManager.getAllJob();
        for (Map<String, Object> quartzJob : quartzJobs) {
            String jobName = (String) quartzJob.get("jobName");
            if (!timeTaskJobNames.contains(jobName)) {
                quartzJobManager.deleteJobByName(jobName);
            }
        }

        for (PasTimeTask task : tasks) {
            try {
                Trigger trigger = quartzJobManager.queryOneJob(task.getJobName(), task.getJobName());
                if (taskjobOpen &&
                        task.getPartition() != null &&
                        Arrays.asList(task.getPartition().split(",")).contains(partition)) {
                    if (trigger == null) {
                        quartzJobManager.addJob(task);
                    } else {
                        quartzJobManager.updateJob(task);
                    }
                } else {
                    if (trigger != null) {
                        quartzJobManager.deleteJob(task);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
//                System.out.println(e.getMessage());
            }
        }
    }
}
