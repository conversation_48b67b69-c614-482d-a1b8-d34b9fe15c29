package com.epaylinks.efps.pas.pas.service;

import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.pas.pas.controller.dto.BankDTO;
import com.epaylinks.efps.pas.pas.controller.dto.CityDTO;
import com.epaylinks.efps.pas.pas.controller.dto.ProvinceDTO;
import com.epaylinks.efps.pas.pas.dao.BankMapper;
import com.epaylinks.efps.pas.pas.dao.CityMapper;
import com.epaylinks.efps.pas.pas.dao.ProvinceMapper;
import com.epaylinks.efps.pas.pas.domain.Bank;
import com.epaylinks.efps.pas.pas.domain.City;
import com.epaylinks.efps.pas.pas.domain.Province;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Service
public class AreaCodeService {

    @Autowired
    private ProvinceMapper provinceMapper;
    @Autowired
    private CityMapper cityMapper;
    @Autowired
    private BankMapper bankMapper;
    @Autowired
    private RedisTemplate<Object, Object> redisTemplate;
    
    private static final String PROVINCE_CODE_KEY_PREFIX = "PAS_PCOP_PROVINCE_CODE:"; 
    private static final String CITY_CODE_KEY_PREFIX = "PAS_CCOP_CITY_CODE:"; 


    @Logable(businessTag = "queryProvince")
    public List<ProvinceDTO> queryProvince() {
        List<ProvinceDTO> dtoList = new ArrayList<>();
        List<Province> list = provinceMapper.selectAll();
        for (Province x : list) {
            ProvinceDTO dto = new ProvinceDTO(x.getProvCd(), x.getProvNm());
            dtoList.add(dto);
        }
        return dtoList;
    }

    @Logable(businessTag = "queryCityByProvinceCode")
    public List<CityDTO> queryCityByProvinceCode(String provinceCode) {
        List<CityDTO> dtoList = new ArrayList<>();
        List<City> list = cityMapper.selectByProvinceCode(provinceCode);
        for (City x : list) {
            CityDTO dto = new CityDTO(x.getCityCd(), x.getCityNm());
            dtoList.add(dto);
        }
        return dtoList;
    }

    @Logable(businessTag = "queryBank")
    public List<BankDTO> queryBank() {
        List<BankDTO> dtoList = new ArrayList<>();
        List<Bank> list = bankMapper.selectAll();
        for (Bank x : list) {
            BankDTO dto = new BankDTO(x.getCorpOrg(), x.getBnkNm(),x.getLbnkCd());
            dtoList.add(dto);
        }
        return dtoList;
    }
    
    @Logable(businessTag = "queryProvinceNameByKey")
    public String queryProvinceNameByKey(String provinceCode) {
    	
    	String provinceName = (String) redisTemplate.opsForValue().get(PROVINCE_CODE_KEY_PREFIX + provinceCode);
		if(provinceName == null) {
			Province province = provinceMapper.selectByPrimaryKey(provinceCode);
			if(province != null) {
				provinceName = province.getProvNm();
				redisTemplate.opsForValue().set(PROVINCE_CODE_KEY_PREFIX + provinceCode, provinceName, 5, TimeUnit.MINUTES);
			}
		}	
		return provinceName;
    }
   
    @Logable(businessTag = "queryCityNameByKey")
    public String queryCityNameByKey(String cityCode) {
    	
    	String cityName = (String) redisTemplate.opsForValue().get(CITY_CODE_KEY_PREFIX + cityCode);
		if(cityName == null) {
			City cityObj = cityMapper.selectByPrimaryKey(cityCode);
			if(cityObj != null) {
				cityName = cityObj.getCityNm();
				redisTemplate.opsForValue().set(CITY_CODE_KEY_PREFIX + cityCode, cityName, 5, TimeUnit.MINUTES);
			}
		}	
		return cityName;
    }
    
    @Logable(businessTag = "queryProviceCodeByName")
    public String queryProviceCodeByName(String province) {
    	
		return provinceMapper.queryProviceCodeByName(province);
    }
    
    @Logable(businessTag = "queryCityCodeByNameAndProvinceCode")
    public String queryCityCodeByNameAndProvinceCode(String city, String proviceCode) {
    	
		return cityMapper.queryCityCodeByNameAndProvinceCode(city, proviceCode);
    }
}
