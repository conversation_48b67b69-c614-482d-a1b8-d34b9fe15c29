package com.epaylinks.efps.pas.pas.controller;

import java.util.*;

import com.epaylinks.efps.common.business.CodeNameResponse;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.pas.domain.ApplyBusiness;
import com.epaylinks.efps.pas.pas.service.OtherService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.pas.common.Result;
import com.epaylinks.efps.pas.pas.domain.Business;
import com.epaylinks.efps.pas.pas.service.BusinessService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import springfox.documentation.annotations.ApiIgnore;

/**
 * <AUTHOR> 5m
 * @Date : 2017/12/27 17:03
 * @Description : 业务管理
 */
@RestController
@RequestMapping("/Business")
@Api(value = "BusinessController", description = "业务管理")
public class BusinessController {

    @Autowired
    private SequenceService sequenceService;

    @Autowired
    private BusinessService businessService;

    @Autowired
    private OtherService otherService;

    @Value("${noSplitBusinessQuery:notOpen}")
    private String noSplitBusinessQuery;

    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @Logable(businessTag = "create")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "新增业务", notes = "新增业务", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "name", value = "业务名称", required = true, dataType = "String", length = 50, paramType = "query"),
            @ApiImplicitParam(name = "type", value = "业务类型:1-互联网支付.2-易票联账户服务", required = true, dataType = "String", length = 1, paramType = "query", valueRange = "{1,2}"),
            @ApiImplicitParam(name = "ratioMode", value = "费率类型:1-单笔固定费率.2-按交易金额比例", required = true, dataType = "String", length = 1, paramType = "query", valueRange = "{1,2}"),
            @ApiImplicitParam(name = "ratio", value = "费率,格式：无封顶：费率;有封顶：费率_封顶手续费(分)", required = true, dataType = "String", length = 100, paramType = "query"),
            @ApiImplicitParam(name = "remark", value = "备注", required = false, dataType = "String", length = 2000, paramType = "query"),
            @ApiImplicitParam(name = "state", value = "状态:1-正常.2-已删除", required = true, dataType = "String", length = 50, paramType = "query", valueRange = "{1,2}"),
            @ApiImplicitParam(name = "creator", value = "创建人", required = false, dataType = "String", length = 50, paramType = "query"),
            @ApiImplicitParam(name = "updator", value = "修改人", required = false, dataType = "String", length = 50, paramType = "query"),
            @ApiImplicitParam(name = "payMethod", value = "支付方式，以英文逗号,分隔", required = false, dataType = "String", length = 50, paramType = "query")})
    public String create(@ApiIgnore Business record, @RequestParam(value = "", required = false) String payMethod,
                         @RequestAttribute(value = "x-userid", required = false) Long userId) {
        record.setCreator(String.valueOf(userId));
        record.setId(sequenceService.nextValue("pas"));
        record.setCreateTime(new Date());
        return Constants.FAIL;//1.3.29版本不允许通过界面新增业务，因为基础业务的业务编码需要手工初始化，目前尚不支持符合业务
        //return businessService.insert(record, payMethod) == 1? Constants.SUCCESS: Constants.FAIL;

    }

    @RequestMapping(value = "/modify", method = RequestMethod.POST)
    @Logable(businessTag = "modify")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "修改业务", notes = "修改业务", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "id", required = true, dataType = "long", length = 50, paramType = "query"),
            @ApiImplicitParam(name = "name", value = "业务名称", required = false, dataType = "String", length = 50, paramType = "query"),
            @ApiImplicitParam(name = "type", value = "业务类型:1-互联网支付.2-易票联账户服务", required = false, dataType = "String", length = 1, paramType = "query", valueRange = "{1,2}"),
            @ApiImplicitParam(name = "ratioMode", value = "费率类型:1-单笔固定费率.2-按交易金额比例", required = false, dataType = "String", length = 1, paramType = "query", valueRange = "{1,2}"),
            @ApiImplicitParam(name = "ratio", value = "费率,格式：无封顶：费率;有封顶：费率_封顶手续费(分)", required = false, dataType = "String", length = 100, paramType = "query"),
            @ApiImplicitParam(name = "remark", value = "备注", required = false, dataType = "String", length = 2000, paramType = "query"),
            @ApiImplicitParam(name = "state", value = "状态:1-正常.2-已删除", required = false, dataType = "String", length = 50, paramType = "query", valueRange = "{1,2}"),
            @ApiImplicitParam(name = "creator", value = "创建人", required = false, dataType = "String", length = 50, paramType = "query"),
            @ApiImplicitParam(name = "updator", value = "修改人", required = false, dataType = "String", length = 50, paramType = "query"),
            @ApiImplicitParam(name = "payMethod", value = "支付方式，以英文逗号,分隔", required = false, dataType = "String", length = 50, paramType = "query")})
    public String modify(@ApiIgnore Business record, @RequestParam(value = "", required = false) String payMethod,
                         @RequestAttribute(value = "x-userid", required = false) Long userId) {
        record.setUpdator(String.valueOf(userId));
        record.setUpdateTime(new Date());
        return Constants.FAIL;//1.3.29版本不允许通过界面删除业务，因为基础业务的业务编码需要手工初始化，目前尚不支持符合业务
        //return businessService.updateByPrimaryKeySelective(record, payMethod) == 1? Constants.SUCCESS: Constants.FAIL;

    }


    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @Logable(businessTag = "delete")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "删除业务", notes = "删除业务", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "id", required = true, dataType = "long", length = 50, paramType = "query")})
    public String delete(@RequestParam Long id) {
        return Constants.FAIL;//1.3.29版本不允许通过界面删除业务，因为基础业务的业务编码需要手工初始化，目前尚不支持符合业务
        //return businessService.deleteByPrimaryKey(id) == 1? Constants.SUCCESS: Constants.FAIL;

    }


    @RequestMapping(value = "/select", method = RequestMethod.GET)
    @Logable(businessTag = "select")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "搜索业务", notes = "搜索业务", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "name", value = "业务名称", required = false, dataType = "String", length = 50, paramType = "query"),
            @ApiImplicitParam(name = "code", value = "业务编码", required = false, dataType = "String", length = 50, paramType = "query"),
            @ApiImplicitParam(name = "type", value = "业务类型:1-互联网支付.2-易票联账户服务", required = false, dataType = "String", length = 1, paramType = "query", valueRange = "{1,2}"),
            @ApiImplicitParam(name = "ratioMode", value = "费率类型:1-单笔固定费率.2-按交易金额比例", required = false, dataType = "String", length = 1, paramType = "query", valueRange = "{1,2}"),
            @ApiImplicitParam(name = "ratio", value = "费率", required = false, dataType = "String", length = 100, paramType = "query"),
            @ApiImplicitParam(name = "state", value = "状态:1-正常.2-已删除", required = false, dataType = "String", length = 50, paramType = "query", valueRange = "{1,2}"),
            @ApiImplicitParam(name = "templateCode", value = "模版编码", required = false, dataType = "String", length = 50, paramType = "query"),
            @ApiImplicitParam(name = "businessGroup", value = "业务分组", required = false, dataType = "String", length = 50, paramType = "query"),
            @ApiImplicitParam(name = "businessParentGroup", value = "上级业务分组", required = false, dataType = "String", length = 50, paramType = "query"),
            @ApiImplicitParam(name = "pageNum", value = "查询开始页", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "查询的页面大小", required = false, dataType = "int", paramType = "query")})
    public PageResult<Business> select(@ApiIgnore Business record,
                                       @RequestParam(value = "pageNum", required = false) Integer pageNum,
                                       @RequestParam(value = "pageSize", required = false) Integer pageSize) {


        List<Business> businessNoPageList = businessService.selectBySelective(record);

        if (pageNum == null) {
            pageNum = 1;
        }
        if (pageSize == null) {
            pageSize = businessNoPageList.size();
        }

        int endRowNo = pageNum * pageSize;
        int beginRowNo = (pageNum - 1) * pageSize + 1;

        Map map = new HashMap();
        map.put("name", record.getName());
        map.put("code", record.getCode());
        map.put("type", record.getType());
        map.put("ratioMode", record.getRatioMode());
        map.put("ratio", record.getRatio());
        map.put("state", record.getState());
        map.put("templateCode", record.getTemplateCode());
        map.put("businessGroup", record.getBusinessGroup());
        map.put("beginRowNo", beginRowNo);
        map.put("endRowNo", endRowNo);

        List<Business> businessList = businessService.selectByPage(map);

        PageResult<Business> page = new PageResult<>();
        page.setRows(businessList);
        page.setTotal(businessNoPageList.size());
        return page;
    }

    @RequestMapping(value = "/selectBySelective", method = RequestMethod.GET)
    @Logable(businessTag = "selectBySelective")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "搜索业务List", notes = "搜索业务List", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "name", value = "业务名称", required = false, dataType = "String", length = 50, paramType = "query"),
            @ApiImplicitParam(name = "code", value = "业务编码", required = false, dataType = "String", length = 50, paramType = "query"),
            @ApiImplicitParam(name = "templateCode", value = "模版编码", required = false, dataType = "String", length = 50, paramType = "query"),
            @ApiImplicitParam(name = "businessGroup", value = "业务分组", required = false, dataType = "String", length = 50, paramType = "query"),
            @ApiImplicitParam(name = "businessParentGroup", value = "上级业务分组", required = false, dataType = "String", length = 50, paramType = "query"),
            @ApiImplicitParam(name = "type", value = "业务类型:1-互联网支付.2-易票联账户服务", required = false, dataType = "String", length = 1, paramType = "query", valueRange = "{1,2}"),
            @ApiImplicitParam(name = "ratioMode", value = "费率类型:1-单笔固定费率.2-按交易金额比例", required = false, dataType = "String", length = 1, paramType = "query", valueRange = "{1,2}"),
            @ApiImplicitParam(name = "ratio", value = "费率", required = false, dataType = "String", length = 100, paramType = "query"),
            @ApiImplicitParam(name = "state", value = "状态:1-正常.2-已删除", required = false, dataType = "String", length = 50, paramType = "query", valueRange = "{1,2}")
    })
    public List<Business> selectBySelective(@ApiIgnore Business record) {

        return businessService.selectBySelective(record);
    }

    @RequestMapping(value = "/selectById", method = RequestMethod.GET)
    @Logable(businessTag = "selectById")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "根据ID搜索单条业务", notes = "根据ID搜索单条业务", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "id", required = true, dataType = "long", length = 50, paramType = "query")})
    public Business selectById(@RequestParam Long id) {
        return businessService.selectByPrimaryKey(id);
    }

    @RequestMapping(value = "/optimal", method = RequestMethod.GET)
    @Logable(businessTag = "optimal")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "根据支付方式编码返回最优费率", notes = "根据支付方式编码返回最优费率", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "payMethodCode", value = "支付方式编码", required = true, dataType = "String", length = 50, paramType = "query"),
            @ApiImplicitParam(name = "amount", value = "交易金额(单位分)", required = true, dataType = "String", length = 50, paramType = "query")})
    public Business Optimal(@RequestParam String payMethodCode, @RequestParam String amount) {
        Business business = businessService.optimal(payMethodCode, amount);
        if (business == null) {
            throw new AppException(Constants.ReturnCode.FAIL.code);
        }
        return business;
    }


    /**
     * 同步所有的业务信息（往kafka发送数据）
     *
     * @return 成功 "1" ， 失败  "0"
     */
    @RequestMapping(value = "/sync", method = RequestMethod.GET)
    public Result<String> syncBusiness() {
        businessService.syncAllNormalBusiness();
        Result<String> result = new Result<>();
        result.setData(Constants.SUCCESS);
        return result;
    }

    /**
     * 结算系统处理老数据
     * 处理策略：查询PAS中所有客户，如果某个客户的最新客户信息ID不为空，找到对应的客户结算信息，如果结算周期为1天，则为该客户当前最新的业务实例生成
     * D+1的结算周期规则实例，并触发客户数据同步至CUM
     * 1.3.29升级版本一次性调用接口，后续不需使用，当前生产环境中所有业务实例的结算周期都是D+1，所以仅需处理结算周期为1天的客户即可
     * @return
     */
//    @RequestMapping("/handleSett")
//    @Logable(businessTag = "handleSett")
//    public String handleSett() {
//    	List<Customer> customers = CustomerService.getAllCustomer();
//    	for (Customer customer : customers) {
//			if (StringUtils.isNotBlank(customer.getCustomerCode()) 
//					&& customer.getNewestCustomerInfoId() != null) {
//				//查询客户的结算周期，现有生产的结算周期实例应该都是D+1
//				CustomerSettleInfo customerSettleInfo = customerSettleInfoService
//						.getCustomerSettleInfoByCustomerInfoId(customer.getNewestCustomerInfoId());
//				if (customerSettleInfo.getCircle().equals(1)) {
//					//只处理结算周期为D+1的
//					//如果客户编码不为空，那么查询该客户对应的业务实例
//					List<CustomerBusinessInfo> customerBusinessInfos = customerBusinessInfoService
//							.queryListByCustomerInfoId(customer.getNewestCustomerInfoId());
//					//新增结算周期实例
//					for (CustomerBusinessInfo customerBusinessInfo : customerBusinessInfos) {
//						if(pasSettCycleRuleInstService.queryByBusinessInstId(customerBusinessInfo.getBusinessExamId()).size()==0)
//						{//不重复增加
//							String cycleRuleCode = "D+1";
//							Long SettCycleRuleInstId = sequenceService.nextValue(ADDSETTCYCLERULEINSTCATEGORY);
//							if(customerBusinessInfo.getBusinessCode().equalsIgnoreCase(BusinessCode.API_REFUND.code)||
//									customerBusinessInfo.getBusinessCode().equalsIgnoreCase(BusinessCode.WITHDRAW.code))
//							{
//								 cycleRuleCode = "RealTime";
//							}
//							Date now = new Date();
//							PasSettCycleRuleInst pasSettCycleRuleInst = new PasSettCycleRuleInst(
//									SettCycleRuleInstId, 
//									customerBusinessInfo.getBusinessExamId(), 
//									cycleRuleCode, 
//									now, 
//									new Date(2099, 1, 1), 
//									now, 
//									null, 
//									now, 
//									null);
//							pasSettCycleRuleInstService.addInst(pasSettCycleRuleInst);
//						}
//					}
//					CustomerService.syncCustomerInfo(null, customer.getNewestCustomerInfoId(), "operateType");
//				}
//			}
//		}
//		return null;
//    }

    /**
     * 根据业务类别查询业务
     */
    @GetMapping("category")
    @Logable(businessTag = "getBuseinssByBusinessCategory")
    @Exceptionable
    @Validatable
    public CommonOuterResponse getBuseinssByBusinessCategory(@RequestParam(value = "businessCategory") String businessCategory,
                                                             @RequestParam(value = "staffUserId",required = false) Long staffUserId) {
        List<Business> data = businessService.getBusinessByCategory(businessCategory);
        List<Business>  processData = data;
        // 过检角色过滤部分业务：交易分账、无卡快捷储蓄卡分账、无卡快捷信用卡分账、被分账、账户分账
//        if("yes".equals(noSplitBusinessQuery) && data!=null && !data.isEmpty()){
        if (data != null && !data.isEmpty() && !StringUtils.isEmpty(staffUserId) && otherService.judgeCheckRole(staffUserId) > 0) {
            processData = new ArrayList<>();
            for(Business item : data){
                if(item!=null && item.getName()!=null && !item.getName().contains("分账")){
                    processData.add(item);
                }
            }
        }
        CommonOuterResponse response = new CommonOuterResponse();
        response.setData(processData);
        return response;
    }


    /**
     * 根据业务标签查询业务
     */
    @GetMapping("getByLabel")
    @Logable(businessTag = "getBuseinssCodeByLabel")
    @Exceptionable
    @Validatable
    public CommonOuterResponse getBuseinssCodeByLabel(@RequestParam("businessLabel") String businessLabel) {
        List<String> data = businessService.getBusinessCodeByLabel(businessLabel);
        CommonOuterResponse response = new CommonOuterResponse();
        response.setData(data);
        return response;
    }


    @RequestMapping(value = "/getBusinessByCodeOrName", method = RequestMethod.GET)
    @Logable(businessTag = "getBusinessByCodeOrName")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "查询业务，按编号名称", notes = "查询业务，按编号名称", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "businessCode", value = "编码", required = false, dataType = "String", length = 50, paramType = "query"),
            @ApiImplicitParam(name = "businessName", value = "名称", required = false, dataType = "String", length = 500, paramType = "query")})
    public Business getBusinessByCodeOrName(
            @RequestParam(value = "businessCode", required = false) String businessCode,
            @RequestParam(value = "businessName", required = false) String businessName
    ) {
        Map map = new HashMap();
        map.put("businessCode", businessCode);
        map.put("businessName", businessName);
        return businessService.selectByCodeOrName(map);
    }

    //将原来的业务类别补上值暂未用1904
    @RequestMapping(value = "/modifyBusinessCategory", method = RequestMethod.GET)
    @Logable(businessTag = "modifyBusinessCategory")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "修改业务类别", notes = "修改业务类别临时", httpMethod = "GET")
    public int modifyBusinessCategory(

    ) {
        return businessService.modifyBusinessCategory();
    }


    /**
     * 根据业务名称查询业务对应的业务类别，有多个的
     */
    @GetMapping("/getBusinessCatetoryByBusiness")
    @Logable(businessTag = "getBusinessCatetoryByBusiness")
    @Exceptionable
    @ApiOperation(value = "根据业务查所属业务类别", notes = "根据业务查所属业务类别", httpMethod = "GET")
    @Validatable
    public CommonOuterResponse getBusinessCatetoryByBusiness(@RequestParam("businessCode") String businessCode) {
        List<Business> data = businessService.getBusinessCatetoryByBusiness(businessCode);
        CommonOuterResponse response = new CommonOuterResponse();
        response.setData(data);
        return response;
    }

    @RequestMapping(value = "/getApplyBusinessByDisplayCode", method = RequestMethod.GET)
    @Logable(businessTag = "getApplyBusinessByDisplayCode")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "根据进件编码查询业务", notes = "查询业务，按件编码", httpMethod = "GET")
    @ApiImplicitParams({@ApiImplicitParam(name = "displayCode", value = "件编码", required = true, dataType = "String", length = 50, paramType = "query")})
    public List<ApplyBusiness> selectApplyBusinessByDisplayCode(@RequestParam(value = "displayCode", required = false) String displayCode) {
        return businessService.selectApplyBusinessByDisplayCode(displayCode);
    }

    @RequestMapping(value = "/getApplyBusinessByCode", method = RequestMethod.GET)
    @Logable(businessTag = "getApplyBusinessByCode")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "根据编码查询业务", notes = "查询业务，编码", httpMethod = "GET")
    @ApiImplicitParams({@ApiImplicitParam(name = "code", value = "编码", required = true, dataType = "String", length = 50, paramType = "query")})
    public List<ApplyBusiness> selectApplyBusinessByCode(@RequestParam(value = "code", required = false) String code) {
        return businessService.selectApplyBusinessByCode(code);
    }

    /**
     * 根据业务类别查询业务
     */
    @GetMapping("/getBuseinssByScope")
    @Logable(businessTag = "getBuseinssByScope")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "根据适用范围查询业务", notes = "根据适用范围查询业务", httpMethod = "GET")
    @ApiImplicitParams({@ApiImplicitParam(name = "scope", value = "适用范围（ALL：所有场景；IF：接口;TS:交易分账）", required = true, dataType = "String", length = 50, paramType = "query")})
    public CommonOuterResponse<List<Business>> getBuseinssByScope(@RequestParam("scope") String scope) {
        List<Business> data = businessService.getBuseinssByScope(scope);
        return CommonOuterResponse.success(data);
    }

    @RequestMapping(value = "/fuzzyQuery", method = RequestMethod.GET)
    @Logable(businessTag = "fuzzyQuery")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "模糊查询业务列表，条件keyword[名称]", notes = "模糊查询业务列表，条件keyword[名称]", httpMethod = "GET")
    @ApiImplicitParams({@ApiImplicitParam(name = "keyword", value = "条件keyword的范围[名称]", dataType = "String", length = 50, paramType = "query")})
    public PageResult<CodeNameResponse> fuzzyQuery(String keyword) {
        PageResult<CodeNameResponse> pageResult = null;
        try {
            return businessService.fuzzyQuery(keyword);
        } catch (Exception e) {
            pageResult = new PageResult<>();
            if (e instanceof AppException) {
                pageResult.setCode(((AppException) e).getErrorCode());
                pageResult.setResult(((AppException) e).getErrorMsg());
            } else {
                pageResult.setCode(PasCode.SYSTEM_EXCEPTION.code);
                pageResult.setResult(PasCode.SYSTEM_EXCEPTION.message);
            }
            return pageResult;
        }
    }

    @GetMapping("/queryAll")
    public List<Business> queryAll() {
        return businessService.selectAll();
    }
}
