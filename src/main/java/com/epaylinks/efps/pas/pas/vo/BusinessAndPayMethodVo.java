package com.epaylinks.efps.pas.pas.vo;
/**
 * 业务及其开通的支付方式值对象
 * <AUTHOR>
 *
 */

import java.util.List;

import com.epaylinks.efps.pas.pas.domain.Business;

public class BusinessAndPayMethodVo {
	/**
	 * 开通的业务对象
	 */
	private Business business;
	/**
	 * 对应的支付方式编码
	 */
	private List<String> payMethods;
	public Business getBusiness() {
		return business;
	}
	public void setBusiness(Business business) {
		this.business = business;
	}
	public List<String> getPayMethods() {
		return payMethods;
	}
	public void setPayMethods(List<String> payMethods) {
		this.payMethods = payMethods;
	}
}
