package com.epaylinks.efps.pas.pas.vo;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by adm on 2018/9/27.
 */
public class UnionQuotaRequest {
    private String issrIdAccount;
    private Long amount;
    private String trxCategory;
    private Long state;
    private Date createTime;
    private Long userId;
    private String channelTradeNo;
    private String channelReturnCode;
    private String channelReturnMessage;
    private Date channelReturnDate;

    public String getIssrIdAccount() {
        return issrIdAccount;
    }

    public void setIssrIdAccount(String issrIdAccount) {
        this.issrIdAccount = issrIdAccount;
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public String getTrxCategory() {
        return trxCategory;
    }

    public void setTrxCategory(String trxCategory) {
        this.trxCategory = trxCategory;
    }

    public Long getState() {
        return state;
    }

    public void setState(Long state) {
        this.state = state;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getChannelTradeNo() {
        return channelTradeNo;
    }

    public void setChannelTradeNo(String channelTradeNo) {
        this.channelTradeNo = channelTradeNo;
    }

    public String getChannelReturnCode() {
        return channelReturnCode;
    }

    public void setChannelReturnCode(String channelReturnCode) {
        this.channelReturnCode = channelReturnCode;
    }

    public String getChannelReturnMessage() {
        return channelReturnMessage;
    }

    public void setChannelReturnMessage(String channelReturnMessage) {
        this.channelReturnMessage = channelReturnMessage;
    }

    public Date getChannelReturnDate() {
        return channelReturnDate;
    }

    public void setChannelReturnDate(Date channelReturnDate) {
        this.channelReturnDate = channelReturnDate;
    }
}
