package com.epaylinks.efps.pas.pas.common;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("pasLogService")
public class LogService {

    @Autowired
    private CommonService commonService;

    public void printLog(Exception e) {
        commonService.logException(e);
    }

    public void printLog(String e) {
        commonService.pasLog(e);
    }
}
