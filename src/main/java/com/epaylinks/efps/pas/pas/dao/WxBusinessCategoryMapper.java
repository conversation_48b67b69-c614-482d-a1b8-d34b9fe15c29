package com.epaylinks.efps.pas.pas.dao;

import com.epaylinks.efps.pas.pas.domain.WxBusinessCategory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface WxBusinessCategoryMapper {

    int deleteByPrimaryKey(Long id);

    int insert(WxBusinessCategory record);

    int insertSelective(WxBusinessCategory record);

    WxBusinessCategory selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(WxBusinessCategory record);

    int updateByPrimaryKey(WxBusinessCategory record);

    void batchInsert(@Param("records") List<WxBusinessCategory> records);

    List<String> selectLevel1s();

    List<String> selectLevel2s(@Param("level1") String level1);

    List<String> selectLevel3s(@Param("level1") String level1, @Param("level2") String level2);

    List<WxBusinessCategory> selectLevel4s(@Param("level1") String level1, @Param("level2") String level2, @Param("level3") String level3);
    
    List<WxBusinessCategory> selectByUnionMcc(@Param("unionMcc") String unionMcc, @Param("mccType") Long mccType);
}