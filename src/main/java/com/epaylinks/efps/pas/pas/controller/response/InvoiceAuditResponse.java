package com.epaylinks.efps.pas.pas.controller.response;

import com.epaylinks.efps.common.datadownload.annotation.FieldAnnotation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
public class InvoiceAuditResponse {

    private Long invoiceId;

    @FieldAnnotation(fieldName="商户编号")
    @ApiModelProperty(value = "商户编号",dataType = "String")
    private String customerNo;

    @FieldAnnotation(fieldName="商户名称")
    @ApiModelProperty(value = "商户名称",dataType = "String")
    private String customerName;

    @FieldAnnotation(fieldName="发票类型",dictionaries = "1:增值税专用发票,2:普通发票")
    @ApiModelProperty(value = "发票类型[1：增值税专用发票；2：普通发票]",dataType = "String")
    private String invoiceType;

    @FieldAnnotation(fieldName="发票介质",dictionaries = "1:电子发票,2:纸质发票")
    @ApiModelProperty(value = "发票介质[1：电子发票；2：纸质发票]",dataType = "String")
    private String invoiceMedium;

    @FieldAnnotation(fieldName="发票编号")
    @ApiModelProperty(value = "发票编号",dataType = "String")
    private String invoiceNo;

    @FieldAnnotation(fieldName="开票日期")
    @ApiModelProperty(value = "开票日期",dataType = "Date")
    private String invoiceTime;

    @FieldAnnotation(fieldName="税率")
    @ApiModelProperty(value = "税率",dataType = "Integer")
    private Integer taxRatio;

    @ApiModelProperty(value = "发票金额",dataType = "Double")
    private Long invoiceAmount;

    @FieldAnnotation(fieldName="发票金额（元）")
    private Double exportInvoiceAmount;

    @FieldAnnotation(fieldName="状态",dictionaries = "0:待审核,1:审核通过,2:审核不通过")
    @ApiModelProperty(value = "状态",dataType = "String")
    private String status;

    @ApiModelProperty(value = "附件ID",dataType = "String")
    private String uniqueid;

    @ApiModelProperty(value = "附件下载链接",dataType = "String")
    private String url;

    @ApiModelProperty(value = "附件名称",dataType = "String")
    private String attachmentName;

    @ApiModelProperty(value = "备注",dataType = "String")
    private String remarks;

    @ApiModelProperty(value = "创建人ID",dataType = "Long")
    private Long creator;

    @FieldAnnotation(fieldName="创建人")
    @ApiModelProperty(value = "创建人",dataType = "String")
    private String creatorName;

    @FieldAnnotation(fieldName="创建时间")
    @ApiModelProperty(value = "创建时间",dataType = "String")
    private String createTime;

    @ApiModelProperty(value = "审核人ID",dataType = "Long")
    private Long reviewerId;

    @ApiModelProperty(value = "审核人名称",dataType = "String")
    private String reviewerName;

    @ApiModelProperty(value = "审核意见",dataType = "String")
    private String auditComment;

    @FieldAnnotation(fieldName="审核时间")
    @ApiModelProperty(value = "审核时间",dataType = "String")
    private String auditTime;
}
