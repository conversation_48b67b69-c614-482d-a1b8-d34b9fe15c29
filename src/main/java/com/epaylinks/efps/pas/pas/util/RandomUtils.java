package com.epaylinks.efps.pas.pas.util;

import java.util.Random;

public class RandomUtils {

    private static final Random random = new Random();

    /**
     * 生成数字随机码
     *
     * @param length 随机码长度
     */
    public static String makeRandom(int length) {
        StringBuffer flag = new StringBuffer();
        for (int j = 0; j < length; j++) {
            flag.append(random.nextInt(9));
        }
        return flag.toString();
    }

    public static void main(String[] args) {
        System.out.println(Integer.parseInt("0000324"));
        System.out.println("IN2022062501NMGBIN.Z".endsWith(".Z"));
    }
}
