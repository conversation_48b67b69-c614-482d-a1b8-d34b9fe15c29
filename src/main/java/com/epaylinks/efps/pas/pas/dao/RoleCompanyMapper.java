package com.epaylinks.efps.pas.pas.dao;

import com.epaylinks.efps.pas.pas.domain.RoleCompany;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
@Mapper
public interface RoleCompanyMapper {
    int deleteByPrimaryKey(Long roleId);

    int insert(RoleCompany record);

    int insertSelective(RoleCompany record);

    RoleCompany selectByPrimaryKey(Long roleId);

    int updateByPrimaryKeySelective(RoleCompany record);

    int updateByPrimaryKey(RoleCompany record);

    List<Long> selectCompanyIdByRoleId(Long roleId);

    List<Long> selectCompanyIdByRoleIds(@Param("list") List<Long> list);

    List<RoleCompany> selectByRoleId(Long roleId);

    int deleteByRoleId(long uid);
}