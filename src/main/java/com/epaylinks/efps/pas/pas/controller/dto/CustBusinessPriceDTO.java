package com.epaylinks.efps.pas.pas.controller.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;

@ApiModel
public class CustBusinessPriceDTO {
    @ApiModelProperty(value = "阶梯计费ID", dataType = "Long")
    private Long custBusinessPriceId;

    @ApiModelProperty(value = "客户ID", dataType = "Long")
    private Long customerId;

    @ApiModelProperty(value = "计费ID", dataType = "Long")
    private Long custBusinessId;

    @ApiModelProperty(value = "阶梯起始交易金额", dataType = "Long")
    private Long feeFrom;

    @ApiModelProperty(value = "阶梯终止交易金额", dataType = "Long")
    private Long feeTo;

    @ApiModelProperty(value = "费率模式[1:单笔，2:按比率,3:单笔+比例]", dataType = "Short")
    private Short feeMode;

    @ApiModelProperty(value = "费率，万分率", dataType = "BigDecimal")
    private BigDecimal feeRate;

    @ApiModelProperty(value = "单笔金额", dataType = "Long")
    private Long feePer;

    @ApiModelProperty(value = "排序号", dataType = "Long")
    private Long orderno;

    @ApiModelProperty(value = "阶梯起始模式 [1:>;2:>=]", dataType = "Long")
    private Short feeFromMode;

    @ApiModelProperty(value = "阶梯结束模式 [3:<;4:<=]", dataType = "Long")
    private Short feeToMode;

    @ApiModelProperty(value = "业务编码",dataType = "String")
    private String businessCode;

    public Long getCustBusinessPriceId() {
        return custBusinessPriceId;
    }

    public void setCustBusinessPriceId(Long custBusinessPriceId) {
        this.custBusinessPriceId = custBusinessPriceId;
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public Long getCustBusinessId() {
        return custBusinessId;
    }

    public void setCustBusinessId(Long custBusinessId) {
        this.custBusinessId = custBusinessId;
    }

    public Long getFeeFrom() {
        return feeFrom;
    }

    public void setFeeFrom(Long feeFrom) {
        this.feeFrom = feeFrom;
    }

    public Long getFeeTo() {
        return feeTo;
    }

    public void setFeeTo(Long feeTo) {
        this.feeTo = feeTo;
    }

    public Short getFeeMode() {
        return feeMode;
    }

    public void setFeeMode(Short feeMode) {
        this.feeMode = feeMode;
    }

    public BigDecimal getFeeRate() {
        return feeRate;
    }

    public void setFeeRate(BigDecimal feeRate) {
        this.feeRate = feeRate;
    }

    public Long getFeePer() {
        return feePer;
    }

    public void setFeePer(Long feePer) {
        this.feePer = feePer;
    }

    public Long getOrderno() {
        return orderno;
    }

    public void setOrderno(Long orderno) {
        this.orderno = orderno;
    }

    public Short getFeeFromMode() {
        return feeFromMode;
    }

    public void setFeeFromMode(Short feeFromMode) {
        this.feeFromMode = feeFromMode;
    }

    public Short getFeeToMode() {
        return feeToMode;
    }

    public void setFeeToMode(Short feeToMode) {
        this.feeToMode = feeToMode;
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }
}
