package com.epaylinks.efps.pas.pas.service;

import com.epaylinks.efps.pas.pas.domain.BizPayMethod;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR> 5m
 * @Date : 2017/12/27 14:36
 * @Description :
 */
public interface BizPayMethodService {

    public int deleteByPrimaryKey(Long id);

    public int deleteBySelective(BizPayMethod record);

    public int insert(BizPayMethod record);

    public int insertSelective(BizPayMethod record);

    public BizPayMethod selectByPrimaryKey(Long id);

    public int updateByPrimaryKeySelective(BizPayMethod record);

    public int updateByPrimaryKey(BizPayMethod record);

    public List<BizPayMethod> selectBySelective(BizPayMethod record);

    public int addBizPayMethods(List<BizPayMethod> list);
    
	public void syncAllBizPayMethod();
}
