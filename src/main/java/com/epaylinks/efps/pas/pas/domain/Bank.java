package com.epaylinks.efps.pas.pas.domain;

import com.epaylinks.efps.common.datadownload.annotation.FieldAnnotation;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

public class Bank {
    /**
     * 银行编号
     */
    @ApiModelProperty(value="银行ID", dataType = "String")
    @FieldAnnotation(fieldName="银行ID")
    private String lbnkCd;

    /**
     * 银行名称
     */
    @ApiModelProperty(value="银行名称", dataType = "String")
    @FieldAnnotation(fieldName="银行名称")
    private String bnkNm;

    /**
     * 机构号
     */
    private String corpOrg;

    /**
     * 标志位
     */
    private String flg;

    /**
     * 时间戳
     */
    private String tmSmp;

    /**
     * 交易发生节点名
     */
    private String nodId;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    @FieldAnnotation(fieldName="创建时间", dateFormat="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    @FieldAnnotation(fieldName="更新时间", dateFormat="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty(value="操作人", dataType = "String")
    @FieldAnnotation(fieldName="操作人")
    private String operatorId;

    @ApiModelProperty(value="状态 0：停用 1：启用", dataType = "String")
    @FieldAnnotation(fieldName="状态", dictionaries="0:停用,1:启用")
    private String flag;

    public String getLbnkCd() {
        return lbnkCd;
    }

    public void setLbnkCd(String lbnkCd) {
        this.lbnkCd = lbnkCd;
    }

    public String getBnkNm() {
        return bnkNm;
    }

    public void setBnkNm(String bnkNm) {
        this.bnkNm = bnkNm;
    }

    public String getCorpOrg() {
        return corpOrg;
    }

    public void setCorpOrg(String corpOrg) {
        this.corpOrg = corpOrg;
    }

    public String getFlg() {
        return flg;
    }

    public void setFlg(String flg) {
        this.flg = flg;
    }

    public String getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(String tmSmp) {
        this.tmSmp = tmSmp;
    }

    public String getNodId() {
        return nodId;
    }

    public void setNodId(String nodId) {
        this.nodId = nodId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }
}