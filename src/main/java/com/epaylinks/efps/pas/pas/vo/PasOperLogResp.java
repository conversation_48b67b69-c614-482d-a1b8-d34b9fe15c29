package com.epaylinks.efps.pas.pas.vo;

import com.epaylinks.efps.common.datadownload.annotation.FieldAnnotation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel
public class PasOperLogResp {

    @ApiModelProperty(value = "登录账号", dataType = "String")
    @FieldAnnotation(fieldName = "登录账号")
    private String username;

    @ApiModelProperty(value = "姓名/商户名称", dataType = "String")
    @FieldAnnotation(fieldName = "姓名/商户名称")
    private String realname;

    @ApiModelProperty(value = "操作时间", dataType = "String")
    @FieldAnnotation(fieldName = "操作时间")
    private String operTime;

    @ApiModelProperty(value = "操作IP", dataType = "String")
    @FieldAnnotation(fieldName = "操作IP")
    private String operIp;

    @ApiModelProperty(value = "操作人", dataType = "String")
    @FieldAnnotation(fieldName = "操作人")
    private String operator;

    @ApiModelProperty(value = "操作内容", dataType = "String")
    @FieldAnnotation(fieldName = "操作内容")
    private String remark;
    
    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getRealname() {
        return realname;
    }

    public void setRealname(String realname) {
        this.realname = realname;
    }

    public String getOperTime() {
        return operTime;
    }

    public void setOperTime(String operTime) {
        this.operTime = operTime;
    }

    public String getOperIp() {
        return operIp;
    }

    public void setOperIp(String operIp) {
        this.operIp = operIp;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

}
