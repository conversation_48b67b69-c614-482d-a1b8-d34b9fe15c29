package com.epaylinks.efps.pas.pas.controller;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.pas.domain.AliBusinessCategory;
import com.epaylinks.efps.pas.pas.service.AliBusinessCategoryService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * 支付宝经营类目和MCC
 */
@RestController
@RequestMapping("AliBusinessCategory")
public class AliBusinessCategoryController {

    @Autowired
    private AliBusinessCategoryService aliBusinessCategoryService;

    /**
     * 原始数据导入from上游文档
     */
    @GetMapping("batchInsert")
    public void batchInsert() {
        String data = "DataAli.data";
        String[] arr = StringUtils.split(data, "\n");
        List<AliBusinessCategory> list = new ArrayList<>();
        for (String s : arr) {
            String[] a = StringUtils.split(s.trim(), " ");
            AliBusinessCategory r = new AliBusinessCategory();
            r.setMcc(a[0]);
            String[] codes = a[1].split("_");
            r.setLevel1Code(codes[0]);
            r.setLevel2Code(codes[1]);
            r.setLevel3Code(codes[2]);
            r.setLevel1Name(a[2]);
            r.setLevel2Name(a[3]);
            r.setLevel3Name(a[4]);
            list.add(r);
        }
        aliBusinessCategoryService.batchInsert(list);
    }

    @GetMapping("queryLevel1s")
    @Logable(businessTag = "queryLevel1s")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "查询第一级类目", httpMethod = "GET")
    public CommonOuterResponse queryLevel1s() {
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            response.setData(aliBusinessCategoryService.queryLevel1s());
        } catch (Exception e) {
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
            return response;
        }
        return response;
    }

    @GetMapping("queryLevel2s")
    @Logable(businessTag = "queryLevel2s")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "根据第一级查询第二级类目", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "level1", value = "第一级类目", required = true, dataType = "String", paramType = "query")
    })
    public CommonOuterResponse queryLevel2s(@RequestParam String level1) {
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            response.setData(aliBusinessCategoryService.queryLevel2s(level1));
        } catch (Exception e) {
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
            return response;
        }
        return response;
    }

    @GetMapping("queryLevel3s")
    @Logable(businessTag = "queryLevel3s", outputResult = false)
    @Exceptionable
    @Validatable
    @ApiOperation(value = "根据第一、二级查询第三级类目", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "level1", value = "第一级类目", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "level2", value = "第二级类目", required = true, dataType = "String", paramType = "query")
    })
    public CommonOuterResponse queryLevel3s(@RequestParam String level1, @RequestParam String level2) {
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            response.setData(aliBusinessCategoryService.queryLevel3s(level1, level2));
        } catch (Exception e) {
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
            return response;
        }
        return response;
    }

    @GetMapping("getBusinessCategoryByMcc")
    @Logable(businessTag = "getBusinessCategoryByMcc")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "根据MCC获取经营类目信息", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "mcc", value = "MCC", required = true, dataType = "String", paramType = "query")
    })
    public AliBusinessCategory getBusinessCategoryByMcc(@RequestParam String mcc) {
        return aliBusinessCategoryService.getBusinessCategoryByMcc(mcc);
    }


    /**
     * 通过关键字模糊查询支付宝经营类目
     * @param keyword
     * @return
     */
    @GetMapping("getAliBusinessCategoryByWord")
    @Logable(businessTag = "getAliBusinessCategoryByWord")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "通过关键字模糊查询支付宝经营类目", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "keyword", required = true, dataType = "String", paramType = "query")
    })
    public List<AliBusinessCategory> getAliBusinessCategoryByWord(@RequestParam String keyword) {
        return aliBusinessCategoryService.getAliBusinessCategoryByWord(keyword);
    }
    
    @GetMapping("queryBusinessCategoryByUnionMcc")
    @Logable(businessTag = "queryBusinessCategoryByUnionMcc")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "根据MCC获取经营类目信息", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "unionMcc", value = "银联MCC", required = true, dataType = "String", paramType = "query")
    })
    public CommonOuterResponse<AliBusinessCategory> queryBusinessCategoryByUnionMcc(@RequestParam String unionMcc) {
        try {
            List<AliBusinessCategory> list = aliBusinessCategoryService.queryBusinessCategoryByUnionMcc(unionMcc);
            return CommonOuterResponse.success(list != null && !list.isEmpty() ? list.get(0) : null);
        } catch (AppException e) {
            return CommonOuterResponse.fail(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
        }
        
    }
    
    
}
