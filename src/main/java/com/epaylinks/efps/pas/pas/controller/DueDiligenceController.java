package com.epaylinks.efps.pas.pas.controller;


import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.pas.service.DueDiligenceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/due")
@Api(value = "DueDiligenceController", description = "尽职调查表管理")
public class DueDiligenceController {

    @Autowired
    private CommonLogger logger;

    @Autowired
    private DueDiligenceService dueDiligenceService;


    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @Exceptionable
    @ApiOperation(value = "创建尽职调查表", notes = "创建尽职调查表", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "merchantName", value = "商户名称", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "parentName", value = "父商户名称", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "busiNo", value = "营业执照",required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "regDate", value = "成立日期", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "remark", value = "业务场景",required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "type", value = "来源，1运营门户，2接口", required = false, dataType = "String", paramType = "query"),
    })
    @Logable(businessTag = "DueDiligenceController.create")
    public CommonOuterResponse create(
            @RequestParam(value = "merchantName", required = false) String merchantName,
            @RequestParam(value = "parentName", required = false) String parentName,
            @RequestParam(value = "busiNo", required = false) String busiNo,
            @RequestParam(value = "regDate", required = false) String regDate,
            @RequestParam(value = "remark", required = false) String remark,
            @RequestParam(value = "type", required = false) String type
    ){
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            String uid = dueDiligenceService.createTableByData(merchantName,parentName,busiNo,regDate,remark,type);
            response.setData(uid);
            return response;
        } catch (Exception e) {
            logger.printMessage(e);
            logger.printMessage("创建尽职调查表错误：" + e.getMessage());
            if (e instanceof AppException) {
                return CommonOuterResponse.fail(((AppException) e).getErrorCode(), ((AppException) e).getErrorMsg());
            } else {
                return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
            }
        }
    }

}
