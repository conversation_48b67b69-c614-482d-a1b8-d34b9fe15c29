package com.epaylinks.efps.pas.pas.controller;

import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.pas.common.PageResult;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.mch.domain.PasHoliday;
import com.epaylinks.efps.pas.pas.controller.response.HolidayResponse;

import com.epaylinks.efps.pas.pas.domain.Holiday;
import com.epaylinks.efps.pas.pas.service.HolidayService;

import com.epaylinks.efps.pas.pas.vo.HolidayRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;


/**
 * Created by adm on 2018/8/23.
 */
@RestController
@RequestMapping("/holiday")
@Api(value = "BusinessController", description = "节假日管理")
public class HolidayController {
    @Autowired
    private HolidayService holidayService;

    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @Logable(businessTag = "create")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "新增节假日", notes = "新增节假日", httpMethod = "POST")
    @ApiImplicitParams({
//            @ApiImplicitParam(name = "name", value = "业务名称", required = true, dataType = "String", length = 50, paramType = "query")
    })
//    {"holidayList":[{"dateStr":"20190101","type":"Festival"},{"dateStr":"20190201","type":"Holiday"}]     }
//    {"holidayList":["20230101","20230101"     ]     }用这个报文
    public HolidayResponse create(@RequestBody HolidayRequest vo,
                                  @RequestHeader(value = "x-userid", required = true) Long userId) {
        HolidayResponse response = new HolidayResponse();
        try {
            vo.setCreator(userId);
            holidayService.insert(vo);
        } catch (Exception e) {
            response = new HolidayResponse();
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
            return response;
        }
        return response;
    }

    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @Logable(businessTag = "update")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "修改节假日", notes = "修改节假日", httpMethod = "POST")
    public HolidayResponse update(@RequestBody HolidayRequest vo,
                                  @RequestHeader(value = "x-userid", required = true) Long userId) {
        HolidayResponse response = new HolidayResponse();
        try {
            vo.setCreator(userId);
            holidayService.update(vo);
        } catch (Exception e) {
            response = new HolidayResponse();
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
            return response;
        }
        return response;
    }

   /* @RequestMapping(value = "/selectList", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @Logable(businessTag = "HolidayController.selectList")
    @Exceptionable
    @Validatable
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dateYear", value = "查询年份", required = false, dataType = "String", length = 4, paramType = "query"),
            @ApiImplicitParam(name = "pageNum", value = "查询开始页", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "查询的页面大小", required = true, dataType = "int", paramType = "query")})
    public PageResult<Holiday> selectByPage(@RequestParam(value = "dateYear", required = false) String dateYear,
                                            @RequestParam(value = "pageNum", required = true) Integer pageNum,
                                            @RequestParam(value = "pageSize", required = true) Integer pageSize,
                                            @RequestHeader(value = "x-userid", required = true) Long userId) {
        PageResult<Holiday> userPage = new PageResult<Holiday>();
        try {
            int endRowNo = pageNum * pageSize;
            int beginRowNo = (pageNum - 1) * pageSize + 1;
            Map map = new HashMap();
            map.put("dateYear", dateYear);
            map.put("beginRowNo", beginRowNo);
            map.put("endRowNo", endRowNo);
            userPage = holidayService.selectByPage(map);
        } catch (Exception e) {
            userPage = new PageResult<Holiday>();
            if (e instanceof AppException) {
                userPage.setReturnCode(((AppException) e).getErrorCode());
                userPage.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                userPage.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                userPage.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
            return userPage;
        }
        return userPage;
    }*/


    @RequestMapping(value = "/selectList", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @Logable(businessTag = "HolidayController.selectList")
    @Exceptionable
    @Validatable
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dateYear", value = "查询年份", required = false, dataType = "String", length = 4, paramType = "query"),
            @ApiImplicitParam(name = "pageNum", value = "查询开始页", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "查询的页面大小", required = true, dataType = "int", paramType = "query")})
    public PageResult<Holiday> selectByPage(@RequestParam(value = "dateYear", required = false) String dateYear,
                                            @RequestParam(value = "pageNum", required = true) Integer pageNum,
                                            @RequestParam(value = "pageSize", required = true) Integer pageSize,
                                            @RequestHeader(value = "x-userid", required = true) Long userId) {
        PageResult<Holiday> userPage = new PageResult<Holiday>();
        try {
            int endRowNo = pageNum * pageSize;
            int beginRowNo = (pageNum - 1) * pageSize + 1;
            Map map = new HashMap();
            map.put("dateYear", dateYear);
            map.put("beginRowNo", beginRowNo);
            map.put("endRowNo", endRowNo);
            userPage = holidayService.selectByPage2(map);
        } catch (Exception e) {
            userPage = new PageResult<Holiday>();
            if (e instanceof AppException) {
                userPage.setReturnCode(((AppException) e).getErrorCode());
                userPage.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                userPage.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                userPage.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
            return userPage;
        }
        return userPage;
    }

    @RequestMapping(value = "/checkHoliday", method = RequestMethod.GET)
    @Logable(businessTag = "checkHoliday")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "查询是否节假日", notes = "查询是否节假日", httpMethod = "GET")
    //根据日期查询是否节假日
    public Boolean checkHoliday(@RequestParam("date")String date){
        PasHoliday pasHoliday = holidayService.selectByDate(date);

        if(pasHoliday != null){
            return true;
        }
        return  false;
    }


}
