package com.epaylinks.efps.pas.pas.domain;

public class WxBusinessCategory {
    /**
     * 类目ID
     */
    private Long id;

    /**
     * 父导航名称，第一级
     */
    private String parentNav;

    /**
     * 导航名称，第二级
     */
    private String nav;

    /**
     * 品类系统名称，第三级
     */
    private String categorySys;

    /**
     * 品类名称，第四级
     */
    private String category;
    
    /**
     * 映射银联MCC
     */
    private String unionMcc; 

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getParentNav() {
        return parentNav;
    }

    public void setParentNav(String parentNav) {
        this.parentNav = parentNav;
    }

    public String getNav() {
        return nav;
    }

    public void setNav(String nav) {
        this.nav = nav;
    }

    public String getCategorySys() {
        return categorySys;
    }

    public void setCategorySys(String categorySys) {
        this.categorySys = categorySys;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getUnionMcc() {
        return unionMcc;
    }

    public void setUnionMcc(String unionMcc) {
        this.unionMcc = unionMcc;
    }
    
    
}