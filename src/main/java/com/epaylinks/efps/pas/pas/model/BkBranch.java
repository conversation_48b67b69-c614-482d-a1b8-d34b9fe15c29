package com.epaylinks.efps.pas.pas.model;

import com.epaylinks.efps.common.datadownload.annotation.FieldAnnotation;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

public class BkBranch {
    @ApiModelProperty(value="联行号", dataType = "String")
    @FieldAnnotation(fieldName="联行号")
    private String lbnkNo;

    @ApiModelProperty(value="支行名称", dataType = "String")
    @FieldAnnotation(fieldName="支行名称")
    private String lbnkNm;

    @ApiModelProperty(value="行别代码", dataType = "String")
    @FieldAnnotation(fieldName="行别代码")
    private String lbnkCd;

    private String corpOrg;

    @ApiModelProperty(value="市地区代码", dataType = "String")
    @FieldAnnotation(fieldName="市地区代码")
    private String admCity;

    @ApiModelProperty(value="省地区代码", dataType = "String")
    @FieldAnnotation(fieldName="省地区代码")
    private String admProv;

    private String admRgn;

    private String provCd;

    private String cityCd;

    private String tmSmp;

    private String nodId;

    private String updDt;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    @FieldAnnotation(fieldName="创建时间", dateFormat="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    @FieldAnnotation(fieldName="更新时间", dateFormat="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty(value="操作人", dataType = "String")
    @FieldAnnotation(fieldName="操作人")
    private String operatorId;

    @ApiModelProperty(value="状态 0：停用 1：启用", dataType = "String")
    @FieldAnnotation(fieldName="状态", dictionaries="0:停用,1:启用")
    private String flag;

    public String getLbnkNo() {
        return lbnkNo;
    }

    public void setLbnkNo(String lbnkNo) {
        this.lbnkNo = lbnkNo;
    }

    public String getLbnkNm() {
        return lbnkNm;
    }

    public void setLbnkNm(String lbnkNm) {
        this.lbnkNm = lbnkNm;
    }

    public String getLbnkCd() {
        return lbnkCd;
    }

    public void setLbnkCd(String lbnkCd) {
        this.lbnkCd = lbnkCd;
    }

    public String getCorpOrg() {
        return corpOrg;
    }

    public void setCorpOrg(String corpOrg) {
        this.corpOrg = corpOrg;
    }

    public String getAdmCity() {
        return admCity;
    }

    public void setAdmCity(String admCity) {
        this.admCity = admCity;
    }

    public String getAdmProv() {
        return admProv;
    }

    public void setAdmProv(String admProv) {
        this.admProv = admProv;
    }

    public String getAdmRgn() {
        return admRgn;
    }

    public void setAdmRgn(String admRgn) {
        this.admRgn = admRgn;
    }

    public String getProvCd() {
        return provCd;
    }

    public void setProvCd(String provCd) {
        this.provCd = provCd;
    }

    public String getCityCd() {
        return cityCd;
    }

    public void setCityCd(String cityCd) {
        this.cityCd = cityCd;
    }

    public String getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(String tmSmp) {
        this.tmSmp = tmSmp;
    }

    public String getNodId() {
        return nodId;
    }

    public void setNodId(String nodId) {
        this.nodId = nodId;
    }

    public String getUpdDt() {
        return updDt;
    }

    public void setUpdDt(String updDt) {
        this.updDt = updDt;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }
}