package com.epaylinks.efps.pas.pas.service;

import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR> 5m
 * @Date : 2018/1/2 14:30
 * @Description :
 */
public interface MchinletService {

    public String judge(String institutionCode, String customerCode);

    public String inlet(String institutionCode, String customerCode,String businessCode);

    public String reInlet(Long id);

}
