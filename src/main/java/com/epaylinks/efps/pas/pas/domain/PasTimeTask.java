package com.epaylinks.efps.pas.pas.domain;

import java.util.Date;

public class PasTimeTask {
    /**
     * 主键
     */
    private Long id;

    /**
     * 任务组
     */
    private String groupName;

    /**
     * 发布状态 0为开启，1为停止
     */
    private String jobStatus;

    /**
     * 参数
     */
    private String jobData;

    /**
     * 任务描述
     */
    private String remark;

    /**
     * 创建者名称
     */
    private String createName;

    /**
     * 更新者名称
     */
    private String updateName;

    /**
     * 定时任务名称
     */
    private String jobName;

    /**
     * 表达式
     */
    private String cron;

    /**
     * 创建者id
     */
    private Long createId;

    /**
     * 更新者id
     */
    private Long updateId;

    /**
     * 是否并发执行，0为是ture，1为false
     */
    private String concurrent;

    /**
     * 类名称
     */
    private String beanName;

    /**
     * 锁的时间
     */
    private Long lockTime;

    /**
     * 执行任务的ip地址，到接口段
     */
    private String ipAddres;

    private String realBaenName;

    private String partition;

    private Long lastExecuteRecordId;

    private Integer maxExecuteDuration;

    private Date lastFireTime;

    private Integer recordSaveDays;

    private String parentJobs;

    private String deadline;

    private PasTimeTaskRecord lastExecuteRecord;

    public String getRealBaenName() {
        return realBaenName;
    }

    public void setRealBaenName(String realBaenName) {
        this.realBaenName = realBaenName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public String getJobStatus() {
        return jobStatus;
    }

    public void setJobStatus(String jobStatus) {
        this.jobStatus = jobStatus;
    }

    public String getJobData() {
        return jobData;
    }

    public void setJobData(String jobData) {
        this.jobData = jobData;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public String getJobName() {
        return jobName;
    }

    public void setJobName(String jobName) {
        this.jobName = jobName;
    }

    public String getCron() {
        return cron;
    }

    public void setCron(String cron) {
        this.cron = cron;
    }

    public Long getCreateId() {
        return createId;
    }

    public void setCreateId(Long createId) {
        this.createId = createId;
    }

    public Long getUpdateId() {
        return updateId;
    }

    public void setUpdateId(Long updateId) {
        this.updateId = updateId;
    }

    public String getConcurrent() {
        return concurrent;
    }

    public void setConcurrent(String concurrent) {
        this.concurrent = concurrent;
    }

    public String getBeanName() {
        return beanName;
    }

    public void setBeanName(String beanName) {
        this.beanName = beanName;
    }

    public Long getLockTime() {
        return lockTime;
    }

    public void setLockTime(Long lockTime) {
        this.lockTime = lockTime;
    }

    public String getIpAddres() {
        return ipAddres;
    }

    public void setIpAddres(String ipAddres) {
        this.ipAddres = ipAddres;
    }

    public String getPartition() {
        return partition;
    }

    public void setPartition(String partition) {
        this.partition = partition;
    }

    public Long getLastExecuteRecordId() {
        return lastExecuteRecordId;
    }

    public void setLastExecuteRecordId(Long lastExecuteRecordId) {
        this.lastExecuteRecordId = lastExecuteRecordId;
    }

    public Integer getMaxExecuteDuration() {
        return maxExecuteDuration;
    }

    public void setMaxExecuteDuration(Integer maxExecuteDuration) {
        this.maxExecuteDuration = maxExecuteDuration;
    }

    public boolean isExecuteConcurrent() {
        return "0".equals(concurrent);
    }

    public Date getLastFireTime() {
        return lastFireTime;
    }

    public void setLastFireTime(Date lastFireTime) {
        this.lastFireTime = lastFireTime;
    }

    public Integer getRecordSaveDays() {
        return recordSaveDays;
    }

    public void setRecordSaveDays(Integer recordSaveDays) {
        this.recordSaveDays = recordSaveDays;
    }

    public PasTimeTaskRecord getLastExecuteRecord() {
        return lastExecuteRecord;
    }

    public void setLastExecuteRecord(PasTimeTaskRecord lastExecuteRecord) {
        this.lastExecuteRecord = lastExecuteRecord;
    }

    public String getParentJobs() {
        return parentJobs;
    }

    public void setParentJobs(String parentJobs) {
        this.parentJobs = parentJobs;
    }

    public String getDeadline() {
        return deadline;
    }

    public void setDeadline(String deadline) {
        this.deadline = deadline;
    }
}