package com.epaylinks.efps.pas.pas.dao;

import com.epaylinks.efps.pas.pas.domain.ManagementPersonnel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ManagementPersonnelMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ManagementPersonnel record);

    int insertSelective(ManagementPersonnel record);

    ManagementPersonnel selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ManagementPersonnel record);

    int updateByPrimaryKey(ManagementPersonnel record);

    Long queryMPNextSeq();

    List<ManagementPersonnel> selectPersonnelList();

    String getReceiveId();

    ManagementPersonnel selectByReceiverId(@Param("receiverId") String receiverId);
}