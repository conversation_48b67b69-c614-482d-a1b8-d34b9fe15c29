package com.epaylinks.efps.pas.pas.service.impl;

import com.epaylinks.efps.pas.pas.dao.ApplicationMapper;
import com.epaylinks.efps.pas.pas.domain.Application;
import com.epaylinks.efps.pas.pas.service.ApplicationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 5m
 * @Date : 2017/12/28 17:05
 * @Description :
 */
@Service
public class ApplicationServiceImpl implements ApplicationService{

    @Autowired
    ApplicationMapper applicationMapper;

    @Override
    public int deleteByPrimaryKey(Long id) {
        return applicationMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(Application record) {
        return applicationMapper.insert(record);
    }

    @Override
    public int insertSelective(Application record) {
        return applicationMapper.insertSelective(record);
    }

    @Override
    public Application selectByPrimaryKey(Long id) {
        return applicationMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(Application record) {
        return applicationMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(Application record) {
        return applicationMapper.updateByPrimaryKey(record);
    }

    @Override
    public List<Application> selectBySelective(Application record) {
        return applicationMapper.selectBySelective(record);
    }

    @Override
    public String checkUpValueIsNotNull(Map map){
        return applicationMapper.checkUpValueIsNotNull(map) ;
    }

    @Override
    public String getFileUrlId(Map map) {
        return applicationMapper.getFileUrlId(map);
    }

    @Override
    public String getInstitutionMCC(Map map) { return applicationMapper.getInstitutionMCC(map); }
}
