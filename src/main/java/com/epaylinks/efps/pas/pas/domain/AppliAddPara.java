package com.epaylinks.efps.pas.pas.domain;


public class AppliAddPara {
    /**
     * 主键
     */
    private Long id;

    /**
     * 商户的客户编码
     */
    private String customerCode;

    /**
     * 进件所需字段的efpskey
     */
    private String efpsKey;

    /**
     * 指定商户的某个进件所需字段的value
     */
    private String value;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getEfpsKey() {
        return efpsKey;
    }

    public void setEfpsKey(String efpsKey) {
        this.efpsKey = efpsKey;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}