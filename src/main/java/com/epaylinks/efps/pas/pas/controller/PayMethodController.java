package com.epaylinks.efps.pas.pas.controller;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.pas.common.Result;
import com.epaylinks.efps.pas.pas.domain.PayMethod;
import com.epaylinks.efps.pas.pas.service.PayMethodService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> 5m
 * @Date : 2017/12/27 17:03
 * @Description :
 */
@RestController
@RequestMapping("/PayMethod")
@Api(value = "PayMethodController", description = "支付方式接口")
public class PayMethodController {

    @Autowired
    private SequenceService sequenceService;

    @Autowired
    private PayMethodService payMethodService;

    @RequestMapping(value ="/create", method = RequestMethod.POST)
    @Logable(businessTag = "create")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "新增支付方式", notes = "新增支付方式", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "payMethod", value = "支付方式（代码）", required = true, dataType = "String", length =2, paramType = "query"),
            @ApiImplicitParam(name = "payMethodName", value = "支付方式名称", required = true, dataType = "String", length =50, paramType = "query"),
            @ApiImplicitParam(name = "creator", value = "创建人", required = false, dataType = "String", length =50, paramType = "query"),
            @ApiImplicitParam(name = "updator", value = "修改人", required = false, dataType = "String", length =50, paramType = "query"),
            @ApiImplicitParam(name = "creditPay", value = "支付信用卡支付:0-否.1-是", required = false, dataType = "String", length =50, paramType = "query",valueRange="{0,1}"),
            @ApiImplicitParam(name = "debitPay", value = "支持借记卡支付:0-否.1-是", required = false, dataType = "String", length =50, paramType = "query",valueRange="{0,1}"),
            @ApiImplicitParam(name = "publicAccount", value = "支持对公账户:0-否.1-是", required = false, dataType = "String", length =50, paramType = "query",valueRange="{0,1}"),
            @ApiImplicitParam(name = "privateAccount", value = "支持对私账户:0-否.1-是", required = false, dataType = "String", length =50, paramType = "query",valueRange="{0,1}"),
            @ApiImplicitParam(name = "state", value = "状态:0-不正常.1-正常", required = true, dataType = "String", length =1, paramType = "query",valueRange="{0,1}"),
            @ApiImplicitParam(name = "allowConfigAppId", value = "是否允许配置关注公众号的appId:0-否.1-是", required = true, dataType = "String", length =1, paramType = "query",valueRange="{0,1}"),
            @ApiImplicitParam(name = "needApplyInInstitution", value = "是否需要为商户在上游进件:0-否.1-是", required = true, dataType = "String", length =1, paramType = "query",valueRange="{0,1}")})
    public String create(@ApiIgnore PayMethod record) {
        record.setId(sequenceService.nextValue("pas"));
        record.setCreateTime(new Date());
        return payMethodService.insert(record) == 1? Constants.SUCCESS: Constants.FAIL;

    }

    @RequestMapping(value ="/modify", method = RequestMethod.POST)
    @Logable(businessTag = "modify")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "修改支付方式", notes = "修改支付方式", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "id", required = true, dataType = "long", length =50, paramType = "query"),
            @ApiImplicitParam(name = "payMethod", value = "支付方式（代码）", required = false, dataType = "String", length =2, paramType = "query"),
            @ApiImplicitParam(name = "payMethodName", value = "支付方式名称", required = false, dataType = "String", length =50, paramType = "query"),
            @ApiImplicitParam(name = "creator", value = "创建人", required = false, dataType = "String", length =50, paramType = "query"),
            @ApiImplicitParam(name = "updator", value = "修改人", required = false, dataType = "String", length =50, paramType = "query"),
            @ApiImplicitParam(name = "creditPay", value = "支付信用卡支付:0-否.1-是", required = false, dataType = "String", length =50, paramType = "query",valueRange="{0,1}"),
            @ApiImplicitParam(name = "debitPay", value = "支持借记卡支付:0-否.1-是", required = false, dataType = "String", length =50, paramType = "query",valueRange="{0,1}"),
            @ApiImplicitParam(name = "publicAccount", value = "支持对公账户:0-否.1-是", required = false, dataType = "String", length =50, paramType = "query",valueRange="{0,1}"),
            @ApiImplicitParam(name = "privateAccount", value = "支持对私账户:0-否.1-是", required = false, dataType = "String", length =50, paramType = "query",valueRange="{0,1}"),
            @ApiImplicitParam(name = "state", value = "状态:0-不正常.1-正常", required = false, dataType = "String", length =1, paramType = "query",valueRange="{0,1}"),
            @ApiImplicitParam(name = "allowConfigAppId", value = "是否允许配置关注公众号的appId:0-否.1-是", required = false, dataType = "String", length =1, paramType = "query",valueRange="{0,1}"),
            @ApiImplicitParam(name = "needApplyInInstitution", value = "是否需要为商户在上游进件:0-否.1-是", required = false, dataType = "String", length =1, paramType = "query",valueRange="{0,1}")})
    public String modify(@ApiIgnore PayMethod record ) {
        record.setUpdateTime(new Date());
        return payMethodService.updateByPrimaryKeySelective(record) == 1? Constants.SUCCESS: Constants.FAIL;

    }


    @RequestMapping(value ="/delete", method = RequestMethod.GET)
    @Logable(businessTag = "delete")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "删除支付方式", notes = "删除支付方式", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "id", required = true, dataType = "long", length =50, paramType = "query")})
    public String delete(@RequestParam Long id) {
        return payMethodService.deleteByPrimaryKey(id) == 1? Constants.SUCCESS: Constants.FAIL;

    }


    @RequestMapping(value ="/select", method = RequestMethod.GET)
    @Logable(businessTag = "select")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "搜索支付方式", notes = "搜索支付方式", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "payMethod", value = "支付方式（代码）", required = false, dataType = "String", length =2, paramType = "query"),
            @ApiImplicitParam(name = "payMethodName", value = "支付方式名称", required = false, dataType = "String", length =50, paramType = "query"),
            @ApiImplicitParam(name = "creator", value = "创建人", required = false, dataType = "String", length =50, paramType = "query"),
            @ApiImplicitParam(name = "updator", value = "修改人", required = false, dataType = "String", length =50, paramType = "query"),
            @ApiImplicitParam(name = "creditPay", value = "支付信用卡支付", required = false, dataType = "String", length =50, paramType = "query",valueRange="{0,1}"),
            @ApiImplicitParam(name = "debitPay", value = "支持借记卡支付", required = false, dataType = "String", length =50, paramType = "query",valueRange="{0,1}"),
            @ApiImplicitParam(name = "publicAccount", value = "支持对公账户", required = false, dataType = "String", length =50, paramType = "query",valueRange="{0,1}"),
            @ApiImplicitParam(name = "privateAccount", value = "支持对私账户", required = false, dataType = "String", length =50, paramType = "query",valueRange="{0,1}"),
            @ApiImplicitParam(name = "state", value = "状态", required = false, dataType = "String", length =1, paramType = "query",valueRange="{0,1}"),
            @ApiImplicitParam(name = "allowConfigAppId", value = "是否允许配置关注公众号的appId", required = false, dataType = "String", length =1, paramType = "query",valueRange="{0,1}"),
            @ApiImplicitParam(name = "needApplyInInstitution", value = "是否需要为商户在上游进件", required = false, dataType = "String", length =1, paramType = "query",valueRange="{0,1}")})
    public List<PayMethod> select(@ApiIgnore PayMethod record ) {
        return payMethodService.selectBySelective(record) ;

    }

    @RequestMapping(value ="/selectById", method = RequestMethod.GET)
    @Logable(businessTag = "selectById")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "根据ID搜索单条支付方式", notes = "根据ID搜索单条支付方式", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "id", required = true, dataType = "long", length =50, paramType = "query")})
    public PayMethod selectById(@RequestParam Long id) {
        return payMethodService.selectByPrimaryKey(id) ;
    }

    /**
     * 全量同步支付方式接口
     * @return
     */
    @RequestMapping(value = "/sync" , method = RequestMethod.GET)
    public Result<String> syncPayMethod() {
    	payMethodService.syncPayMethod();
    	Result<String> result = new Result<>();
    	result.setData(Constants.SUCCESS);
		return result;
    }

    /**
     * 查询支付方式
     */
    @GetMapping("queryPayMethodByType")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "查询支付方式", notes = "查询支付方式", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "类型", required = true, dataType = "String", length = 20, paramType = "query", valueRange = "{pay,withdraw,all}")})
    @Logable(businessTag = "queryPayMethodByType", outputResult = false)
    public CommonOuterResponse queryPayMethodByType(@RequestParam("type") String type) {
        List<PayMethod> data = payMethodService.queryPayMethodByType(type);
        CommonOuterResponse response = new CommonOuterResponse();
        response.setData(data);
        return response;
    }
}
