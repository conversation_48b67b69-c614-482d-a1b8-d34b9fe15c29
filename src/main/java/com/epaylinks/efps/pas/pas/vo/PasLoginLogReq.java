package com.epaylinks.efps.pas.pas.vo;

public class PasLoginLogReq {
	private String startTime;
	private String endTime;
	private String username;
	private String usertype;
	private String state;
	private String customercode;
	private String platcustomer;
	private String servicecustomer;
	private int endNum;
	private int startNum;
	private Boolean download;
    private Short logType; // 日志类型：1或空:登录日志（默认）；2：操作日志
	
	public String getStartTime() {
		return startTime;
	}
	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}
	public String getEndTime() {
		return endTime;
	}
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
	public String getUsername() {
		return username;
	}
	public void setUsername(String username) {
		this.username = username;
	}
	public String getUsertype() {
		return usertype;
	}
	public void setUsertype(String usertype) {
		this.usertype = usertype;
	}
	public String getState() {
		return state;
	}
	public void setState(String state) {
		this.state = state;
	}
	public String getCustomercode() {
		return customercode;
	}
	public void setCustomercode(String customercode) {
		this.customercode = customercode;
	}
	public String getPlatcustomer() {
		return platcustomer;
	}
	public void setPlatcustomer(String platcustomer) {
		this.platcustomer = platcustomer;
	}
	public String getServicecustomer() {
		return servicecustomer;
	}
	public void setServicecustomer(String servicecustomer) {
		this.servicecustomer = servicecustomer;
	}
	public int getEndNum() {
		return endNum;
	}
	public void setEndNum(int endNum) {
		this.endNum = endNum;
	}
	public int getStartNum() {
		return startNum;
	}
	public void setStartNum(int startNum) {
		this.startNum = startNum;
	}
	public Boolean getDownload() {
		return download;
	}
	public void setDownload(Boolean download) {
		this.download = download;
	}
    public Short getLogType() {
        return logType;
    }
    public void setLogType(Short logType) {
        this.logType = logType;
    }

	
}
