package com.epaylinks.efps.pas.pas.controller.response;


import com.epaylinks.efps.common.business.CommonOuterResponse;

/**
 * 额度调整-响应结果
 */
public class QueryQuotaResponse extends CommonOuterResponse {
//    交易类型
    private String txnType   ; //43备付金 余额查询

//    机构代码
    private String acqInsCode   ;
    //交易流水号
    private String txnNo   ;
    //交易日期
    private String txnDate   ;
    //发送时间
    private String sndTime   ;
//    ACS 备付金存管账号
    private String bankNo   ;
//请求方保留域
    private String reqReserved   ;
//    保留域
    private String reserved   ;
    //应答码
    private String respCode   ;
    //应答信息
    private String respMsg   ;
    //货币
    private String currencyCode   ;
    //备付金虚拟记账余额
    private String proAcctBal   ;
    //备付金虚拟记账可用余额
    private String proAvlbBal  ;
//    ACS 备付金存管账户可用余额
    private String acsAcctBal  ;
//    ACS 备付金存管账户名称
    private String acsAcctName  ;
    //可支取额度
    private String avlbQuotaAmt  ;
    private String state     ;

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getTxnType() {
        return txnType;
    }

    public void setTxnType(String txnType) {
        this.txnType = txnType;
    }

    public String getAcqInsCode() {
        return acqInsCode;
    }

    public void setAcqInsCode(String acqInsCode) {
        this.acqInsCode = acqInsCode;
    }

    public String getTxnNo() {
        return txnNo;
    }

    public void setTxnNo(String txnNo) {
        this.txnNo = txnNo;
    }

    public String getTxnDate() {
        return txnDate;
    }

    public void setTxnDate(String txnDate) {
        this.txnDate = txnDate;
    }

    public String getSndTime() {
        return sndTime;
    }

    public void setSndTime(String sndTime) {
        this.sndTime = sndTime;
    }

    public String getBankNo() {
        return bankNo;
    }

    public void setBankNo(String bankNo) {
        this.bankNo = bankNo;
    }

    public String getReqReserved() {
        return reqReserved;
    }

    public void setReqReserved(String reqReserved) {
        this.reqReserved = reqReserved;
    }

    public String getReserved() {
        return reserved;
    }

    public void setReserved(String reserved) {
        this.reserved = reserved;
    }

    public String getRespCode() {
        return respCode;
    }

    public void setRespCode(String respCode) {
        this.respCode = respCode;
    }

    public String getRespMsg() {
        return respMsg;
    }

    public void setRespMsg(String respMsg) {
        this.respMsg = respMsg;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getProAcctBal() {
        return proAcctBal;
    }

    public void setProAcctBal(String proAcctBal) {
        this.proAcctBal = proAcctBal;
    }

    public String getProAvlbBal() {
        return proAvlbBal;
    }

    public void setProAvlbBal(String proAvlbBal) {
        this.proAvlbBal = proAvlbBal;
    }

    public String getAcsAcctBal() {
        return acsAcctBal;
    }

    public void setAcsAcctBal(String acsAcctBal) {
        this.acsAcctBal = acsAcctBal;
    }

    public String getAcsAcctName() {
        return acsAcctName;
    }

    public void setAcsAcctName(String acsAcctName) {
        this.acsAcctName = acsAcctName;
    }

    public String getAvlbQuotaAmt() {
        return avlbQuotaAmt;
    }

    public void setAvlbQuotaAmt(String avlbQuotaAmt) {
        this.avlbQuotaAmt = avlbQuotaAmt;
    }

    /**
     * 上游返回响应码
     */
//    private String channelRespCode;
    /**
     * 上游返回响应消息
     */
//    private String channelRespMsg;


/*
    public String getChannelRespCode() {
        return channelRespCode;
    }

    public void setChannelRespCode(String channelRespCode) {
        this.channelRespCode = channelRespCode;
    }

    public String getChannelRespMsg() {
        return channelRespMsg;
    }

    public void setChannelRespMsg(String channelRespMsg) {
        this.channelRespMsg = channelRespMsg;
    }*/
}
