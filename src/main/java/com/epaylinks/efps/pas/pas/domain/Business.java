package com.epaylinks.efps.pas.pas.domain;

import com.epaylinks.efps.pas.pas.common.PasConstant;
import com.epaylinks.efps.pas.pas.controller.dto.CustBusinessPriceDTO;
import com.epaylinks.efps.pas.pas.util.BigDecimalUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

public class Business {
    /**
     * 主键
     */
    private Long id;

    /**
     * 业务名称
     */
    private String name;

    /**
     * 业务编码
     */
    private String code;

    /**
     * 业务类型:1-互联网支付.2-易票联账户服务
     */
    private String type;

    /**
     * 费率类型:1-单笔固定费率.2-按交易金额比例
     */
    private String ratioMode;

    /**
     * 费率，费率类型不同，解释不同：单笔固定费率时，为一个非负整数，表示每笔交易需多少手续费，单位分；按交易金额比例时，为一个非负整数，范围0-10000，表示收费万分比

     */
    private String ratio;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态:1-正常.2-已删除
     */
    private String state;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 修改人
     */
    private String updator;

    private List<PayMethod> payMethodList;
    /**
     * 业务类别：efpsAccountService: efps账务服务, efpsPayService: efps支付服务
     */
    private String businessCategory;
    
    private String businessLabel;

    private String canRepeat;  //0不允许，1允许

    private Short cardType;//卡类型 1.借记卡;2.贷记卡
    
    private String chargeGroup; // 计费分组（相关业务绑定分组）

    public Short getCardType() {
        return cardType;
    }

    public void setCardType(Short cardType) {
        this.cardType = cardType;
    }

    public void setPayMethodList(List<PayMethod> payMethodList) {
        this.payMethodList = payMethodList;
    }

    public String getCanRepeat() {
        return canRepeat;
    }

    public void setCanRepeat(String canRepeat) {
        this.canRepeat = canRepeat;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getRatioMode() {
        return ratioMode;
    }

    public void setRatioMode(String ratioMode) {
        this.ratioMode = ratioMode;
    }

    public String getRatio() {
        return ratio;
    }

    public void setRatio(String ratio) {
        this.ratio = ratio;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdator() {
        return updator;
    }

    public void setUpdator(String updator) {
        this.updator = updator;
    }

    public List<PayMethod> getPayMethodList() {
        return payMethodList;
    }

    /**
     * 计算手续费
     */
    public String calculationFee(String amount){

        String fee ;//手续费
        String rate ;//费率
        String max = null ;//封顶手续费

        String[] ratios = this.ratio.split("_");
        rate = ratios[0];
        if(ratios.length ==2 ){ max = ratios[1]; }

        if(PasConstant.RatioMode.Single.code.equals(this.ratioMode)){
            fee = rate;//固定费率(单位分)
        }else {
            //计算费率 万分比
            fee = BigDecimalUtil.mul(amount , rate);
            fee = BigDecimalUtil.div(fee, "10000" , 0);

            if(StringUtils.isNotBlank(max)){
                if (Double.parseDouble(BigDecimalUtil.subIn(fee, max)) > 0) {
                    fee = max;
                }
            }
        }


        return fee;
    }

    public String getBusinessCategory() {
        return businessCategory;
    }

    public void setBusinessCategory(String businessCategory) {
        this.businessCategory = businessCategory;
    }

    //是否显示支付控制，0不显示，1显示
     private String showPayControl;

    public String getShowPayControl() {
        return showPayControl;
    }

    public void setShowPayControl(String showPayControl) {
        this.showPayControl = showPayControl;
    }

	public String getBusinessLabel() {
		return businessLabel;
	}

	public void setBusinessLabel(String businessLabel) {
		this.businessLabel = businessLabel;
	}
    
    private String templateCode;
    private String templateName;
    private String businessGroup;
    private String businessGroupName;
    private String displayCode;
    private String scope;
    private String settCycle;
    private String maxProfitProportion;
    /**
     * 商户业务分组树新增费率信息返回
     */
    private CustBusinessPriceDTO price;

    public String getSettCycle() {
        return settCycle;
    }

    public void setSettCycle(String settCycle) {
        this.settCycle = settCycle;
    }

    public String getMaxProfitProportion() {
        return maxProfitProportion;
    }

    public void setMaxProfitProportion(String maxProfitProportion) {
        this.maxProfitProportion = maxProfitProportion;
    }

    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    public String getBusinessGroupName() {
        return businessGroupName;
    }

    public void setBusinessGroupName(String businessGroupName) {
        this.businessGroupName = businessGroupName;
    }

    public String getTemplateCode() {
        return templateCode;
    }

    public void setTemplateCode(String templateCode) {
        this.templateCode = templateCode;
    }

    public String getBusinessGroup() {
        return businessGroup;
    }

    public void setBusinessGroup(String businessGroup) {
        this.businessGroup = businessGroup;
    }

    public String getDisplayCode() {
        return displayCode;
    }

    public void setDisplayCode(String displayCode) {
        this.displayCode = displayCode;
    }

    public String getScope() {
        return scope;
    }

    public void setScope(String scope) {
        this.scope = scope;
    }

    public String getChargeGroup() {
        return chargeGroup;
    }

    public void setChargeGroup(String chargeGroup) {
        this.chargeGroup = chargeGroup;
    }

    public CustBusinessPriceDTO getPrice() {
        return price;
    }

    public void setPrice(CustBusinessPriceDTO price) {
        this.price = price;
    }
}