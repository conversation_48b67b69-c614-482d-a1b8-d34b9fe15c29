package com.epaylinks.efps.pas.pas.controller;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.pas.domain.PasLoginInfo;
import com.epaylinks.efps.pas.pas.service.LoginInfoService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/**
* 登录错误信息
* <AUTHOR>
* @date：2020年5月21日 下午4:13:49
*/
@RestController
@RequestMapping("/loginInfo")
@Api(value = "LoginInfoController", description = "登录错误信息类")
public class LoginInfoController {

	@Autowired
	private LoginInfoService loginInfoService;

	@RequestMapping(value = "/searchLoginInfo", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
	@Logable(businessTag = "LoginInfoController.searchLoginInfo")
	@Exceptionable
	@Validatable
	@ApiOperation(value = "根据用户名和用户类型查询登录信息", notes = "根据用户名和用户类型查询登录信息", httpMethod = "GET")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "userName", value = "用户名", required = true, dataType = "String", paramType = "query"),
			@ApiImplicitParam(name = "userType", value = "用户类型", required = true, dataType = "String", paramType = "query")
	})
	public CommonOuterResponse<PasLoginInfo> searchStore(
			@RequestParam(value = "userName", required = true) String userName,
			@RequestParam(value = "userType", required = true) String userType
	) {
		CommonOuterResponse<PasLoginInfo> pasLoginInfoResp = new CommonOuterResponse<PasLoginInfo>();
		try {
			PasLoginInfo pl = new PasLoginInfo();
			pl.setUsername(userName);
			pl.setUsertype(userType);
			PasLoginInfo pasLoginInfo = loginInfoService.searchLoginInfo(pl);
	
			pasLoginInfoResp.setData(pasLoginInfo);
		} catch (Exception e) {
			pasLoginInfoResp = new CommonOuterResponse<PasLoginInfo>();
			if (e instanceof AppException) {
				pasLoginInfoResp.setReturnCode(((AppException) e).getErrorCode());
				pasLoginInfoResp.setReturnMsg(((AppException) e).getErrorMsg());
			} else {
				pasLoginInfoResp.setReturnCode(PasCode.LOGININFO_ERROR.code);
				pasLoginInfoResp.setReturnMsg(PasCode.LOGININFO_ERROR.message);
			}
			return pasLoginInfoResp;
		}
		return pasLoginInfoResp;

	}

    @RequestMapping(value = "/recoveryErrorCount", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @Logable(businessTag = "LoginInfoController.recoveryErrorCount")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "对用户登录错误次数清零", notes = "对用户登录错误次数清零", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userName", value = "用户名", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "userType", value = "用户类型", required = true, dataType = "String", paramType = "query") })
    public CommonOuterResponse recoveryErrorCountByUsernameAndType(
            @RequestParam(value = "userName", required = true) String userName,
            @RequestParam(value = "userType", required = true) String userType) {

        try {
            loginInfoService.recoveryErrorCountByUsernameAndType(userName, userType);
            return CommonOuterResponse.success();

        } catch (AppException e) {
            return CommonOuterResponse.fail(e.getErrorCode(), e.getErrorMsg());

        } catch (Exception e) {
            return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
        }

    }

}
