package com.epaylinks.efps.pas.pas.controller.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

@Data
@ApiModel
public class InvoiceAuditRequest {
    private Long invoiceId;

    @ApiModelProperty(value = "商户编号",dataType = "String")
    private String customerNo;

    @ApiModelProperty(value = "商户名称",dataType = "String")
    private String customerName;

    @ApiModelProperty(value = "发票介质[1：电子发票；2：纸质发票]",dataType = "String")
    private String invoiceMedium;

    @ApiModelProperty(value = "发票类型[1：增值税专用发票；2：普通发票]",dataType = "String")
    private String invoiceType;

    @ApiModelProperty(value = "发票编号",dataType = "String")
    private String invoiceNo;

    @ApiModelProperty(value = "开票日期",dataType = "String")
    private String invoiceTime;

    @ApiModelProperty(value = "税率",dataType = "Integer")
    private Integer taxRatio;

    @ApiModelProperty(value = "发票金额",dataType = "Long")
    private Long invoiceAmount;

    @ApiModelProperty(value = "附件ID",dataType = "String")
    private String uniqueid;

    @ApiModelProperty(value = "备注",dataType = "String")
    private String remarks;

//    @ApiModelProperty(value = "创建人ID",dataType = "Long")
//    private Long creator;
}
