package com.epaylinks.efps.pas.pas.controller;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.business.pay.request.UserType;
import com.epaylinks.efps.common.datadownload.annotation.DownloadAble;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.pas.domain.DkteRecord;
import com.epaylinks.efps.pas.pas.domain.DkteRecordVo;
import com.epaylinks.efps.pas.pas.service.DkteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@RestController
@RequestMapping("/dkte")
@Api(value = "DkteController", description = "待开调额记录管理")
public class DkteController {

    @Autowired
    private DkteService dkteService;

    @Autowired
    private CommonLogger logger;

    @GetMapping("/pageQuery")
    @Logable(businessTag = "DkteController.pageQuery",outputResult = false)
    @Exceptionable
    @ApiOperation(value = "待开调额记录查询", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startCreateTime", value = "开始时间yyyyMMddHHmmss", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endCreateTime", value = "结束时间yyyyMMddHHmmss", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "transactionNo", value = "易票联流水号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "adjustType", value = "调额类型：1增额，2减额", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "recordState", value = "状态，1：审核通过；2：审核不通过；3：待审核", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "customerCode", value = "商户编号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageNum", value = "查询开始页", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "查询的页面大小不需要分页则把此值填大一点", required = true, dataType = "int", paramType = "query")
    })
    @DownloadAble
    public PageResult<DkteRecord> pageQuery(
            @RequestHeader(value = "x-user-type", required = false) String userType,
            @RequestParam(value = "startCreateTime", required = false) String startCreateTime,
            @RequestParam(value = "endCreateTime", required = false) String endCreateTime,
            @RequestParam(value = "transactionNo", required = false) String transactionNo,
            @RequestParam(value = "adjustType", required = false) String adjustType,
            @RequestParam(value = "recordState", required = false) String recordState,
            @RequestParam(value = "customerCode", required = false) String customerCode,
            @RequestParam(value = "pageNum", required = true) Integer pageNum,
            @RequestParam(value = "pageSize", required = true) Integer pageSize,
            @RequestParam(value = "download", required = false) Boolean download,
            @RequestParam(value = "fileName", required = false) String fileName,
            @RequestParam(value = "fileSource", required = false) String fileSource,
            @RequestParam(value = "type", required = false) String type) {
        PageResult<DkteRecord> response = new PageResult<>();
        try {
            //该接口只支持运营门户
            if (!UserType.PAS_USER.code.equals(userType)) {
                throw new AppException(PasCode.CUSTOMER_TYPE_INVALID.code);
            }
            Map map = new HashMap();
            if (StringUtils.isNotBlank(startCreateTime)) {
                map.put("startCreateTime", startCreateTime);
            }
            if (StringUtils.isNotBlank(endCreateTime)) {
                map.put("endCreateTime", endCreateTime);
            }
            if (StringUtils.isNotBlank(customerCode)) {
                map.put("customerCode", customerCode);
            }
            if (StringUtils.isNotBlank(transactionNo)) {
                map.put("transactionNo", transactionNo);
            }
            if (StringUtils.isNotBlank(adjustType)) {
                map.put("adjustType", adjustType);
            }
            if (StringUtils.isNotBlank(recordState)) {
                map.put("recordState", recordState);
            }
            if (StringUtils.isNotBlank(customerCode)) {
                map.put("customerCode", customerCode);
            }
            int total = dkteService.queryCount(map);
            logger.printMessage("待开调额记录查询到数据条数："+total);
            response.setTotal(total);
            if (download != null && download) {
                pageSize = total;
                pageNum = 1;
            }
            int endRowNo = pageNum * pageSize;
            int beginRowNo = (pageNum - 1) * pageSize + 1;
            map.put("beginRowNo", beginRowNo);
            map.put("endRowNo", endRowNo);
            response.setRows(dkteService.pageQuery(map));
            response.setResult(Constants.detailReturnCode.RETURN_SUCCESS.code);
            response.setCode(Constants.detailReturnCode.RETURN_SUCCESS.code);
            return response;
        } catch (Exception e) {
            logger.printMessage(e);
            logger.printMessage("待开调额记录查询错误：" + e.getMessage());
            if (e instanceof AppException) {
                response.setResult(((AppException) e).getErrorCode());
                response.setCode(((AppException) e).getErrorCode());
                response.setErrorMsg(((AppException) e).getErrorMsg());
                response.setMessage(((AppException) e).getErrorMsg());
            } else {
                response.setResult(PasCode.SYSTEM_EXCEPTION.code);
                response.setCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setErrorMsg(PasCode.SYSTEM_EXCEPTION.message);
                response.setMessage(PasCode.SYSTEM_EXCEPTION.message);
            }
            return response;
        }
    }

    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Exceptionable
    @Validatable
    @ApiOperation(value = "新增待开调额记录", notes = "新增待开调额记录", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "customerCode", value = "商户编号", length = 32, required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "customerName", value = "商户名称", length = 128, required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "adjustType", value = "调额类型：1增额，2减额，3冻结，4解冻",length = 4, required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "adjustReason", value = "调额事由：BZFR，补增分润差额；KJSD，扣减发票税点；KJFR，扣减分润差额；", length = 10,required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "adjustAmout", value = "调整金额，单位分", length = 14, required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "remark", value = "备注", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "attachmentUid", value = "附件ID", length = 100, required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "attachmentName", value = "附件名称", length = 100, required = false, dataType = "String", paramType = "query"),
    })
    @Logable(businessTag = "DkteController.add")
    public CommonOuterResponse add(
            @RequestHeader(value = "x-user-type", required = false) String userType,
            @RequestHeader(value = "x-userid", required = false) String userId,
            @Valid @ApiIgnore DkteRecordVo record,
            BindingResult bindingResult
    ){
        try {
            //该接口只支持运营门户
            if (!UserType.PAS_USER.code.equals(userType)) {
                throw new AppException(PasCode.CUSTOMER_TYPE_INVALID.code);
            }
            if(Objects.isNull(record)){
                throw new AppException(PasCode.DATA_ERROR.code,  "请求参数不能为空");
            }
            if(StringUtils.isBlank(record.getCustomerCode())){
                throw new AppException(PasCode.DATA_ERROR.code,  "商户编号不能为空");
            }
            if(StringUtils.isBlank(record.getCustomerName())){
                throw new AppException(PasCode.DATA_ERROR.code,  "商户名称不能为空");
            }
//            if(!"1".equals(record.getAdjustType()) && !"2".equals(record.getAdjustType())){
//                throw new AppException(PasCode.DATA_ERROR.code,  "调额类型有误");
//            }
//            if(Objects.isNull(record.getAdjustAmout()) || record.getAdjustAmout()<0){
//                throw new AppException(PasCode.DATA_ERROR.code,  "调整金额有误");
//            }
//            if("1".equals(record.getAdjustType()) && !"BZFR".equals(record.getAdjustReason())){
//                throw new AppException(PasCode.DATA_ERROR.code,  "选择增额时，调额事由只能为：补增分润差额”");
//            }
//            if("2".equals(record.getAdjustType()) && !"KJSD".equals(record.getAdjustReason()) && !"KJFR".equals(record.getAdjustReason())){
//                throw new AppException(PasCode.DATA_ERROR.code,  "选择减额时，调额事由只能为：扣减发票税点、扣减分润差额”");
//            }
            dkteService.check(record,bindingResult);
            dkteService.add(record,userId);
            return CommonOuterResponse.success();
        } catch (Exception e) {
            logger.printMessage(e);
            logger.printMessage("待开调额记录新增错误：" + e.getMessage());
            if (e instanceof AppException) {
                return CommonOuterResponse.fail(((AppException) e).getErrorCode(), ((AppException) e).getErrorMsg());
            } else {
                return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
            }
        }
    }

    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @Exceptionable
    @Validatable
    @ApiOperation(value = "修改待开调额记录", notes = "修改待开调额记录", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dkteId", value = "记录ID", length = 32, required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "customerCode", value = "商户编号", length = 32, required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "customerName", value = "商户名称", length = 128, required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "adjustType", value = "调额类型：1增额，2减额",length = 4, required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "adjustReason", value = "调额事由：BZFR，补增分润差额；KJSD，扣减发票税点；KJFR，扣减分润差额；", length = 10,required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "adjustAmout", value = "调整金额，单位分", length = 14, required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "remark", value = "备注", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "attachmentUid", value = "附件ID", length = 100, required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "attachmentName", value = "附件名称", length = 100, required = false, dataType = "String", paramType = "query"),
    })
    @Logable(businessTag = "DkteController.update")
    public CommonOuterResponse update(
            @RequestHeader(value = "x-user-type", required = false) String userType,
            @RequestHeader(value = "x-userid", required = false) String userId,
            @Valid @ApiIgnore DkteRecordVo record,
            BindingResult bindingResult
    ){
        try {
            //该接口只支持运营门户
            if (!UserType.PAS_USER.code.equals(userType)) {
                throw new AppException(PasCode.CUSTOMER_TYPE_INVALID.code);
            }
            if(Objects.isNull(record)){
                throw new AppException(PasCode.DATA_ERROR.code,  "请求参数不能为空");
            }
            if(Objects.isNull(record.getDkteId())){
                throw new AppException(PasCode.DATA_ERROR.code,  "记录ID不能为空");
            }
            if(StringUtils.isBlank(record.getCustomerCode())){
                throw new AppException(PasCode.DATA_ERROR.code,  "商户编号不能为空");
            }
            if(StringUtils.isBlank(record.getCustomerName())){
                throw new AppException(PasCode.DATA_ERROR.code,  "商户名称不能为空");
            }
//            if(!"1".equals(record.getAdjustType()) && !"2".equals(record.getAdjustType())){
//                throw new AppException(PasCode.DATA_ERROR.code,  "调额类型有误");
//            }
//            if(Objects.isNull(record.getAdjustAmout()) || record.getAdjustAmout()<0){
//                throw new AppException(PasCode.DATA_ERROR.code,  "调整金额有误");
//            }
//            if("1".equals(record.getAdjustType()) && !"BZFR".equals(record.getAdjustReason())){
//                throw new AppException(PasCode.DATA_ERROR.code,  "选择增额时，调额事由只能为：补增分润差额”");
//            }
//            if("2".equals(record.getAdjustType()) && !"KJSD".equals(record.getAdjustReason()) && !"KJFR".equals(record.getAdjustReason())){
//                throw new AppException(PasCode.DATA_ERROR.code,  "选择减额时，调额事由只能为：扣减发票税点、扣减分润差额”");
//            }
            dkteService.check(record,bindingResult);
            dkteService.update(record,userId);
            return CommonOuterResponse.success();
        } catch (Exception e) {
            logger.printMessage(e);
            logger.printMessage("待开调额记录修改错误：" + e.getMessage());
            if (e instanceof AppException) {
                return CommonOuterResponse.fail(((AppException) e).getErrorCode(), ((AppException) e).getErrorMsg());
            } else {
                return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
            }
        }
    }

    @RequestMapping(value = "/audit", method = RequestMethod.POST)
    @Exceptionable
    @Validatable
    @ApiOperation(value = "审核待开调额记录", notes = "审核待开调额记录", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dkteId", value = "记录ID", length = 32, required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "auditResult", value = "审核结果1：审核通过；2：审核不通过；",length = 4, required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "auditReason", value = "审核意见", length = 50,required = false, dataType = "String", paramType = "query"),
    })
    @Logable(businessTag = "DkteController.audit")
    public CommonOuterResponse audit(
            @RequestHeader(value = "x-user-type", required = false) String userType,
            @RequestHeader(value = "x-userid", required = false) String userId,
            @RequestParam(value = "dkteId",required = false)Long dkteId,
            @RequestParam(value = "auditResult",required = false)String auditResult,
            @RequestParam(value = "auditReason",required = false)String auditReason
    ){
        try {
            //该接口只支持运营门户
            if (!UserType.PAS_USER.code.equals(userType)) {
                throw new AppException(PasCode.CUSTOMER_TYPE_INVALID.code);
            }
            if(Objects.isNull(dkteId)){
                throw new AppException(PasCode.DATA_ERROR.code,  "记录ID不能为空");
            }
            if(StringUtils.isBlank(auditResult)){
                throw new AppException(PasCode.DATA_ERROR.code,  "审核结果不能为空");
            }
            if(!"1".equals(auditResult) && !"2".equals(auditResult)){
                throw new AppException(PasCode.DATA_ERROR.code,  "审核结果格式有误");
            }
            if("2".equals(auditResult) && StringUtils.isBlank(auditReason)){
                throw new AppException(PasCode.DATA_ERROR.code,  "审核不通过，需输入审核意见");
            }
            dkteService.audit(dkteId, auditResult, auditReason, userId);
            return CommonOuterResponse.success();
        } catch (Exception e) {
            logger.printMessage(e);
            logger.printMessage("待开调额记录审核错误：" + e.getMessage());
            if (e instanceof AppException) {
                return CommonOuterResponse.fail(((AppException) e).getErrorCode(), ((AppException) e).getErrorMsg());
            } else {
                return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
            }
        }
    }

    @PostMapping("/getFreezeRecord")
    @ApiOperation(value = "获取原冻结流水列表", notes = "获取原冻结流水列表", httpMethod = "POST")
    public CommonOuterResponse<List<List<DkteRecord>>> getFreezeRecord(@RequestParam(value = "customerNo",required = false) String customerNo,
                                               @RequestParam(value = "transactionNo") String transactionNo,
                                               @RequestHeader("x-userid") Long userId) {
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            List<DkteRecord> vos = dkteService.getFreezeRecord(customerNo,transactionNo);
            response = CommonOuterResponse.success(vos);
        } catch (Exception e) {
            logger.printMessage("获取原冻结流水信息错误：" + e.getMessage());
            logger.printMessage(e);
            if (e instanceof AppException) {
                return CommonOuterResponse.fail(((AppException) e).getErrorCode(), ((AppException) e).getErrorMsg());
            } else {
                return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
            }
        }
        return response;
    }
}
