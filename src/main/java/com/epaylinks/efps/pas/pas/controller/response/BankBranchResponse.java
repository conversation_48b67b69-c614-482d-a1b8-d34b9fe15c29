package com.epaylinks.efps.pas.pas.controller.response;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.pas.pas.controller.dto.BankBranchDTO;

import java.util.List;

public class BankBranchResponse extends CommonOuterResponse {

    private List<BankBranchDTO> list;

    public List<BankBranchDTO> getList() {
        return list;
    }

    public void setList(List<BankBranchDTO> list) {
        this.list = list;
    }
}
