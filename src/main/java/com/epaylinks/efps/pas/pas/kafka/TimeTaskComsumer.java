package com.epaylinks.efps.pas.pas.kafka;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.pas.common.PasConstants;
import com.epaylinks.efps.pas.pas.dao.PasTimeTaskRecordMapper;
import com.epaylinks.efps.pas.pas.domain.PasTimeTaskRecord;
import com.epaylinks.efps.pas.pas.service.PasTimeTaskService;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.KafkaListener;

import java.util.Date;
import java.util.Optional;

@Configuration
public class TimeTaskComsumer {

    @Autowired
    private PasTimeTaskRecordMapper taskRecordMapper;

    @Autowired
    private PasTimeTaskService pasTimeTaskService;

    @Logable(businessTag = "TIME_TASK")
    @KafkaListener(topics = {"TIME_TASK"}, containerFactory = "customContainerFactory")
    public void consumeMsg(ConsumerRecord<?, ?> record) {

        Optional<?> kafkaMessage = Optional.ofNullable(record.value());
        if (kafkaMessage.isPresent()) {
            String message = (String) kafkaMessage.get();

            //pasTimeTaskService.timtaskLog("接收到定时任务执行结果：" + message);

            Object obj = JSONObject.parse(message);
            JSONObject response = JSON.parseObject(obj.toString());

            try {
                //根据taskId查找record
                PasTimeTaskRecord taskRecord = taskRecordMapper.selectByPrimaryKey(response.getLong("taskId"));
                if (response.getString("returnCode").equalsIgnoreCase(CommonOuterResponse.SUCCEE)) {
                    taskRecord.setStatus(PasConstants.timeTaskRecordStatus.SUCCESS.code);
                } else {
                    taskRecord.setStatus(PasConstants.timeTaskRecordStatus.FAIL.code);
                }
                taskRecord.setReturnCode(response.getString("returnCode"));
                taskRecord.setReturnMessage(response.getString("returnMessage"));
                taskRecord.setIpAddress(response.getString("ip"));
                taskRecord.setUpdateTime(new Date());

                taskRecordMapper.updateByPrimaryKeySelective(taskRecord);
            } catch (Exception e) {
                pasTimeTaskService.timtaskLog("定时任务结果处理失败：" + response.getLong("taskId") + ",失败原因：" + e.getMessage());
            }
        }
    }
}
