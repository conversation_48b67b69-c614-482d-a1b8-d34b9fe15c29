package com.epaylinks.efps.pas.pas.controller.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
public class ManagementDataRequest {
    @ApiModelProperty(value = "图片id1",dataType = "String")
    private String uniqueId1;

    @ApiModelProperty(value = "图片名称1",dataType = "String")
    private String fileName1;

    @ApiModelProperty(value = "图片id2",dataType = "String")
    private String uniqueId2;

    @ApiModelProperty(value = "图片名称2",dataType = "String")
    private String fileName2;

    @ApiModelProperty(value = "备注",dataType = "String")
    private String remark;
}
