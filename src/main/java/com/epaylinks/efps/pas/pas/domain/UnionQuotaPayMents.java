package com.epaylinks.efps.pas.pas.domain;

import java.util.Date;

/**
 * 回款记录
 */
public class UnionQuotaPayMents {
    /**
     * 由本系统生成，作为交易流水号传输给上游，格式为HKYYYYMMDDHHMMSS+6位序列号
     */
    private String transactionNo;

    /**
     * 上游机构ID
     */
    private Long institutionId;

    /**
     * 机构代码
     */
    private String acqInsCode;

    /**
     * 头寸序号（以机构代码+头寸序号在银联
系统内对应的银行账户为准）
     */
    private String insSeq;

    /**
     * 付款方账号
     */
    private String payerAcctNo;

    /**
     * 付款方账户名称
     */
    private String payerAcctName;

    /**
     * 币种，156：人民币
     */
    private String currencyCode;

    /**
     * 回款金额，单位分
     */
    private Long amount;

    /**
     * 回款状态 00成功 01失败 02上游处理中 03初始化
     */
    private String state;

    /**
     * 结转用途
     * 描述：当交易类型为 06（回款模式一）且机构选择使用银联统一限额管理功能时，该字段必选出现
     * 取值：01：结转利息收入、02：结转手续费收入、03：结转预付费卡现金赎回资金、09：其他
     */
    private String cfdPurpose;

    /**
     */
    private String channelRespCode;

    /**
     */
    private String channelRespMsg;

    /**
     */
    private String channelExtend;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 回款类型，取值：自动回款AUTO;人工触发回款:MANUAL
     */
    private String srcType;

    /**
     * 回款日期，格式YYYYMMDD，为哪天执行的回款（银联记账日期 acctDate）
     */
    private String huikuanDate;

    /**
     * 说明
     */
    private String remark;

    /**
     * 手动回款的用户ID
     */
    private String userId;

    public String getTransactionNo() {
        return transactionNo;
    }

    public void setTransactionNo(String transactionNo) {
        this.transactionNo = transactionNo;
    }

    public Long getInstitutionId() {
        return institutionId;
    }

    public void setInstitutionId(Long institutionId) {
        this.institutionId = institutionId;
    }

    public String getAcqInsCode() {
        return acqInsCode;
    }

    public void setAcqInsCode(String acqInsCode) {
        this.acqInsCode = acqInsCode;
    }

    public String getInsSeq() {
        return insSeq;
    }

    public void setInsSeq(String insSeq) {
        this.insSeq = insSeq;
    }

    public String getPayerAcctNo() {
        return payerAcctNo;
    }

    public void setPayerAcctNo(String payerAcctNo) {
        this.payerAcctNo = payerAcctNo;
    }

    public String getPayerAcctName() {
        return payerAcctName;
    }

    public void setPayerAcctName(String payerAcctName) {
        this.payerAcctName = payerAcctName;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getChannelRespCode() {
        return channelRespCode;
    }

    public void setChannelRespCode(String channelRespCode) {
        this.channelRespCode = channelRespCode;
    }

    public String getChannelRespMsg() {
        return channelRespMsg;
    }

    public void setChannelRespMsg(String channelRespMsg) {
        this.channelRespMsg = channelRespMsg;
    }

    public String getChannelExtend() {
        return channelExtend;
    }

    public void setChannelExtend(String channelExtend) {
        this.channelExtend = channelExtend;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getSrcType() {
        return srcType;
    }

    public void setSrcType(String srcType) {
        this.srcType = srcType;
    }

    public String getHuikuanDate() {
        return huikuanDate;
    }

    public void setHuikuanDate(String huikuanDate) {
        this.huikuanDate = huikuanDate;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getCfdPurpose() {
        return cfdPurpose;
    }

    public void setCfdPurpose(String cfdPurpose) {
        this.cfdPurpose = cfdPurpose;
    }
}