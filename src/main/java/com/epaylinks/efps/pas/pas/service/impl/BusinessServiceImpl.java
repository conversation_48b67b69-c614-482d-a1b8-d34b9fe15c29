package com.epaylinks.efps.pas.pas.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.pas.pas.controller.dto.CustBusinessPriceDTO;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.epaylinks.efps.common.business.CodeNameResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.log.Logable.Level;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.pas.acl.po.User;
import com.epaylinks.efps.pas.acl.service.UserService;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.mch.dao.CustomerBusinessInfoMapper;
import com.epaylinks.efps.pas.mch.domain.CustomerBusinessInfo;
import com.epaylinks.efps.pas.mch.domain.CustomerBusinessInfoVO;
import com.epaylinks.efps.pas.mch.service.SplitCustomerService;
import com.epaylinks.efps.pas.pas.common.PasConstant;
import com.epaylinks.efps.pas.pas.dao.BusinessMapper;
import com.epaylinks.efps.pas.pas.domain.ApplyBusiness;
import com.epaylinks.efps.pas.pas.domain.BizPayMethod;
import com.epaylinks.efps.pas.pas.domain.Business;
import com.epaylinks.efps.pas.pas.service.BizPayMethodService;
import com.epaylinks.efps.pas.pas.service.BusinessService;
import com.epaylinks.efps.pas.pas.service.PayMethodService;
import com.epaylinks.efps.pas.pas.util.BigDecimalUtil;
import com.epaylinks.efps.pas.pas.vo.BusinessAndPayMethodVo;

/**
 * <AUTHOR> 5m
 * @Date : 2017/12/27 16:19
 * @Description :
 */
@Service
public class BusinessServiceImpl implements BusinessService {

    @Autowired
    BusinessMapper businessMapper;
    @Autowired
    CustomerBusinessInfoMapper customerBusinessInfoMapper;

    @Autowired
    private SplitCustomerService customerService;

    @Autowired
    PayMethodService payMethodService;

    @Autowired
    private SequenceService sequenceService;

    @Autowired
    BizPayMethodService bizPayMethodService;

    @Autowired
    UserService userService;

    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;

    public static final String PAS_TOPIC = "PAS_BasicData";

    public static final String ALL_BUSINESSES_KEY = "AllBusinesses";

    public static final String BUSINESS_ADD_KEY = "Business_Add";

    @Override
    @Transactional
    @Logable(businessTag = "deleteByPrimaryKey")
    public int deleteByPrimaryKey(Long id) {
        //删除 业务-支付方式关联表
        Business business = businessMapper.selectByPrimaryKey(id);
        BizPayMethod bm = new BizPayMethod();
        bm.setBusinessCode(business.getCode());
        bizPayMethodService.deleteBySelective(bm);

        return businessMapper.deleteByPrimaryKey(id);
    }

    @Override
    @Transactional
    @Logable(businessTag = "insert")
    public int insert(Business record, String payMethod) {
        BusinessAndPayMethodVo businessAndPayMethodVo = new BusinessAndPayMethodVo();
        List<String> payMethodList = new ArrayList<>();
        //生成业务代码 BC+7位顺延数字
        String code = businessMapper.selectMaxCode();
        if (StringUtils.isNotBlank(code)) {
            code = code.replace("BC", "");
            code = "BC" + StringUtils.leftPad((Integer.valueOf(code) + 1) + "", 7, "0");
        } else {
            code = "*********";
        }
        record.setCode(code);

        //插入 业务-支付方式关联表
        if (StringUtils.isNotBlank(payMethod)) {
            List<BizPayMethod> bizPayMethodList = new ArrayList<BizPayMethod>();
            String[] payMethods = payMethod.split(",");
            for (String method : payMethods) {
                BizPayMethod bizPayMethod = new BizPayMethod();
                bizPayMethod.setId(sequenceService.nextValue("pas"));
                bizPayMethod.setBusinessCode(code);
                bizPayMethod.setPayMethodCode(method);
                bizPayMethodList.add(bizPayMethod);
                //添加对应的支付方式编码以同步到客户管理系统
                payMethodList.add(method);
            }
            bizPayMethodService.addBizPayMethods(bizPayMethodList);
        }
        businessMapper.insert(record);
        //同步数据到客户管理
        businessAndPayMethodVo.setBusiness(record);
        businessAndPayMethodVo.setPayMethods(payMethodList);
        String data = JSON.toJSONString(businessAndPayMethodVo);
        try {
            kafkaTemplate.send(PAS_TOPIC, BUSINESS_ADD_KEY, data);
        } catch (Exception e) {
            // TODO: handle exception
            throw new AppException(PasCode.SEND_KAFKA_EXCEPTION.code, e);
        }
        return 1;
    }

    @Override
    @Logable(businessTag = "insertSelective", level = Level.DEBUG)
    public int insertSelective(Business record) {
        return businessMapper.insertSelective(record);
    }

    @Override
    @Logable(businessTag = "selectByPrimaryKey", level = Level.DEBUG)
    public Business selectByPrimaryKey(Long id) {
        return businessMapper.selectByPrimaryKey(id);
    }

    @Override
    @Transactional
    @Logable(businessTag = "updateByPrimaryKeySelective")
    public int updateByPrimaryKeySelective(Business record, String payMethod) {

        //删除 业务-支付方式关联表
        Business business = businessMapper.selectByPrimaryKey(record.getId());
        BizPayMethod bm = new BizPayMethod();
        bm.setBusinessCode(business.getCode());
        bizPayMethodService.deleteBySelective(bm);

        //插入 业务-支付方式关联表
        if (StringUtils.isNotBlank(payMethod)) {
            List<BizPayMethod> bizPayMethodList = new ArrayList<BizPayMethod>();
            String[] payMethods = payMethod.split(",");
            for (String method : payMethods) {
                BizPayMethod bizPayMethod = new BizPayMethod();
                bizPayMethod.setId(sequenceService.nextValue("pas"));
                bizPayMethod.setBusinessCode(business.getCode());
                bizPayMethod.setPayMethodCode(method);

                bizPayMethodList.add(bizPayMethod);
            }
            bizPayMethodService.addBizPayMethods(bizPayMethodList);
        }
        return businessMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    @Logable(businessTag = "updateByPrimaryKey", level = Level.DEBUG)
    public int updateByPrimaryKey(Business record) {
        return businessMapper.updateByPrimaryKey(record);
    }

    @Override
    @Logable(businessTag = "selectBySelective")
    public List<Business> selectBySelective(Business record) {

        List<Business> resultList = new ArrayList<Business>();
        List<Business> list = businessMapper.selectBySelective(record);
        for (Business business : list) {
            business.setPayMethodList(payMethodService.selectByBusinessCode(business.getCode()));
            resultList.add(business);
        }
        return resultList;
    }

    @Logable(businessTag = "selectByPage")
    @Override
    public List<Business> selectByPage(Map map) {
        List<Business> resultList = new ArrayList<Business>();
        List<Business> list = businessMapper.selectByPage(map);

        //获取所有userid号
        List<Long> useridList = new ArrayList<Long>();
        for (Business row : list) {
            if (StringUtils.isNotBlank(row.getCreator()) && !"null".equals(row.getCreator()) && !useridList.contains(Long.valueOf(row.getCreator()))) {
                useridList.add(Long.valueOf(row.getCreator()));
            }

            if (StringUtils.isNotBlank(row.getUpdator()) && !"null".equals(row.getUpdator()) && !useridList.contains(Long.valueOf(row.getUpdator()))) {
                useridList.add(Long.valueOf(row.getUpdator()));
            }

            row.setPayMethodList(payMethodService.selectByBusinessCode(row.getCode()));
        }

        if (useridList.size() > 0) {
            List<User> userList = userService.getUserByIds(useridList);
            HashMap<String, String> hp = new HashMap<String, String>();
            for (User userInfo : userList) {
                if (!hp.containsKey(userInfo.getUid())) {
                    hp.put(String.valueOf(userInfo.getUid()), userInfo.getName());
                }
            }

            for (Business row : list) {
                if (StringUtils.isNotBlank(row.getCreator()) && !"null".equals(row.getCreator())) {
                    row.setCreator(hp.get(row.getCreator()) != null ? hp.get(row.getCreator()) : "");
                }
                if (StringUtils.isNotBlank(row.getUpdator()) && !"null".equals(row.getUpdator())) {
                    row.setUpdator(hp.get(row.getUpdator()) != null ? hp.get(row.getUpdator()) : "");
                }
                resultList.add(row);
            }

            return resultList;

        } else {
            return list;
        }

    }


    @Logable(businessTag = "getBusinessByCodes", level = Level.DEBUG)
    @Override
    public List<Business> getBusinessByCodes(List<String> codes) {
        return businessMapper.getBusinessByCodes(codes);
    }

    @Override
    @Logable(businessTag = "optimal")
    public Business optimal(String payMethodCode, String amount) {

        Business business = null;

        //根据支付方式编码 获得对应业务ID list
        BizPayMethod bizPayMethod = new BizPayMethod();
        bizPayMethod.setPayMethodCode(payMethodCode);
        List<BizPayMethod> bizPayMethodList = bizPayMethodService.selectBySelective(bizPayMethod);
        List<String> codes = new ArrayList<>();
        if (bizPayMethodList.size() <= 0) {
            return business;
        } else {
            for (BizPayMethod method : bizPayMethodList) {
                if (!codes.contains(method.getBusinessCode())) {
                    codes.add(method.getBusinessCode());
                }
            }
        }

        //根据codes list获取业务model list
        List<Business> businessList = businessMapper.getBusinessByCodes(codes);

        if (businessList.size() <= 0) {
            return business;
        } else {
            //遍历业务model list，返回最优惠费率
            String last = "0";
            String rate;
            for (Business biz : businessList) {

                //状态-正常
                if (!biz.getState().equals(PasConstant.State.Normal.code)) {
                    continue;
                }

                rate = biz.calculationFee(amount);

                if (business == null) {
                    business = biz;
                    last = rate;
                } else {
                    if (Double.parseDouble(BigDecimalUtil.subIn(last, rate)) > 0) {
                        business = biz;
                        last = rate;
                    }
                }

            }
        }
        return business;

    }

    /**
     * 同步所有交易状态为正常的业务
     */
    @Logable(businessTag = "syncAllNormalBusiness")
    @Override
    public void syncAllNormalBusiness() {
        // TODO Auto-generated method stub
        List<Business> businesses = businessMapper.selectByState(PasConstant.State.Normal.code);
        String data = JSON.toJSONString(businesses);
        try {
            kafkaTemplate.send(PAS_TOPIC, ALL_BUSINESSES_KEY, data);
        } catch (Exception e) {
            // TODO: handle exception
            throw new AppException(PasCode.SEND_KAFKA_EXCEPTION.code, e);
        }
    }

    @Override
    public List<Business> getBusinessByCategory(String businessCategory) {
        // TODO Auto-generated method stub
        List<String> paramsList = new ArrayList<>();
        String[] strings = businessCategory.split(",");
        paramsList.addAll(Arrays.asList(strings));
        paramsList.add(Constants.BusinessCategory.EFPS_NOCARD_SERVICE.code);
        return businessMapper.getBusinessesByCatetory(paramsList);
    }

    @Override
    public List<String> getBusinessCodeByLabel(String businessLabel) {
        List<String> resultList = new ArrayList<String>();
        List<Business> list = businessMapper.selectByBusinessLabel(businessLabel);
        if (list != null) {
            list.forEach(business -> {
                resultList.add(business.getCode());
            });
        }
        return resultList;
    }

    public Business selectByCodeOrName(Map map) {
        return businessMapper.selectByCodeOrName(map);
    }

    //临时修改商户业务类别针对之前有些业务的业务类别为空的情况 1904暂未用未测
    @Logable(businessTag = "modifyBusinessCategory")
    public int modifyBusinessCategory() {
        List<CustomerBusinessInfoVO> list = customerBusinessInfoMapper.selectAllEmptyCategory();
        int i = 0;
        try {
            for (CustomerBusinessInfoVO info : list)        //查出待更新的数据
            {
                //查出商户该业务的类别
                if (info.getParentCustomerCode() != null && !info.getParentCustomerCode().equals("") && !info.getParentCustomerCode().equals("-") && info.getParentCustomerCode().length() == 16) {
                    CustomerBusinessInfo vo = customerService.getCustomerBusinessInfo(info.getParentCustomerCode(), info.getBusinessCode());
                    if (vo != null) {
                        CustomerBusinessInfo newVo = new CustomerBusinessInfo();
                        newVo.setBusinessId(info.getBusinessId());
                        newVo.setBusinessCode(info.getBusinessCode());
                        newVo.setBusinessCategoryCode(vo.getBusinessCategoryCode());
                        i += customerBusinessInfoMapper.updateByPkBusiness(newVo);
                    }
                } else {
                    List<String> blist = new ArrayList();
                    blist.add(info.getBusinessCode());
                    List<Business> ll = businessMapper.getBusinessByCodes(blist);
                    if (ll != null && ll.size() > 0) {
                        CustomerBusinessInfo newVo = new CustomerBusinessInfo();
                        newVo.setBusinessId(info.getBusinessId());
                        newVo.setBusinessCode(info.getBusinessCode());
                        newVo.setBusinessCategoryCode(ll.get(0).getBusinessCategory());
                        i += customerBusinessInfoMapper.updateByPkBusiness(newVo);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return i;
    }

    @Override
    public List<Business> getBusinessCatetoryByBusiness(String businessCode) {
        // TODO Auto-generated method stub
        return businessMapper.getBusinessCatetoryByBusiness(businessCode);
    }

    //查分组，模版下的业务
    public List<Business> selectBusinessByGroupOrTemplate(Map map) {
        return businessMapper.selectBusinessByGroupOrTemplate(map);
    }


    public List<Business> selectByState() {
        return businessMapper.selectByState(PasConstant.State.Normal.code);
    }

    @Override
    public List<ApplyBusiness> selectApplyBusinessByDisplayCode(String displayCode) {
        return businessMapper.selectApplyBusinessByDisplayCode(displayCode);
    }

    @Override
    public List<ApplyBusiness> selectApplyBusinessByCode(String code) {
        return businessMapper.selectApplyBusinessByCode(code);
    }

    @Override
    public List<Business> getBuseinssByScope(String scope) {
        return businessMapper.selectBusinessesByScope(scope);
    }

    @Override
    public PageResult<CodeNameResponse> fuzzyQuery(String keyword) {
        PageResult<CodeNameResponse> pageResult = new PageResult<>();
        keyword = com.epaylinks.efps.common.util.StringUtils.isEmpty(keyword) ? "" : keyword;
        List<Business> businessList = businessMapper.queryKeywords(keyword);
        if (null != businessList) {
            Map<String, String> data = businessList.stream().collect(Collectors.toMap(Business::getCode, Business::getName));
            List<CodeNameResponse> list = data.entrySet().stream().sorted(Map.Entry.<String, String>comparingByKey().reversed()).map(obj -> new CodeNameResponse(obj.getKey(), obj.getValue())).collect(Collectors.toList());
            pageResult.setRows(list);
        }
        pageResult.setTotal(businessList.size());
        pageResult.setCode(PageResult.SUCCEE);
        pageResult.setResult("匹配成功");
        return pageResult;
    }

    @Override
    public List<Business> selectByCodeList(List<String> codes) {
        return businessMapper.selectByCodeList(codes);
    }

    @Override
    public List<CustBusinessPriceDTO> selectCustBusinessPrice(Long customerId) {
        return businessMapper.selectCustBusinessPrice(customerId);
    }

    @Override
    public List<Business> selectAll() {
        return businessMapper.selectAll();
    }
}