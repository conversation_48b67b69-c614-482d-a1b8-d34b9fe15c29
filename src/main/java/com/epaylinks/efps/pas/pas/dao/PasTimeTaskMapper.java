package com.epaylinks.efps.pas.pas.dao;

import com.epaylinks.efps.pas.pas.domain.PasTimeTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

@Mapper
public interface PasTimeTaskMapper {
    int deleteByPrimaryKey(Long id);

    int insert(PasTimeTask record);

    int insertSelective(PasTimeTask record);

    PasTimeTask selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PasTimeTask record);

    int updateByPrimaryKey(PasTimeTask record);

    List<PasTimeTask> selectAll();

    PasTimeTask selectByJobName(String jobName);

    PasTimeTask findByJobNameForUpdate(String jobName);

    Integer switchJobPartition(Map map);
}