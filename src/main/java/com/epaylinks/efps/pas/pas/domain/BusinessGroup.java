package com.epaylinks.efps.pas.pas.domain;

import io.swagger.annotations.ApiModelProperty;

import java.util.List;

public class BusinessGroup {

    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column PAS_BUSINESS_GROUP.CODE
     *
     * @mbggenerated Mon May 06 13:18:13 CST 2019
     */
    private String code;
    private String parentCode;
    private String modelCode;

    private String businessCategoryCode;

    public String getBusinessCategoryCode() {
        return businessCategoryCode;
    }

    public void setBusinessCategoryCode(String businessCategoryCode) {
        this.businessCategoryCode = businessCategoryCode;
    }

    public String getModelCode() {
        return modelCode;
    }

    public void setModelCode(String modelCode) {
        this.modelCode = modelCode;
    }

    public String getParentCode() {
        return parentCode;
    }

    public void setParentCode(String parentCode) {
        this.parentCode = parentCode;
    }

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column PAS_BUSINESS_GROUP.NAME
     *
     * @mbggenerated Mon May 06 13:18:13 CST 2019
     */
    private String name;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column PAS_BUSINESS_GROUP.IS_TEMPLATE
     *
     * @mbggenerated Mon May 06 13:18:13 CST 2019
     */
    private String isTemplate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column PAS_BUSINESS_GROUP.REMARK
     *
     * @mbggenerated Mon May 06 13:18:13 CST 2019
     */
    private String remark;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column PAS_BUSINESS_GROUP.SORT_NO
     *
     * @mbggenerated Mon May 06 13:18:13 CST 2019
     */
    private Long sortNo;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column PAS_BUSINESS_GROUP.ID
     *
     * @return the value of PAS_BUSINESS_GROUP.ID
     * @mbggenerated Mon May 06 13:18:13 CST 2019
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column PAS_BUSINESS_GROUP.ID
     *
     * @param id the value for PAS_BUSINESS_GROUP.ID
     * @mbggenerated Mon May 06 13:18:13 CST 2019
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column PAS_BUSINESS_GROUP.CODE
     *
     * @return the value of PAS_BUSINESS_GROUP.CODE
     * @mbggenerated Mon May 06 13:18:13 CST 2019
     */
    public String getCode() {
        return code;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column PAS_BUSINESS_GROUP.CODE
     *
     * @param code the value for PAS_BUSINESS_GROUP.CODE
     * @mbggenerated Mon May 06 13:18:13 CST 2019
     */
    public void setCode(String code) {
        this.code = code;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column PAS_BUSINESS_GROUP.NAME
     *
     * @return the value of PAS_BUSINESS_GROUP.NAME
     * @mbggenerated Mon May 06 13:18:13 CST 2019
     */
    public String getName() {
        return name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column PAS_BUSINESS_GROUP.NAME
     *
     * @param name the value for PAS_BUSINESS_GROUP.NAME
     * @mbggenerated Mon May 06 13:18:13 CST 2019
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column PAS_BUSINESS_GROUP.IS_TEMPLATE
     *
     * @return the value of PAS_BUSINESS_GROUP.IS_TEMPLATE
     * @mbggenerated Mon May 06 13:18:13 CST 2019
     */
    public String getIsTemplate() {
        return isTemplate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column PAS_BUSINESS_GROUP.IS_TEMPLATE
     *
     * @param isTemplate the value for PAS_BUSINESS_GROUP.IS_TEMPLATE
     * @mbggenerated Mon May 06 13:18:13 CST 2019
     */
    public void setIsTemplate(String isTemplate) {
        this.isTemplate = isTemplate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column PAS_BUSINESS_GROUP.REMARK
     *
     * @return the value of PAS_BUSINESS_GROUP.REMARK
     * @mbggenerated Mon May 06 13:18:13 CST 2019
     */
    public String getRemark() {
        return remark;
    }


    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Long getSortNo() {
        return sortNo;
    }


    public void setSortNo(Long sortNo) {
        this.sortNo = sortNo;
    }

    //    @ApiModelProperty(value="结算周期 t0:T-RealTime,t1:T+1,d0:RealTime,d1:D+1", dataType = "String")
    private String settCycle;
    //    @ApiModelProperty(value="最高分润比例", dataType = "String")
    private String maxProfitProportion;


    public String getSettCycle() {
        return settCycle;
    }

    public void setSettCycle(String settCycle) {
        this.settCycle = settCycle;
    }

    public String getMaxProfitProportion() {
        return maxProfitProportion;
    }

    public void setMaxProfitProportion(String maxProfitProportion) {
        this.maxProfitProportion = maxProfitProportion;
    }

    private List<BusinessGroup> templateList;
    private List<Business> businessList;

    public List<BusinessGroup> getTemplateList() {
        return templateList;
    }

    public void setTemplateList(List<BusinessGroup> templateList) {
        this.templateList = templateList;
    }

    public List<Business> getBusinessList() {
        return businessList;
    }

    public void setBusinessList(List<Business> businessList) {
        this.businessList = businessList;
    }
}