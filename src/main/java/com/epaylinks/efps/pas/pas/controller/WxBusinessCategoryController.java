package com.epaylinks.efps.pas.pas.controller;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.pas.domain.WxBusinessCategory;
import com.epaylinks.efps.pas.pas.service.WxBusinessCategoryService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * 微信经营类目
 */
@RestController
@RequestMapping("WxBusinessCategory")
public class WxBusinessCategoryController {

    @Autowired
    private WxBusinessCategoryService wxBusinessCategoryService;

    /**
     * 原始数据导入from上游文档
     */
    @GetMapping("batchInsert")
    public void batchInsert() {
        String data = "DataWx.data";
        String[] arr = StringUtils.split(data, "\n");
        List<WxBusinessCategory> list = new ArrayList<>();
        for (String s : arr) {
            String[] a = StringUtils.split(s, " ");
            WxBusinessCategory r = new WxBusinessCategory();
            r.setId(Long.valueOf(a[4]));
            r.setParentNav(a[0]);
            r.setNav(a[1]);
            r.setCategorySys(a[2]);
            r.setCategory(a[3]);
            list.add(r);
        }
        wxBusinessCategoryService.batchInsert(list);
    }

    @GetMapping("queryLevel1s")
    @Logable(businessTag = "queryLevel1s")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "查询第一级类目", httpMethod = "GET")
    public CommonOuterResponse queryLevel1s() {
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            response.setData(wxBusinessCategoryService.queryLevel1s());
        } catch (Exception e) {
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
            return response;
        }
        return response;
    }

    @GetMapping("queryLevel2s")
    @Logable(businessTag = "queryLevel2s")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "根据第一级查询第二级类目", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "level1", value = "第一级类目", required = true, dataType = "String", paramType = "query")
    })
    public CommonOuterResponse queryLevel2s(@RequestParam String level1) {
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            response.setData(wxBusinessCategoryService.queryLevel2s(level1));
        } catch (Exception e) {
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
            return response;
        }
        return response;
    }

    @GetMapping("queryLevel3s")
    @Logable(businessTag = "queryLevel3s")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "根据第一、二级查询第三级类目", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "level1", value = "第一级类目", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "level2", value = "第二级类目", required = true, dataType = "String", paramType = "query")
    })
    public CommonOuterResponse queryLevel3s(@RequestParam String level1, @RequestParam String level2) {
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            response.setData(wxBusinessCategoryService.queryLevel3s(level1, level2));
        } catch (Exception e) {
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
            return response;
        }
        return response;
    }

    @GetMapping("queryLevel4s")
    @Logable(businessTag = "queryLevel4s")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "根据第一、二、三级查询第四级类目", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "level1", value = "第一级类目", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "level2", value = "第二级类目", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "level3", value = "第三级类目", required = true, dataType = "String", paramType = "query")
    })
    public CommonOuterResponse queryLevel4s(@RequestParam String level1, @RequestParam String level2, @RequestParam String level3) {
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            response.setData(wxBusinessCategoryService.queryLevel4s(level1, level2, level3));
        } catch (Exception e) {
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
            return response;
        }
        return response;
    }

    @GetMapping("getBusinessCategoryById")
    @Logable(businessTag = "getBusinessCategoryById")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "根据类目ID获取经营类目信息", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "类目ID", required = true, dataType = "String", paramType = "query")
    })
    public WxBusinessCategory getBusinessCategoryById(@RequestParam String id) {
        return wxBusinessCategoryService.getBusinessCategoryById(id);
    }
    
    @GetMapping("queryBusinessCategoryByUnionMcc")
    @Logable(businessTag = "queryBusinessCategoryByUnionMcc")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "根据银联MCC获取经营类目信息", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "unionMcc", value = "银联mCC", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "mccType", value = "类型：1：企业；2：个人/个体户；3：政府部门；4：其他组织", required = true, dataType = "Long", paramType = "query")
    })
    public CommonOuterResponse<WxBusinessCategory> queryBusinessCategoryByUnionMcc(
            @RequestParam String unionMcc,
            @RequestParam Long mccType) {
        
        try {
            List<WxBusinessCategory> list = wxBusinessCategoryService.queryBusinessCategoryByUnionMcc(unionMcc, mccType);
            /*if (list == null || list.isEmpty()) {// 由于银联mcc存在对应企业、个人同一个mcc可能，指定类型查无数据时，不指定重新查，
                list = wxBusinessCategoryService.queryBusinessCategoryByUnionMcc(unionMcc, null);
            }*/ // 由于展现企业或个体，与原商户类别不一致，所以注释该处理，只返回商户类型完全匹配的
            return CommonOuterResponse.success(list != null && !list.isEmpty() ? list.get(0) : null);
        } catch (AppException e) {
            return CommonOuterResponse.fail(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
        }
    }
    
}
