package com.epaylinks.efps.pas.pas.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.epaylinks.efps.common.business.pay.request.UserType;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.pas.acl.dao.RoleUserMapper;
import com.epaylinks.efps.pas.acl.dao.UserMapper;
import com.epaylinks.efps.pas.acl.po.User;
import com.epaylinks.efps.pas.common.LogService;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.pas.dao.LoginLogMapper;
import com.epaylinks.efps.pas.pas.domain.PasLoginInfo;
import com.epaylinks.efps.pas.pas.domain.PasLoginLog;
import com.epaylinks.efps.pas.pas.service.LoginInfoService;
import com.epaylinks.efps.pas.pas.service.LoginLogService;
import com.epaylinks.efps.pas.pas.vo.PasLoginLogJson;
import com.epaylinks.efps.pas.pas.vo.PasLoginLogReportResp;
import com.epaylinks.efps.pas.pas.vo.PasLoginLogReq;
import com.epaylinks.efps.pas.pas.vo.PasLoginLogResp;
import com.epaylinks.efps.pas.pas.vo.PasOperLogResp;

@Service
public class LoginLogServiceImpl implements LoginLogService {

	@Autowired
	private LoginLogMapper loginLogMapper;
	
	@Autowired
	private UserMapper userMapper;
	
	@Autowired
	private RoleUserMapper roleUserMapper ;
	
	@Autowired
	private LoginInfoService loginInfoService;
	
    @Autowired
    private SequenceService sequenceService;
    
    @Autowired 
    private LogService logService;

	 @Autowired
	 private KafkaTemplate<String, String> kafkaTemplate;
	    
	 //public static final String BUSINESSES_PAYMETHOD_KEY = "test";
	    
	 public static final String PAS_TOPIC = "user_action_log";
	
	
 	@Override
	public void syncAllBizPayMethod(String value) {
		
    	String data = value;
		try {
			kafkaTemplate.send(PAS_TOPIC, data);
			System.out.println("发送成功");
			
			//loginLogComsumer.consumeMsg(BUSINESSES_PAYMETHOD_KEY);
			
		} catch (Exception e) {
			// TODO: handle exception
			throw new AppException(PasCode.SEND_KAFKA_EXCEPTION.code, e);
		}
	}
	
 	
	@Override
	public PageResult<PasLoginLogResp> pageQuery(PasLoginLogReq pasLoginLogReq) {
		PageResult<PasLoginLogResp> pageResult = new PageResult<>();
		//根据条件查询总记录数
		int total = loginLogMapper.pageQueryTotal(pasLoginLogReq);
		List<PasLoginLogResp> logList = null;
		if(pasLoginLogReq.getDownload()!=null&&pasLoginLogReq.getDownload()==true){
			//下载
			if(UserType.PPS_USER.code.equals(pasLoginLogReq.getUsertype())){
				//商户平台
				logList = loginLogMapper.queryLoginLogForPpsNoPage(pasLoginLogReq);
			}else{
				//运营平台
				logList = loginLogMapper.queryLoginLogForPasNoPage(pasLoginLogReq);
			}
			
		}else{
			//根据条件查询当前页面数据
			
			if(UserType.PPS_USER.code.equals(pasLoginLogReq.getUsertype())){
				//商户平台
				logList = loginLogMapper.queryLoginLogForPps(pasLoginLogReq);
			}else{
				//运营平台
				logList = loginLogMapper.queryLoginLogForPas(pasLoginLogReq);
			}
			
		}

		pageResult.setRows(logList);
		pageResult.setTotal(total);
		return pageResult;
	}
	
	
	@Override
	public void savePasLoginLog(String loginName, String state, String loginIp, String remark, boolean pwdError) {
	    
        User user = userMapper.selectByName(loginName);
        String roleNames = "-";
        String realName = "-";
        if (user != null) {
            realName = user.getRealName();
            roleNames = roleUserMapper.selectRoleNameByUserId(user.getUid());
        }

	    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String dateStr = sdf.format(new Date());
        
	    PasLoginLogJson pasLoginLogJson =  new PasLoginLogJson();
        pasLoginLogJson.setPwdError(pwdError);
	    pasLoginLogJson.setLoginIp(loginIp);
	    pasLoginLogJson.setLoginTime(dateStr);
	    pasLoginLogJson.setRealName(realName);
	    pasLoginLogJson.setRole(roleNames);
	    pasLoginLogJson.setState(state);
	    pasLoginLogJson.setCustomerCode("-");
	    pasLoginLogJson.setPlatCustomer("-");
	    pasLoginLogJson.setServiceCustomer("-");
	    pasLoginLogJson.setRemark(remark);
	    pasLoginLogJson.setUserType(UserType.PAS_USER.code);
	    pasLoginLogJson.setUserName(loginName);
	    
	    this.saveLoginLog(pasLoginLogJson);
	}


	@Transactional
    @Override
    public void saveLoginLog(PasLoginLogJson pasLoginLogJson) {
        
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String date = sdf.format(new Date());
        
        try {
            PasLoginLog pasLoginLog = new PasLoginLog();
            pasLoginLog.setLog_id(sequenceService.nextValue("pas_login_log").toString());
            pasLoginLog.setUsername(pasLoginLogJson.getUserName());
            pasLoginLog.setUsertype(pasLoginLogJson.getUserType());
            pasLoginLog.setLrole(pasLoginLogJson.getRole());
            pasLoginLog.setRealname(pasLoginLogJson.getRealName());
            pasLoginLog.setCustomercode(pasLoginLogJson.getCustomerCode());
            pasLoginLog.setPlatcustomer(pasLoginLogJson.getPlatCustomer());
            pasLoginLog.setServicecustomer(pasLoginLogJson.getServiceCustomer());
            pasLoginLog.setLogin_time(pasLoginLogJson.getLoginTime());
            pasLoginLog.setLogin_ip(pasLoginLogJson.getLoginIp());
            pasLoginLog.setState(pasLoginLogJson.getState());
            pasLoginLog.setRemark(pasLoginLogJson.getRemark());
            pasLoginLog.setCreate_time(date);
            // 默认为登录日志
            pasLoginLog.setLogType(pasLoginLog.getLogType() == null ? new Short("1"):pasLoginLog.getLogType());

            loginLogMapper.insertLoginLog(pasLoginLog);

            PasLoginInfo pasLoginInfo = new PasLoginInfo();
            pasLoginInfo.setUsername(pasLoginLogJson.getUserName());
            pasLoginInfo.setUsertype(pasLoginLogJson.getUserType());
            pasLoginInfo.setLogin_time(pasLoginLogJson.getLoginTime());
            pasLoginInfo.setLogin_ip(pasLoginLogJson.getLoginIp());

            // 根据用户名和用户类型查询是否存在
            Integer pasLoginInfoCount = loginInfoService.checkPasLoginInfoExist(pasLoginInfo);

            if (null == pasLoginInfoCount) {
                // 新增
                pasLoginInfo.setInfo_id(sequenceService.nextValue("pas_login_info").toString());
                pasLoginInfo.setCreate_time(date);
                if ("0".equals(pasLoginLogJson.getState()) && pasLoginLogJson.isPwdError()) {
                    pasLoginInfo.setError_count("1"); // 密码错误的情况记录1次
                } else {
                    pasLoginInfo.setError_count("0");
                }
                loginInfoService.insertLoginInfo(pasLoginInfo);

            } else {
                // 修改
                if ("1".equals(pasLoginLogJson.getState())) { // 登录成功后清空
                    pasLoginInfo.setError_count("0");
                } else {
                    int newCount = pasLoginInfoCount;
                    if (pasLoginLogJson.isPwdError()) { // 密码错误的情况递增1次
                        newCount = pasLoginInfoCount + 1;
                    }
                    pasLoginInfo.setError_count(String.valueOf(newCount));
                }
                pasLoginInfo.setUpdate_time(date);
                loginInfoService.updateLoginInfo(pasLoginInfo);
            }

        } catch (Exception e) {
            logService.printLog(e);
        }
        
    }



    @Override
    public PageResult<PasOperLogResp> pageQueryOperLog(PasLoginLogReq pasLoginLogReq) {
        /* 操作记录统一以操作人所属商户为条件展示，包括员工与管理员；
                     代理商或平台商操作下级商户，下级商户看不到对应记录，只有代理商或平台商看到自己对下级商户的操作。
         */
        PageResult<PasOperLogResp> pageResult = new PageResult<>();
        //根据条件查询总记录数
        int total = loginLogMapper.pageQueryTotal(pasLoginLogReq);
        List<PasLoginLogResp> logList = null;
        if(pasLoginLogReq.getDownload()!=null && pasLoginLogReq.getDownload() == true){
            //下载
            if(UserType.PPS_USER.code.equals(pasLoginLogReq.getUsertype())){
                //商户平台
                logList = loginLogMapper.queryLoginLogForPpsNoPage(pasLoginLogReq);
            }
        } else{
            //根据条件查询当前页面数据
            if(UserType.PPS_USER.code.equals(pasLoginLogReq.getUsertype())){
                //商户平台
                logList = loginLogMapper.queryLoginLogForPps(pasLoginLogReq);
            }
        }
        
        List<PasOperLogResp> resultList = new ArrayList<PasOperLogResp>(); 
        if (logList != null) {
            logList.forEach(record->{
                PasOperLogResp vo = new PasOperLogResp();
                vo.setOperator(record.getRealname());
                vo.setOperIp(record.getLogin_ip());
                vo.setOperTime(record.getLogin_time());
                vo.setRealname(record.getRealname());
                vo.setRemark(record.getRemark());
                vo.setUsername(record.getUsername());
                
                resultList.add(vo);
            });
        }

        pageResult.setRows(resultList);
        pageResult.setTotal(total);
        return pageResult;
    }


    @Override
    public void saveOperLog(PasLoginLogJson pasLoginLogJson) {

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String date = sdf.format(new Date());
        
        try {
            PasLoginLog pasLoginLog = new PasLoginLog();
            pasLoginLog.setLog_id(sequenceService.nextValue("pas_login_log").toString());
            pasLoginLog.setUsername(pasLoginLogJson.getUserName());
            pasLoginLog.setUsertype(pasLoginLogJson.getUserType());
            pasLoginLog.setLrole(pasLoginLogJson.getRole());
            pasLoginLog.setRealname(pasLoginLogJson.getRealName());
            pasLoginLog.setCustomercode(pasLoginLogJson.getCustomerCode());
            pasLoginLog.setPlatcustomer(pasLoginLogJson.getPlatCustomer());
            pasLoginLog.setServicecustomer(pasLoginLogJson.getServiceCustomer());
            pasLoginLog.setLogin_time(pasLoginLogJson.getLoginTime());
            pasLoginLog.setLogin_ip(pasLoginLogJson.getLoginIp());
            pasLoginLog.setState(pasLoginLogJson.getState());
            pasLoginLog.setRemark(pasLoginLogJson.getRemark());
            pasLoginLog.setCreate_time(date);
            pasLoginLog.setLogType(new Short("2"));
            pasLoginLog.setTargetCustomercode(pasLoginLogJson.getTargetCustomercode());
            // 默认为登录日志

            loginLogMapper.insertLoginLog(pasLoginLog);

        } catch (Exception e) {
            logService.printLog(e);
        }
    }


    @Override
    public PageResult<PasLoginLogReportResp> pageQueryReport(PasLoginLogReq pasLoginLogReq) {
        PageResult<PasLoginLogReportResp> pageResult = new PageResult<>();
        //根据条件查询总记录数
        int total = loginLogMapper.countReport(pasLoginLogReq);
        if (total < 1) {
            pageResult.setTotal(total);
            return pageResult; 
        }
        
        List<PasLoginLogReportResp> logList = null;
        if (pasLoginLogReq.getDownload() != null && pasLoginLogReq.getDownload() == true){
            pasLoginLogReq.setStartNum(1);
            pasLoginLogReq.setEndNum(total);
        } 
        //根据条件查询当前页面数据
        //运营平台
        logList = loginLogMapper.pageQueryReport(pasLoginLogReq);

        pageResult.setRows(logList);
        pageResult.setTotal(total);
        return pageResult;
     }




}
