package com.epaylinks.efps.pas.pas.dao;

import com.epaylinks.efps.pas.pas.domain.UnionQuotaRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface UnionQuotaRecordMapper {

    UnionQuotaRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(UnionQuotaRecord record);

    int updateByPrimaryKey(UnionQuotaRecord record);

    int insert(UnionQuotaRecord record);

    int insertSelective(UnionQuotaRecord record);

    Integer selectByParam(Map Map);

    List<UnionQuotaRecord> selectByParamByPage(Map map);

    UnionQuotaRecord selectByTransactionNo(@Param("transactionNo") String transactionNo);
}