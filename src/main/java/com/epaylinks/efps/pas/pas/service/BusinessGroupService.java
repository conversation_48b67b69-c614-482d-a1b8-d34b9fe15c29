package com.epaylinks.efps.pas.pas.service;

import com.epaylinks.efps.pas.pas.domain.BusinessGroup;
import com.epaylinks.efps.pas.pas.domain.BusinessRoot;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 5m
 * @Date : 2017/12/27 14:36
 * @Description :
 */
public interface BusinessGroupService {


    public List<BusinessGroup> selectAllBusinssGroup(String isTemplate);

//    public List<BusinessGroupNode> selectBusinessGroupTree(Map map);

    public BusinessRoot getBasicTree();

    /**
     * 排除指定业务后的业务分组
     * @param source
     * @param excludeList
     * @return
     */
    public BusinessRoot getExcludeBasicTree(String source,String excludeList);

    public BusinessRoot getCustomerTree(Long customerId);

    public List<BusinessGroup> selectAllTemplateBusinssGroup(BusinessGroup record);


    /**
     * 根据业务获取业务分组、业务模板、业务属性
     *
     * @param businssCodes 业务编码
     * @return 业务分组数据
     */
    BusinessRoot getBasicTreeByBusinss(List<String> businssCodes);

}
