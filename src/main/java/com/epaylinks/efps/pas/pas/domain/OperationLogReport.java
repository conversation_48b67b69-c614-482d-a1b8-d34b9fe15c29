package com.epaylinks.efps.pas.pas.domain;

import com.epaylinks.efps.common.datadownload.annotation.FieldAnnotation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel
public class OperationLogReport {

    @ApiModelProperty( value = "用户名" , dataType = "String")
    @FieldAnnotation(fieldName = "用户名")
	private String userName; //用户名

    @ApiModelProperty( value = "真实名称" , dataType = "String")
    @FieldAnnotation(fieldName = "真实名称")
	private String realName; //真实名称

    @ApiModelProperty( value = "操作模块" , dataType = "String")
    @FieldAnnotation(fieldName = "操作模块")
	private String opModule; //操作模块

/*  @ApiModelProperty( value = "操作方式" , dataType = "String")
    @FieldAnnotation(fieldName = "操作方式")
	private String opMethod; //操作方式
*/
    
    @ApiModelProperty( value = "操作次数" , dataType = "String")
    @FieldAnnotation(fieldName = "操作次数")
    private String operCount; //操作次数
    
    @ApiModelProperty( value = "操作日期" , dataType = "String")
    @FieldAnnotation(fieldName = "操作日期")
    private String operDate; //操作日期
    
    

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getOpModule() {
        return opModule;
    }

    public void setOpModule(String opModule) {
        this.opModule = opModule;
    }

/*    public String getOpMethod() {
        return opMethod;
    }

    public void setOpMethod(String opMethod) {
        this.opMethod = opMethod;
    }*/

    public String getOperDate() {
        return operDate;
    }

    public void setOperDate(String operDate) {
        this.operDate = operDate;
    }

    public String getOperCount() {
        return operCount;
    }

    public void setOperCount(String operCount) {
        this.operCount = operCount;
    }


}
