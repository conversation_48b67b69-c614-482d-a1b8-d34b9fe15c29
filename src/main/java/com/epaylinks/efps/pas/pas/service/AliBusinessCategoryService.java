package com.epaylinks.efps.pas.pas.service;

import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.pas.pas.dao.AliBusinessCategoryMapper;
import com.epaylinks.efps.pas.pas.domain.AliBusinessCategory;
import com.epaylinks.efps.pas.pas.vo.CodeAndName;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AliBusinessCategoryService {

	@Autowired
	private AliBusinessCategoryMapper aliBusinessCategoryMapper;

	public void batchInsert(List<AliBusinessCategory> list) {
		aliBusinessCategoryMapper.batchInsert(list);
	}

	@Logable(businessTag = "queryLevel1s")
	public List<CodeAndName> queryLevel1s() {
		return aliBusinessCategoryMapper.selectLevel1s();
	}

	@Logable(businessTag = "queryLevel2s")
	public List<CodeAndName> queryLevel2s(String level1) {
		return aliBusinessCategoryMapper.selectLevel2s(level1);
	}

	@Logable(businessTag = "queryLevel3s")
	public List<AliBusinessCategory> queryLevel3s(String level1, String level2) {
		List<AliBusinessCategory> list = aliBusinessCategoryMapper.selectLevel3s(level1, level2);
		for (AliBusinessCategory r : list) {
			r.setCategoryCode(r.getLevel1Code() + "_" + r.getLevel2Code() + "_" + r.getLevel3Code());
		}
		return list;
	}

	@Logable(businessTag = "getBusinessCategoryByMcc")
    public AliBusinessCategory getBusinessCategoryByMcc(String mcc) {
		return aliBusinessCategoryMapper.selectByPrimaryKey(mcc);
    }
	
	@Logable(businessTag = "queryBusinessCategoryByUnionMcc")
    public List<AliBusinessCategory> queryBusinessCategoryByUnionMcc(String unionMcc) {
        return aliBusinessCategoryMapper.selectByUnionMcc(unionMcc);
    }

    public List<AliBusinessCategory> getAliBusinessCategoryByWord(String keyword) {
		return aliBusinessCategoryMapper.getAliBusinessCategoryByWord(keyword);
    }
}
