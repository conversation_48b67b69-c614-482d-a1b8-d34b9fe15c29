package com.epaylinks.efps.pas.pas.timetask.job;

import com.epaylinks.efps.common.timetask.TaskJob;
import com.epaylinks.efps.common.timetask.TaskRequest;
import com.epaylinks.efps.pas.acc.service.AcctQuotaRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class AcctQuotaSettleJob implements TaskJob {

    @Autowired
    private AcctQuotaRecordService acctQuotaRecordService;


    @Override
    public void execute(TaskRequest request) {
        acctQuotaRecordService.settle();
    }

}
