package com.epaylinks.efps.pas.pas.dao;

import com.epaylinks.efps.pas.pas.domain.DkteRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface DkteRecordMapper {
    /**
     * 获取主键ID
     * @return
     */
    Long selectIdFromSeq();

    int deleteByPrimaryKey(Long dkteId);

    int insert(DkteRecord record);

    int insertSelective(DkteRecord record);

    DkteRecord selectByPrimaryKey(Long dkteId);

    int updateByPrimaryKeySelective(DkteRecord record);

    int updateByPrimaryKey(DkteRecord record);

    List<DkteRecord> selectByPage(Map map);

    int selectCount(Map map);

    List<DkteRecord> queryOrigFreezeRecord(@Param("customerNo")String customerNo,@Param("origFreezeNo") String origFreezeNo);

    DkteRecord queryRecordByTransNo(Map map);

    Integer countRecordByOrigNo(Map map);

    Long countDkjeAmount(@Param("customerNo") String customerNo);
}