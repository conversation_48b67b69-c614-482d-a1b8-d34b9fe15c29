package com.epaylinks.efps.pas.pas.service;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.business.CommonResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.hessian.HessianService;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.pas.acl.service.CallUAAService;
import com.epaylinks.efps.pas.acl.service.SessionService;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.common.PasConstants;
import com.epaylinks.efps.pas.mch.service.feign.CustService;
import com.epaylinks.efps.pas.mch.service.feign.FsService;
import com.epaylinks.efps.pas.pas.controller.request.ManagementDataRequest;
import com.epaylinks.efps.pas.pas.controller.response.ManagementDataResponse;
import com.epaylinks.efps.pas.pas.controller.response.ManagementSendRecordResponse;
import com.epaylinks.efps.pas.pas.dao.ManagementDataMapper;
import com.epaylinks.efps.pas.pas.dao.ManagementPersonnelMapper;
import com.epaylinks.efps.pas.pas.dao.ManagementSendRecordMapper;
import com.epaylinks.efps.pas.pas.domain.*;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.MalformedURLException;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class ManagementDataService {
    @Autowired
    private ManagementDataMapper managementDataMapper;

    @Autowired
    private ManagementPersonnelMapper managementPersonnelMapper;

    @Autowired
    private ManagementSendRecordMapper managementSendRecordMapper;

    @Autowired
    private HessianService hessianService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private SessionService sessionService;

    @Autowired
    private CallUAAService callUAA;

    @Value("${managementData.hour:72}")
    private Long time;

    @Value(("${spring.profiles.active}"))
    private String env;

    @Value("${managementData.url:}")
    private String url;


    @Autowired
    private FsService fsService;

    @Autowired
    private CommonLogger logger;

    @Autowired
    private CustService custService;

    static final String FILE_TYPE_SHOW = "download";
    static final Integer EXPIREDTIME = 30;
    static final Integer MAX_ACCESSCOUNT = 100;

    private final SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");

    private final SimpleDateFormat format2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    public CommonOuterResponse uploadDetail(ManagementDataRequest request,Long userId) {
        if (StringUtils.isBlank(request.getUniqueId1()) && StringUtils.isBlank(request.getUniqueId2())) {
            throw new AppException(PasCode.ERROR_PARAM.code,PasCode.ERROR_PARAM.message + "：缺少附件");
        }
        ManagementData managementData = new ManagementData();
        managementData.setId(managementDataMapper.queryMDNextSeq());
        managementData.setUniqueId1(request.getUniqueId1());
        managementData.setUniqueId2(request.getUniqueId2());
        managementData.setSerialNum(getSerialSum("serialNum"));
        managementData.setOperator(userId);
        String remark = request.getRemark();
        if (StringUtils.isBlank(remark)) {
            remark = StringUtils.isBlank(request.getFileName1()) ? request.getFileName2() : request.getFileName1();
        }
        if (getByteLength(remark) > 60) {
            throw new AppException(PasCode.ERROR_PARAM.code,PasCode.ERROR_PARAM.message + "：长度超过限制");
        }
        managementData.setRemark(remark);
        managementData.setSendStatus(PasConstants.ManagementDataSendStatus.NOT_SENT.code);
        managementData.setCreateTime(new Date());
        managementDataMapper.insert(managementData);
        return CommonOuterResponse.success();
    }

    public CommonOuterResponse add(String receiverId,String name,String phone,Long userId) throws Exception {
        if (StringUtils.isBlank(receiverId) || StringUtils.isBlank(name) || StringUtils.isBlank(phone)) {
            throw new AppException(PasCode.MISSING_PARAMETER.code,PasCode.MISSING_PARAMETER.message);
        }
        if (receiverId.length() > 3) {
            throw new AppException(PasCode.DATA_ERROR.code,"人员ID不超过3位");
        }
        ManagementPersonnel record = managementPersonnelMapper.selectByReceiverId(receiverId);
        if (record != null) {
            throw new AppException(PasCode.DATA_ERROR.code,"人员ID已存在");
        }
        ManagementPersonnel personnel = new ManagementPersonnel();
        personnel.setId(managementPersonnelMapper.queryMPNextSeq());
        personnel.setReceiverId(receiverId);
        personnel.setReceiver(name);
        personnel.setPhone(getHiddenMobilePhone(phone));
        personnel.setPhoneEncrypt(hessianService.symmetricEncryptData(phone));
        personnel.setOperator(userId);
        personnel.setCreateTime(new Date());
        managementPersonnelMapper.insert(personnel);
        return CommonOuterResponse.success();
    }

    public CommonOuterResponse delete(Long id) {
        managementPersonnelMapper.deleteByPrimaryKey(id);
        return CommonOuterResponse.success();
    }

    public List<ManagementPersonnel> query() throws Exception {
        List<ManagementPersonnel> personnels = managementPersonnelMapper.selectPersonnelList();
        if (personnels.size() > 0) {
            for (int i = 0; i < personnels.size(); i++) {
                ManagementPersonnel managementPersonnel = personnels.get(i);
                String phone = hessianService.symmetricDecryptData(managementPersonnel.getPhoneEncrypt());
                managementPersonnel.setPhone(phone);
                personnels.set(i,managementPersonnel);
            }
        }
        return personnels;
    }

    public PageResult pageQuery(Map paramMap, Long userId) {
        PageResult pageResult = new PageResult<>();
        Integer count = managementDataMapper.countManageData(paramMap);
        List<ManagementDataResponse> managementDataList = managementDataMapper.pageQueryManageData(paramMap);
        pageResult.setTotal(count);
        pageResult.setRows(managementDataList);
        return pageResult;
    }

    public PageResult recordQuery(Map paramMap, Long userId) {
        PageResult pageResult = new PageResult<>();
        Integer count = managementSendRecordMapper.countManageSendRecord(paramMap);
        List<ManagementSendRecordResponse> managementSendRecordResponseList = managementSendRecordMapper.pageQuerySendRecord(paramMap);
        pageResult.setTotal(count);
        pageResult.setRows(managementSendRecordResponseList);
        return pageResult;
    }

    public CommonOuterResponse send(String personIds,Long dataId,Long userId) throws Exception {
        ManagementData managementData = managementDataMapper.selectByManagementData(dataId);
        if (managementData == null) {
            throw new AppException(PasCode.RECORD_NOT_EXIST.code,PasCode.RECORD_NOT_EXIST.message);
        }
        if (StringUtils.isBlank(managementData.getUniqueId1()) && StringUtils.isBlank(managementData.getUniqueId2())) {
            throw new AppException(PasCode.DATA_ERROR.code,PasCode.DATA_ERROR.message + "：缺少附件");
        }
        Date modifyDate = new Date();
        String[] persons = personIds.split(",");
        String sendNum = getSerialSum("sendNum"); // 今日发送次数
        for (int i = 0; i < persons.length; i++) {
            Long pId = Long.valueOf(persons[i]);
            ManagementPersonnel personnel = managementPersonnelMapper.selectByPrimaryKey(pId);
            if (personnel == null) {
                throw new AppException(PasCode.RECORD_NOT_EXIST.code,PasCode.RECORD_NOT_EXIST.message);
            }
            // 生成uid并加入Redis
            String randomString = getRandomString(6);
            String uid = "pas:management:" + randomString;
            ManagementDataSendVo vo = new ManagementDataSendVo();
            vo.setDataId(dataId);
            logger.printMessage("打印uid:" + randomString);
            String phone = hessianService.symmetricDecryptData(personnel.getPhoneEncrypt());
            vo.setPhone(phone);
            vo.setSendTime(format2.format(modifyDate));
            vo.setReceiverId(personnel.getReceiverId());
            vo.setWaterMark(sendNum);
            redisTemplate.opsForValue().set(uid,vo,time,TimeUnit.HOURS);
            // 发送短信 randomString
            String domain = "";
            if ("dev".equals(env)) {
                domain = "dev-";
            } else if ("test".equals(env)) {
                domain = "test-";
            }
            custService.sendManageData(phone,managementData.getRemark(),"https://" + domain + url + randomString);
            // 保存发送记录
            ManagementSendRecord record = new ManagementSendRecord();
            record.setId(managementSendRecordMapper.queryMSRNextSeq());
            record.setSendId(userId);
            record.setFileName(managementData.getRemark());
            record.setReceiver(personnel.getReceiver());
            record.setCreateTime(modifyDate);
            managementSendRecordMapper.insert(record);
        }
        // 更新发送时间
        managementData.setSendTime(modifyDate);
        managementData.setSendStatus(PasConstants.ManagementDataSendStatus.HAS_BEEN_SENT.code);
        managementDataMapper.updateByPrimaryKeySelective(managementData);

        return CommonOuterResponse.success();
    }

    public String getSendPhone(String uid) {
        Object obj = redisTemplate.opsForValue().get("pas:management:" + uid);
        if (obj == null) {
            throw new AppException(PasCode.STATUS_ERROR.code,PasCode.STATUS_ERROR.message + "：已过期");
        }
        ManagementDataSendVo sendVo = (ManagementDataSendVo) obj;
        return getHiddenMobilePhone(sendVo.getPhone());
    }

    public CommonOuterResponse sendMessage(String uid) {
        Object obj = redisTemplate.opsForValue().get("pas:management:" + uid);
        if (obj == null) {
            throw new AppException(PasCode.STATUS_ERROR.code,PasCode.STATUS_ERROR.message + "：已过期");
        }
        ManagementDataSendVo sendVo = (ManagementDataSendVo) obj;
//        return sessionService.sendSMSCaptcha(sendVo.getPhone(), PasConstants.serviceCode.MANAGEMENTDATA.code);
        return sessionService.sendSMS(sendVo.getPhone(), PasConstants.serviceCode.MANAGEMENTDATA.code,sendVo.getPhone(),"提取文件");
    }

    public CommonOuterResponse deleteAttach(Long dataId) {
        ManagementData managementData = managementDataMapper.selectByPrimaryKey(dataId);
        if (managementData == null) {
            throw new AppException(PasCode.RECORD_NOT_EXIST.code,PasCode.RECORD_NOT_EXIST.message);
        }
        managementData.setUniqueId1(null);
        managementData.setUniqueId2(null);
        managementData.setUpdateTime(new Date());
        managementDataMapper.updateByPrimaryKey(managementData);
        return CommonOuterResponse.success();
    }

    public CommonOuterResponse extract(String uid,String phone,String code,HttpServletResponse httpServletResponse) throws IOException {
        Object obj = redisTemplate.opsForValue().get("pas:management:" + uid);
        if (obj == null) {
            throw new AppException(PasCode.STATUS_ERROR.code,PasCode.STATUS_ERROR.message + "：已过期");
        }
        ManagementDataSendVo sendVo = (ManagementDataSendVo) obj;
        ManagementData managementData = managementDataMapper.selectByManagementData(sendVo.getDataId());
        if (managementData == null) {
            throw new AppException(PasCode.RECORD_NOT_EXIST.code,PasCode.RECORD_NOT_EXIST.message);
        }
        // 验证码校验
//        if (!PasConstants.SUCCESS.equals(callUAA.validateCode(sendVo.getPhone(),code,PasConstants.serviceCode.MANAGEMENTDATA.code))) {
//            throw new AppException(PasCode.VALIDATECODE_ERROR.code, PasCode.VALIDATECODE_ERROR.message);
//        }
        sessionService.checkSMSCode(sendVo.getPhone(),PasConstants.serviceCode.MANAGEMENTDATA.code,code);
        // 提取文件
        List<Map<String,String>> fileList = new ArrayList<>();
        String num = StringUtils.isBlank(sendVo.getWaterMark()) ? "01" : sendVo.getWaterMark();
        String markText = sendVo.getReceiverId() + "-" + format.format(new Date()) + num;
        if (StringUtils.isNotBlank(managementData.getUniqueId1())) {
            fileList.add(getUrlMap(managementData.getUniqueId1(),markText));
        }
        if (StringUtils.isNotBlank(managementData.getUniqueId2())) {
            fileList.add(getUrlMap(managementData.getUniqueId2(),markText));
        }
        if (fileList.isEmpty()) {
            throw new AppException(PasCode.DATA_ERROR.code,"文件已删除");
        }
        return CommonOuterResponse.success(fileList);
    }

    public void test(String uid,HttpServletResponse response) throws IOException {
        Object obj = redisTemplate.opsForValue().get("pas:management:" + uid);
        if (obj == null) {
            throw new AppException(PasCode.STATUS_ERROR.code,PasCode.STATUS_ERROR.message + "：已过期");
        }
        ManagementDataSendVo sendVo = (ManagementDataSendVo) obj;
        ManagementData managementData = managementDataMapper.selectByManagementData(sendVo.getDataId());
        if (managementData == null) {
            throw new AppException(PasCode.RECORD_NOT_EXIST.code,PasCode.RECORD_NOT_EXIST.message);
        }
        List<Map<String,String>> fileList = new ArrayList<>();
        String markText = sendVo.getReceiverId() + "-" + format.format(new Date()) + managementData.getSerialNum();
        if (StringUtils.isNotBlank(managementData.getUniqueId1())) {
            fileList.add(getUrlMap(managementData.getUniqueId1(),markText));
        }
        if (StringUtils.isNotBlank(managementData.getUniqueId2())) {
            fileList.add(getUrlMap(managementData.getUniqueId2(),markText));
        }
        downloadFile(response,fileList);
    }

    public ResponseEntity<List<InputStream>> downloadFiles(List<String> urlList) {
        List<InputStream> inputStreams = urlList.stream()
                .map(this::downloadFile)
                .collect(Collectors.toList()); // 获取输入流列表

        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_OCTET_STREAM) // 设置响应内容类型为二进制流
                .body(inputStreams); // 将输入流列表作为响应体返回给前端
    }

    public void downloadFile(HttpServletResponse response,List<Map<String,String>> fileMapList) throws IOException {
        BufferedInputStream bis = null;
        try {
            for (int i = 0; i < fileMapList.size(); i++) {
                Map<String,String> map = fileMapList.get(i);
                String filePath = map.get("filePath");
                String fileName = map.get("fileName");
                URL url = new URL(filePath);
                InputStream inputStream = url.openStream(); // 打开输入流
                response.setContentType("application/octet-stream;charset=utf-8"); // 设置响应内容类型为二进制流
                System.out.println("文件名称：" + url.getFile());
                response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\""); // 设置响应头为文件下载提示
                bis = new BufferedInputStream(inputStream);
                OutputStream outputStream = response.getOutputStream();
                byte[] buff = new byte[1024];
                int cache = bis.read(buff);
                while (cache != -1) {
                    outputStream.write(buff, 0, buff.length);
                    outputStream.flush();
                    cache = bis.read(buff);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (bis != null) {
                try {
                    bis.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

    }

    private InputStream downloadFile(String urlString) {
        try {
            URL url = new URL(urlString);
            return url.openStream();
        } catch (Exception e) {
            logger.printMessage("downloadFile错误:" + e.getMessage());
            logger.printLog(e);
        }
        return null;
    }

    /**
     * 生成序号，每日排序
     * @return
     */
    private String getSerialSum(String type) {
        Date date = new Date();
        Object obj = redisTemplate.opsForValue().get("pas:" + type + ":" + format.format(date));
        Integer serialNum = obj != null ? (Integer) obj : 0;
        ++serialNum;
        redisTemplate.opsForValue().set("pas:" + type + ":" + format.format(date),serialNum,24,TimeUnit.HOURS);
        if (serialNum < 10) {
            return "0" + serialNum;
        } else {
            return String.valueOf(serialNum);
        }
    }

    /**
     * 生成人员ID：3位
     * @return
     */
    private String getReceiverId() {
        String receiverId = managementPersonnelMapper.getReceiveId();
        Integer id = Integer.parseInt(receiverId);
        id++;
        if (id < 10) {
            return "00" + id;
        } else if (id >= 10 && id < 100) {
            return "0" + id;
        } else {
            return String.valueOf(id);
        }
    }

    /**
     * 手机号码脱敏
     * @param mobilePhone
     * @return
     */
    private String getHiddenMobilePhone(String mobilePhone) {
        return mobilePhone != null ? mobilePhone.replaceAll("^(.{3}).+(.{4})$", "$1****$2") : mobilePhone;
    }

    /**
     * 获取随机字符串
     * @param length
     * @return
     */
    private String getRandomString(int length) {
        String base = "0123456789abcdefghijklmnopqrstuvwxyzABCDENGHIJKLMNOPQRSTUVWXYZ";// abcdefghijklmnopqrstuvwxyz
        Random random = new Random();
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < length; i++) {
            int number = random.nextInt(base.length());
            sb.append(base.charAt(number));
        }
        return sb.toString();
    }

    private Map<String, String> getUrlMap(String uniqueId,String markText) {
        Map<String, String> map = fsService.filePath(uniqueId, EXPIREDTIME, MAX_ACCESSCOUNT, FILE_TYPE_SHOW,markText);
        if (!CommonResponse.SUCCEE.equals(map.get("resultCode"))) {
            throw new AppException(map.get("resultCode"), map.get("resultMsg"));
        }
        Map<String, String> resultMap = new HashMap<>();
        resultMap.put("filePath",map.get("filePath"));
        resultMap.put("fileName",map.get("fileName"));
        return resultMap;
    }

    private String getDownloadUrl(String uniqueId,String markText) {
        Map<String, String> map = fsService.filePath(uniqueId, EXPIREDTIME, MAX_ACCESSCOUNT, FILE_TYPE_SHOW,markText);
        if (!CommonResponse.SUCCEE.equals(map.get("resultCode"))) {
            throw new AppException(map.get("resultCode"), map.get("resultMsg"));
        }
        return map.get("filePath");
    }

    private Integer getByteLength(String param) {
        try {
            return param.getBytes("GBk").length;
        }catch (Exception e){
            System.out.println(e);
        }
        return 255;
    }
}
