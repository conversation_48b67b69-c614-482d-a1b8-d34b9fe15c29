package com.epaylinks.efps.pas.pas.service;

import com.epaylinks.efps.pas.pas.domain.ApplicationRecord;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 5m
 * @Date : 2017/12/28 17:03
 * @Description :
 */
public interface ApplicationRecordService {

    public int deleteByPrimaryKey(Long id);

    public int insert(ApplicationRecord record);

    public List<ApplicationRecord> selectByPage(Map map);

    public int insertSelective(ApplicationRecord record);

    public ApplicationRecord selectByPrimaryKey(Long id);

    public int updateByPrimaryKeySelective(ApplicationRecord record);

    public int updateByPrimaryKey(ApplicationRecord record);

    public List<ApplicationRecord> selectBySelective(ApplicationRecord record);
}
