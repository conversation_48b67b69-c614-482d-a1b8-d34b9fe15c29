package com.epaylinks.efps.pas.pas.domain;

import org.springframework.stereotype.Component;

import com.epaylinks.efps.common.datadownload.annotation.FieldAnnotation;

@Component
public class PasLoginLog {
	 private String log_id;
	 private String username;
	 private String usertype;
	 private String lrole;
	 private String realname;
	 private String customercode;
	 private String platcustomer;
	 private String servicecustomer;
	 private String login_time;
	 private String login_ip;
	 private String state;
	 private String remark;
	 private String create_time;
	 private Short logType;
	 private String targetCustomercode;
	 
	public String getLog_id() {
		return log_id;
	}
	public void setLog_id(String log_id) {
		this.log_id = log_id;
	}
	public String getUsername() {
		return username;
	}
	public void setUsername(String username) {
		this.username = username;
	}
	public String getUsertype() {
		return usertype;
	}
	public void setUsertype(String usertype) {
		this.usertype = usertype;
	}
	public String getLrole() {
		return lrole;
	}
	public void setLrole(String lrole) {
		this.lrole = lrole;
	}
	public String getRealname() {
		return realname;
	}
	public void setRealname(String realname) {
		this.realname = realname;
	}
	public String getCustomercode() {
		return customercode;
	}
	public void setCustomercode(String customercode) {
		this.customercode = customercode;
	}
	public String getPlatcustomer() {
		return platcustomer;
	}
	public void setPlatcustomer(String platcustomer) {
		this.platcustomer = platcustomer;
	}
	public String getServicecustomer() {
		return servicecustomer;
	}
	public void setServicecustomer(String servicecustomer) {
		this.servicecustomer = servicecustomer;
	}
	public String getLogin_time() {
		return login_time;
	}
	public void setLogin_time(String login_time) {
		this.login_time = login_time;
	}
	public String getLogin_ip() {
		return login_ip;
	}
	public void setLogin_ip(String login_ip) {
		this.login_ip = login_ip;
	}
	public String getState() {
		return state;
	}
	public void setState(String state) {
		this.state = state;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public String getCreate_time() {
		return create_time;
	}
	public void setCreate_time(String create_time) {
		this.create_time = create_time;
	}
    public Short getLogType() {
        return logType;
    }
    public void setLogType(Short logType) {
        this.logType = logType;
    }
    public String getTargetCustomercode() {
        return targetCustomercode;
    }
    public void setTargetCustomercode(String targetCustomercode) {
        this.targetCustomercode = targetCustomercode;
    }
	 
}
