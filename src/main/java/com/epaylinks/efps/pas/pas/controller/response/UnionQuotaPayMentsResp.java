package com.epaylinks.efps.pas.pas.controller.response;

import com.epaylinks.efps.common.datadownload.annotation.FieldAnnotation;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.util.Date;

public class UnionQuotaPayMentsResp {

    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    @FieldAnnotation(fieldName="回款时间", dateFormat="yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    @FieldAnnotation(fieldName="回款渠道")
    private String channelName = "EPSP";
    @FieldAnnotation(fieldName="回款流水号")
    private String transactionNo;
    @FieldAnnotation(fieldName="入金账户")
    private String insSeq;
    @FieldAnnotation(fieldName="出金虚拟账户")
    private String payerAcctNo;
    @FieldAnnotation(fieldName="回款金额（元）")
    private BigDecimal amount;
    @FieldAnnotation(fieldName="回款状态")
    private String state;
    @FieldAnnotation(fieldName="结转用途")
    private String cfdPurpose;
    @FieldAnnotation(fieldName="备注")
    private String remark;
    @FieldAnnotation(fieldName="原因")
    private String channelRespMsg;
    @FieldAnnotation(fieldName="操作人")
    private String userId;

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    public String getTransactionNo() {
        return transactionNo;
    }

    public void setTransactionNo(String transactionNo) {
        this.transactionNo = transactionNo;
    }

    public String getInsSeq() {
        return insSeq;
    }

    public void setInsSeq(String insSeq) {
        this.insSeq = insSeq;
    }

    public String getPayerAcctNo() {
        return payerAcctNo;
    }

    public void setPayerAcctNo(String payerAcctNo) {
        this.payerAcctNo = payerAcctNo;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getCfdPurpose() {
        return cfdPurpose;
    }

    public void setCfdPurpose(String cfdPurpose) {
        this.cfdPurpose = cfdPurpose;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getChannelRespMsg() {
        return channelRespMsg;
    }

    public void setChannelRespMsg(String channelRespMsg) {
        this.channelRespMsg = channelRespMsg;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }
}