package com.epaylinks.efps.pas.pas.domain;

import lombok.Data;

import java.util.Date;

@Data
public class InvoiceAudit {
    /**
     * ID
     */
    private Long invoiceId;

    /**
     * 易票联流水号
     */
    private String eplSerialNo;

    /**
     * 商户编号
     */
    private String customerNo;

    /**
     * 商户名称
     */
    private String customerName;

    /**
     * 发票类型
     */
    private String invoiceType;

    /**
     * 发票介质
     */
    private String invoiceMedium;

    /**
     * 发票编号
     */
    private String invoiceNo;

    /**
     * 开票时间
     */
    private Date invoiceTime;

    /**
     * 税率
     */
    private Integer taxRatio;

    /**
     * 发票金额
     */
    private Long invoiceAmount;

    /**
     * 附件ID
     */
    private String uniqueid;

    /**
     * 审核状态
     */
    private String auditStatus;

    /**
     * 审核意见
     */
    private String auditComment;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 来源
     */
    private String source;

    /**
     * 创建人ID
     */
    private Long creator;

    /**
     * 审核人
     */
    private Long reviewer;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 审核时间
     */
    private Date auditTime;

    private String reviewerName;

    private String creatorName;

    public Long getInvoiceId() {
        return invoiceId;
    }

    public void setInvoiceId(Long invoiceId) {
        this.invoiceId = invoiceId;
    }

    public String getEplSerialNo() {
        return eplSerialNo;
    }

    public void setEplSerialNo(String eplSerialNo) {
        this.eplSerialNo = eplSerialNo;
    }

    public String getCustomerNo() {
        return customerNo;
    }

    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getInvoiceType() {
        return invoiceType;
    }

    public void setInvoiceType(String invoiceType) {
        this.invoiceType = invoiceType;
    }

    public String getInvoiceMedium() {
        return invoiceMedium;
    }

    public void setInvoiceMedium(String invoiceMedium) {
        this.invoiceMedium = invoiceMedium;
    }

    public String getInvoiceNo() {
        return invoiceNo;
    }

    public void setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo;
    }

    public Date getInvoiceTime() {
        return invoiceTime;
    }

    public void setInvoiceTime(Date invoiceTime) {
        this.invoiceTime = invoiceTime;
    }

    public Integer getTaxRatio() {
        return taxRatio;
    }

    public void setTaxRatio(Integer taxRatio) {
        this.taxRatio = taxRatio;
    }

    public Long getInvoiceAmount() {
        return invoiceAmount;
    }

    public void setInvoiceAmount(Long invoiceAmount) {
        this.invoiceAmount = invoiceAmount;
    }

    public String getUniqueid() {
        return uniqueid;
    }

    public void setUniqueid(String uniqueid) {
        this.uniqueid = uniqueid;
    }

    public String getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(String auditStatus) {
        this.auditStatus = auditStatus;
    }

    public String getAuditComment() {
        return auditComment;
    }

    public void setAuditComment(String auditComment) {
        this.auditComment = auditComment;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public Long getCreator() {
        return creator;
    }

    public void setCreator(Long creator) {
        this.creator = creator;
    }

    public Long getReviewer() {
        return reviewer;
    }

    public void setReviewer(Long reviewer) {
        this.reviewer = reviewer;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(Date auditTime) {
        this.auditTime = auditTime;
    }
}