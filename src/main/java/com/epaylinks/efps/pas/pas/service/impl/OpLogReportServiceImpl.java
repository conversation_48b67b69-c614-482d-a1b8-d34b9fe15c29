package com.epaylinks.efps.pas.pas.service.impl;


import com.epaylinks.efps.common.business.CommonResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.pas.acl.dao.UserMapper;
import com.epaylinks.efps.pas.acl.po.User;
import com.epaylinks.efps.pas.acl.service.PermService;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.pas.dao.OpLogReportMapper;
import com.epaylinks.efps.pas.pas.domain.OpLogReport;
import com.epaylinks.efps.pas.pas.domain.OperationLogReport;
import com.epaylinks.efps.pas.pas.service.OpLogReportService;
import com.epaylinks.efps.pas.pas.util.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
public class OpLogReportServiceImpl implements OpLogReportService {

    private static final String PASS = "1";
    private static final String NOPASS = "2";
    private static final String NOPASS_MSG = "用户跨权限操作";

    @Autowired
    private OpLogReportMapper opLogReportMapper;

    @Autowired
    private UserMapper userDAO;

    @Autowired
    private CommonLogger logger;

    @Autowired
    private PermService permService;

    @Override
    public String checkOpLogReportByDate(String dateStr) {
        if(StringUtils.isBlank(dateStr)){
            dateStr = DateUtils.getBeforeDate2(-1);
        }
        logger.printMessage("开始审计指定日期的操作日志，传入日期参数："+dateStr);
        //日期校验
        String pattern = "\\d{4}-\\d{2}-\\d{2}";
        if(StringUtils.isBlank(dateStr) || !dateStr.matches(pattern)){
            throw new AppException(PasCode.ERROR_PARAM.code,  "指定日期为空或格式错误");
        }
        //已执行校验
        if(opLogReportMapper.queryOpAuditExist(dateStr)){
            throw new AppException(PasCode.DATA_ERROR.code,  "指定日期已进行过日志审计");
        }
        //查询符合条件的操作日志统计记录
        List<OperationLogReport> opList = opLogReportMapper.queryOperationByDate(dateStr);
        if(Objects.isNull(opList) || opList.isEmpty()){
            throw new AppException(PasCode.DATA_ERROR.code,  "指定日期操作日志统计数据为空");
        }
        //对每条统计记录进行审计
        for(OperationLogReport report : opList){
            //复制统计信息到审计中
            OpLogReport logReport = fillOpLogReport(report);
            //判断是否为超管用户
            User user = userDAO.selectByName(report.getUserName());
            if("0".equals(String.valueOf(user.getUid())) || permService.checkIsSuperAdmin(user.getUid())){
                //超管用户拥有所有权限，审计为正常，继续审计下一条
                logReport.setCheckState(PASS);
                opLogReportMapper.insert(logReport);
                logger.printMessage(report.getUserName()+"用户为超管，操作模块："+report.getOpModule()+"，审计为正常");
                continue;
            }
            //查询用户所有权限，查询模块对应权限，比对是否超出权限
            List<Long> permIds = permService.queryPermIdByUser(user.getUid());
            if(Objects.isNull(permIds) || permIds.isEmpty()){
                //查询用户权限为空，审计为异常
                logReport.setCheckState(NOPASS);
                logReport.setCheckMsg(NOPASS_MSG);
                opLogReportMapper.insert(logReport);
                logger.printMessage(report.getUserName()+"用户查询权限为空，操作模块："+report.getOpModule()+"，审计为异常");
                continue;
            }
            String modulePerm = opLogReportMapper.selectPermByModule(report.getOpModule());
            if(StringUtils.isBlank(modulePerm)){
                //根据操作模块找不到对应权限，忽略，审计为正常
                logReport.setCheckState(PASS);
                opLogReportMapper.insert(logReport);
                logger.printMessage(report.getUserName()+"用户，操作模块："+report.getOpModule()+"，无法映射权限，审计为正常");
                continue;
            }
            logger.printMessage(report.getUserName()+"用户，操作模块："+report.getOpModule()+"，映射权限："+modulePerm+"，用户权限："+permIds);
            boolean isOk = false;
            for(Long permId : permIds){
                if(modulePerm.indexOf(",")<0){  //映射单个权限
                    if(modulePerm.equals(String.valueOf(permId))){
                        isOk = true;
                        logger.printMessage(report.getUserName()+"用户，操作模块："+report.getOpModule()+"，包含映射权限："+modulePerm+"，审计为正常1");
                        break;
                    }
                }else {     //映射多个权限
                    String[] modulePerms = modulePerm.split(",");
                    for(String item : modulePerms){
                        if(item.equals(String.valueOf(permId))){
                            isOk = true;
                            logger.printMessage(report.getUserName()+"用户，操作模块："+report.getOpModule()+"，包含映射权限："+modulePerm+"，审计为正常2");
                            break;
                        }
                    }
                    if(isOk){
                        break;
                    }
                }
            }
            if(isOk){
                logReport.setCheckState(PASS);
                opLogReportMapper.insert(logReport);
            }else {
                logReport.setCheckState(NOPASS);
                logReport.setCheckMsg(NOPASS_MSG);
                opLogReportMapper.insert(logReport);
                logger.printMessage(report.getUserName()+"用户，操作模块："+report.getOpModule()+"，无权限，审计为异常");
            }
        }
        return "审计操作统计信息条数："+opList.size();
    }

    /**
     * 将日志统计数据填充到日志审计中
     * @param report
     * @return
     */
    private OpLogReport fillOpLogReport(OperationLogReport report){
        OpLogReport logReport = new OpLogReport();
        Long reportId = opLogReportMapper.selectIdFromSeq();
        logReport.setReportId(reportId);
        logReport.setUserName(report.getUserName());
        logReport.setRealName(report.getRealName());
        logReport.setOpModule(report.getOpModule());
        logReport.setOpNum(Integer.parseInt(report.getOperCount()));
        logReport.setOpTime(report.getOperDate());
        return  logReport;
    }

    @Override
    public com.epaylinks.efps.common.util.page.PageResult<OpLogReport> OperationLogReport(Map<String, Object> map) {

        com.epaylinks.efps.common.util.page.PageResult<OpLogReport> pageResult = new com.epaylinks.efps.common.util.page.PageResult<>();
        pageResult.setCode(CommonResponse.SUCCEE);
        pageResult.setResult(CommonResponse.SUCCEE);
        //根据条件查询总记录数
        int total = opLogReportMapper.countOperLogReport(map);
        if (total < 1) {
            pageResult.setTotal(total);
            return pageResult;
        }

        //根据条件查询当前页面数据
        List<OpLogReport> logList = opLogReportMapper.pageOperLogReport(map);

        pageResult.setRows(logList);
        pageResult.setTotal(total);
        return pageResult;
    }

    @Override
    public String logAudit(Long opId, String auditMsg, String auditUser) {
        OpLogReport logReport = opLogReportMapper.selectByPrimaryKey(opId);
        if(Objects.isNull(logReport)){
            throw new AppException(PasCode.ERROR_PARAM.code,  "记录不存在");
        }
        /*if(!NOPASS.equals(logReport.getCheckState())){
            throw new AppException(PasCode.ERROR_PARAM.code,  "只有异常状态的记录，才能处理异常");
        }*/
        /*if(StringUtils.isNotBlank(logReport.getAuditMsg())){
            throw new AppException(PasCode.ERROR_PARAM.code,  "已处理过的记录，不能重复处理");
        }*/
        logReport.setAuditMsg(auditMsg);
        logReport.setAuditName(auditUser);
        logReport.setAuditTime(new Date());
        int res = opLogReportMapper.updateByPrimaryKey(logReport);
        return "处理记录数："+res;
    }
}
