package com.epaylinks.efps.pas.mch.common;

import com.epaylinks.efps.pas.common.ExceptionUtil;

/**
 * Feign调用处理工具
 * <AUTHOR>
 */
public interface FeignUtil {
	
	/**
	 * Feign调用异常时的错误码获取
	 */
	public static String getFeignErrorCode(Exception e) {
		// 判断异常类型（系统异常、业务异常）
		boolean isSystemError = true;
		if ("feign.FeignException".equals(e.getClass().getName())) {// 业务方面的异常
			isSystemError = false;
		}
		// 定义错误码
		String errorCode = null;
		if (isSystemError) {
			errorCode = MchConstants.MchFeignError.CUM_ACCESS_EXCEPTION.code;
		} else {
			errorCode = ExceptionUtil.getErrorCode(e);
		}
		return errorCode;
	}
}
