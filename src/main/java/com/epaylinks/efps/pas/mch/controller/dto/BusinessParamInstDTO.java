package com.epaylinks.efps.pas.mch.controller.dto;

import com.alibaba.fastjson.JSON;

/**
 * 业务参数实例
 * <AUTHOR>
 *
 */
public class BusinessParamInstDTO {
	private String code;//业务参数编码
	private String name;//业务参数名称
	private String type;//业务参数取值类型
	private String value;//业务参数取值
	private String businessExamId;//业务实例id
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getValue() {
		return value;
	}
	public void setValue(String value) {
		this.value = value;
	}
	public String getBusinessExamId() {
		return businessExamId;
	}
	public void setBusinessExamId(String businessExamId) {
		this.businessExamId = businessExamId;
	}
	
	@Override
	public String toString() {
		// TODO Auto-generated method stub
		return JSON.toJSONString(this);
	}
}
