package com.epaylinks.efps.pas.mch.client;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import com.epaylinks.efps.common.config.DefaultFeignConfiguration;
import com.epaylinks.efps.pas.mch.client.model.Base64FileUploadRequest;
import com.epaylinks.efps.pas.mch.client.model.FileUploadResponse;

import java.util.Map;

@FeignClient(value = "FS", configuration = DefaultFeignConfiguration.class)
public interface FsClient {

    /**
     * 申请拿到上传文件的token
     * @param businessType 文件类型
     * @param fromSystemId 上传的文件来自哪个子系统，系统编号
     * @param needEncrypt 是否需加密，后续扩展使用，本期不使用，默认否.0：否 1：是
     * @param remark 文件描述信息
     * @return
     */
    @RequestMapping(value = "/UploadToekn", method = RequestMethod.POST)
    public String uploadToekn(@RequestParam("businessType") String businessType, @RequestParam("fromSystemId") String fromSystemId, @RequestParam("needEncrypt") String needEncrypt, @RequestParam("remark") String remark);

    /**
     * 上传文件
     * @param file 文件
     * @param uploadToken 文件上传token
     * @return
     */
    @RequestMapping(value = "/File", method = RequestMethod.POST)
    public FileUploadResponse fileUpload(@RequestParam(name = "file", required = true) MultipartFile file, @RequestParam("uploadToken") String uploadToken);

    @RequestMapping(value = "/File/BaseSixFour", method = RequestMethod.POST)
    public FileUploadResponse baseSixFour(@RequestBody(required = true) Base64FileUploadRequest base64Req);

    /**
     *  获取文件路径
     * @param uniqueId
     * @param expiredTime
     * @param maxAccessCount
     * @param type
     * @return
     */
    @PostMapping("/FilePath")
    Map<String,String> filePath(
            @RequestParam("uniqueId") String uniqueId,
            @RequestParam("expiredTime") Integer expiredTime,
            @RequestParam(defaultValue = "-1", required = false,value = "maxAccessCount") int maxAccessCount,
            @RequestParam("type") String type
    );

    /**
     * 上传文件（新）
     * @param file 文件
     * @param uploadToken 文件上传token
     * @return
     */
    @RequestMapping(value = "/File" , method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    FileUploadResponse uploadFile(
            @RequestPart("file")MultipartFile file ,
            @RequestParam("uploadToken")String uploadToken,
            @RequestParam("customerCode")String customerCode
    );

}