package com.epaylinks.efps.pas.mch.dao;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.epaylinks.efps.pas.mch.domain.PasSettCycleRuleInst;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface PasSettCycleRuleInstMapper {
    int deleteByPrimaryKey(Long id);

    int insert(PasSettCycleRuleInst record);

    PasSettCycleRuleInst selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PasSettCycleRuleInst record);

    int updateByPrimaryKey(PasSettCycleRuleInst record);
    
    List<PasSettCycleRuleInst> selectByBusinessInstDBId(Long businessInstDBId);

    String selectSettCycleCodeInSameBizCategory(@Param("customerInfoId") Long customerInfoId, @Param("businessCode") String businessCode);

	PasSettCycleRuleInst selectByBusinessInstDBIdAndDate(@Param("businessInstDBId")Long businessId, @Param("date")Date date);

    int deleteByBusinessExamId(@Param("businessExamId") String businessExamId);
}