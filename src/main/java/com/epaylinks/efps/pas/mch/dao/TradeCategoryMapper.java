package com.epaylinks.efps.pas.mch.dao;

import com.epaylinks.efps.pas.mch.domain.TradeCategory;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TradeCategoryMapper {

    int deleteByPrimaryKey(Long id);

    int insert(TradeCategory record);

    int insertSelective(TradeCategory record);

    TradeCategory selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TradeCategory record);

    int updateByPrimaryKey(TradeCategory record);

    List<TradeCategory> selectBySelective(TradeCategory record);
}