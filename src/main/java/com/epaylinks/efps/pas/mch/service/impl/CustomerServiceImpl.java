package com.epaylinks.efps.pas.mch.service.impl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.epaylinks.efps.pas.mch.dao.*;
import com.epaylinks.efps.pas.mch.domain.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.pas.acl.dao.UserMapper;
import com.epaylinks.efps.pas.acl.po.User;
import com.epaylinks.efps.pas.common.AreaCodeUtil;
import com.epaylinks.efps.pas.common.PasConstants;
import com.epaylinks.efps.pas.mch.client.CumClient;
import com.epaylinks.efps.pas.mch.client.model.CustomerObjects;
import com.epaylinks.efps.pas.mch.common.FeignUtil;
import com.epaylinks.efps.pas.mch.common.MchConstants;
import com.epaylinks.efps.pas.mch.common.MchError;
import com.epaylinks.efps.pas.mch.model.CustomerResult;
import com.epaylinks.efps.pas.mch.service.CustomerBusinessInfoService;
import com.epaylinks.efps.pas.mch.service.CustomerSettleInfoService;
import com.epaylinks.efps.pas.pas.common.PasConstant;
import com.epaylinks.efps.pas.pas.dao.BusinessParamInstMapper;
import com.epaylinks.efps.pas.pas.domain.BusinessParamInst;

/**
 * 业务逻辑层：客户控制
 *
 * <AUTHOR>
 */
@Service
@RefreshScope
public class CustomerServiceImpl {

    @Autowired
    private CustomerMapper customerMapper;
    @Autowired
    private CustomerInfoMapper customerInfoMapper;
    @Autowired
    private CustomerSettleInfoMapper customerSettleInfoMapper;
    @Autowired
    private CustomerBusinessInfoMapper customerBusinessInfoMapper;
    @Autowired
    private CustomerContactsInfoMapper customerContactsInfoMapper;
    @Autowired
    private CustomerStateUpdateMapper customerStateUpdateMapper;
    @Autowired
    private CustomerSyncInfoMapper customerSyncInfoMapper;
    @Autowired
    private CustomerAttachmentInfoMapper customerAttachmentInfoMapper;
    @Autowired
    private CustomerInfoServiceImpl customerInfoService;
    @Autowired
    private CustomerSettleInfoService customerSettleInfoService;
    @Autowired
    private CustomerBusinessInfoService customerBusinessInfoService;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private SequenceService sequenceService;
    @Autowired
    private CumClient cumClient;
    @Autowired
    private PasSettCycleRuleInstMapper pasSettCycleRuleInstMapper;

    @Value("${INIT_LOGIN_PSW_LEN}")
    private int INIT_LOGIN_PSW_LEN;

    private static final String ADDSETTCYCLERULEINSTCATEGORY = "addSettCycleRuleInst";
    @Autowired
    private BusinessParamInstMapper businessParamInstMapper;


    @Autowired
    private CustomerAuditInfoMapper customerAuditInfoMapper;
    /**
     * 客户信息: for ADD or UPDATE
     * 注：获取当前录入/修改的客户信息，如果不存在则新建一条缺省数据并返回
     */
    @Transactional
    @Logable(businessTag = "getCustomerInfoForAddOrUpdate")
    public CustomerInfo getCustomerInfoForAddOrUpdate(Long customerInfoId, Long curUserId) {
        if (customerInfoId != null) {
            // 修改：如果数据存在，则先克隆并返回
            return this.getCustomerForUpdate(customerInfoId);
        }
        // 获取当前正被录入的商户资料（两种情况：1.修改正被录入中的数据 2.完全新增）
        customerInfoId = customerMapper.findInputingCustomerInfoId(curUserId);
        if (customerInfoId != null) {
            // 根据customerInfoId查询录入客户信息for编辑
            return customerInfoMapper.selectByPrimaryKey(customerInfoId);
        } else {
            // 如果当前没有录入的客户，则新建一个缺省数据的客户并返回
            return this.insertDefaultCustomerInfo(curUserId,"运营门户");
        }
    }

    /**
     * 已审核的客户查询
     */
    @Logable(businessTag = "pageQueryCustomerInfos")
    public PageResult<CustomerResult> pageQueryCustomerInfos(int pageNum, int pageSize, String beginCreateTime, String endCreateTime,
                                                             String customerCode, String customerName, Long customerState, String businessType, Long settleState, String province, String city, String district,String sourceChannel) {
    	SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
    	Date beginTime = null;
    	Date endTime = null;
    	if (StringUtils.isNotBlank(beginCreateTime)) {
			try {
				beginTime = formatter.parse(beginCreateTime);
			} catch (ParseException e) {
				// TODO Auto-generated catch block
				throw new AppException(MchError.CUM_INVALID_PARAM.code);
			}
		}
		if (StringUtils.isNotBlank(endCreateTime)) {
			try {
				endTime = formatter.parse(endCreateTime);
			} catch (ParseException e) {
				// TODO Auto-generated catch block
				throw new AppException(MchError.CUM_INVALID_PARAM.code);
			}
		}
    	
    	String areaCodeQueryStr = AreaCodeUtil.getAreaCodeQueryStr(province, city, district);
        
        // 查询总记录数
        int total = customerMapper.countCustomerInfos(beginTime, endTime, customerCode, customerName, customerState, businessType, settleState, areaCodeQueryStr,sourceChannel);

        // 查询分页数据
        int beginRowNo = (pageNum - 1) * pageSize + 1;
        int endRowNo = pageNum * pageSize;
        List<CustomerResult> list = customerMapper.pageQueryCustomerInfos(beginRowNo, endRowNo, beginTime, endTime, customerCode, customerName, customerState, businessType, settleState, areaCodeQueryStr,sourceChannel);
        // 为商户信息关联最新状态更新信息
        this.setStatusUpdateInfoForMerchant(list);

        // 返回结果
        PageResult<CustomerResult> pagingResult = new PageResult<>();
        pagingResult.setTotal(total);
        pagingResult.setRows(list);
        return pagingResult;
    }

    /**
     * 修改客户状态（0：初始化，1：正常，2：冻结，3：审核中）
     */
    @Transactional
    @Logable(businessTag = "modifyCustomerState")
    public void modifyCustomerState(Long id, Long newState, String updateComment, Long curUserId) {
        // 旧数据
        Customer customer = customerMapper.selectByPrimaryKey(id);
        if (customer == null) {
            throw new AppException(MchError.CUM_NOT_EXIST.code);
        }
        Long oldState = Long.valueOf(customer.getState());
        if (newState.equals(oldState)) {
            throw new AppException(MchError.CUM_STATE_CONFLICT.code);
        }

        // 添加状态修改记录
        CustomerStateUpdate stateUpdateRecord = new CustomerStateUpdate();
        Long stateUpdateRecordId = sequenceService.nextValue("addMchStateUpdate");
        stateUpdateRecord.setUpdateId(stateUpdateRecordId);
        stateUpdateRecord.setCustomerId(id);
        stateUpdateRecord.setNewState(newState);
        stateUpdateRecord.setOldState(oldState);
        stateUpdateRecord.setOperateTime(new Date());
        stateUpdateRecord.setOperatorId(curUserId);
        stateUpdateRecord.setUpdateComment(updateComment);
        customerStateUpdateMapper.insert(stateUpdateRecord);

        // 更新商户记录
        customer.setState(newState.shortValue());
        customer.setNewestStateId(stateUpdateRecordId);
        customerMapper.updateByPrimaryKey(customer);

        // 同步数据到客户子系统（注意：这里先同步一次，if失败则留给定时任务执行；状态修改无需审核）
        this.syncCustomerState(null, stateUpdateRecord, customer.getCustomerCode());
    }

    /**
     * 同步客户信息
     */
    @Logable(businessTag = "syncCustomerInfo")
    public String syncCustomerInfo(CustomerSyncInfo syncInfo, Long customerInfoId, String operateType) {
        String result = Constants.SUCCESS;

        // 获取客户数据
        Customer customer = customerMapper.getByCustomerInfoId(customerInfoId);
        if (customer == null) {
            throw new AppException(MchError.CUM_NOT_EXIST.code);
        }
        CustomerInfo customerInfo = customerInfoService.getCustomerInfoById(customerInfoId);
        CustomerSettleInfo settleInfo = customerSettleInfoService.getCustomerSettleInfoByCustomerInfoId(customerInfoId);
        List<CustomerBusinessInfo> businessInfoList = customerBusinessInfoService.getCustomerBusinessInfos(customerInfoId);

        List<PasSettCycleRuleInst> pasSettCycleRuleInstReq = new ArrayList<>();
        for (CustomerBusinessInfo customerBusinessInfo : businessInfoList) {
            List<PasSettCycleRuleInst> pasSettCycleRuleInsts = pasSettCycleRuleInstMapper.selectByBusinessInstDBId(customerBusinessInfo.getBusinessId());
            pasSettCycleRuleInstReq.addAll(pasSettCycleRuleInsts);
        }

        CustomerContactsInfo contactsInfo = customerContactsInfoMapper.getByCustomerInfoId(customerInfoId);
        List<CustomerContactsInfo> contactsInfoList = new ArrayList<>();
        contactsInfoList.add(contactsInfo);
        List<CustomerAttachmentInfo> attachmentInfoList = customerAttachmentInfoMapper.getAttachmentByCustomerInfoId(customerInfoId);
        CustomerAgreementInfo agreementInfo = new CustomerAgreementInfo();
        agreementInfo.copyDataFromCustomer(customer);// 补充未对应数据（TODO 由于两系统之间不对应、临时这样处理）

        // 初始支付密码
        String initPayPwd = customer.getInitPayPsw();

        // 是否自动创建管理员用户、初始登录密码（明文）
        Long autoCreateUser = 0L;//TODO CUM那边需判断手机号是否有改变
        String initLoginPwd = null;
        if (MchConstants.CustomerOperateType.INFO_ADD.code.equals(operateType)) {
            autoCreateUser = 1L;
            // 初始登录密码
            initLoginPwd = customer.getInitLoginPsw();
        }

        // 设置同步对象
        CustomerObjects customerObjects = new CustomerObjects();
        customerObjects.setCode(customer.getCustomerCode());
        customerObjects.setStatus(Long.valueOf(customer.getState()));
        customerObjects.setPayPassword(initPayPwd);
        customerObjects.setBasicInfo(customerInfo);
        customerObjects.setCustomerSettmentInfo(settleInfo);
        customerObjects.setCustomerBusinessInfoList(businessInfoList);
        customerObjects.setCustomerContactsInfoList(contactsInfoList);
        customerObjects.setCustomerAttachmentInfoList(attachmentInfoList);
        customerObjects.setCustomerAgreementInfo(agreementInfo);
        customerObjects.setSettCycleRuleInsts(pasSettCycleRuleInstReq);
        // 同步数据
        try {
            cumClient.syncCustomers(customerObjects, autoCreateUser, initLoginPwd);
        } catch (Exception e) {
            String errorCode = FeignUtil.getFeignErrorCode(e);
            // 加入到“客户同步通知列表”for定时任务
            if (syncInfo == null) {
                syncInfo = new CustomerSyncInfo();
                Long syncId = sequenceService.nextValue("addMchSyncInfo");
                syncInfo.setSyncId(syncId);
                syncInfo.setCustomerCode(customer.getCustomerCode());
                syncInfo.setCreateTime(new Date());
                syncInfo.setSyncType(operateType);
                syncInfo.setRecordId(customerInfoId);
                syncInfo.setRetryCount(0L);
                syncInfo.setErrorCode(errorCode);
                customerSyncInfoMapper.insert(syncInfo);
            } else {
                syncInfo.setRetryCount(syncInfo.getRetryCount() + 1);
                syncInfo.setErrorCode(errorCode);
                customerSyncInfoMapper.updateByPrimaryKey(syncInfo);
            }
            result = Constants.FAIL;
        }
        return result;
    }

    /**
     * 同步客户状态
     */
    
    @Logable(businessTag = "syncCustomerState")
    public String syncCustomerState(CustomerSyncInfo syncInfo, CustomerStateUpdate stateUpdateRecord, String customerCode) {
        String result = Constants.SUCCESS;
        try {
            cumClient.updateCusStatus(customerCode, stateUpdateRecord.getOldState(), stateUpdateRecord.getNewState());
        } catch (Exception e) {
            String errorCode = FeignUtil.getFeignErrorCode(e);
            // 加入到“客户同步通知列表”for定时任务
            if (syncInfo == null) {
                syncInfo = new CustomerSyncInfo();
                Long syncId = sequenceService.nextValue("addMchSyncInfo");
                syncInfo.setSyncId(syncId);
                syncInfo.setCustomerCode(customerCode);
                syncInfo.setCreateTime(new Date());
                syncInfo.setSyncType(MchConstants.CustomerOperateType.STATE_UPDATE.code);
                syncInfo.setRecordId(stateUpdateRecord.getUpdateId());
                syncInfo.setRetryCount(0L);
                syncInfo.setErrorCode(errorCode);
                customerSyncInfoMapper.insert(syncInfo);
            } else {
                syncInfo.setRetryCount(syncInfo.getRetryCount() + 1);
                syncInfo.setErrorCode(errorCode);
                customerSyncInfoMapper.updateByPrimaryKey(syncInfo);
            }
            result = Constants.FAIL;
        }
        return result;
    }

    @Logable(businessTag = "insertDefaultCustomerInfo")
    private CustomerInfo insertDefaultCustomerInfo(Long curUserId,String sourceChannel) {
        // 首先，记录到“客户表”
        Customer customer = new Customer();
        Long customerId = sequenceService.nextValue("addMch");
        customer.setCustomerId(customerId);
        customer.setCreateTime(new Date());
        customer.setCreatorId(curUserId);
        customer.setState(Short.valueOf(MchConstants.CustomerState.INIT.code));
        customer.setSourceChannel(sourceChannel);//20190219 add
        customerMapper.insert(customer);

        // 新增客户信息
        CustomerInfo customerInfo = new CustomerInfo();
        Long customerInfoId = sequenceService.nextValue("addMchInfo");
        customerInfo.setInfoId(customerInfoId);
        customerInfo.setCustomerId(customerId);
        customerInfo.initDefault();
        //通过界面添加的客户，业务类别肯定是易票联客户
        customerInfo.setCustomerCategory(Constants.customerCategory.EFPS_CUSTOMER.code);
        customerInfoMapper.insert(customerInfo);
        
        // 新增客户结算信息
        CustomerSettleInfo settleInfo = new CustomerSettleInfo();
        Long settleInfoId = sequenceService.nextValue("addMchSettle");
        settleInfo.setSettleId(settleInfoId);
        settleInfo.setCustomerInfoId(customerInfoId);
        settleInfo.initDefault();
        customerSettleInfoMapper.insert(settleInfo);

        // 新增客户联系人信息
        CustomerContactsInfo contactsInfo = new CustomerContactsInfo();
        Long contactsInfoId = sequenceService.nextValue("addMchContactsInfo");
        contactsInfo.setContactId(contactsInfoId);
        contactsInfo.setCustomerInfoId(customerInfoId);
        contactsInfo.initDefault();
        customerContactsInfoMapper.insert(contactsInfo);

        // 保存成功后返回
        return customerInfoMapper.selectByPrimaryKey(customerInfoId);
    }

    @Logable(businessTag = "getCustomerForUpdate")
    public CustomerInfo getCustomerForUpdate(Long oldCustomerInfoId) {
        // 克隆并保存一份新版本的“客户信息”
        CustomerInfo customerInfo = customerInfoMapper.selectByPrimaryKey(oldCustomerInfoId);
        Long customerInfoId = sequenceService.nextValue("addMchInfo");
        customerInfo.setInfoId(customerInfoId);
        customerInfoMapper.insert(customerInfo);

        // 克隆结算信息
        CustomerSettleInfo settleInfo = customerSettleInfoMapper.getByCustomerInfoId(oldCustomerInfoId);
        if (settleInfo != null) {
            Long settleInfoId = sequenceService.nextValue("addMchSettle");
            settleInfo.setSettleId(settleInfoId);
            settleInfo.setCustomerInfoId(customerInfoId);
            customerSettleInfoMapper.insert(settleInfo);
        }

        // 克隆业务信息
        List<CustomerBusinessInfo> businessInfoList = customerBusinessInfoMapper.getByCustomerInfoId(oldCustomerInfoId);
        for (CustomerBusinessInfo businessInfo : businessInfoList) {
            Long oldBusinessId = businessInfo.getBusinessId();
            Long businessInfoId = sequenceService.nextValue("addMchBusiness");
            //克隆结算周期规则实例信息
            List<PasSettCycleRuleInst> pasSettCycleRuleInsts = pasSettCycleRuleInstMapper
                    .selectByBusinessInstDBId(oldBusinessId);
            for (PasSettCycleRuleInst pasSettCycleRuleInst : pasSettCycleRuleInsts) {
                pasSettCycleRuleInst.setId(sequenceService.nextValue(ADDSETTCYCLERULEINSTCATEGORY));
                pasSettCycleRuleInst.setBusinessInstDBId(businessInfoId);
                pasSettCycleRuleInstMapper.insert(pasSettCycleRuleInst);
            }
            List<BusinessParamInst> businessParamInsts = businessParamInstMapper
                    .selectByBusinessInstDBId(oldBusinessId);
            for (BusinessParamInst businessParamInst : businessParamInsts) {
                businessParamInst.setBusinessInstDBId(businessInfoId);
                businessParamInstMapper.insert(businessParamInst);
            }
            businessInfo.setBusinessId(businessInfoId);
            businessInfo.setCustomerInfoId(customerInfoId);
            customerBusinessInfoMapper.insert(businessInfo);
        }

        // 克隆联系人（管理员）信息
        CustomerContactsInfo contactsInfo = customerContactsInfoMapper.getByCustomerInfoId(oldCustomerInfoId);
        if (contactsInfo != null) {
            Long contactsInfoId = sequenceService.nextValue("addMchContactsInfo");
            contactsInfo.setContactId(contactsInfoId);
            contactsInfo.setCustomerInfoId(customerInfoId);
            customerContactsInfoMapper.insert(contactsInfo);
        }

        // 克隆附件信息
        List<CustomerAttachmentInfo> attachmentInfoList = customerAttachmentInfoMapper.getAttachmentByCustomerInfoId(oldCustomerInfoId);
        for (CustomerAttachmentInfo attachmentInfo : attachmentInfoList) {
            Long attachmentInfoId = sequenceService.nextValue("addMchAttachment");
            attachmentInfo.setAttachmentId(attachmentInfoId);
            attachmentInfo.setCustomerInfoId(customerInfoId);
            customerAttachmentInfoMapper.insert(attachmentInfo);
        }

        return customerInfoMapper.selectByPrimaryKey(customerInfoId);
    }

    /**
     * 为商户信息关联最新状态更新信息
     */
    private void setStatusUpdateInfoForMerchant(List<CustomerResult> mchInfoList) {
        List<Long> stateIdList = new ArrayList<>();
        for (CustomerResult x : mchInfoList) {
            if (x.getNewestStateId() != null) {
                stateIdList.add(x.getNewestStateId());
            }
        }
        if (stateIdList.size() == 0) {
            return;
        }
        // 查询 comment、操作人名字 并设置
        Map<Long, String> idAndCommentMap = new HashMap<>();
        Map<Long, Long> idAndOperatorIdMap = new HashMap<>();
        List<Long> userIdList = new ArrayList<>();
        List<CustomerStateUpdate> stateUpdateInfos = customerStateUpdateMapper.selectStateCommentsByIds(stateIdList);
        stateUpdateInfos.stream().forEach(x -> {
            idAndCommentMap.put(x.getUpdateId(), x.getUpdateComment());
            idAndOperatorIdMap.put(x.getUpdateId(), x.getOperatorId());
            userIdList.add(x.getOperatorId());
        });
        // 操作人名字需再关联查询
        Map<Long, String> userIdAndNameMap = new HashMap<>();
        if (userIdList.size() > 0) {
            List<User> users = userMapper.selectNamesByIds(userIdList);
            users.stream().forEach(x -> {
                userIdAndNameMap.put(x.getUid(), x.getName());
            });
        }

        for (CustomerResult x : mchInfoList) {
            if (idAndCommentMap.containsKey(x.getNewestStateId())) {
                x.setStateComment(idAndCommentMap.get(x.getNewestStateId()));

                Long userId = idAndOperatorIdMap.get(x.getNewestStateId());
                if (userIdAndNameMap.containsKey(userId)) {
                    x.setLastUpdator(userIdAndNameMap.get(userId));
                }
            }
        }
    }

    public List<Customer> getAllCustomer() {
        // TODO Auto-generated method stub
        return customerMapper.selectAll();
    }
    
    public Customer getCustomerByCustomerCode(String customerCode) {
    	return customerMapper.selectByCustomerCode(customerCode);
    }
    
    @Logable(businessTag = "updateCustomer")
    public void updateCustomer(Customer customer) {
    	customerMapper.updateByPrimaryKey(customer);
    }


    //同步客户资料，从CUST到cum
   public void  syncCustomerSyncInfo (CustomerSyncInfo syncInfo ){
        customerSyncInfoMapper.insert( syncInfo);
    }
    public void  updateByPrimaryKey (CustomerSyncInfo syncInfo ){
        customerSyncInfoMapper.updateByPrimaryKey( syncInfo);
    }

    @Logable(businessTag = "addCustomerFromCust")
    public void  addCustomerFromCust (String customerNo,String customerName ,Long customerInfoId,Long curUserId) {
        CustomerInfo customerInfo =  getCustomerInfoForAddOrUpdateCust(customerNo,customerInfoId,curUserId);
        // 更新商户记录  customercode 0219
        Customer customer = customerMapper.selectByPrimaryKey(customerInfo.getCustomerId());
        customer.setCustomerId(customerInfo.getCustomerId());
        customer.setCustomerCode(customerNo);
        customerMapper.updateByPrimaryKey(customer);

        //更新customername
        customerInfo.setName(customerName);
        customerInfoMapper.updateByPrimaryKey(customerInfo);

        // 删除商户待审核记录，只保留一条待审核信息。 add by fwy 2090505
        customerAuditInfoMapper.deleteWaitAuditInfosByCustomerId(customerInfo.getCustomerId());
        
        //生成待审核记录
        CustomerAuditInfo auditInfo = new CustomerAuditInfo();
        Long auditInfoId = sequenceService.nextValue("addMchAuditInfo");
        auditInfo.setAuditInfoId(auditInfoId);
        auditInfo.setCustomerInfoId(customerInfo.getInfoId());//customerInfoId
//        Customer customer = customerMapper.getByCustomerInfoId(customerInfoId);  //0219
        String operationType = (customer.getNewestCustomerInfoId() == null) ? MchConstants.CustomerOperateType.INFO_ADD.code : MchConstants.CustomerOperateType.INFO_MODIFY.code;
        auditInfo.setOperationType(operationType); //operationType
        auditInfo.setAuditStatus(MchConstants.CustomerInfoAuditState.WAIT_SECOND_AUDIT.code);
        auditInfo.setCreatorId(-1L);   //curUserId
        Date now = new Date();
        auditInfo.setCreateTime(now);
        auditInfo.setUpdateTime(now);
        customerAuditInfoMapper.insert(auditInfo);
    }

    @Transactional
    @Logable(businessTag = "getCustomerInfoForAddOrUpdateCust")
    public CustomerInfo getCustomerInfoForAddOrUpdateCust(String customerNo, Long customerInfoId, Long curUserId) {
        Customer customer = customerMapper.selectByCustomerCode(customerNo);
        if (customer!=null && customer.getNewestCustomerInfoId() != null) {  //已正常在用的
            customerInfoId = customer.getNewestCustomerInfoId();
        } else   if (customer!=null && customer.getNewestCustomerInfoId() == null)
        {
           List<CustomerInfo> list     =customerInfoMapper.selectListByCustomerId(customer.getCustomerId());
            if(list!=null && list.size()>0) {
                CustomerInfo customerInfo = list.get(0);
                customerInfoId = customerInfo.getInfoId();
            }
        }
        if (customerInfoId != null) {
            // 修改：如果数据存在，则先克隆并返回
            return this.getCustomerForUpdate(customerInfoId);
        }
        // 获取当前正被录入的商户资料（两种情况：1.修改正被录入中的数据 2.完全新增）
        customerInfoId = customerMapper.findInputingCustomerInfoId(curUserId);
        if (customerInfoId != null) {
            // 根据customerInfoId查询录入客户信息for编辑
            return customerInfoMapper.selectByPrimaryKey(customerInfoId);
        } else {
            // 如果当前没有录入的客户，则新建一个缺省数据的客户并返回
            return this.insertDefaultCustomerInfo(curUserId, Constants.SourceChannel.UNION_YUN.code);   //商户来源 1:EPSP平台录入，2:EPSP接口,3:云闪付开放平台，4:旧系统
        }
    }

}