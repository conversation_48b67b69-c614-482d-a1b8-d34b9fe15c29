package com.epaylinks.efps.pas.mch.client.model;

/**
 * 描述 ：
 *
 * <AUTHOR>
 * @date 2018/1/5.
 */
public class FileUploadResponse {
    /**
     * 返回码
     * 0000 表示成功
     * 0001 : 文件不能为空
     * 0002 : 参数错误
     * 其他表示失败，由研发同学定义
     */
    private String resultCode;
    private String resultMsg;
    /**
     * 成功时必填，该文件的唯一ID
     */
    private String uniqueId;

    public FileUploadResponse(String resultCode, String resultMsg) {
        this.resultCode = resultCode;
        this.resultMsg = resultMsg;
    }

    public FileUploadResponse() {
    }

    public String getResultCode() {
        return resultCode;
    }

    public void setResultCode(String resultCode) {
        this.resultCode = resultCode;
    }

    public String getResultMsg() {
        return resultMsg;
    }

    public void setResultMsg(String resultMsg) {
        this.resultMsg = resultMsg;
    }

    public String getUniqueId() {
        return uniqueId;
    }

    public void setUniqueId(String uniqueId) {
        this.uniqueId = uniqueId;
    }
}
