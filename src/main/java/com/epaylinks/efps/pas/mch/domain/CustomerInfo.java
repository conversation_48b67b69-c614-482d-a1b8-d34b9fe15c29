package com.epaylinks.efps.pas.mch.domain;

import java.util.Date;

public class CustomerInfo {
	public enum CustomerType
	{
		GE_TI(10,"个体商户"),
		QI_YE(20,"企业客户"),
		QU_DAO(30,"渠道客户"),
		NEI_BU(40,"内部客户"),
		XIAO_WEI(50,"小微客户");
		
		private CustomerType(Integer code,String comment)
		{
			this.code = code;
			this.comment = comment;
		}
		public Integer code;
		public String comment;
	};
	
    private Long infoId;

    private Long customerId;

    private String customerCode;

    private String name;

    private String shortName;

    private String mobile;

    private String telephone;

    private String businessAddress;

    private Integer useUscc;

    private Integer customerType;

    private String businessLicenseNo;

    private Date businessLicenseExpDate;

    private String registeredAddress;

    private String natTaxRegCerNo;

    private Date natTaxRegExpDate;

    private String locTaxRegCerNo;

    private Date locTaxRegCerExpDate;

    private String orgStructureCode;

    private String leaPersonName;

    private Short leaPersoniDentificationType;

    private String leaPersoniDentificationNo;

    private Date leaPerDenExpDate;

    private String parentCustomerCode;

    private String webAddress;

    private String icpLicenseNo;

    private String businessScope;

    private String industryDescription;

    private Integer employeeCount;

    private String businessType;

    private String areaCode;

    private Long registeredCapital;

    private String tradeCategory;
    
    private String notifyUrl;

    private String customerCategory;
    /**
     * 签约模式，0-商户签约，1-平台签约
     */
    private String signModel;
    
    private String billMode; // 账单模式：0：结算日模式；1：自然日模式
    
    
    public String getSignModel() {
		return signModel;
	}

	public void setSignModel(String signModel) {
		this.signModel = signModel;
	}
    
    public CustomerInfo() {}

	public void initDefault() {
		this.useUscc = 0;
		this.customerType = 0;
		this.businessLicenseExpDate = new Date();
		this.registeredCapital = 0L;
		this.leaPersoniDentificationType = 0;
		this.areaCode = "000000";
		this.tradeCategory = "0000000000000000";
	}

    public Long getInfoId() {
        return infoId;
    }

    public void setInfoId(Long infoId) {
        this.infoId = infoId;
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getShortName() {
        return shortName;
    }

    public void setShortName(String shortName) {
        this.shortName = shortName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getBusinessAddress() {
        return businessAddress;
    }

    public void setBusinessAddress(String businessAddress) {
        this.businessAddress = businessAddress;
    }

    public Integer getUseUscc() {
        return useUscc;
    }

    public void setUseUscc(Integer useUscc) {
        this.useUscc = useUscc;
    }

    public Integer getCustomerType() {
        return customerType;
    }

    public void setCustomerType(Integer customerType) {
        this.customerType = customerType;
    }

    public String getBusinessLicenseNo() {
        return businessLicenseNo;
    }

    public void setBusinessLicenseNo(String businessLicenseNo) {
        this.businessLicenseNo = businessLicenseNo;
    }

    public Date getBusinessLicenseExpDate() {
        return businessLicenseExpDate;
    }

    public void setBusinessLicenseExpDate(Date businessLicenseExpDate) {
        this.businessLicenseExpDate = businessLicenseExpDate;
    }

    public String getRegisteredAddress() {
        return registeredAddress;
    }

    public void setRegisteredAddress(String registeredAddress) {
        this.registeredAddress = registeredAddress;
    }

    public String getNatTaxRegCerNo() {
        return natTaxRegCerNo;
    }

    public void setNatTaxRegCerNo(String natTaxRegCerNo) {
        this.natTaxRegCerNo = natTaxRegCerNo;
    }

    public Date getNatTaxRegExpDate() {
        return natTaxRegExpDate;
    }

    public void setNatTaxRegExpDate(Date natTaxRegExpDate) {
        this.natTaxRegExpDate = natTaxRegExpDate;
    }

    public String getLocTaxRegCerNo() {
        return locTaxRegCerNo;
    }

    public void setLocTaxRegCerNo(String locTaxRegCerNo) {
        this.locTaxRegCerNo = locTaxRegCerNo;
    }

    public Date getLocTaxRegCerExpDate() {
        return locTaxRegCerExpDate;
    }

    public void setLocTaxRegCerExpDate(Date locTaxRegCerExpDate) {
        this.locTaxRegCerExpDate = locTaxRegCerExpDate;
    }

    public String getOrgStructureCode() {
        return orgStructureCode;
    }

    public void setOrgStructureCode(String orgStructureCode) {
        this.orgStructureCode = orgStructureCode;
    }

    public String getLeaPersonName() {
        return leaPersonName;
    }

    public void setLeaPersonName(String leaPersonName) {
        this.leaPersonName = leaPersonName;
    }

    public Short getLeaPersoniDentificationType() {
        return leaPersoniDentificationType;
    }

    public void setLeaPersoniDentificationType(Short leaPersoniDentificationType) {
        this.leaPersoniDentificationType = leaPersoniDentificationType;
    }

    public String getLeaPersoniDentificationNo() {
        return leaPersoniDentificationNo;
    }

    public void setLeaPersoniDentificationNo(String leaPersoniDentificationNo) {
        this.leaPersoniDentificationNo = leaPersoniDentificationNo;
    }

    public Date getLeaPerDenExpDate() {
        return leaPerDenExpDate;
    }

    public void setLeaPerDenExpDate(Date leaPerDenExpDate) {
        this.leaPerDenExpDate = leaPerDenExpDate;
    }

    public String getParentCustomerCode() {
        return parentCustomerCode;
    }

    public void setParentCustomerCode(String parentCustomerCode) {
        this.parentCustomerCode = parentCustomerCode;
    }

    public String getWebAddress() {
        return webAddress;
    }

    public void setWebAddress(String webAddress) {
        this.webAddress = webAddress;
    }

    public String getIcpLicenseNo() {
        return icpLicenseNo;
    }

    public void setIcpLicenseNo(String icpLicenseNo) {
        this.icpLicenseNo = icpLicenseNo;
    }

    public String getBusinessScope() {
        return businessScope;
    }

    public void setBusinessScope(String businessScope) {
        this.businessScope = businessScope;
    }

    public String getIndustryDescription() {
        return industryDescription;
    }

    public void setIndustryDescription(String industryDescription) {
        this.industryDescription = industryDescription;
    }

    public Integer getEmployeeCount() {
        return employeeCount;
    }

    public void setEmployeeCount(Integer employeeCount) {
        this.employeeCount = employeeCount;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public Long getRegisteredCapital() {
        return registeredCapital;
    }

    public void setRegisteredCapital(Long registeredCapital) {
        this.registeredCapital = registeredCapital;
    }

    public String getTradeCategory() {
        return tradeCategory;
    }

    public void setTradeCategory(String tradeCategory) {
        this.tradeCategory = tradeCategory;
    }

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }

	public String getCustomerCategory() {
		return customerCategory;
	}

	public void setCustomerCategory(String customerCategory) {
		this.customerCategory = customerCategory;
	}

	public String getBillMode() {
		return billMode;
	}

	public void setBillMode(String billMode) {
		this.billMode = billMode;
	}
	
	
    
}