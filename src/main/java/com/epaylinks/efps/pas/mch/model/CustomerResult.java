package com.epaylinks.efps.pas.mch.model;

import java.util.Date;

public class CustomerResult {

    private Long customerId;
    
    private Long customerInfoId;
    
    private String customerCode;

    private String customerName;
    
    private String businessType;

    private Long settleState;

    private String areaCode;
    
    private Date createTime;
    
    /**
     * 最新状态ID
     */
    private Long newestStateId;
    
	/**
     * 最新状态修改人名字
     */
    private String lastUpdator;

    /**
     * 最新状态
     */
    private String customerState;
    
    /**
     * 最新状态修改注释
     */
    private String stateComment;

	private String sourceChannel;//商户来源

    public String getSourceChannel() {
        return sourceChannel;
    }

	public void setSourceChannel(String sourceChannel) {
		this.sourceChannel = sourceChannel;
	}

	public CustomerResult() {
		
	}
	
	public CustomerResult(Long customerId, Long customerInfoId, String customerCode, String customerName, String businessType, Long settleState,
			String areaCode, Date createTime, Long newestStateId, String customerState, String lastUpdator, String stateComment) {
		this.customerId = customerId;
		this.customerInfoId = customerInfoId;
		this.customerCode = customerCode;
		this.customerName = customerName;
		this.businessType = businessType;
		this.settleState = settleState;
		this.areaCode = areaCode;
		this.createTime = createTime;
		this.newestStateId = newestStateId;
		this.customerState = customerState;
		this.lastUpdator = lastUpdator;
		this.stateComment = stateComment;
	}

	public Long getCustomerId() {
		return customerId;
	}

	public void setCustomerId(Long customerId) {
		this.customerId = customerId;
	}
	
	public Long getCustomerInfoId() {
		return customerInfoId;
	}

	public void setCustomerInfoId(Long customerInfoId) {
		this.customerInfoId = customerInfoId;
	}

	public String getCustomerCode() {
		return customerCode;
	}

	public void setCustomerCode(String customerCode) {
		this.customerCode = customerCode;
	}

	public String getCustomerName() {
		return customerName;
	}

	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	public String getBusinessType() {
		return businessType;
	}

	public void setBusinessType(String businessType) {
		this.businessType = businessType;
	}

	public Long getSettleState() {
		return settleState;
	}

    public Long getNewestStateId() {
		return newestStateId;
	}

	public void setNewestStateId(Long newestStateId) {
		this.newestStateId = newestStateId;
	}

	public void setSettleState(Long settleState) {
		this.settleState = settleState;
	}

	public String getAreaCode() {
		return areaCode;
	}

	public void setAreaCode(String areaCode) {
		this.areaCode = areaCode;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getLastUpdator() {
		return lastUpdator;
	}

	public void setLastUpdator(String lastUpdator) {
		this.lastUpdator = lastUpdator;
	}

	public String getCustomerState() {
		return customerState;
	}

	public void setCustomerState(String customerState) {
		this.customerState = customerState;
	}

	public String getStateComment() {
		return stateComment;
	}

	public void setStateComment(String stateComment) {
		this.stateComment = stateComment;
	}
}
