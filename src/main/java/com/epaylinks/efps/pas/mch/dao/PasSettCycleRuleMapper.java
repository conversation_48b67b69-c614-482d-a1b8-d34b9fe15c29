package com.epaylinks.efps.pas.mch.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.epaylinks.efps.pas.mch.domain.PasSettCycleRule;
@Mapper
public interface PasSettCycleRuleMapper {
    int deleteByPrimaryKey(Long id);

    int insert(PasSettCycleRule record);

    int insertSelective(PasSettCycleRule record);

    PasSettCycleRule selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PasSettCycleRule record);

    int updateByPrimaryKey(PasSettCycleRule record);
    
    PasSettCycleRule selectByTypeAndParamsAndStatus(@Param("type")String type , 
    		@Param("params") String parmas , @Param("status") String status);
    
    PasSettCycleRule selectByTypeAndStatus(@Param("type")String type , 
    		@Param("status") String status);
    
    PasSettCycleRule selectByCode(@Param("code")String code);
    
    List<PasSettCycleRule> selectAll();
    
    List<PasSettCycleRule> selectRuleByStatus(@Param("status") String status);
    
}