package com.epaylinks.efps.pas.mch.client.model;

/**
* <AUTHOR> :Liuq 
* @date 创建时间：2017年9月13日 下午7:21:45 
* @version 添加客户信息model
* @parameter   
* @return  
*/

import java.util.List;

import com.alibaba.fastjson.JSON;
import com.epaylinks.efps.pas.mch.domain.CustomerAgreementInfo;
import com.epaylinks.efps.pas.mch.domain.CustomerAttachmentInfo;
import com.epaylinks.efps.pas.mch.domain.CustomerBusinessInfo;
import com.epaylinks.efps.pas.mch.domain.CustomerContactsInfo;
import com.epaylinks.efps.pas.mch.domain.CustomerInfo;
import com.epaylinks.efps.pas.mch.domain.CustomerSettleInfo;
import com.epaylinks.efps.pas.mch.domain.PasSettCycleRuleInst;

public class CustomerObjects {
	
	//客户编码
	public String code;
	
	//状态 0：预开户 1：正常 2：锁定 3：注销  4：加入黑名单
	public Long status;
	
	//初始支付密码，明文
	public String payPassword;
	
	//支付密码过期日期，YYYYMMDD，不填表示永不过期
	public String payPasswordExpiredDate;
	
	//客户基本信息
	public CustomerInfo basicInfo;
	
	//客户结算信息
	public CustomerSettleInfo customerSettmentInfo;
	
	//联系人列表
	public List<CustomerContactsInfo> customerContactsInfoList;
	
	//开通业务列表
	public List<CustomerBusinessInfo> customerBusinessInfoList;
	
	//客户附件信息列表
	public List<CustomerAttachmentInfo> customerAttachmentInfoList;
	
	//企业客户协议信息
	public CustomerAgreementInfo customerAgreementInfo;

	//客户的结算周期实例
	private List<PasSettCycleRuleInst> settCycleRuleInsts;
	
	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public Long getStatus() {
		return status;
	}

	public void setStatus(Long status) {
		this.status = status;
	}

	public String getPayPassword() {
		return payPassword;
	}

	public void setPayPassword(String payPassword) {
		this.payPassword = payPassword;
	}

	public String getPayPasswordExpiredDate() {
		return payPasswordExpiredDate;
	}

	public void setPayPasswordExpiredDate(String payPasswordExpiredDate) {
		this.payPasswordExpiredDate = payPasswordExpiredDate;
	}

	public CustomerInfo getBasicInfo() {
		return basicInfo;
	}

	public void setBasicInfo(CustomerInfo basicInfo) {
		this.basicInfo = basicInfo;
	}

	public CustomerSettleInfo getCustomerSettmentInfo() {
		return customerSettmentInfo;
	}

	public void setCustomerSettmentInfo(CustomerSettleInfo customerSettmentInfo) {
		this.customerSettmentInfo = customerSettmentInfo;
	}

	public List<CustomerContactsInfo> getCustomerContactsInfoList() {
		return customerContactsInfoList;
	}

	public void setCustomerContactsInfoList(List<CustomerContactsInfo> customerContactsInfoList) {
		this.customerContactsInfoList = customerContactsInfoList;
	}

	public List<CustomerBusinessInfo> getCustomerBusinessInfoList() {
		return customerBusinessInfoList;
	}

	public void setCustomerBusinessInfoList(List<CustomerBusinessInfo> customerBusinessInfoList) {
		this.customerBusinessInfoList = customerBusinessInfoList;
	}

	public List<CustomerAttachmentInfo> getCustomerAttachmentInfoList() {
		return customerAttachmentInfoList;
	}

	public void setCustomerAttachmentInfoList(List<CustomerAttachmentInfo> customerAttachmentInfoList) {
		this.customerAttachmentInfoList = customerAttachmentInfoList;
	}

	public CustomerAgreementInfo getCustomerAgreementInfo() {
		return customerAgreementInfo;
	}

	public void setCustomerAgreementInfo(CustomerAgreementInfo customerAgreementInfo) {
		this.customerAgreementInfo = customerAgreementInfo;
	}

	public List<PasSettCycleRuleInst> getSettCycleRuleInsts() {
		return settCycleRuleInsts;
	}

	public void setSettCycleRuleInsts(List<PasSettCycleRuleInst> settCycleRuleInsts) {
		this.settCycleRuleInsts = settCycleRuleInsts;
	}

	@Override
	public String toString() {
		// TODO Auto-generated method stub
		return JSON.toJSONString(this);
	}
}