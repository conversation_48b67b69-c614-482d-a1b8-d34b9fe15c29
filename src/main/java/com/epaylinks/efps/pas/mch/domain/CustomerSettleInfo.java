package com.epaylinks.efps.pas.mch.domain;

public class CustomerSettleInfo {
    private Long settleId;

    private Long customerInfoId;

    private Long customerId;

    private String customerCode;

    private Integer circle;

    private Integer transferMoneyDay;

    private Integer riskPrematurity;

    private Short target;

    private String currency;

    private Short pauseSett;

    private Short bankAccountType;

    private String bankCode;

    private String bankLineNumber;

    private String openAccountBankName;

    private String bankAccountNo;

    private String customerNameInBank;

    private String settleMode;

    private Long startAmount;

    private String bankReservedMobile;

    private String bankReservedLpidNo;

    private String areaCode;
    
    public CustomerSettleInfo() {}

	public void initDefault() {
		this.bankAccountType = 1;
		this.transferMoneyDay = 0;
		/**
		 * 初始化客户的结算周期为D+1
		 */
		this.circle = 1;
		this.riskPrematurity = 0;
		this.target = 0;
		this.pauseSett = 0;
	}

    public Long getSettleId() {
        return settleId;
    }

    public void setSettleId(Long settleId) {
        this.settleId = settleId;
    }

    public Long getCustomerInfoId() {
        return customerInfoId;
    }

    public void setCustomerInfoId(Long customerInfoId) {
        this.customerInfoId = customerInfoId;
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public Integer getCircle() {
        return circle;
    }

    public void setCircle(Integer circle) {
        this.circle = circle;
    }

    public Integer getTransferMoneyDay() {
        return transferMoneyDay;
    }

    public void setTransferMoneyDay(Integer transferMoneyDay) {
        this.transferMoneyDay = transferMoneyDay;
    }

    public Integer getRiskPrematurity() {
        return riskPrematurity;
    }

    public void setRiskPrematurity(Integer riskPrematurity) {
        this.riskPrematurity = riskPrematurity;
    }

    public Short getTarget() {
        return target;
    }

    public void setTarget(Short target) {
        this.target = target;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public Short getPauseSett() {
        return pauseSett;
    }

    public void setPauseSett(Short pauseSett) {
        this.pauseSett = pauseSett;
    }

    public Short getBankAccountType() {
        return bankAccountType;
    }

    public void setBankAccountType(Short bankAccountType) {
        this.bankAccountType = bankAccountType;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankLineNumber() {
        return bankLineNumber;
    }

    public void setBankLineNumber(String bankLineNumber) {
        this.bankLineNumber = bankLineNumber;
    }

    public String getOpenAccountBankName() {
        return openAccountBankName;
    }

    public void setOpenAccountBankName(String openAccountBankName) {
        this.openAccountBankName = openAccountBankName;
    }

    public String getBankAccountNo() {
        return bankAccountNo;
    }

    public void setBankAccountNo(String bankAccountNo) {
        this.bankAccountNo = bankAccountNo;
    }

    public String getCustomerNameInBank() {
        return customerNameInBank;
    }

    public void setCustomerNameInBank(String customerNameInBank) {
        this.customerNameInBank = customerNameInBank;
    }

    public String getSettleMode() {
        return settleMode;
    }

    public void setSettleMode(String settleMode) {
        this.settleMode = settleMode;
    }

    public Long getStartAmount() {
        return startAmount;
    }

    public void setStartAmount(Long startAmount) {
        this.startAmount = startAmount;
    }

    public String getBankReservedMobile() {
        return bankReservedMobile;
    }

    public void setBankReservedMobile(String bankReservedMobile) {
        this.bankReservedMobile = bankReservedMobile;
    }

    public String getBankReservedLpidNo() {
        return bankReservedLpidNo;
    }

    public void setBankReservedLpidNo(String bankReservedLpidNo) {
        this.bankReservedLpidNo = bankReservedLpidNo;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }
    
    public static enum settTarget {
    	SETT_TO_BANK((short)1 , "结算到银行卡"),
    	SETT_TO_EFPS_ACCOUNT((short)2 , "结算到易票联账户");
    	
    	public final short code;
    	
    	public final String message;

		private settTarget(short code, String message) {
			this.code = code;
			this.message = message;
		}


    }
}