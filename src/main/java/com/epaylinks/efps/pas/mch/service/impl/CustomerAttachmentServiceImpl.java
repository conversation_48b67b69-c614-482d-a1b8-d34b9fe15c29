package com.epaylinks.efps.pas.mch.service.impl;

import java.io.File;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.pas.mch.common.MchError;
import com.epaylinks.efps.pas.mch.dao.CustomerAttachmentInfoMapper;
import com.epaylinks.efps.pas.mch.domain.CustomerAttachmentInfo;
import com.epaylinks.efps.pas.mch.service.CustomerAttachmentService;
import com.epaylinks.efps.pas.mch.service.feign.FsService;

/**
 * 业务逻辑层：客户附件信息
 * <AUTHOR>
 */
@Service
public class CustomerAttachmentServiceImpl implements CustomerAttachmentService {
	
	@Autowired
	private CustomerAttachmentInfoMapper customerAttachmentInfoMapper;
	@Autowired
    private SequenceService sequenceService;
	@Autowired
	private FsService fsService;
	
	/**
	 * 附件上传，如果输入的attachmentId为空，则会创建一个，否则使用入参中的值
	 */
	@Override
	@Logable(businessTag = "uploadAttachment")
	public Long uploadAttachment(Long attachmentId, Long customerInfoId, String attachmentCode, String attachmentName,
			String attachmentUrl, Long attachmentSize) {
        // 保存附件信息
        CustomerAttachmentInfo attachmentInfo;
        if (attachmentId == null) {
        	attachmentInfo = new CustomerAttachmentInfo();
            attachmentId = sequenceService.nextValue("addMchAttachment");
            attachmentInfo.setAttachmentId(attachmentId);
            attachmentInfo.setCustomerInfoId(customerInfoId);
            attachmentInfo.setAttachmentCode(attachmentCode);
            attachmentInfo.setAttachmentName(attachmentName);
            attachmentInfo.setAttachmentUrl(attachmentUrl);
            attachmentInfo.setAttachmenSize(attachmentSize);
            customerAttachmentInfoMapper.insert(attachmentInfo);
        } else {
        	attachmentInfo = customerAttachmentInfoMapper.selectByPrimaryKey(attachmentId);
        	attachmentInfo.setCustomerInfoId(customerInfoId);
            attachmentInfo.setAttachmentCode(attachmentCode);
            attachmentInfo.setAttachmentName(attachmentName);
            attachmentInfo.setAttachmentUrl(attachmentUrl);
            attachmentInfo.setAttachmenSize(attachmentSize);
            customerAttachmentInfoMapper.updateByPrimaryKey(attachmentInfo);
        }
        return attachmentInfo.getAttachmentId();
	}
	
	/**
	 * 附件查询
	 */
	@Override
	@Logable(businessTag = "getAttachmentByCustomerInfoId")
	public List<CustomerAttachmentInfo> getAttachmentByCustomerInfoId(Long customerInfoId) {
		return customerAttachmentInfoMapper.getAttachmentByCustomerInfoId(customerInfoId);
	}
	
	/**
	 * 附件删除
	 */
	@Override
	@Logable(businessTag = "deleteAttachmentById")
	@Transactional
	public void deleteAttachmentById(Long attachmentId) {
		CustomerAttachmentInfo attachmentInfo = customerAttachmentInfoMapper.selectByPrimaryKey(attachmentId);
		if (attachmentInfo == null) {
			throw new AppException(MchError.ATTACHMENT_NOT_EXIST.code);
		}
		// 删除
		customerAttachmentInfoMapper.deleteByPrimaryKey(attachmentId);
		// 从服务器指定目录删除文件
//        File file = new File(attachmentInfo.getAttachmentCode());
//        if (file.exists()) {
//            file.delete();
//        }
	}
	
	/**
	 * 获取文件扩展名
	 * @param {String} fileName 示例：xxx.png
	 * @return {String} fileName 示例：.png
	 */
	@Logable(businessTag = "getFileType")
	private String getFileType(String fileName) {
		if (fileName != null) {
			return fileName.substring(fileName.lastIndexOf("."), fileName.length());
		}
		return null;
	}

	/**
	 * 生成附件文件名称
	 */
	@Logable(businessTag = "generateFileName")
	private String generateFileName(Long customerInfoId, String attachmentCode) {
		Long sequenceId = sequenceService.nextValue("addMchAttachmentName");
		return customerInfoId + "_" + attachmentCode + "_" + sequenceId;
	}
	
	/**
	 * 获取附件存放的路径
	 * 
	 * 注：根据customerInfoId生成hashCode来散列存放文件，
	 * 取倒数两个三位为组成目录，如-1830122167对应上传目录为：上传根目录/122/167/
	 */
	@Logable(businessTag = "generateSubDir")
	private String generateSubDir(Long customerInfoId) {
		if (customerInfoId != null) {
			String hashCode = String.valueOf(("0123456789_" + customerInfoId).hashCode());
			String dir1 = hashCode.substring(hashCode.length() - 6, hashCode.length() - 3);
			String dir2 = hashCode.substring(hashCode.length() - 3, hashCode.length());
			return dir1 + File.separator + dir2;
		}
		return null;
	}

	@Logable(businessTag = "uploadToken")
	@Override
	public String uploadToken(String businessType) {
		String token = fsService.uploadToken(businessType, "pas", null, null);
		return token;
	}

}