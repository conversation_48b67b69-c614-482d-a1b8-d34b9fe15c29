package com.epaylinks.efps.pas.mch.client;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient("TXS")
public interface TxsClient {
	
	/**
	 * 根据业务编码以及业务实例查询当前业务实例是否产生过交易
	 * @param businessCode 业务编码
	 * @param businessExamId 业务实例
	 * @return true（已经产生过交易），false（从没产生过交易）
	 */
	@PostMapping(value = "/confirmTransactionRecord")
	public boolean confirmTransactionRecord(
			@RequestParam("businessCode") String businessCode,
			@RequestParam("businessExamId") String businessExamId);
}