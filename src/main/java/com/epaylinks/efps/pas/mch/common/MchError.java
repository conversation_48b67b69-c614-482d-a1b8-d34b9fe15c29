package com.epaylinks.efps.pas.mch.common;

import com.epaylinks.efps.common.systemcode.SystemCode;

@SystemCode
public enum MchError {
	FAIL("310001", "支付系统内部错误"),
    ALL_PARAM_NULL("310002", "至少输入一个参数"),
    CUM_NOT_EXIST("310003", "客户不存在"),
    CUM_STATE_CONFLICT("310004", "当前修改的状态与原状态相同"),
    CUM_AUDITINFO_NOT_EXIST("310005", "客户审核信息不存在"),
    GET_CUSTOMER_CODE_FAIL("310006", "客户编码获取不到"),
    UPLOAD_FILE_EMPTY("310007", "上传文件不能为空"),
    UPLOAD_FILE_SIZE_LIMIT("310008", "上传的文件大小超过最大限制"),
    UPLOAD_FILE_FAIL("310009", "上传文件失败"),
    CERTIFICATE_IMPORT_FAIL("310010", "证书导入失败"),
    ATTACHMENT_NOT_EXIST("310011", "附件信息不存在"),
    ADMIN_INFO_NOT_EXIST("310012", "管理员信息不存在"),
    OPERATOR_EMAIL_NOT_EXIST("310013", "操作人邮箱为空，无法通知"),
    CUST_BANK_ACCOUNT_ERROR("310014", "商户结算银行卡账号错误"),
    CUM_NAME_EXIST("310015", "商户名称已存在，不可重复"),
    CUM_LICENSENO_EXIST("310016", "商户营业执照号已存在，不可重复"),
	CUM_MOBILE_EXIST("310017", "手机号码已存在，不可重复"),
	CUM_LEA_PERSON_ID_EXIST("310018", "小微商户的经营者身份证号码已存在，不可重复"),
	CUM_BIZ_SETT_CYCLE_ERROR("310019", "商户业务信息的结算周期错误"),
	CUM_INVALID_PARAM("310020" , "入参参数校验错误"),
	BUSINESS_EXAM_ID_USED("310021" , "业务实例已经被使用"),
	BUSINESS_MUTEX("310022" , "入参业务互斥"),
	PRIVATE_MUST_WITH_MOBILE("310023" , "对私账户银行预留手机号码必填"),
	BUSINESS_NOT_EXIST("310024" , "商户业务信息不存在"),
	CUSTOMER_BUSINESS_NOT_EXIST("310024" , "商户对应的业务信息不正确"),
	T0_MUST_OPEN_DF_ERROR("310025" , "该商户有业务开通了T+0或D+0结算，需要开通普通代付业务（无论是储蓄卡还是信用卡）"),
	FEE_PER_ERROR("310026" , "单笔费率不能为空"),
	FEE_RATE_ERROR("310027" , "按比例费率不能为空");
    public final String code;
    public final String message;
    MchError(String code, String message) {
        this.code = code;
        this.message = message;
    }
	public String getCode() {
		return code;
	}

	public String getMessage() {
		return message;
	}

	public static String getMessageByCode(String code) {
		for(MchError v: MchError.values())
		{
			if(v.code.equalsIgnoreCase(code))
				return v.message;
		}
		return null;
	}
}
