package com.epaylinks.efps.pas.mch.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.epaylinks.efps.pas.mch.domain.CustomerAttachmentInfo;

@Mapper
public interface CustomerAttachmentInfoMapper {
    int deleteByPrimaryKey(Long attachmentId);

    int insert(CustomerAttachmentInfo record);

    int insertSelective(CustomerAttachmentInfo record);

    CustomerAttachmentInfo selectByPrimaryKey(Long attachmentId);

    int updateByPrimaryKeySelective(CustomerAttachmentInfo record);

    int updateByPrimaryKey(CustomerAttachmentInfo record);

	List<CustomerAttachmentInfo> getAttachmentByCustomerInfoId(Long customerInfoId);
}