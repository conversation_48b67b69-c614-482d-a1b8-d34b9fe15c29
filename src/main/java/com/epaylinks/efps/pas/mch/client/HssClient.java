package com.epaylinks.efps.pas.mch.client;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.epaylinks.efps.common.log.Logable;

@FeignClient("HSS")
public interface HssClient {

	/**
	 * 发送HTML格式邮件
	 * 
	 * @param tos 接收人[多个接收人，逗号隔开]
	 * @param ccs 抄送人[多个接收人，逗号隔开]
	 * @param bccs 密送人[多个接收人，逗号隔开]
	 * @param subject 主题
	 * @param msg 内容
	 */
	@PostMapping("HtmlMail")
	@Logable(businessTag = "HSS.sendHtmlMail")
	public String sendHtmlMail(
			@RequestParam("tos") String tos,
			@RequestParam(value = "ccs", required = false) String ccs,
			@RequestParam(value = "bccs", required = false) String bccs,
			@RequestParam("subject") String subject,
			@RequestParam("msg") String msg);

	//发送短信内容
	@PostMapping("/Sms")
	@Logable(businessTag = "HSS.sendSms")
	public String sendSms(
			@RequestParam("phone") String tos,
			@RequestParam("msg") String msg);
}