package com.epaylinks.efps.pas.mch.domain;

import java.util.Date;

public class Customer {
	
    private Long customerId;

    private String customerCode;

    private Long creatorId;

    private Date createTime;

    private Long newestCustomerInfoId;

    private Long newestStateId;

    private Short state;

    private String initLoginPsw;

    private String initPayPsw;
    private String sourceChannel;//商户来源

    public String getSourceChannel() {
        return sourceChannel;
    }

    public void setSourceChannel(String sourceChannel) {
        this.sourceChannel = sourceChannel;
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getNewestCustomerInfoId() {
        return newestCustomerInfoId;
    }

    public void setNewestCustomerInfoId(Long newestCustomerInfoId) {
        this.newestCustomerInfoId = newestCustomerInfoId;
    }

    public Long getNewestStateId() {
        return newestStateId;
    }

    public void setNewestStateId(Long newestStateId) {
        this.newestStateId = newestStateId;
    }

    public Short getState() {
        return state;
    }

    public void setState(Short state) {
        this.state = state;
    }

    public String getInitLoginPsw() {
        return initLoginPsw;
    }

    public void setInitLoginPsw(String initLoginPsw) {
        this.initLoginPsw = initLoginPsw;
    }

    public String getInitPayPsw() {
        return initPayPsw;
    }

    public void setInitPayPsw(String initPayPsw) {
        this.initPayPsw = initPayPsw;
    }
}