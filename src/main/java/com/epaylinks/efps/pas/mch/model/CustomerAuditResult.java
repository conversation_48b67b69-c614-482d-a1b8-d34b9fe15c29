package com.epaylinks.efps.pas.mch.model;

import java.util.Date;

public class CustomerAuditResult {

    private Long customerAuditInfoId;
    
    private Long customerInfoId;
    
    private String customerName;

    private String operationType;
    
    private Date updateTime;

    private Long creatorId;
    
    private String auditStatus;
    private String sourceChannel;

	public String getSourceChannel() {
		return sourceChannel;
	}

	public void setSourceChannel(String sourceChannel) {
		this.sourceChannel = sourceChannel;
	}

	/**
     *  操作人（结果模型新加的）
     */
    private String creatorUserName;
    
	public CustomerAuditResult() {
    	
    }

	public CustomerAuditResult(Long customerAuditInfoId, Long customerInfoId, String customerName, String operationType,
			Date updateTime, Long creatorId, String auditStatus) {
		this.customerAuditInfoId = customerAuditInfoId;
		this.customerInfoId = customerInfoId;
		this.customerName = customerName;
		this.operationType = operationType;
		this.updateTime = updateTime;
		this.creatorId = creatorId;
		this.auditStatus = auditStatus;
	}

	public Long getCustomerAuditInfoId() {
		return customerAuditInfoId;
	}

	public void setCustomerAuditInfoId(Long customerAuditInfoId) {
		this.customerAuditInfoId = customerAuditInfoId;
	}

	public Long getCustomerInfoId() {
		return customerInfoId;
	}

	public void setCustomerInfoId(Long customerInfoId) {
		this.customerInfoId = customerInfoId;
	}

	public String getCustomerName() {
		return customerName;
	}

	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	public String getOperationType() {
		return operationType;
	}

	public void setOperationType(String operationType) {
		this.operationType = operationType;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public Long getCreatorId() {
		return creatorId;
	}

	public void setCreatorId(Long creatorId) {
		this.creatorId = creatorId;
	}

	public String getAuditStatus() {
		return auditStatus;
	}

	public void setAuditStatus(String auditStatus) {
		this.auditStatus = auditStatus;
	}

	public String getCreatorUserName() {
		return creatorUserName;
	}

	public void setCreatorUserName(String creatorUserName) {
		this.creatorUserName = creatorUserName;
	}
}