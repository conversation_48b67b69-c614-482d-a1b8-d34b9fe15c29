package com.epaylinks.efps.pas.mch.controller;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.pas.common.PasCode;

import com.epaylinks.efps.pas.mch.service.impl.CustomerAuditInfoServiceImpl;
import com.epaylinks.efps.pas.mch.service.impl.CustomerServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * Created by adm on 2019/2/19.
 */
@RestController
@Api(value = "CustomerController", description = "商户同步管理")
public class CustomerController {
    @Autowired
    private CustomerServiceImpl customerService;

    @Autowired
    private CustomerAuditInfoServiceImpl customerAuditInfoService;

    /**
     * 商户信息审核（待复审）
     */
    @PostMapping("/addCustomerFromCust")
    @Logable(businessTag = "auditCustomerInfo")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "商户信息审核（生成待复审）", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "customerNo", value = "商户信息customerNo", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "customerName", value = "商户名称customerName", required = true, dataType = "String", paramType = "query"),
//            @ApiImplicitParam(name = "customerInfoId", value = "商户信息ID", required = true, dataType = "Long", paramType = "query", digit = true)
    })
    public CommonOuterResponse addCustomerFromCust(
            @RequestParam String customerNo,
            @RequestParam String customerName,
            @RequestHeader(value = "x-userid", required = false) Long curUserId) {
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            customerService.addCustomerFromCust(customerNo, customerName, null, -1L);
        } catch (Exception e) {
            response = new CommonOuterResponse();
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
            return response;
        }
        return response;
    }


    /**
     * 商户信息审核（初审/复审）
     */
    @PostMapping("/auditCust")
    @Logable(businessTag = "auditCust")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "商户信息审核（复审）for CUST", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "auditInfoId", value = "商户审核信息ID", required = true, dataType = "Long", paramType = "query", digit = true),
            @ApiImplicitParam(name = "auditOption", value = "审核意见（00：通过，01：否决）", required = true, dataType = "String", paramType = "query", valueRange = "{00,01}"),
            @ApiImplicitParam(name = "comment", value = "审核说明", required = false, dataType = "String", paramType = "query", length = 200)
    })
    public CommonOuterResponse auditCust(
            @RequestParam Long auditInfoId,
            @RequestParam String auditOption,// 00同意, 01否决
            @RequestParam(required = false) String comment,
            @RequestHeader(value = "x-userid", required = true) Long curUserId) {
//        Long curUserId = -1L;
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            customerAuditInfoService.auditCustomerInfoForCust(auditInfoId, auditOption, comment, curUserId);
        } catch (Exception e) {
            response = new CommonOuterResponse();
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
            return response;
        }
        return response;
    }

}
