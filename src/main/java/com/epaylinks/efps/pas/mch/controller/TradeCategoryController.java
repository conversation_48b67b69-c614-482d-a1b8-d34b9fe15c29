package com.epaylinks.efps.pas.mch.controller;

import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.pas.mch.domain.TradeCategory;
import com.epaylinks.efps.pas.mch.service.TradeCategoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Date;
import java.util.List;


@RestController
@RequestMapping("/TradeCategory")
@Api(value = "TradeCategoryController", description = "行业类别接口")
public class TradeCategoryController {

    @Autowired
    private SequenceService sequenceService;

    @Autowired
    private TradeCategoryService tradeCategoryService;

    @RequestMapping(value ="/create", method = RequestMethod.POST)
    @Logable(businessTag = "create")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "新增行业类别", notes = "新增行业类别", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "parentId", value = "上级类目ID", required = true, dataType = "long", length =50, paramType = "query"),
            @ApiImplicitParam(name = "code", value = "行业类别编码", required = true, dataType = "String", length =50, paramType = "query"),
            @ApiImplicitParam(name = "name", value = "行业类别名称", required = true, dataType = "String", length =100, paramType = "query"),
            @ApiImplicitParam(name = "remark", value = "描述", required = false, dataType = "String", length =500, paramType = "query"),
            @ApiImplicitParam(name = "mcc", value = "mcc", required = false, dataType = "String", length =4, paramType = "query"),
            @ApiImplicitParam(name = "xmpf", value = "厦门浦发MCC", required = false, dataType = "String", length =10, paramType = "query"),
            @ApiImplicitParam(name = "szpa", value = "深圳平安MCC", required = false, dataType = "String", length =10, paramType = "query")})
    public String create(@ApiIgnore TradeCategory record) {
        record.setId(sequenceService.nextValue("pas"));
        return tradeCategoryService.insertSelective(record) == 1? Constants.SUCCESS: Constants.FAIL;

    }

    @RequestMapping(value ="/modify", method = RequestMethod.POST)
    @Logable(businessTag = "modify")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "修改行业类别", notes = "修改行业类别", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "id", required = true, dataType = "long", length =50, paramType = "query"),
            @ApiImplicitParam(name = "parentId", value = "上级类目ID", required = false, dataType = "long", length =50, paramType = "query"),
            @ApiImplicitParam(name = "code", value = "行业类别编码", required = false, dataType = "String", length =50, paramType = "query"),
            @ApiImplicitParam(name = "name", value = "行业类别名称", required = false, dataType = "String", length =100, paramType = "query"),
            @ApiImplicitParam(name = "remark", value = "描述", required = false, dataType = "String", length =500, paramType = "query"),
            @ApiImplicitParam(name = "mcc", value = "mcc", required = false, dataType = "String", length =4, paramType = "query"),
            @ApiImplicitParam(name = "xmpf", value = "厦门浦发MCC", required = false, dataType = "String", length =10, paramType = "query"),
            @ApiImplicitParam(name = "szpa", value = "深圳平安MCC", required = false, dataType = "String", length =10, paramType = "query")})
    public String modify(@ApiIgnore TradeCategory record ) {
        return tradeCategoryService.updateByPrimaryKeySelective(record) == 1? Constants.SUCCESS: Constants.FAIL;

    }


    @RequestMapping(value ="/delete", method = RequestMethod.GET)
    @Logable(businessTag = "delete")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "删除行业类别", notes = "删除行业类别", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "id", required = true, dataType = "long", length =50, paramType = "query")})
    public String delete(@RequestParam Long id) {
        return tradeCategoryService.deleteByPrimaryKey(id) == 1? Constants.SUCCESS: Constants.FAIL;

    }


    @RequestMapping(value ="/select", method = RequestMethod.GET)
    @Logable(businessTag = "select")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "搜索行业类别", notes = "搜索行业类别", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "parentId", value = "上级类目ID", required = false, dataType = "long", length =50, paramType = "query"),
            @ApiImplicitParam(name = "code", value = "行业类别编码", required = false, dataType = "String", length =50, paramType = "query"),
            @ApiImplicitParam(name = "name", value = "行业类别名称", required = false, dataType = "String", length =100, paramType = "query"),
            @ApiImplicitParam(name = "remark", value = "描述", required = false, dataType = "String", length =500, paramType = "query"),
            @ApiImplicitParam(name = "mcc", value = "mcc", required = false, dataType = "String", length =4, paramType = "query"),
            @ApiImplicitParam(name = "xmpf", value = "厦门浦发MCC", required = false, dataType = "String", length =10, paramType = "query"),
            @ApiImplicitParam(name = "szpa", value = "深圳平安MCC", required = false, dataType = "String", length =10, paramType = "query")})
    public List<TradeCategory> select(@ApiIgnore TradeCategory record ) {
        return tradeCategoryService.selectBySelective(record) ;

    }

    @RequestMapping(value ="/selectById", method = RequestMethod.GET)
    @Logable(businessTag = "selectById")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "根据ID搜索单条行业类别", notes = "根据ID搜索单条行业类别", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "id", required = true, dataType = "long", length =50, paramType = "query")})
    public TradeCategory selectById(@RequestParam Long id) {
        return tradeCategoryService.selectByPrimaryKey(id) ;
    }

}
