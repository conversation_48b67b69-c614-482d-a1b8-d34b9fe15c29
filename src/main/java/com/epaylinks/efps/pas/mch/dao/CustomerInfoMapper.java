package com.epaylinks.efps.pas.mch.dao;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.epaylinks.efps.pas.mch.domain.CustomerBusinessInfo;
import com.epaylinks.efps.pas.mch.domain.CustomerInfo;

@Mapper
public interface CustomerInfoMapper {
	
    int deleteByPrimaryKey(Long infoId);

    int insert(CustomerInfo record);

    int insertSelective(CustomerInfo record);

    CustomerInfo selectByPrimaryKey(Long infoId);

    int updateByPrimaryKeySelective(CustomerInfo record);

    int updateByPrimaryKey(CustomerInfo record);
    
    CustomerInfo getCustomerCode(Map<String,Object> parameterMap);
    
    CustomerInfo getCustomerInfo(Map<String,Object> parameterMap);
    
    //通过customerCode删除该客户的基本信息
    int deleteByCustomerCode(String customerCode);
    
    //通过customerParentsCode查找对象
    CustomerInfo selectByParentCus(String parentCustomerCode);

	CustomerInfo getCustomerInfoByByCustomerCode(String customerCode);

	List<CustomerInfo> getCustomerInfoByParam(Map<String,Object> parameterMap);
	
	/**
	 * 查询名字或手机相同的客户信息
	 * @param mobile
	 * @param name
	 * @return
	 */
	List<CustomerInfo> getCustomerInfoByMobileOrName(@Param("mobile") String mobile,@Param("name")String name);

	List<CustomerInfo> getBusinessParamByParam(Map<String,Object> parameterMap);

	String selectMerchantNameByInfoId(@Param("customerInfoId") Long customerInfoId);

	CustomerInfo selectByCustomerId(Long customerId);
	
	List<CustomerInfo>  selectByBusinessLicenseNo(String businessLicenseNo);
	
	List<CustomerInfo>  selectByLeaPersoniDentificationNo(String leaPersoniDentificationNo);

	List<CustomerInfo> selectListByCustomerId(Long customerId);

}