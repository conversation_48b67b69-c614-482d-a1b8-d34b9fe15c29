package com.epaylinks.efps.pas.mch.client;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.epaylinks.efps.common.config.DefaultFeignConfiguration;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.pas.mch.client.model.Certificate;

@FeignClient(value = "UAA" , configuration = DefaultFeignConfiguration.class)
public interface UaaClient {
	
	/**
	 * 查询证书
	 */
	@GetMapping("Certificate")
	public PageResult<Certificate> pageQueryCertificates(
			@RequestParam("pageNum") Integer pageNum,
			@RequestParam("pageSize") Integer pageSize,
			@RequestParam(value = "customerCode", required = false) String customerCode,
			@RequestParam(value = "customerName", required = false) String customerName);
	
	/**
	 * 查看证书
	 */
	@GetMapping("Certificate/detail")
	public Certificate viewCertificate(@RequestParam("signSN") String signSN);

	/**
	 * 删除证书
	 */
	@DeleteMapping("Certificate")
	public void deleteCertificate(@RequestParam("signSN") String signSN);

	/**
	 * 新增证书
	 */
	@PostMapping("Certificate")
	public void addCertificate(@RequestBody Certificate certificate);
}