package com.epaylinks.efps.pas.mch.service;

import com.epaylinks.efps.pas.mch.domain.TradeCategory;

import java.util.List;

/**
 * <AUTHOR> 5m
 * @Date : 2018/1/24 11:54
 * @Description :
 */
public interface TradeCategoryService {

    int deleteByPrimaryKey(Long id);

    int insert(TradeCategory record);

    int insertSelective(TradeCategory record);

    TradeCategory selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TradeCategory record);

    int updateByPrimaryKey(TradeCategory record);

    List<TradeCategory> selectBySelective(TradeCategory record);
}
