package com.epaylinks.efps.pas.mch.dao;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.epaylinks.efps.pas.mch.domain.CustomerAuditInfo;
import com.epaylinks.efps.pas.mch.model.CustomerAuditResult;

@Mapper
public interface CustomerAuditInfoMapper {
	
    int deleteByPrimaryKey(Long auditInfoId);

    int insert(CustomerAuditInfo record);

    int insertSelective(CustomerAuditInfo record);

    CustomerAuditInfo selectByPrimaryKey(Long auditInfoId);

    int updateByPrimaryKeySelective(CustomerAuditInfo record);

    int updateByPrimaryKey(CustomerAuditInfo record);

	CustomerAuditInfo getByCustomerInfoId(Long customerInfoId);
	/**
	 * 按照条件查询符合 条件的数量
	 * @param submitTimeBegin
	 * @param submitTimeEnd
	 * @param customerName
	 * @param operationType
	 * @param auditStatus
	 * @return
	 */
	int countCustomerAuditInfosOfWaitAudit(@Param("submitTimeBegin") Date submitTimeBegin , @Param("submitTimeEnd") Date submitTimeEnd , 
			@Param("customerName") String customerName , @Param("operationType") String operationType , 
			@Param("auditStatus") String auditStatus,@Param("sourceChannel") String sourceChannel);
	List<CustomerAuditResult> pageQueryCustomerAuditInfosOfWaitAudit(@Param("beginRowNo")int beginRowNo, @Param("endRowNo")int endRowNo , 
			@Param("submitTimeBegin") Date submitTimeBegin , @Param("submitTimeEnd") Date submitTimeEnd , 
			@Param("customerName") String customerName , @Param("operationType") String operationType , 
			@Param("auditStatus") String auditStatus,@Param("sourceChannel") String sourceChannel);
	int countCustomerAuditInfosOfRefuse(@Param("submitTimeBegin")String submitTimeBegin, @Param("submitTimeEnd")String submitTimeEnd, @Param("operationType")String operationType,
			@Param("customerName")String customerName, @Param("auditStatus")String auditStatus, @Param("sourceChannel")String sourceChannel);

	List<CustomerAuditResult> pageQueryCustomerAuditInfosOfRefuse(@Param("beginRowNo")int beginRowNo, @Param("endRowNo")int endRowNo, @Param("submitTimeBegin")String submitTimeBegin,
			@Param("submitTimeEnd")String submitTimeEnd, @Param("operationType")String operationType, @Param("customerName")String customerName, @Param("auditStatus")String auditStatus,
			 @Param("sourceChannel")String sourceChannel);

	boolean checkExistOfMchNameInWaitAuditeds(@Param("customerInfoId") Long customerInfoId, @Param("mchName") String mchName);
	
	boolean checkExistOfLicenseNoInWaitAuditeds(@Param("customerInfoId") Long customerInfoId, @Param("businessLicenseNo") String businessLicenseNo);
	
	boolean checkExistOfLeaPersonIDNoInWaitAuditeds(@Param("customerInfoId") Long customerInfoId, @Param("leaPersoniDentificationNo") String leaPersoniDentificationNo, @Param("parentCustomerCode") String parentCustomerCode);
	
    int deleteWaitAuditInfosByCustomerId(Long customerId);

}