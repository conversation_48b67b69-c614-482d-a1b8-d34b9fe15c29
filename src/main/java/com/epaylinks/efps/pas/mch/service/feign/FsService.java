package com.epaylinks.efps.pas.mch.service.feign;


import java.util.Map;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import com.epaylinks.efps.common.config.DefaultFeignConfiguration;
import com.epaylinks.efps.pas.acc.entity.FileUploadResponse;

@FeignClient(value = "FS", configuration = DefaultFeignConfiguration.class)
public interface FsService {
    /**
     * 申请拿到上传文件的token
     *
     * @param businessType 文件类型
     * @param fromSystemId 上传的文件来自哪个子系统，系统编号
     * @param needEncrypt  是否需加密，后续扩展使用，本期不使用，默认否.0：否 1：是
     * @param remark       文件描述信息
     * @return
     */
    @RequestMapping(value = "/UploadToekn", method = RequestMethod.POST)
    public String uploadToken(
            @RequestParam("businessType") String businessType,
            @RequestParam("fromSystemId") String fromSystemId,
            @RequestParam("needEncrypt") String needEncrypt,
            @RequestParam("remark") String remark);

    /**
     * @return
     */
    @RequestMapping(value = "/File", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public String uploadFile(
            @RequestPart("file") MultipartFile file,
            @RequestParam("uploadToken") String uploadToken);

    /**
     * @param file        文件
     * @param uploadToken 文件上传token
     * @return
     */
    @RequestMapping(value = "/File", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    FileUploadResponse uploadFile(
            @RequestPart("file") MultipartFile file,
            @RequestParam("uploadToken") String uploadToken,
            @RequestParam("customerCode") String customerCode
    );


    /**
     * 获取文件路径
     *
     * @param uniqueId
     * @param expiredTime
     * @param maxAccessCount
     * @param type
     * @return
     */
    @PostMapping("/FilePath")
    Map<String, String> filePath(
            @RequestParam("uniqueId") String uniqueId,
            @RequestParam("expiredTime") Integer expiredTime,
            @RequestParam(defaultValue = "-1", required = false, value = "maxAccessCount") int maxAccessCount,
            @RequestParam("type") String type
    );

    @PostMapping("/FilePathMark")
    Map<String, String> filePath(@RequestParam("uniqueId") String uniqueId,
                                 @RequestParam("expiredTime") Integer expiredTime,
                                 @RequestParam(defaultValue = "-1", required = false, value = "maxAccessCount") int maxAccessCount,
                                 @RequestParam("type") String type,
                                 @RequestParam("markText") String markText);
}
