package com.epaylinks.efps.pas.mch.client;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.config.DefaultFeignConfiguration;
import com.epaylinks.efps.pas.mch.client.model.CustomerObjects;

@FeignClient(value = "CUM", configuration = DefaultFeignConfiguration.class)
public interface CumClient {
	
	/**
	 * 分配新客户编码
	 */
	@RequestMapping(value="/Customer/CustomerCode", method = RequestMethod.POST)
	public String newCustomerCode(@RequestParam("customerType")int customerType);
	
	/**
	 * 同步：添加/修改客户
	 */
	@RequestMapping(value="/Customer", method = RequestMethod.POST)
	public String syncCustomers(@RequestBody CustomerObjects customerObjects,
		@RequestParam(value = "autoCreateUser", required = true) Long autoCreateUser,// 0/1
		@RequestParam(value = "initLoginPwd", required = false) String initLoginPwd);
	
	/**
	 * 同步：修改客户状态
	 */
	@RequestMapping(value="/Customer/customerStatus", method = RequestMethod.PUT)
	public String updateCusStatus(
			@RequestParam(value = "customerCode", required = true) String customerCode,
			@RequestParam(value = "oldStatus", required = true) Long oldStatus,
			@RequestParam(value = "newStatus", required = true) Long newStatus);
	
	@RequestMapping(value = "/Customer/syncBusinessManName", method = RequestMethod.POST)
	public CommonOuterResponse syncBusinessManName(
			@RequestParam("businessManId")Long businessManId, @RequestParam("businessManName")String businessManName);
	
	@RequestMapping(value = "/Customer/syncCompanyName", method = RequestMethod.POST)
	public CommonOuterResponse syncCompanyName(
			@RequestParam("companyId")Long companyId, @RequestParam("companyName")String companyName);

}