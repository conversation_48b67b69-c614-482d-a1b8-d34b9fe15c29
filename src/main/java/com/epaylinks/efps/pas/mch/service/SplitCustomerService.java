package com.epaylinks.efps.pas.mch.service;

import com.epaylinks.efps.pas.mch.domain.CustomerBusinessInfo;
import com.epaylinks.efps.pas.mch.model.CustomerRequest;
import com.epaylinks.efps.pas.mch.model.CustomerResponse;

/**
 * 业务逻辑层：客户控制
 * <AUTHOR>
 */
public interface SplitCustomerService {
	/**
	 * 根据入参更新或新建某条客户信息记录
	 * 会捕获所有异常转换为错误码封装到响应中
	 * @param customerRequest
	 * @param createrCusCode
	 * @return
	 */
	public CustomerResponse saveSplitMerchant(CustomerRequest customerRequest, String createrCusCode) ;
	/**
	 * 根据入参更新客户的信息
	 * @param customerRequest 入参子商户需要更新的字段
	 */
	public long updateSplitMerchant(CustomerRequest customerRequest , String createrCusCode);

	public CustomerBusinessInfo getCustomerBusinessInfo(String createrCusCode, String businessCode) ;
	
	/**
	 * 会员从cust进件
	 * @param customerRequest
	 * @param customerCodeFromHeader
	 * @return
	 */
	public CustomerResponse saveSplitMerchantToCust(CustomerRequest customerRequest, String parentCustomerCode);
	/**
	 * cust会员信息更新
	 * @param customerRequest
	 * @param customerCodeFromHeader
	 * @return
	 */
	public CustomerResponse updateSplitMerchantInCust(CustomerRequest customerRequest, String customerCodeFromHeader);

}