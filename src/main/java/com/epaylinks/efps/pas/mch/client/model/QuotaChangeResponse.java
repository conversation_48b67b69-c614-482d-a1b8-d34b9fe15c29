package com.epaylinks.efps.pas.mch.client.model;
/**
    * 额度调整-响应结果
    */
   public class QuotaChangeResponse {
    /**
     * 订单号
     */
    private String transactionNo;
    /**
     * 状态
     */
    private String state;
    /**
     * 上游返回响应码
     */
    private String channelRespCode;
    /**
     * 上游返回响应消息
     */
    private String channelRespMsg;

    public String getTransactionNo() {
        return transactionNo;
    }

    public void setTransactionNo(String transactionNo) {
        this.transactionNo = transactionNo;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getChannelRespCode() {
        return channelRespCode;
    }

    public void setChannelRespCode(String channelRespCode) {
        this.channelRespCode = channelRespCode;
    }

    public String getChannelRespMsg() {
        return channelRespMsg;
    }

    public void setChannelRespMsg(String channelRespMsg) {
        this.channelRespMsg = channelRespMsg;
    }

    /**
     * 额度调整状态（银联资金结算）
     */
    enum UnionFundsettQuotaChangeState {
        SUCCESS("00", "成功"),
        FAIL("01", "失败"),
        DOING("02", "上游处理中"),
        INIT("03", "初始化"),
        UNKNOW("04", "未知");
        public final String code;
        public final String comment;

        UnionFundsettQuotaChangeState(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }
    }

    /**
     * 额度调整类型（银联资金结算）
     */
    enum UnionFundsettQuotaChangeType {
        REDUCE("REDUCE", "调减"),
        ADD("ADD", "调增");
        public final String code;
        public final String comment;

        UnionFundsettQuotaChangeType(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }
    }
}
