package com.epaylinks.efps.pas.mch.common;

import com.epaylinks.efps.common.util.Constants;

import java.util.HashMap;
import java.util.Map;

/**
 * 运营门户》商户管理模块常量
 * <AUTHOR>
 */
public interface MchConstants extends Constants {
	
	public static enum MchFeignError {
        CUM_ACCESS_EXCEPTION("310101", "客户子系统访问异常");
        public final String code;
        public final String comment;
        MchFeignError(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }
        public String getCode() {
            return code;
        }
        public String getComment() {
            return comment;
        }
    }

	//客户状态类型
	enum CustomerState {
		INIT("0", "初始化"),
		NORMAL("1", "正常"),
		LOCK("2", "冻结"),
		AUDITING("3", "审核中");
		public final String code;
		public final String comment;
		CustomerState(String code, String comment) {
			this.code = code;
			this.comment = comment;
		}
	}
	
	// 客户信息审核状态（00：待初审，01：初审未通过，02：待复审，03：复审未通过，04：审核成功）
	enum CustomerInfoAuditState {
		WAIT_FIRST_AUDIT("00", "待初审"),
		FIRST_AUDIT_FAIL("01", "初审未通过"),
		WAIT_SECOND_AUDIT("02", "待复审"),
		SECOND_AUDIT_FAIL("03", "复审未通过"),
		AUDIT_SUCCESS("04", "审核成功");
		public final String code;
		public final String comment;
		CustomerInfoAuditState(String code, String comment) {
			this.code = code;
			this.comment = comment;
		}
	}
	
	// 行为类型（1：运营门户-客户状态变更，2：运营门户-客户资料修改，3：运营门户-客户资料新增，4：客户门户-客户结算账户修改）
	enum CustomerOperateType {
		STATE_UPDATE("1", "运营门户-客户状态变更"),
		INFO_MODIFY("2", "运营门户-客户资料修改"),
		INFO_ADD("3", "运营门户-客户资料新增"),
		SETTLE_INFO_MODIFY("4", "客户门户-客户结算账户修改");
		public final String code;
		public final String comment;
		CustomerOperateType(String code, String comment) {
			this.code = code;
			this.comment = comment;
		}
	}
	
	// 审核意见（00：同意、01：否决）
	enum CustomerInfoAuditOption {
		ALLOW("00", "同意"),
		REFUSE("01", "否决");
		public final String code;
		public final String comment;
		CustomerInfoAuditOption(String code, String comment) {
			this.code = code;
			this.comment = comment;
		}
	}
	
	//客户类型
	enum CumUserType {
		PERSONAL_CUSTOMER (1, "个人客户"),
		ENTERPRISE_CUSTOMER (2,   "企业客户");
		public final int code;
		public final String comment;
		CumUserType(int code, String comment) {
			this.code = code;
			this.comment = comment;
		}
	}
	
	// 是否存在（0：存在、1：不存在）
	enum Exist {
		TRUE("1", "存在"),
		FALSE("0", "不存在");
		public final String code;
		public final String comment;
		Exist(String code, String comment) {
			this.code = code;
			this.comment = comment;
		}
	}
	
	enum AuditResult {
		ACCEPT(0L , "通过"),
		REJECT(1L , "未通过");
		public final Long code;
		public final String comment;
		AuditResult(Long code, String comment) {
			this.code = code;
			this.comment = comment;
		}
	}
	
	enum BankAccountType {
		TO_PUBLIC("1" , "对公"),
		TO_PRIVATE("2" , "对私");
		public final String code;
		public final String comment;
		BankAccountType(String code, String comment) {
			this.code = code;
			this.comment = comment;
		}
	}

	/**
	 * 结算周期对应的名称(即基础数据表pas_business)
	 * 说明：由于产品要求界面显示的值与旧数据不一致，这里作翻译
	 */
	Map<String, String> SETT_CYCLE_CODE_MAP = new HashMap<String, String>(){{
        put("D+1", "D+1");
        put("T+1", "T+1");
        put("RealTime", "D+0");
	    put("T-RealTime", "T+0");
	}};
}