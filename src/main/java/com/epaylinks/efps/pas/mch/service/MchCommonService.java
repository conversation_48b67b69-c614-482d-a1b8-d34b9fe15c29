package com.epaylinks.efps.pas.mch.service;

/**
 * 业务逻辑层：商户公共逻辑
 * <AUTHOR>
 */
public interface MchCommonService {
	
	/**
	 * 商户名称重复性检查
	 * @return true:已存在, false:不存在
	 */
	boolean checkExistOfMchName(String mchName, Long customerId, Long customerInfoId);
	
	/**
	 * 商户营业执照号重复性检查
	 * @return true:已存在, false:不存在
	 */
	boolean checkExistOfLicenseNo(String businessLicenseNo, Long customerId, Long customerInfoId);

	/**
	 * 商户的法人或经营者的身份证号码是否重复
	 * 返回true说明已存在
	 * @param leaPersoniDentificationNo
	 * @param customerId
	 * @param customerInfoId
	 * @return
	 */
	boolean checkExistOfLeaPersonIDNo(String leaPersoniDentificationNo, Long customerId, Long customerInfoId, String parentCustomerCode);
}