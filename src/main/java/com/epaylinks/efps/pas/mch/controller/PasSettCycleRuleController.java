package com.epaylinks.efps.pas.mch.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.pas.common.Result;
import com.epaylinks.efps.pas.mch.domain.PasSettCycleRule;
import com.epaylinks.efps.pas.mch.service.PasSettCycleRuleService;

@RestController
public class PasSettCycleRuleController {
	/**
	 * 获取所有的结算周期规则
	 */
	@Autowired
	private PasSettCycleRuleService pasSettCycleRuleService;
	
	/**
	 * 查询所有正在生效的结算周期规则
	 * @return
	 */
	@RequestMapping(value = "/pasSettCycleRules" , method = RequestMethod.GET)
	public List<PasSettCycleRule> getAllPasSettCycleRule(){
		return pasSettCycleRuleService.selectAllValid();
	}
	
	/**
	 * 同步所有正在生效的结算周期规则（往kafka发送数据）
	 * @return 成功 "0" , 失败 "1"
	 */
	@RequestMapping(value = "/pasSettCycleRules/sync" , method = RequestMethod.GET)
	public Result<String> syncValidPasSettCycleRule() {
		pasSettCycleRuleService.syncPasSettCycleRule();
		Result<String> result = new Result<>();
		result.setData(Constants.SUCCESS);
		return result;
	}
}
