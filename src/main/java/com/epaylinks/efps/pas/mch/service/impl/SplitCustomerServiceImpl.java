package com.epaylinks.efps.pas.mch.service.impl;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.epaylinks.efps.common.util.Base64;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.epaylinks.efps.common.business.cust.request.MemberPaper;
import com.epaylinks.efps.common.business.cust.request.MemberRequest;
import com.epaylinks.efps.common.business.cust.response.MemberApplyResponse;
import com.epaylinks.efps.common.business.cust.response.MemberModifyResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.common.util.Constants.BusinessCode;
import com.epaylinks.efps.common.util.Constants.BusinessParamCode;
import com.epaylinks.efps.common.util.DateUtils;
import com.epaylinks.efps.common.util.UUIDUtils;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.mch.client.FsClient;
import com.epaylinks.efps.pas.mch.client.model.Base64FileUploadRequest;
import com.epaylinks.efps.pas.mch.client.model.FileUploadResponse;
import com.epaylinks.efps.pas.mch.common.MchError;
import com.epaylinks.efps.pas.mch.dao.CustomerBusinessInfoMapper;
import com.epaylinks.efps.pas.mch.dao.CustomerContactsInfoMapper;
import com.epaylinks.efps.pas.mch.dao.CustomerInfoMapper;
import com.epaylinks.efps.pas.mch.dao.CustomerMapper;
import com.epaylinks.efps.pas.mch.dao.CustomerSettleInfoMapper;
import com.epaylinks.efps.pas.mch.dao.PasSettCycleRuleInstMapper;
import com.epaylinks.efps.pas.mch.domain.Customer;
import com.epaylinks.efps.pas.mch.domain.CustomerBusinessInfo;
import com.epaylinks.efps.pas.mch.domain.CustomerContactsInfo;
import com.epaylinks.efps.pas.mch.domain.CustomerInfo;
import com.epaylinks.efps.pas.mch.domain.CustomerInfo.CustomerType;
import com.epaylinks.efps.pas.mch.domain.CustomerSettleInfo;
import com.epaylinks.efps.pas.mch.domain.PasSettCycleRuleInst;
import com.epaylinks.efps.pas.mch.model.Business;
import com.epaylinks.efps.pas.mch.model.Contact;
import com.epaylinks.efps.pas.mch.model.CustomerRequest;
import com.epaylinks.efps.pas.mch.model.CustomerResponse;
import com.epaylinks.efps.pas.mch.model.CustomerResult;
import com.epaylinks.efps.pas.mch.service.CustomerAttachmentService;
import com.epaylinks.efps.pas.mch.service.CustomerBusinessInfoService;
import com.epaylinks.efps.pas.mch.service.CustomerSettleInfoService;
import com.epaylinks.efps.pas.mch.service.SplitCustomerService;
import com.epaylinks.efps.pas.mch.service.feign.CustService;
import com.epaylinks.efps.pas.mch.vo.BusinessParamInstVo;
import com.epaylinks.efps.pas.mch.vo.CustomerBusinessInfoVo;
import com.epaylinks.efps.pas.pas.dao.BusinessParamInstMapper;
import com.epaylinks.efps.pas.pas.domain.BusinessParamInst;

/**
 * 业务逻辑层：客户控制
 *
 * <AUTHOR>
 */
@Service
public class SplitCustomerServiceImpl implements SplitCustomerService{

    @Autowired
    private CustomerMapper customerMapper;
    @Autowired
    private CustomerInfoMapper customerInfoMapper;
    @Autowired
    private CustomerInfoServiceImpl customerInfoService;
    @Autowired
    private CustomerServiceImpl customerServiceImpl;
    @Autowired
    private CustomerSettleInfoService customerSettleInfoService;
    @Autowired
    private CustomerBusinessInfoService customerBusinessInfoService;
    @Autowired
    private CustomerAttachmentService customerAttachmentService;
    @Autowired
    private CustomerBusinessInfoMapper customerBusinessInfoMapper;
    @Autowired
    private CustomerContactsInfoServiceImpl customerContactsInfoService;
    @Autowired
	private CustomerAuditInfoServiceImpl customerAuditInfoService;
    @Autowired
    private FsClient fsClient;
    @Autowired
    private SequenceService sequenceService;
    @Autowired
    private SplitCustomerServiceImpl self;
    @Autowired
    private BusinessParamInstMapper businessParamInstMapper;
    @Autowired
    private CustService custService;
    
    
    private static final String ADD_MCHBUSINESS = "addMchBusiness";
    
    
    
    
    private static final String MEMBER = "MEM";
    
    private static final String D0 = "D0";
    
    @Autowired
    private CustomerSettleInfoMapper customerSettleInfoMapper;
    @Autowired
    private CustomerContactsInfoMapper customerContactsInfoMapper;
    @Autowired
    private PasSettCycleRuleInstMapper settCycleRuleInstMapper;
    
    @Value("${BUSINESS_ID}")
    private String businessId;
    
    private boolean checkFileHeader(String base64File) {
    	if(base64File==null ||base64File.trim().isEmpty())
    		return false;
        if (base64File.indexOf(',') < 0) {
            return false;
        }
        String dataHead = base64File.substring(0, base64File.indexOf(','));
        if (!dataHead.contains("data:image")) {
            return false;
        }
        return true;
    }

   private void checkCustomerInfo(CustomerRequest vo) {
	   Integer customerTypeI = checkAndParseCustomerType(vo);
	   if(customerTypeI!=CustomerType.XIAO_WEI.code)
	   {//非小微商户需要校验营业执照
		   checkBusinessLicense(vo);
	   }
//	   取消对法人信息的校验
//	   checkLealPersonIdentification(vo);
    }

	private void checkLealPersonIdentification(CustomerRequest vo) {

		if (!checkFileHeader(vo.getLealPersonIdentificationImage1())) {
			throw new AppException(PasCode.LEAL_PERSON_ID_IMAGE_INVALID.code);
		}
		if (!checkFileHeader(vo.getLealPersonIdentificationImage2())) {
			throw new AppException(PasCode.LEAL_PERSON_ID_IMAGE_INVALID.code);
		}
		if(vo.getLealPersonIdentificationNo()==null)
		{
			throw new AppException(PasCode.LEAL_PERSON_NO_INVALID.code);
		}
		if (null == DateUtils.parseDate(vo.getLealPersonIdentificationExpiredDate(), CustomerRequest.DATE_FORMATTER)) {
			throw new AppException(PasCode.LEAL_PERSON_ID_EXPIRED_DATE_EXCEPTION.code);
		}
		if(vo.getLealPersonName()==null)
		{
			throw new AppException(PasCode.LEAL_PERSON_NAME_INVALID.code);
		}
	}

	/**
    * 
    * @param vo
    * @return
    */
	private Integer checkAndParseCustomerType(CustomerRequest vo) {
		Integer customerTypeI = 0;
		try {
			customerTypeI = Integer.parseInt(vo.getType().trim());
		} catch (NumberFormatException e) {
			throw new AppException(PasCode.CUSTOMER_TYPE_INVALID.code);
		}
		boolean customerTypeValid = false;
		for (CustomerType type : CustomerType.values()) {
			if (type.code == customerTypeI) {
				customerTypeValid = true;
			}
		}
		if (!customerTypeValid) {
			throw new AppException(PasCode.CUSTOMER_TYPE_INVALID.code);
		}
		return customerTypeI;
	}


	private void checkBusinessLicense(CustomerRequest vo) {
		if (!checkFileHeader(vo.getBusinessLinenseImage())) {
			throw new AppException(PasCode.BUSINESS_LINENSE_IMAGE_EXCEPTION.code);
		}
		if (vo.getBusinessLicenseNo() == null || vo.getBusinessLicenseNo().trim().length() == 0) {
			throw new AppException(PasCode.BUSINESS_LINENSENO_EXCEPTION.code);
		}
		if (null == DateUtils.parseDate(vo.getBusinessLicenesExpiredDate(), CustomerRequest.DATE_FORMATTER)) {
			throw new AppException(PasCode.BUSINESS_LINENSE_EXPIRED_DATE_EXCEPTION.code);
		}
	}

	@Override
    @Transactional
    @Logable(businessTag = "saveSplitMerchant")
	public CustomerResponse saveSplitMerchant(CustomerRequest customerRequest, String createrCusCode) {
		CustomerResponse res = new CustomerResponse();
		String nonceStr = UUIDUtils.uuid();
		res.setNonceStr(nonceStr);
		PageResult<CustomerResult> customerResult = customerServiceImpl.pageQueryCustomerInfos(1, 1, null, null,
				createrCusCode, null, null, null, null, null, null, null,null);
		if (customerResult == null || customerResult.getTotal() != 1) {
            throw new AppException(PasCode.PARENT_CUS_INVALID.code);
        }
		self.checkCustomerParam(customerRequest);
		//校验入参业务是否合法
		self.checkBusinessInfo(customerRequest);
		// 校验父级业务
		self.checkParentCustomerBusiness(customerRequest.getBusinessList(), customerResult.getRows().get(0));
		// 检查参 数 判断图片是否合法
		checkCustomerInfo(customerRequest);
		CustomerInfo childCustomerInfo = self.createCustomerInfo(customerRequest, createrCusCode);
		CustomerInfo parent = customerInfoMapper.getCustomerInfoByByCustomerCode(createrCusCode);
		if(StringUtils.isNotBlank(parent.getParentCustomerCode()) && Constants.QuickpaySignModel.PLAT_SIGN.code.equals(customerRequest.getSignModel())) {
			throw new AppException(PasCode.CUSTOMER_NOT_PLAT.code);
		}
		// 后续流程对修改与新增是一致的
		CustomerInfo customerInfo = self.initCustomerInfo(customerRequest, createrCusCode, childCustomerInfo);
		customerInfoService.modifyCustomerInfo(customerInfo, new Long(createrCusCode));
		
		CustomerSettleInfo settleInfo = self.initCustomerSettleInfo(customerRequest, customerInfo);
		customerSettleInfoService.modifyCustomerSettleInfo(settleInfo);

		CustomerContactsInfo customerContactsInfo = self.initCustomerContactInfo(customerRequest, customerInfo);
		customerContactsInfoService.modifyCustomerContactsInfo(customerContactsInfo);

		self.addBusinessInfo(customerRequest, createrCusCode, childCustomerInfo , CustomerRequest.action.ADD.code);

		self.uploadAttachements(customerRequest, customerInfo);

		// 调用审核通知接口
		String customerCode = customerInfoService.submitCustomerInfoForAudit(customerInfo.getInfoId(), new Long(createrCusCode), null);
		// 调用自动审核
		if (Constants.customerCategory.EFPS_CUSTOMER_MEMBER.code.equals(customerInfo.getCustomerCategory())) {
			//如果添加的是客户会员，那么直接自动审核通过
			customerAuditInfoService.autoAuditCustomerInfo(customerInfo.getInfoId());
		}
		
		res.setMemberId(customerCode);
		res.setReturnCode("0000");
		res.setCustomerInfoId(customerInfo.getInfoId().toString());
		res.setReturnMsg("Success");
		return res;
	}
	
	@Override
	public CustomerResponse saveSplitMerchantToCust(CustomerRequest customerRequest, String parentCustomerCode) {
		
		CustomerResponse res = new CustomerResponse();
		String nonceStr = UUIDUtils.uuid();
		res.setNonceStr(nonceStr);
		self.checkCustomerParam(customerRequest);
		//校验入参业务是否合法
		self.checkBusinessInfo(customerRequest);
		// 校验父级业务
		self.checkParentBusinessFromCust(customerRequest.getBusinessList(), parentCustomerCode);
		// 检查参 数 判断图片是否合法
		checkCustomerInfo(customerRequest);
		
		// 组件cust会员进件参数
		MemberRequest request = buildCustMemberRequest(customerRequest, parentCustomerCode, true);
		MemberApplyResponse applyResp = custService.applyMember(request);

		res.setMemberId(applyResp.getAcqMerId());
		res.setReturnCode(applyResp.getRespCode());
		res.setCustomerInfoId(applyResp.getAcqCustomerId());
		res.setReturnMsg(applyResp.getRespMsg());
		return res;
	}

	/**
	 * 创建cust会员进件、修改请求对象
	 * @param customerRequest
	 * @param parentCustomerCode
	 * @return
	 */
	private MemberRequest buildCustMemberRequest(CustomerRequest customerRequest, String parentCustomerCode, boolean isAdd) {
		
		MemberRequest request = new MemberRequest();
		request.setAcqSpId(parentCustomerCode);
		request.setBackUrl(customerRequest.getNotifyURL());
		request.setMerchantName(customerRequest.getName());
		
		MemberPaper paper = new MemberPaper();
		paper.setMerchantName(customerRequest.getName());
		paper.setShortName(customerRequest.getShortName());
		paper.setContactPhone(customerRequest.getMobile());
		paper.setServiceTel(customerRequest.getTelephone());
		if( Constants.CustomerType.BUSINESSMAN.code.toString().equals( customerRequest.getType() ) ){
			paper.setMerchantType("1");
		}else if( Constants.CustomerType.ENTERPRISE.code.toString().equals(customerRequest.getType() ) )
			paper.setMerchantType("2");
		else if( Constants.CustomerType.MICRO.code.toString().equals(customerRequest.getType() ) ){
			paper.setMerchantType("3"); // 目前只有小微商户: 商户类型 1：个体工商户 2：企业 3：个人(小微)
		}
		paper.setArea(customerRequest.getAreaCode());
		paper.setIsCc(customerRequest.getUseUSCC());
		paper.setBusinessLicensePhoto(customerRequest.getBusinessLinenseImage());
		paper.setBusinessLicenseCode(customerRequest.getBusinessLicenseNo());
		if(!"2099-12-31".equals(customerRequest.getBusinessLicenesExpiredDate())) {// cust长期为空，不是长期才设置
		// 营业执照到期时间,YYYY-MM-DD或长期，小微商户可选，其他商户类型必填，如果为长期，则填2099-12-31表示长期
			paper.setBusinessLicenseTo(DateUtils.formatDate(
					DateUtils.parseDate(customerRequest.getBusinessLicenesExpiredDate()), "yyyyMMdd")); 
		}
		paper.setLawyerName(customerRequest.getLealPersonName());
		paper.setCertificateName(customerRequest.getLealPersonName());
		paper.setLawyerCertNo(customerRequest.getLealPersonIdentificationNo());
		paper.setLawyerCertPhotoFront(customerRequest.getLealPersonIdentificationImage1());
		paper.setLawyerCertPhotoBack(customerRequest.getLealPersonIdentificationImage2());
		paper.setLawyerCertType(customerRequest.getLealPersonIdentificationType());
		if(!"2099-12-31".equals(customerRequest.getLealPersonIdentificationExpiredDate())) {// cust长期为空，不是长期才设置
		// 证件到期时间,YYYY-MM-DD或长期，小微商户可选，其他商户类型必填，如果为长期，则填2099-12-31表示长期
			paper.setCertificateTo(DateUtils.formatDate(
					DateUtils.parseDate(customerRequest.getLealPersonIdentificationExpiredDate()), "yyyyMMdd"));
		}
        /*imr.setSettMode("D");
        imr.setSettCircle("0");*/
		// 结算周期： 结算模式+周期
		if(customerRequest != null && customerRequest.getSettCircle() != null) {
			paper.setSettCycle(customerRequest.getSettMode()+ "+" + customerRequest.getSettCircle());
		}
		paper.setSettleAccountType(customerRequest.getBankAccountType());
		paper.setOpenBank(customerRequest.getBankName());
		paper.setSettleAccount(customerRequest.getCustomerNameInBank());
		paper.setSettleAccountNo(customerRequest.getAccountNo());
		paper.setSettleTarget(customerRequest.getSettTarget());
		paper.setOpenBankCode(customerRequest.getBankLineNumber());
		paper.setOpenSubBank(customerRequest.getBankName());
		paper.setOpenBankReservePhone(customerRequest.getBankReservedMobile());
		if (isAdd 
			&& ( StringUtils.isBlank(customerRequest.getBankName())
			|| StringUtils.isBlank(customerRequest.getCustomerNameInBank())
			|| StringUtils.isBlank(customerRequest.getAccountNo())
			|| StringUtils.isBlank(customerRequest.getBankLineNumber()) )) {
			// 如果上述的结算信息不全，那么必须结算到易票联账户
			paper.setSettleTarget(String.valueOf(CustomerSettleInfo.settTarget.SETT_TO_EFPS_ACCOUNT.code));
		}
		if(customerRequest.getContactList()!= null && 
				!customerRequest.getContactList().isEmpty()) {
			paper.setEmail(customerRequest.getContactList().get(0).getEmail());
			paper.setContactPerson(customerRequest.getContactList().get(0).getName());
		}
		paper.setSignMode(customerRequest.getSignModel());
		if(customerRequest.getBusinessList() != null) {
			paper.setBusinessList(JSON.toJSONString(customerRequest.getBusinessList()));
		}
		request.setPaper(paper.toString());
		
		return request;
	}
	
	/**
	 * 校验分账子商户父级商户信息
	 * @param businesses
	 * @param result
	 */
	@Logable(businessTag = "checkMemberParentBusiness")
	public void checkParentBusinessFromCust(List<Business> businesses, String parentCustomerNo) {
		
		Set<String> businessCodesSet = custService.queryCustomerBusinessCodes(parentCustomerNo);
		if(businesses != null) {
	        for (Business business : businesses) {
	        	String code = business.getCode();
				if (Business.InletBusiness.SPLITTED.name().equals(code)) {
					//如果是被分账，那么检查header头的customerCode是否开通了分账业务
					if (!businessCodesSet.contains(BusinessCode.FZ.code)) {
						throw new AppException(PasCode.PARENT_NOT_OPEN_FZ.code);
					}
				}else {
					//如果开通的不是被分账业务，那么可能开通会员内转业务
					Set<String> businessCodes = business.getBusinessCode();
					for (String businessCode : businessCodes) {
						if (!businessCode.startsWith("Member") ) {
							if (!businessCodesSet.contains(BusinessCode.DL.code)) {
								throw new AppException(PasCode.PARENT_NOT_OPEN_DL.code);
							}
							if (!businessCodesSet.contains(businessCode)){
								throw new AppException(PasCode.TZSH_OPEN_EXCEPTION.code);
							}
						}
					}
				}
			}
		}
	}
	/**
	 * 校验用户的业务信息
	 * 开通的业务只能是一类，要不就是分账、会员、D0、或者普通收单
	 */
	@Logable(businessTag = "checkBusinessInfo")
	public void checkBusinessInfo(CustomerRequest customerRequest) {
		List<Business> businesses = customerRequest.getBusinessList();
		if(businesses == null) {
			return;
		}
		boolean bfzService = false;
		boolean memberService = false;
		boolean payService = false;
		boolean d0 = false;
		for (Business business : businesses) {
			if ("SPLITTED".equals(business.getCode())
					|| Constants.EfpsNocardService.FZ_NOCARD_PAY.name().equalsIgnoreCase(business.getCode())
					|| Constants.EfpsNocardService.FZ_NOCARD_PAY_CREDIT.name().equalsIgnoreCase(business.getCode())
					|| "WITHDRAW_CREDIT_CARD".equalsIgnoreCase(business.getCode())) {
				//如果当前添加的业务是被分账业务
				if (memberService == true || payService == true || d0 == true) {
					//如果其他互斥业务已经存在，抛出异常
					throw new AppException(MchError.BUSINESS_MUTEX.code);
				}
				bfzService = true;
			}else if ("MEM_EXCHANGE".equals(business.getCode()) || 
					"MEM_REXCHANGE".equals(business.getCode()) || 
					"MEM_INNER_TRANS".equals(business.getCode())) {
				if (bfzService == true || payService == true || d0 == true) {
					//如果其他互斥业务已经存在，抛出异常
					throw new AppException(MchError.BUSINESS_MUTEX.code);
				}
				memberService = true;
			}else if ("D0_WITHDRAW".equals(business.getCode()) || 
					"D0_QUICK_PAY".equals(business.getCode())
					) {
				if (bfzService == true || memberService == true || payService == true) {
					throw new AppException(MchError.BUSINESS_MUTEX.code);
				}
				d0 = true;
			}else {
				//否则添加的是支付类业务
				if (bfzService == true || memberService == true || d0 == true) {
					//如果其他互斥业务已经存在，抛出异常
					throw new AppException(MchError.BUSINESS_MUTEX.code);
				}
				payService = true;
			}
		}
	}
	
	/**
	 * 上传附件并生成数据库的附件记录
	 * @param customerRequest
	 * @param customerInfo
	 */
	@Logable(businessTag = "uploadAttachements")
	protected void uploadAttachements(CustomerRequest customerRequest, CustomerInfo customerInfo) {
		try {
			if(!StringUtils.isBlank(customerRequest.getBusinessLinenseImage()))
			{
				self.commonUpload("03", customerRequest.getBusinessLinenseImage(), "", customerInfo.getInfoId(), "03营业执照");
			}
			if(!StringUtils.isBlank(customerRequest.getLealPersonIdentificationImage1()))
			{
			self.commonUpload("01", customerRequest.getLealPersonIdentificationImage1(), "", customerInfo.getInfoId(),
					"01身份证正面");
			}
			if(!StringUtils.isBlank(customerRequest.getLealPersonIdentificationImage2()))
			{
			self.commonUpload("02", customerRequest.getLealPersonIdentificationImage2(), "", customerInfo.getInfoId(),
					"02身份证背面");
			}
		} catch (Exception e) {
			throw new AppException(PasCode.UPLOAD_ATTACHEMENT_FAILED.code);
		}
	}

	/**
	 * 创建一个新的CustomerInfo，会插入数据库，后续新增/修改均等同于对该CustomerInfo的修改操作
	 * @param customerRequest
	 * @param createrCusCode
	 * @return
	 */
	public CustomerInfo createCustomerInfo(CustomerRequest customerRequest, String createrCusCode) {
		CustomerInfo childCustomerInfo = new CustomerInfo();

		childCustomerInfo = customerServiceImpl.getCustomerInfoForAddOrUpdate(null, new Long(createrCusCode));
		return childCustomerInfo;
	}

	@Logable(businessTag="addBusinessInfo")
	public void addBusinessInfo(CustomerRequest customerRequest, String createrCusCode,
			CustomerInfo childCustomerInfo , String action) {
		// 新增子商户要加业务信息,修改的怎么弄还需要想一下，有效期的问题
		List<Business> businessList = customerRequest.getBusinessList();
		if( businessList == null || businessList.isEmpty()) {
			return;
		}
		self.initMemberBusiness(businessList);
		for (Business business : businessList) {
			Set<String> businessCodes = business.getBusinessCode();
			if (Business.InletBusiness.SPLITTED.name().equals(business.getCode())) {
				// 如果是分账
				for (String businessCode : businessCodes) {
					CustomerBusinessInfoVo businessInfo = self.initCustomerBusinessInfoVo(childCustomerInfo, business,
							businessCode);
					List<BusinessParamInstVo> voList = businessInfo.getBusinessParamInstList();
					voList.add(new BusinessParamInstVo(BusinessParamCode.FZ_CUSTOMER_CODE.code, createrCusCode));
					businessInfo.setBusinessParamInstList(voList);
					String parentSettCycle = self.getParentBusinessSettCode(createrCusCode, businessCode);
					businessInfo.setSettCycle(parentSettCycle);
					CustomerBusinessInfo pareentCustomerBusiness = self.getCustomerBusinessInfo(createrCusCode,businessCode);
					businessInfo.setBusinessCategoryCode(pareentCustomerBusiness.getBusinessCategoryCode());  //0416
					if(businessInfo.getRateMode()==1)
					{
						businessInfo.setFeePer(new Long(businessInfo.getRateParam()));
						businessInfo.setRateParam("");
					}
					customerBusinessInfoService.createCustomerBusinessInfo(businessInfo);
				}
			} else {
				// 如果不是分账业务，那么就必然是代理业务或者是会员相关业务，需要设置代理商客户编码
				for (String businessCode : businessCodes) {
					CustomerBusinessInfoVo businessInfo = self.initCustomerBusinessInfoVo(childCustomerInfo, business,
							businessCode);
					List<BusinessParamInstVo> voList = businessInfo.getBusinessParamInstList();
					if (voList == null) {
						voList = new ArrayList<BusinessParamInstVo>();
					}
					if (business.getCode().startsWith(MEMBER)) {
						if (businessCode.equals(Constants.EfpsAccountService.MEMBER_EXCHANGE_IN.code)) {
							// 如果是会员兑换转入业务
							self.MemberServiceInitRatio(createrCusCode, voList,
									Constants.EfpsAccountService.MEMBER_EXCHANGE_IN.code);
						} else if (businessCode.equals(Constants.EfpsAccountService.MEMBER_EXCHANGE_OUT.code)) {
							self.MemberServiceInitRatio(createrCusCode, voList,
									Constants.EfpsAccountService.MEMBER_EXCHANGE_OUT.code);
						} else if (businessCode.equals(Constants.EfpsAccountService.MEMBER_OPPOSITE_EXCHANGE_IN.code)) {
							self.MemberServiceInitRatio(createrCusCode, voList,
									Constants.EfpsAccountService.MEMBER_OPPOSITE_EXCHANGE_IN.code);
						} else if (businessCode
								.equals(Constants.EfpsAccountService.MEMBER_OPPOSITE_EXCHANGE_OUT.code)) {
							self.MemberServiceInitRatio(createrCusCode, voList,
									Constants.EfpsAccountService.MEMBER_OPPOSITE_EXCHANGE_OUT.code);
						}
					}else if (business.getCode().startsWith(D0)) {
						self.checkD0(businessList);
						voList.add(new BusinessParamInstVo(BusinessParamCode.AGENT_CUSTOMER_CODE.code, createrCusCode));
					} else {
						voList.add(new BusinessParamInstVo(BusinessParamCode.AGENT_CUSTOMER_CODE.code, createrCusCode));
					}
					String parentSettCycle = self.getParentBusinessSettCode(createrCusCode, businessCode);
					businessInfo.setSettCycle(parentSettCycle);
					businessInfo.setBusinessParamInstList(voList);
					CustomerBusinessInfo pareentCustomerBusiness = self.getCustomerBusinessInfo(createrCusCode, businessCode);
					businessInfo.setBusinessCategoryCode(pareentCustomerBusiness.getBusinessCategoryCode()); //0416
					if (businessInfo.getRateMode() == 1) {
						businessInfo.setFeePer(new Long(businessInfo.getRateParam()));
						businessInfo.setRateParam("");
					}
					customerBusinessInfoService.createCustomerBusinessInfo(businessInfo);
				}
			}
		}
		if (action.equals(CustomerRequest.action.ADD.code)) {
			// 子商户进件开通被分账业务，需要开通提现业务,固定费率，手续费为1
			Business business = businessList.get(0);
			business.setCode("WITHDRAW");
			Customer customer = customerMapper.selectByCustomerCode(createrCusCode);
			CustomerBusinessInfo customerBusinessInfo = customerBusinessInfoMapper.
					selectByInfoIdAndBusinessCodeAndDate(customer.getNewestCustomerInfoId(), 
							Constants.BusinessCode.WITHDRAW.code, new Date());
			if(customerBusinessInfo == null) {
				throw new AppException(PasCode.PARENT_NOT_OPEN_WITHDRAW_EXCEPTION.code, PasCode.PARENT_NOT_OPEN_WITHDRAW_EXCEPTION.message);
			}
			business.setRatioMode(customerBusinessInfo.getRateMode() + "");
			business.setRatioParam(customerBusinessInfo.getRateParam());
			Set<String> businessCodes = business.getBusinessCode();
			for (String businessCode : businessCodes) {
				CustomerBusinessInfoVo txBusinessInfo = self.initCustomerBusinessInfoVo(childCustomerInfo, business,
						businessCode);
				if (customerBusinessInfo != null && businessCode.equals(Constants.BusinessCode.WITHDRAW.code)) {
					txBusinessInfo.setFeePer(customerBusinessInfo.getFeePer());
				}
				CustomerBusinessInfo pareentCustomerBusiness = self.getCustomerBusinessInfo(createrCusCode,businessCode);
				txBusinessInfo.setBusinessCategoryCode(pareentCustomerBusiness.getBusinessCategoryCode());//0416
				customerBusinessInfoService.createCustomerBusinessInfo(txBusinessInfo);
			}
		}
	}

	/**
	 * D0业务校验（必须配套开通D0-快捷以及D0-代付业务）
	 * @param businesses
	 */
	@Logable(businessTag = "checkD0")
	public void checkD0(List<Business> businesses) {
		boolean d0QuickPay = false;
		boolean d0WithDraw = false;
		for (Business business : businesses) {
			if (Business.InletBusiness.D0_QUICK_PAY.name().equals(business.getCode())) {
				d0QuickPay = true;
			}
			if (Business.InletBusiness.D0_WITHDRAW.name().equals(business.getCode())) {
				d0WithDraw = true;
			}
		}
		if (!d0QuickPay || !d0WithDraw) {
			throw new AppException(PasCode.D0_BUSINESSLIST_EXCEPTION.code);
		}
	}
	
	/**
	 * 对入参添加的业务进行初始化，如果添加兑换业务，那么必然加上反兑换业务，反之亦然
	 * @param businesseList
	 * @return
	 */
	@Logable(businessTag = "initMemberBusiness")
	public List<Business> initMemberBusiness(List<Business> businesseList){
		Business memberExchange = null;
		Business memberRexchange = null;
		if(businesseList != null) {
			for (Business business : businesseList) {
				if (Business.InletBusiness.MEM_EXCHANGE.name().equals(business.getCode())) {
					memberExchange = business;
				}
				if (Business.InletBusiness.MEM_REXCHANGE.name().equals(business.getCode())) {
					memberRexchange = business;
				}
			}
			if (memberExchange != null && memberRexchange == null) {
				//如果入参业务有会员兑换但是没有会员反兑换，那么假如会员反兑换业务
				memberRexchange = new Business();
				memberRexchange.setBusinessCategory(memberExchange.getBusinessCategory());
				memberRexchange.setCode(Business.InletBusiness.MEM_REXCHANGE.name());
				memberRexchange.setEndDate(memberExchange.getEndDate());
				memberRexchange.setRatioMode(memberExchange.getRatioMode());
				memberRexchange.setRatioParam(memberExchange.getRatioParam());
				memberRexchange.setStartDate(memberExchange.getStartDate());
				businesseList.add(memberRexchange);
			}
			if (memberExchange == null && memberRexchange != null) {
				//如果兑换业务为空，但是反兑换业务不为空
				memberExchange = new Business();
				memberExchange.setBusinessCategory(memberRexchange.getBusinessCategory());
				memberExchange.setCode(Business.InletBusiness.MEM_EXCHANGE.name());
				memberExchange.setEndDate(memberRexchange.getEndDate());
				memberExchange.setRatioMode(memberRexchange.getRatioMode());
				memberExchange.setRatioParam(memberRexchange.getRatioParam());
				memberExchange.setStartDate(memberRexchange.getStartDate());
				businesseList.add(memberExchange);
			}
		}
		return businesseList;
	}
	
	
	/**
	 * 会员业务初始化兑换比例业务参数
	 * @param customerRequest
	 * @param voList
	 */
	@Logable(businessTag = "MemberServiceInitRatio")
	public void MemberServiceInitRatio(String parentCustomerCode, List<BusinessParamInstVo> voList , String businessCode) {
		Customer customer = customerMapper.selectByCustomerCode(parentCustomerCode);
		CustomerBusinessInfo customerBusinessInfo = customerBusinessInfoMapper.selectByInfoIdAndBusinessCodeAndDate(
				customer.getNewestCustomerInfoId() , 
				businessCode , 
				new Date());
		if (customerBusinessInfo != null) {
			BusinessParamInst businessParamInst = businessParamInstMapper.
					selectByBusinessInstDBIdAndCode(customerBusinessInfo.getBusinessId(), 
							Constants.BusinessParamCode.SUBSCRIPTION_RATIO.code);
			if (businessParamInst != null) {
				voList.add(new BusinessParamInstVo(businessParamInst.getCode(), businessParamInst.getValue()));
			}else {
				throw new AppException(PasCode.SUBSCRIPTION_RATIO_NOT_EXIT.code);
			}
		}else {
			throw new AppException(PasCode.PARENT_CUSTOMER_BUSINESS_NOT_EXIT.code);
		}
	}

	/**
	 * 校验分账子商户父级商户信息
	 * @param businesses
	 * @param result
	 */
	@Logable(businessTag = "checkParentCustomerBusiness")
	public void checkParentCustomerBusiness(List<Business> businesses, CustomerResult result) {
     
		if (result == null || !result.getCustomerState().equals("1")) {
        	throw new AppException(PasCode.PARENT_CUS_INVALID.code);
        }
		
		if(businesses != null) {
	        for (Business business : businesses) {
	        	String code = business.getCode();
				if (Business.InletBusiness.SPLITTED.name().equals(code)) {
					//如果是被分账，那么检查header头的customerCode是否开通了分账业务
					boolean openFzBusiness = customerBusinessInfoMapper.isBusinessInfoExist(result.getCustomerInfoId(), BusinessCode.FZ.code);
					if (!openFzBusiness) {
						throw new AppException(PasCode.PARENT_NOT_OPEN_FZ.code);
					}
				}else {
					//如果开通的不是被分账业务，那么可能开通会员内转业务
					Set<String> businessCodes = business.getBusinessCode();
					for (String businessCode : businessCodes) {
						if (!businessCode.startsWith("Member") ) {
							if (!customerBusinessInfoMapper.isBusinessInfoExist(result.getCustomerInfoId(), BusinessCode.DL.code)) {
								throw new AppException(PasCode.PARENT_NOT_OPEN_DL.code);
							}
							if (!customerBusinessInfoMapper.isBusinessInfoExist(result.getCustomerInfoId(), businessCode)) {
								throw new AppException(PasCode.TZSH_OPEN_EXCEPTION.code);
							}
						}
					}
				}
			}
		}
	}
    /**
     * 参数检查
     * 核心为业务检查+联系人检查
     * @param customerRequest
     * @param customerResult
     * @return
     */
    @Logable(businessTag = "checkCustomerParam")
	public void checkCustomerParam(CustomerRequest customerRequest ) {
	
  
        if(StringUtils.isBlank(customerRequest.getShortName()))
        {
        	throw new AppException(PasCode.SHORTNAME_NOT_ALLOW_NULL.code);
        }
		if(	customerRequest.getShortName().length() > 8 ){
			throw new AppException(PasCode.SHORT_NAME_LENGTH_ERROR.code, PasCode.SHORT_NAME_LENGTH_ERROR.message);
		}
        if(StringUtils.isBlank(customerRequest.getAreaCode()))
        {
        	throw new AppException(PasCode.AREACODE_NOT_ALLOW_NULL.code);
        }
        if(StringUtils.isBlank(customerRequest.getSettMode()))
        {
        	throw new AppException(PasCode.SETTMODE_NOT_ALLOW_NULL.code);
        }
        if(StringUtils.isBlank(customerRequest.getSettTarget()))
        {
        	throw new AppException(PasCode.SETTTARGET_NOT_ALLOW_NULL.code);
        }
/*        if( !customerRequest.getSettTarget().equals("1") &&  !customerRequest.getSettTarget().equals("2")){
        	throw new AppException(PasCode.SETTTARGET_ERROR.code, PasCode.SETTTARGET_ERROR.message);
        }*/
        if(StringUtils.isBlank(customerRequest.getNotifyURL()))
        {
        	throw new AppException(PasCode.NOTIFYURL_NOT_ALLOW_NULL.code);
        }
        if(customerRequest.getMobile()==null || customerRequest.getMobile().trim().length()==0)
        {
        	throw new AppException(PasCode.CUSTOMER_MOBILE_MUST.code);
        }
        if(StringUtils.isNotBlank(customerRequest.getUseUSCC())){// 三证合一校验
    		if( !"0".equals(customerRequest.getUseUSCC()) && !"1".equals(customerRequest.getUseUSCC())){
    			throw new AppException(PasCode.USE_UECC_ERROR.code, PasCode.USE_UECC_ERROR.message);
        	}
        }else {
        	if( !Constants.CustomerType.MICRO.code.toString().equals(customerRequest.getType())) {// 非小微则必填
    			throw new AppException(PasCode.USE_UECC_NOT_ALLOW_NULL.code, PasCode.USE_UECC_NOT_ALLOW_NULL.message);
        	}
        }
        
        String customerCategory = self.checkCustomerCategory(customerRequest);
        if (!Constants.customerCategory.EFPS_CUSTOMER.code.equals(customerCategory)) {// 无卡进件商户校验个人信息
            if(StringUtils.isBlank(customerRequest.getLealPersonName())){
            	throw new AppException(PasCode.LEAL_PERSON_NAME_NOT_ALLOW_NULL.code, PasCode.LEAL_PERSON_NAME_NOT_ALLOW_NULL.message);
            }
            if(StringUtils.isBlank(customerRequest.getLealPersonIdentificationType())){
            	throw new AppException(PasCode.LEAL_PERSON_ID_TYPE_NOT_ALLOW_NULL.code, PasCode.LEAL_PERSON_ID_TYPE_NOT_ALLOW_NULL.message);
            }
            if(StringUtils.isBlank(customerRequest.getLealPersonIdentificationNo())){
            	throw new AppException(PasCode.LEAL_PERSON_ID_NO_NOT_ALLOW_NULL.code, PasCode.LEAL_PERSON_ID_NO_NOT_ALLOW_NULL.message);
            }
		}
        
        List<Business> businesses = customerRequest.getBusinessList();
        
       /* if (businesses == null || businesses.isEmpty()) { //  去掉业务入参限制
			//如果为空
        	throw new AppException(PasCode.BUSINESS_NOT_ALLOW_NULL.code);
		}*/
        //检查是否有非法业务编码列表，或者有重复的业务编码
        if(businesses != null) {
        	Set<String> businessCodeSet = new HashSet<String>();
	        for (Business business : businesses)
	        {
	        	if(DateUtils.parseDate(business.getStartDate(),CustomerRequest.BUSINESS_DATE_FORMATTER)==null)
	        	{
	        		throw new AppException(PasCode.BUSINESS_DATE_INVALID.code);
	        	}
	        	if(business.getEndDate()==null ||business.getEndDate().trim().isEmpty())
	        	{
	        		business.setEndDate(CustomerRequest.LONG_TIME_DATE_STR);
	        	}
	        	if(DateUtils.parseDate(business.getEndDate(),CustomerRequest.BUSINESS_DATE_FORMATTER)==null)
	        	{
	        		throw new AppException(PasCode.BUSINESS_DATE_INVALID.code);
	        	}
				//0424
				if(!"1".equals(business.getRatioMode()) && !"2".equals(business.getRatioMode()))
				{
					if(!Business.InletBusiness.WITHDRAW_CREDIT_CARD.name().equals(business.getCode())) { // 代付到信用卡业务不限制 20190626 
						throw new AppException(PasCode.FARE_RATE_TYPE_ERROR.code);
					}
				}
				if (StringUtils.trimToEmpty(business.getRatioParam()).equals("") || new BigDecimal(business.getRatioParam()).doubleValue() < 0) {
					throw new AppException(PasCode.FARE_RATE_ERROR.code);
				}
				Set<String> businessCodes = business.getBusinessCode();//转换为内部的业务编码
	        	if(businessCodes == null || businessCodes.isEmpty())
	        	{
	        		throw new AppException(PasCode.INPUT_BUSINESS_CODE_INVALID.code);
	        	}
	        	for (String businessCode : businessCodes) {
	        		if(!businessCodeSet.add(businessCode))
	            	{
	            		throw new AppException(PasCode.REPEATED_INPUT_BUSIINESS_CODE.code);
	            	}
				}
	        }
        }
        
        //结算账户名校验 add fwy ******** -> 针对2.0版本，********
       if( customerRequest.isLatestVersion() ) {
	       if(!Constants.bankAccountType.public_account.code.equals(customerRequest.getBankAccountType())
	        		&& !Constants.bankAccountType.private_account.code.equals(customerRequest.getBankAccountType())) {
				throw new AppException(PasCode.BANK_ACCOUNT_TYPE_INVALID.code,	PasCode.BANK_ACCOUNT_TYPE_INVALID.message);
	        }
	        if(customerRequest.getCustomerNameInBank() != null) {
		        if(Constants.bankAccountType.public_account.code.equals(customerRequest.getBankAccountType())) {
					if( !customerRequest.getCustomerNameInBank().equals(customerRequest.getName())) {
						throw new AppException(PasCode.SETTLE_ACCOUNT_UNMATCH_MERCHANT.code,	PasCode.SETTLE_ACCOUNT_UNMATCH_MERCHANT.message);
					}
				}else if(Constants.bankAccountType.private_account.code.equals(customerRequest.getBankAccountType())) {
					if( !customerRequest.getCustomerNameInBank().equals(customerRequest.getLealPersonName())) {
						throw new AppException(PasCode.SETTLE_ACCOUNT_UNMATCH_LAWYER.code,	PasCode.SETTLE_ACCOUNT_UNMATCH_LAWYER.message);
					}
				}
	        }
       }
       checkContact(customerRequest.getContactList());
	}
    

    /**
     * 检查联系人数据，目前版本要求必须有且仅有一个类型为管理员的联系人
     * @param contactList
     */
    private void checkContact(List<Contact> contactList) {
    	if(contactList == null || contactList.size()!=1)
        {
        	throw new AppException(PasCode.MUST_HAVE_ONE_CONATCT.code);
        }
    	Contact contact = contactList.get(0);
		if(contact==null)
		{
			throw new AppException(PasCode.MUST_HAVE_ONE_CONATCT.code);
		}
		if(contact.getType()!=Contact.adminType || contact.getEmail()==null || contact.getMobile()==null ||contact.getName()==null)
		{
			throw new AppException(PasCode.MUST_HAVE_ONE_CONATCT.code);
		}
	}

	@Logable(businessTag = "initCustomerContactInfo")
   	public CustomerContactsInfo initCustomerContactInfo(CustomerRequest customerRequest, CustomerInfo customerInfo) {
   		CustomerContactsInfo customerContactsInfo = customerContactsInfoService
   				.getCustomerContactsInfoByCustomerInfoId(customerInfo.getInfoId());
   		customerContactsInfo.setCustomerInfoId(customerInfo.getInfoId());
   		customerContactsInfo.setName(customerRequest.getContactList().get(0).getName());
   		customerContactsInfo.setMobile(customerRequest.getContactList().get(0).getMobile());
   		customerContactsInfo.setTelephone(customerRequest.getTelephone());
   		customerContactsInfo.setEmail(customerRequest.getContactList().get(0).getEmail());
   		customerContactsInfo.setType(customerRequest.getContactList().get(0).getType());
   		customerContactsInfo.setQq(customerRequest.getContactList().get(0).getQq());
   		return customerContactsInfo;
   	}
    
    @Logable(businessTag = "initCustomerSettleInfo")
	public CustomerSettleInfo initCustomerSettleInfo(CustomerRequest customerRequest, CustomerInfo customerInfo) {
		CustomerSettleInfo settleInfo = customerSettleInfoService.getCustomerSettleInfoByCustomerInfoId(customerInfo.getInfoId());
        settleInfo.setCustomerInfoId(customerInfo.getInfoId());
        settleInfo.setCircle(StringUtils.isBlank(customerRequest.getSettCircle())? 
        		settleInfo.getCircle(): new Integer(customerRequest.getSettCircle()));
        settleInfo.setBankAccountType(StringUtils.isBlank(customerRequest.getBankAccountType())?
        		settleInfo.getBankAccountType() : new Short(customerRequest.getBankAccountType()));
        settleInfo.setOpenAccountBankName(customerRequest.getBankName());
        settleInfo.setCustomerNameInBank(customerRequest.getCustomerNameInBank());
        settleInfo.setBankAccountNo(customerRequest.getAccountNo());
        settleInfo.setBankLineNumber(customerRequest.getBankLineNumber());
        if(StringUtils.isNotBlank(customerRequest.getSettTarget())) {
        	settleInfo.setTarget(new Short(customerRequest.getSettTarget()));
        }
        if (StringUtils.isBlank(customerRequest.getBankAccountType()) || 
        	StringUtils.isBlank(customerRequest.getBankName()) || 
        	StringUtils.isBlank(customerRequest.getCustomerNameInBank()) ||
        	StringUtils.isBlank(customerRequest.getAccountNo()) ||
        	StringUtils.isBlank(customerRequest.getBankLineNumber())) {
        	//如果上述的结算信息不全，那么必须结算到易票联账户
        	settleInfo.setTarget(CustomerSettleInfo.settTarget.SETT_TO_EFPS_ACCOUNT.code);
		}
        settleInfo.setSettleMode(customerRequest.getSettMode());
		return settleInfo;
	}

    @Logable(businessTag = "initCustomerInfo")
	public CustomerInfo initCustomerInfo(CustomerRequest customerRequest, String customerCodeFromHeader,
			CustomerInfo childCustomerInfo) {
		CustomerInfo customerInfo = customerInfoService.getCustomerInfoById(childCustomerInfo.getInfoId());
        customerInfo.setCustomerId(childCustomerInfo.getCustomerId());
        customerInfo.setInfoId(childCustomerInfo.getInfoId());
        customerInfo.setName(customerRequest.getName());
        customerInfo.setShortName(customerRequest.getShortName());
        customerInfo.setMobile(customerRequest.getMobile());
        customerInfo.setTelephone(customerRequest.getTelephone());
        customerInfo.setAreaCode(customerRequest.getAreaCode());
        if(!StringUtils.isEmpty(customerRequest.getUseUSCC()))
        {
        	customerInfo.setUseUscc(new Integer(customerRequest.getUseUSCC()));
        }
        customerInfo.setCustomerType(new Integer(customerRequest.getType()));
        customerInfo.setBusinessLicenseNo(customerRequest.getBusinessLicenseNo());
        customerInfo.setBusinessLicenseExpDate(DateUtils.parseDate(customerRequest.getBusinessLicenesExpiredDate(), CustomerRequest.DATE_FORMATTER));
        customerInfo.setLeaPersonName(customerRequest.getLealPersonName());
        if (StringUtils.isNotBlank(customerRequest.getLealPersonIdentificationType())) {
        	customerInfo.setLeaPersoniDentificationType(new Short(customerRequest.getLealPersonIdentificationType()));
		}
        customerInfo.setLeaPersoniDentificationNo(customerRequest.getLealPersonIdentificationNo());
        customerInfo.setLeaPerDenExpDate(DateUtils.parseDate(customerRequest.getLealPersonIdentificationExpiredDate(), CustomerRequest.DATE_FORMATTER));
        customerInfo.setParentCustomerCode(customerCodeFromHeader); //parentCustomerCode
        customerInfo.setBusinessAddress(customerRequest.getBusinessAddress());//0124数据库改为可空
        customerInfo.setRegisteredAddress(customerRequest.getRegisteredAddress()); //0124数据库改为可空
        customerInfo.setNotifyUrl(customerRequest.getNotifyURL()); //异步URL
        String signModel = StringUtils.isBlank(customerRequest.getSignModel()) ?Constants.QuickpaySignModel.CUSTOMER_SIGN.code: customerRequest.getSignModel();
        customerInfo.setSignModel(signModel);
        String customerCategory = self.checkCustomerCategory(customerRequest);
        if (Constants.customerCategory.EFPS_CUSTOMER.code.equals(customerCategory)) {
			//如果判断得出客户，那么做银行卡信息校验
        	
		}
        customerInfo.setCustomerCategory(customerCategory);
		return customerInfo;
	}
    
    @Logable(businessTag = "checkCustomerCategory")
    public String checkCustomerCategory(CustomerRequest customerRequest) {
    	List<Business> businesses = customerRequest.getBusinessList();
    	if(businesses != null) {
	    	for (Business business : businesses) {
				if (Business.InletBusiness.SPLITTED.name().equals(business.getCode()) || 
						Business.InletBusiness.MEM_INNER_TRANS.name().equals(business.getCode()) || 
						Business.InletBusiness.MEM_EXCHANGE.name().equals(business.getCode()) || 
						Business.InletBusiness.MEM_REXCHANGE.name().equals(business.getCode()) || 
						Business.InletBusiness.D0_QUICK_PAY.name().equals(business.getCode()) || 
						Business.InletBusiness.D0_WITHDRAW.name().equals(business.getCode()) ||
						Business.InletBusiness.FZ_NOCARD_PAY.name().equals(business.getCode()) ||
						Business.InletBusiness.FZ_NOCARD_PAY_CREDIT.name().equals(business.getCode()) ||
						Business.InletBusiness.FZ_NOCARD_WITHDRAW.name().equals(business.getCode())
						) {
					return Constants.customerCategory.EFPS_CUSTOMER_MEMBER.code;
				}
			}
    	}
    	return Constants.customerCategory.EFPS_CUSTOMER.code;
    }
    
    /**
     * 将入参中的Business转换为运营管理门户界面录入业务所使用的VO对象
     * @param childCustomerInfo
     * @param business
     * @return
     */
    @Logable(businessTag = "initCustomerBusinessInfoVo")
	public CustomerBusinessInfoVo initCustomerBusinessInfoVo(CustomerInfo childCustomerInfo, Business business , String businessCode) {
    	CustomerBusinessInfoVo businessInfo = new CustomerBusinessInfoVo();
		businessInfo.setBusinessId(sequenceService.nextValue(ADD_MCHBUSINESS));
		businessInfo.setBusinessCode(businessCode);
		businessInfo.setCustomerInfoId(childCustomerInfo.getInfoId());
		businessInfo.setFollowWechatAccount("0");
		businessInfo.setRateMode(Short.valueOf(business.getRatioMode()));
		businessInfo.setRateName(null);
		businessInfo.setRateParam(business.getRatioParam());// 费率取值
		businessInfo.setStatus(new Short("1"));
		businessInfo.setBusinessExamId(null);
		businessInfo.setBusinessParamInstList(new ArrayList<BusinessParamInstVo>());
		try
		{
			SimpleDateFormat formater = new SimpleDateFormat(CustomerRequest.BUSINESS_DATE_FORMATTER);
			Date startTime = formater.parse(business.getStartDate());
			Date endTime = formater.parse(business.getEndDate());
			businessInfo.setBeginTime(startTime);
			businessInfo.setEndTime(endTime);
		}
		catch(Exception e)
		{
			throw new AppException(PasCode.BUSINESS_DATE_INVALID.code);
		}
		return businessInfo;
	}

    @Logable(businessTag = "getCustomerInfoByParam")
    public List<CustomerInfo> getCustomerInfoByParam(Map<String, Object> parameterMap) {
        return customerInfoMapper.getCustomerInfoByParam(parameterMap);
    }

    @Logable(businessTag = "getBusinessParamByParam")
    public List getBusinessParamByParam(Map<String, Object> parameterMap) {
        return customerInfoMapper.getBusinessParamByParam(parameterMap);
    }


    public byte[] base64ToIoByte(String strBase64) throws IOException {
        String string = strBase64;
        try {
            // 解码，然后将字节转换为文件
            byte[] bytes = Base64.decode(string);   //将字符串转换为byte数组
            return bytes;
        } catch (Exception e) {

        }
        return null;
    }


    /**
     * fileBaseSixFour为带base64前缀的图片文件内容
     * 会抛出异常
     * @param businessType
     * @param fileBaseSixFour
     * @param fileName
     * @param customerInfoId
     * @param remark
     */
    @Logable(businessTag="commonUpload")
    public void commonUpload(String businessType, String fileBaseSixFour, String fileName, Long customerInfoId, String remark) {
    	String fileBase64Content = fileBaseSixFour.substring(fileBaseSixFour.indexOf(",")+1, fileBaseSixFour.length());
        String token = fsClient.uploadToekn(businessType, "PAS", "0", remark);
        Base64FileUploadRequest base64Req = new Base64FileUploadRequest();
        base64Req.setBusinessType(businessType);
        base64Req.setFileBaseSixFour(fileBase64Content);
        base64Req.setFileName("");
        base64Req.setUploadToken(token);
        FileUploadResponse fileUploadResponse = fsClient.baseSixFour(base64Req);
        //            附件类型编码（01:身份证正面, 02:身份证背面, 03:组织机构代码, 04:营业执照, 05:店铺招牌, 06:店铺内景, 07:合作协议, 08:授权委托书, 09:签约银行卡图片, 10:手持证件照, 11:入境证明, 12:银行账户信息, 13:其它证件/文件）", required = true, dataType = "String", paramType = "query", valueRange = "{01,02,03,04,05,06,07,08,09,10,11,12,13}")
        customerAttachmentService.uploadAttachment(null, customerInfoId, businessType, "", fileUploadResponse.getUniqueId(), null);

    }

    
    /**
	 * 根据入参更新客户的信息
	 * @param customerRequest 入参子商户需要更新的字段
	 */
    @Logable(businessTag = "updateSplitMerchant")
	@Override
	@Transactional
	public long updateSplitMerchant(CustomerRequest customerRequest , String createrCusCode) {
		// TODO Auto-generated method stub
    	//入参必填业务校验
    	self.checkUpdateSplitMerchantParam(customerRequest , createrCusCode);
    	//将资料批量备份
    	CustomerInfo customerInfo = self.copyCustomerAllInfo(customerRequest);
    	//更新商户基本资料
    	self.updateCustomerInfo(customerRequest, customerInfo);
    	//更新结算信息
    	self.updateCustomerSettleInfo(customerRequest, customerInfo);
    	//更新业务信息
    	self.updateCustomerBusinessInfo(customerRequest, customerInfo);
    	//更新商户联系人信息
    	self.updateCustomerContactInfo(customerRequest, customerInfo);
    	//更新商户的附件信息
    	self.updateCustomerAttachmentInfo(customerRequest, customerInfo);
    	// 调用审核通知接口
    	customerInfoService.submitCustomerInfoForAudit(customerInfo.getInfoId(), new Long(createrCusCode), null);
    	if (Constants.customerCategory.EFPS_CUSTOMER_MEMBER.code.equals(customerInfo.getCustomerCategory())) {
			//如果添加的是客户会员，那么直接自动审核通过
			customerAuditInfoService.autoAuditCustomerInfo(customerInfo.getInfoId());
		}
    	return customerInfo.getInfoId();
	}
    /**
     * 修改子商户接口入参业务参数校验
     * @param customerRequest
     */
    @Logable(businessTag = "checkUpdateSplitMerchantParam")
    protected void checkUpdateSplitMerchantParam(CustomerRequest customerRequest , String createrCusCode) {
    	String customerCode = customerRequest.getCustomerCode();
    	if(customerRequest.isLatestVersion()) {
    		customerCode = customerRequest.getMemberId();
    	}
    	Customer customer = customerMapper.selectByCustomerCode(customerCode);
    	if (customer == null) {
			throw new AppException(MchError.CUM_NOT_EXIST.code);
		}
    	CustomerInfo customerInfo = customerInfoMapper.selectByPrimaryKey(customer.getNewestCustomerInfoId());
    	if (!createrCusCode.equals(customerInfo.getParentCustomerCode())) {
    		throw new AppException(PasCode.PARENT_CUS_INVALID.code);
		}
    	if (StringUtils.isBlank(customerRequest.getCustomerCode()) || 
    		StringUtils.isBlank(customerRequest.getNotifyURL()) || 
    		StringUtils.isBlank(customerRequest.getNonceStr())) {
			throw new AppException(MchError.CUM_INVALID_PARAM.code);
		}
/*        if(StringUtils.isNotBlank(customerRequest.getSettTarget())
        		&& !customerRequest.getSettTarget().equals("1") &&  !customerRequest.getSettTarget().equals("2")){
        	throw new AppException(PasCode.SETTTARGET_ERROR.code, PasCode.SETTTARGET_ERROR.message);
        }*/
		if(	StringUtils.isNotBlank(customerRequest.getSignModel())
				&& !Constants.QuickpaySignModel.contains(customerRequest.getSignModel())) {
			throw new AppException(PasCode.SIGN_MODEL_ERROR.code, PasCode.SIGN_MODEL_ERROR.message);
		}
		if(StringUtils.isNotBlank(customerRequest.getShortName()) &&
				customerRequest.getShortName().length() > 8 ){
			throw new AppException(PasCode.SHORT_NAME_LENGTH_ERROR.code, PasCode.SHORT_NAME_LENGTH_ERROR.message);
		}
 /*   	if (MchConstants.BankAccountType.TO_PRIVATE.code.equals(customerRequest.getBankAccountType())) {
			if (StringUtils.isBlank(customerRequest.getBankReservedMobile())) {
				throw new AppException(MchError.PRIVATE_MUST_WITH_MOBILE.code);
			}
		}*/
    }
    
    /**
     * 修改子商户接口入参业务参数校验
     * @param customerRequest
     */
    @Logable(businessTag = "copyCustomerAllInfo")
    protected CustomerInfo copyCustomerAllInfo(CustomerRequest customerRequest) {
    	String customerCode = customerRequest.getCustomerCode();
    	if(customerRequest.isLatestVersion()) {
    		customerCode = customerRequest.getMemberId();
    	}
    	Customer customer = customerMapper.selectByCustomerCode(customerCode);
    	CustomerInfo customerInfo = customerServiceImpl.getCustomerForUpdate(customer.getNewestCustomerInfoId());
    	return customerInfo;
    }
    
    /**
     * 更新商户的基本资料
     * @param customerRequest
     * @param customerInfo
     * @return
     */
    @Logable(businessTag = "updateCustomerInfo")
    protected void updateCustomerInfo(CustomerRequest customerRequest , CustomerInfo customerInfo) {
    	if (StringUtils.isNotBlank(customerRequest.getName())) {
			customerInfo.setName(customerRequest.getName());
		}
    	if (StringUtils.isNotBlank(customerRequest.getShortName())) {
			customerInfo.setShortName(customerRequest.getShortName());
		}
    	if (StringUtils.isNotBlank(customerRequest.getTelephone())) {
			customerInfo.setTelephone(customerRequest.getTelephone());
		}
       	if (StringUtils.isNotBlank(customerRequest.getMobile())) {
			customerInfo.setMobile(customerRequest.getMobile());
		}
    	if (StringUtils.isNotBlank(customerRequest.getAreaCode())) {
			customerInfo.setAreaCode(customerRequest.getAreaCode());
		}
    	if (StringUtils.isNotBlank(customerRequest.getBusinessLicenesExpiredDate())) {
			customerInfo.setBusinessLicenseExpDate(DateUtils.parseDate(customerRequest.getBusinessLicenesExpiredDate(), CustomerRequest.DATE_FORMATTER));
		}
    	if (StringUtils.isNotBlank(customerRequest.getLealPersonName())) {
			customerInfo.setLeaPersonName(customerRequest.getLealPersonName());
		}
    	if (StringUtils.isBlank(customerInfo.getLeaPersoniDentificationNo())) {
    		//如果之前没提交过法人证件号
    		if (StringUtils.isNotBlank(customerRequest.getLealPersonIdentificationNo())) {
    			customerInfo.setLeaPersoniDentificationNo(customerRequest.getLealPersonIdentificationNo());
    		}
		}else {
			//如果数据库中已经有相关记录
			if (StringUtils.isNotBlank(customerRequest.getLealPersonIdentificationNo())
					&& !customerInfo.getLeaPersoniDentificationNo().equals(customerRequest.getLealPersonIdentificationNo())) {
				throw new AppException(PasCode.NOT_ALLOW_CHANGE_LPIN.code);
			}
		}
    	if (StringUtils.isNotBlank(customerRequest.getLealPersonIdentificationExpiredDate())) {
			customerInfo.setLeaPerDenExpDate(DateUtils.parseDate(customerRequest.getLealPersonIdentificationExpiredDate() , CustomerRequest.DATE_FORMATTER));
		}
    	customerInfo.setNotifyUrl(customerRequest.getNotifyURL());
    	customerInfoMapper.updateByPrimaryKeySelective(customerInfo);
    }
    
    /**
     * 更新商户的结算信息
     * @param customerRequest
     * @param customerInfo
     * @return
     */
    @Logable(businessTag = "updateCustomerInfo")
    protected void updateCustomerSettleInfo(CustomerRequest customerRequest , CustomerInfo customerInfo) {
    	CustomerSettleInfo customerSettleInfo = customerSettleInfoMapper.getByCustomerInfoId(customerInfo.getInfoId());
    	if (StringUtils.isNotBlank(customerRequest.getBankAccountType())) {
			customerSettleInfo.setBankAccountType(Short.parseShort(customerRequest.getBankAccountType()));
		}
    	if (StringUtils.isNotBlank(customerRequest.getBankName())) {
			customerSettleInfo.setOpenAccountBankName(customerRequest.getBankName());
		}
    	if (StringUtils.isNotBlank(customerRequest.getCustomerNameInBank())) {
			customerSettleInfo.setCustomerNameInBank(customerRequest.getCustomerNameInBank());
		}
    	if (StringUtils.isNotBlank(customerRequest.getAccountNo())) {
			customerSettleInfo.setBankAccountNo(customerRequest.getAccountNo());
		}
    	if (StringUtils.isNotBlank(customerRequest.getBankLineNumber())) {
			customerSettleInfo.setBankLineNumber(customerRequest.getBankLineNumber());
		}
    	if (StringUtils.isNotBlank(customerRequest.getSettTarget())) {
			customerSettleInfo.setTarget(Short.parseShort(customerRequest.getSettTarget()));
		}
    	if (StringUtils.isNoneBlank(customerRequest.getBankReservedMobile())) {
			customerSettleInfo.setBankReservedMobile(customerRequest.getBankReservedMobile());
		}
    	customerSettleInfoMapper.updateByPrimaryKeySelective(customerSettleInfo);
    }
    
    /**
     * 更新商户的业务信息
     * @param customerRequest
     * @param customerInfo
     * @return
     */
    @Logable(businessTag = "updateCustomerBusinessInfo")
    protected void updateCustomerBusinessInfo(CustomerRequest customerRequest , CustomerInfo customerInfo) {
    	List<Business> businesses = customerRequest.getBusinessList();
    	if (businesses != null && !businesses.isEmpty()) {
    		for (Business business : businesses) {
    			Date startDate = DateUtils.parseDate(business.getStartDate(), "yyyyMMdd");
    			Date endDate = DateUtils.parseDate(business.getEndDate(), "yyyyMMdd");
    			Set<String> buinessCodes = business.getBusinessCode();
    			for (String businessCode : buinessCodes) {
    				CustomerBusinessInfo customerBusinessInfo = customerBusinessInfoMapper.selectByInfoIdAndBusinessCodeAndDate(customerInfo.getInfoId(), businessCode, startDate);
    				if (customerBusinessInfo != null) {
						throw new AppException(PasCode.NOT_ALLOW_REPEATED_BUSINESS.code);
					}
    				customerBusinessInfo = customerBusinessInfoMapper.selectByInfoIdAndBusinessCodeAndDate(customerInfo.getInfoId(), businessCode, endDate);
    				if (customerBusinessInfo != null) {
						throw new AppException(PasCode.NOT_ALLOW_REPEATED_BUSINESS.code);
					}
				}
			}
    		//不存在重复的业务，那么再添加业务
    		self.addBusinessInfo(customerRequest, customerInfo.getParentCustomerCode(), customerInfo , CustomerRequest.action.MODIFY.code);
		}
    }
    
    /**
     * 更新商户的联系人信息
     * @param customerRequest
     * @param customerInfo
     * @return
     */
    @Logable(businessTag = "updateCustomerContactInfo")
    protected void updateCustomerContactInfo(CustomerRequest customerRequest , CustomerInfo customerInfo) {
    	List<Contact> contacts = customerRequest.getContactList();
    	if (contacts == null || contacts.isEmpty()) {
    		return;
		}
    	CustomerContactsInfo customerContactsInfo = customerContactsInfoMapper.selectByCustomerInfoId(customerInfo.getInfoId());
    	Contact contact = contacts.get(0);
    	if (contact.getType() != null) {
			customerContactsInfo.setType(contact.getType());
		}
    	if (StringUtils.isNotBlank(contact.getEmail())) {
			customerContactsInfo.setEmail(contact.getEmail());
		}
    	if (StringUtils.isNotBlank(contact.getMobile())) {
			customerContactsInfo.setMobile(contact.getMobile());
		}
    	if (StringUtils.isNotBlank(contact.getName())) {
			customerContactsInfo.setName(contact.getName());
		}
    	if (StringUtils.isNotBlank(contact.getQq())) {
			customerContactsInfo.setQq(contact.getQq());
		}
    	customerContactsInfoMapper.updateByPrimaryKeySelective(customerContactsInfo);
    }
    
    /**
     * 更新商户的附件信息
     * @param customerRequest
     * @param customerInfo
     * @return
     */
    @Logable(businessTag = "updateCustomerContactInfo")
    protected void updateCustomerAttachmentInfo(CustomerRequest customerRequest , CustomerInfo customerInfo) {
    	self.uploadAttachements(customerRequest, customerInfo);
    }
    @Logable(businessTag = "getParentBusinessSettCode")
    protected String getParentBusinessSettCode(String parentCustomerCode , String businessCode) {
    	if ("BFZ".equals(businessCode)) {
			businessCode = "FZ";
		}
    	Date date = new Date();
    	Customer customer = customerMapper.selectByCustomerCode(parentCustomerCode);
    	CustomerBusinessInfo customerBusinessInfo = customerBusinessInfoMapper.
				selectByInfoIdAndBusinessCodeAndDate(customer.getNewestCustomerInfoId(),
						businessCode, new Date());
		if (customerBusinessInfo == null) {
			throw new AppException(PasCode.PARENT_CUSTOMER_BUSINESS_NOT_EXIT.code);
		}
    	PasSettCycleRuleInst pasSettCycleRuleInst = settCycleRuleInstMapper.selectByBusinessInstDBIdAndDate(customerBusinessInfo.getBusinessId() , date);
		if (pasSettCycleRuleInst == null) {
			throw new AppException(PasCode.CAN_NOT_FIND_SETT_CYCLE_INST.code);
		}
    	return pasSettCycleRuleInst.getSettCycleRuleCode();
    }

	@Logable(businessTag = "getCustomerBusinessInfo")
	//查询父商户业务
	public CustomerBusinessInfo getCustomerBusinessInfo(String createrCusCode, String businessCode) {
		Customer customer = customerMapper.selectByCustomerCode(createrCusCode);
		CustomerBusinessInfo customerBusinessInfo = customerBusinessInfoMapper.
				selectByInfoIdAndBusinessCodeAndDate(customer.getNewestCustomerInfoId(),
						businessCode, new Date());
		return customerBusinessInfo;
	}

	@Override
	public CustomerResponse updateSplitMerchantInCust(CustomerRequest customerRequest, String customerCodeFromHeader) {
		
		CustomerResponse resp = new CustomerResponse();
		self.checkUpdateSplitMerchantParamInCust(customerRequest, customerCodeFromHeader) ;

		// 组件请求报文
		MemberRequest request = buildCustMemberRequest(customerRequest, customerCodeFromHeader, false);
		if(customerRequest.isLatestVersion()) {
			request.setAcqMerId(customerRequest.getMemberId());
		}else {
			request.setAcqMerId(customerRequest.getCustomerCode()); // 非最新版本该字段为子商户编号
		}
		MemberModifyResponse modifyResp = custService.modify(request, customerCodeFromHeader);
		
		resp.setMemberId(modifyResp.getAcqMerId());
		resp.setReturnCode(modifyResp.getRespCode());
		resp.setCustomerInfoId(modifyResp.getAcqCustomerId());
		resp.setReturnMsg(modifyResp.getRespMsg());
		resp.setNonceStr(UUIDUtils.uuid());
		
		return resp;
	}


    /**
     * 修改子商户接口入参业务参数校验
     * @param customerRequest
     */
    @Logable(businessTag = "checkUpdateSplitMerchantParamInCust")
    protected void checkUpdateSplitMerchantParamInCust(CustomerRequest customerRequest , String createrCusCode) {

    	if (StringUtils.isBlank(customerRequest.getCustomerCode()) || 
    		StringUtils.isBlank(customerRequest.getNotifyURL()) || 
    		StringUtils.isBlank(customerRequest.getNonceStr())) {
			throw new AppException(MchError.CUM_INVALID_PARAM.code);
		}
        /*if(StringUtils.isNotBlank(customerRequest.getSettTarget())
        		&& !customerRequest.getSettTarget().equals("1") &&  !customerRequest.getSettTarget().equals("2")){
        	throw new AppException(PasCode.SETTTARGET_ERROR.code, PasCode.SETTTARGET_ERROR.message);
        }*/
		if(	StringUtils.isNotBlank(customerRequest.getSignModel())
				&& !Constants.QuickpaySignModel.contains(customerRequest.getSignModel())) {
			throw new AppException(PasCode.SIGN_MODEL_ERROR.code, PasCode.SIGN_MODEL_ERROR.message);
		}
		if(StringUtils.isNotBlank(customerRequest.getShortName()) &&
				customerRequest.getShortName().length() > 8 ){
			throw new AppException(PasCode.SHORT_NAME_LENGTH_ERROR.code, PasCode.SHORT_NAME_LENGTH_ERROR.message);
		}
   /* 	if (MchConstants.BankAccountType.TO_PRIVATE.code.equals(customerRequest.getBankAccountType())) {
			if (StringUtils.isBlank(customerRequest.getBankReservedMobile())) {
				throw new AppException(MchError.PRIVATE_MUST_WITH_MOBILE.code);
			}
		}
    */	
    }
    

}
