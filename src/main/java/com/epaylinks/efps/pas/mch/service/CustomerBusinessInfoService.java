package com.epaylinks.efps.pas.mch.service;

import java.util.List;
import java.util.Map;

import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.pas.mch.controller.dto.SettCycleRuleDTO;
import com.epaylinks.efps.pas.mch.domain.CustomerBusinessInfo;
import com.epaylinks.efps.pas.mch.vo.CustomerBusinessInfoVo;

/**
 * 业务逻辑层：客户业务信息
 */
public interface CustomerBusinessInfoService {

	void createCustomerBusinessInfo(CustomerBusinessInfoVo customerBusinessInfoVo);

	PageResult<CustomerBusinessInfo> pageQueryCustomerBusinessInfos(int pageNum, int pageSize, Long customerInfoId);

	boolean deleteCustomerBusinessInfo(Long id);

	List<CustomerBusinessInfo> getCustomerBusinessInfos(Long customerInfoId);

	CustomerBusinessInfo getCustomerBusinessInfoByPk(Long businessId);

	List<CustomerBusinessInfo> queryListByCustomerInfoId(Long customerInfoId);

	int countCustomerBusinessInfo(Long customerInfoId);

	String checkCustomerBusinessInfoExist(Long customerInfoId, String businessCode);

	List<CustomerBusinessInfo> getAllCustomerBusinessInfo();
	/**
	 * 根据customerCode获取用户的业务实例
	 * @param customerCode 客户编码
	 * @return
	 */
	List<CustomerBusinessInfo> getCustBusinessInfoByCustomerCode(String customerCode);

	void addRefundBusiness(List<CustomerBusinessInfo> customerBusinessInfoVos);

	public PageResult<CustomerBusinessInfo> pageQueryCustomerBusinessInfos(int pageNum, int pageSize,Map map) ;

	public void modifyCustomerBusinessInfo(CustomerBusinessInfoVo customerBusinessInfoVo) ;
	
	List<SettCycleRuleDTO> querySupportSettCycleByBusinessCode(String businessCode);

	public boolean isRealtimeOfBusiness(String businessCode) ;
}