package com.epaylinks.efps.pas.mch.model;

import com.epaylinks.efps.common.business.CommonOuterResponse;

/**
 * Created by adm on 2018/1/23.
 */
public class CustomerResponse extends CommonOuterResponse {
    private String returnCode;
    private String customerInfoId;
    private String returnMsg;
    private String nonceStr;
    
    private String memberId;//会员编码、无卡分账业务用到

    public String getCustomerInfoId() {
        return customerInfoId;
    }

    public void setCustomerInfoId(String customerInfoId) {
        this.customerInfoId = customerInfoId;
    }

    public String getReturnMsg() {
        return returnMsg;
    }

    public void setReturnMsg(String returnMsg) {
        this.returnMsg = returnMsg;
    }

    public String getNonceStr() {
        return nonceStr;
    }

    public void setNonceStr(String nonceStr) {
        this.nonceStr = nonceStr;
    }

    public String getReturnCode() {
        return returnCode;
    }

    public void setReturnCode(String returnCode) {
        this.returnCode = returnCode;
    }

	public String getMemberId() {
		return memberId;
	}

	public void setMemberId(String memberId) {
		this.memberId = memberId;
	}

}
