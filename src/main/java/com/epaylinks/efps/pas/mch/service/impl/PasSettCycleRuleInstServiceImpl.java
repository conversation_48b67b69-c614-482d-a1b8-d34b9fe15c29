package com.epaylinks.efps.pas.mch.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.pas.mch.dao.PasSettCycleRuleInstMapper;
import com.epaylinks.efps.pas.mch.domain.PasSettCycleRuleInst;
import com.epaylinks.efps.pas.mch.service.PasSettCycleRuleInstService;

import java.util.List;

@Service
public class PasSettCycleRuleInstServiceImpl implements PasSettCycleRuleInstService{
	@Autowired
	private PasSettCycleRuleInstMapper pasSettCycleRuleInstMapper;
	
	@Override
	@Logable(businessTag="PasSettCycleRuleInstServiceImpl.addInst")
	public void addInst(PasSettCycleRuleInst pasSettCycleRuleInst) {
		// TODO Auto-generated method stub
		pasSettCycleRuleInstMapper.insert(pasSettCycleRuleInst);
	}
		@Logable(businessTag="PasSettCycleRuleInstServiceImpl.selectByBusinessInstDBId")
	public List<PasSettCycleRuleInst>  selectByBusinessInstDBId(Long businessId) {
		// TODO Auto-generated method stub
		return pasSettCycleRuleInstMapper.selectByBusinessInstDBId(businessId);
	}


}
