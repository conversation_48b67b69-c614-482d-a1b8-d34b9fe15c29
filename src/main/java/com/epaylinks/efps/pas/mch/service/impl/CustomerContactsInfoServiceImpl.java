package com.epaylinks.efps.pas.mch.service.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.pas.mch.common.MchConstants;
import com.epaylinks.efps.pas.mch.common.MchError;
import com.epaylinks.efps.pas.mch.dao.CustomerContactsInfoMapper;
import com.epaylinks.efps.pas.mch.domain.CustomerContactsInfo;

import java.util.List;

/**
 * 业务逻辑层：客户联系人信息
 * <AUTHOR>
 */
@Service
public class CustomerContactsInfoServiceImpl {

	@Autowired
	CustomerContactsInfoMapper customerContactsInfoMapper;
	
	/**
	 * 查看客户联系人信息
	 */
	@Logable(businessTag = "getCustomerContactsInfoByCustomerInfoId")
	public CustomerContactsInfo getCustomerContactsInfoByCustomerInfoId(Long customerInfoId) {
		CustomerContactsInfo contactsInfo = customerContactsInfoMapper.getByCustomerInfoId(customerInfoId);
		if (contactsInfo == null) {
			throw new AppException(MchError.ADMIN_INFO_NOT_EXIST.code);
		}
		return contactsInfo;
	}
	
	/**
	 * 修改客户联系人信息
	 * @return customerInfoId
	 */
	@Logable(businessTag = "modifyCustomerContactsInfo")
	public void modifyCustomerContactsInfo(CustomerContactsInfo contactsInfo) {
		if(StringUtils.isNoneBlank(contactsInfo.getMobile())){
			//如果入参中有手机号，那么就直接修改手机号
			customerContactsInfoMapper.updateByPrimaryKey(contactsInfo);
		}
	}
}
