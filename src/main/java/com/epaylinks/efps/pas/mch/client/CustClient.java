package com.epaylinks.efps.pas.mch.client;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.business.cust.model.Customer;
import com.epaylinks.efps.common.log.Logable;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient("CUST")
public interface CustClient {
	
	/**
	 * 审核通知接口
	 * @param customerNo 业务编码
	 * @param auditOption 审核意见 00通过,01否决
	 * @return true（已经产生过交易），false（从没产生过交易）
	 */
	@PostMapping(value = "/merchant/auditNotify")
	@Logable(businessTag = "PAS_CustClient.auditNotify.CUST")
	public CommonOuterResponse auditNotify(
			@RequestParam("customerNo") String customerNo,
			@RequestParam("auditOption") String auditOption,
			@RequestParam("comment") String comment);

	/**
	 * 根据客户编号查询cust下游客户
	 *
	 * @param customerNo
	 * @return
	 */
	@RequestMapping(value = "/customer/queryCustomerByCustomerNo", method = RequestMethod.GET)
	Customer queryCustomerByCustomerNo(@RequestParam("customerNo") String customerNo);
}