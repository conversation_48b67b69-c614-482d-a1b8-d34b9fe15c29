package com.epaylinks.efps.pas.mch.domain;

import java.util.Date;
import java.util.List;

import com.epaylinks.efps.pas.pas.domain.BusinessParamInst;
import com.alibaba.fastjson.JSON;

public class CustomerBusinessInfo {
    private Long businessId;

    private Long customerInfoId;//比CUM多的字段

    private Long customerId;

    private String customerCode;

    private String businessCode;

    private String rateName;

    private Short rateMode;

    private String rateParam;

    private Short status;

    private String followWechatAccount;

    private String refundProducureFee;

    private String refundRateMode;

    private String refundRateParam;

    private List<String> payMethods;

    private String payMethodNames;

    private String businessName;
    /**
     * 业务实例的业务主键（取值：业务编码_客户编码_有效期开始时间点）
     */
    private String businessExamId;
    /**
     * 业务实例的开始时间
     */
    private Date beginTime;
    /**
     * 业务实例的结束时间
     */
    private Date endTime;
    /**
     * 业务实例对应的业务参数实例
     */

    private Long feePer;
    private Long minFee;
    private Long maxFee;

    /**
     * 是否禁止使用信用卡
     * 0：禁止使用信用卡
     * 1：允许使用信用卡
     */
    private String noCreditcards;

    private String canRefund;
    private String canAdvance;  //是否允许垫资
    private String businessCategoryCode;  //业务类型
    private String businessCategoryName;  //业务类型

    public String getCanAdvance() {
        return canAdvance;
    }

    public void setCanAdvance(String canAdvance) {
        this.canAdvance = canAdvance;
    }

    public String getBusinessCategoryCode() {
        return businessCategoryCode;
    }

    public void setBusinessCategoryCode(String businessCategoryCode) {
        this.businessCategoryCode = businessCategoryCode;
    }

    private String settCycle;
    
    public String getCanRefund() {
        return canRefund;
    }

    public void setCanRefund(String canRefund) {
        this.canRefund = canRefund;
    }

    public Long getFeePer() {
        return feePer;
    }

    public void setFeePer(Long feePer) {
        this.feePer = feePer;
    }

    public Long getMinFee() {
        return minFee;
    }

    public void setMinFee(Long minFee) {
        this.minFee = minFee;
    }

    public Long getMaxFee() {
        return maxFee;
    }

    public void setMaxFee(Long maxFee) {
        this.maxFee = maxFee;
    }

    private List<BusinessParamInst> businessParamInsts;
    public Long getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    public Long getCustomerInfoId() {
        return customerInfoId;
    }

    public void setCustomerInfoId(Long customerInfoId) {
        this.customerInfoId = customerInfoId;
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }

    public String getRateName() {
        return rateName;
    }

    public void setRateName(String rateName) {
        this.rateName = rateName;
    }

    public Short getRateMode() {
        return rateMode;
    }

    public void setRateMode(Short rateMode) {
        this.rateMode = rateMode;
    }

    public String getRateParam() {
        return rateParam;
    }

    public void setRateParam(String rateParam) {
        this.rateParam = rateParam;
    }

    public Short getStatus() {
        return status;
    }

    public void setStatus(Short status) {
        this.status = status;
    }

    public String getFollowWechatAccount() {
        return followWechatAccount;
    }

    public void setFollowWechatAccount(String followWechatAccount) {
        this.followWechatAccount = followWechatAccount;
    }

    public String getRefundProducureFee() {
        return refundProducureFee;
    }

    public void setRefundProducureFee(String refundProducureFee) {
        this.refundProducureFee = refundProducureFee;
    }

    public String getRefundRateMode() {
        return refundRateMode;
    }

    public void setRefundRateMode(String refundRateMode) {
        this.refundRateMode = refundRateMode;
    }

    public String getRefundRateParam() {
        return refundRateParam;
    }

    public void setRefundRateParam(String refundRateParam) {
        this.refundRateParam = refundRateParam;
    }

    public List<String> getPayMethods() {
        return payMethods;
    }

    public void setPayMethods(List<String> payMethods) {
        this.payMethods = payMethods;
    }

    public String getPayMethodNames() {
        return payMethodNames;
    }

    public void setPayMethodNames(String payMethodNames) {
        this.payMethodNames = payMethodNames;
    }

    public String getBusinessName() {
        return businessName;
    }

    public void setBusinessName(String businessName) {
        this.businessName = businessName;
    }

	public String getBusinessExamId() {
		return businessExamId;
	}

	public void setBusinessExamId(String businessExamId) {
		this.businessExamId = businessExamId;
	}

	public Date getBeginTime() {
		return beginTime;
	}

	public void setBeginTime(Date beginTime) {
		this.beginTime = beginTime;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public List<BusinessParamInst> getBusinessParamInsts() {
		return businessParamInsts;
	}

	public void setBusinessParamInsts(List<BusinessParamInst> businessParamInsts) {
		this.businessParamInsts = businessParamInsts;
	}
	
	public String getNoCreditcards() {
		return noCreditcards;
	}

	public void setNoCreditcards(String noCreditcards) {
		this.noCreditcards = noCreditcards;
	}

	public enum NoCreditcards {
		FORBID("0" , "禁止使用信用卡"),
		ALLOW("1" , "允许使用信用卡");
		public final String code;
		public final String message;
		NoCreditcards(String code, String message) {
			this.code = code;
			this.message = message;
		}
		public String getCode() {
			return code;
		}
		public String getMessage() {
			return message;
		}
	}

	public String getSettCycle() {
        return settCycle;
    }

    public void setSettCycle(String settCycle) {
        this.settCycle = settCycle;
    }

	@Override
	public String toString() {
		// TODO Auto-generated method stub
		return JSON.toJSONString(this);
	}
}