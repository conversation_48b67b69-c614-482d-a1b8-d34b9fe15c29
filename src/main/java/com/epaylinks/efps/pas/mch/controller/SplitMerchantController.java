package com.epaylinks.efps.pas.mch.controller;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.systemcode.ReturnCodeUtil;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.common.util.UUIDUtils;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.pas.common.LogService;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.mch.model.CustomerRequest;
import com.epaylinks.efps.pas.mch.model.CustomerResponse;
import com.epaylinks.efps.pas.mch.model.SplitAuditStatusRequest;
import com.epaylinks.efps.pas.mch.model.SplitAuditStatusResponse;
import com.epaylinks.efps.pas.mch.service.SplitCustomerService;
import com.epaylinks.efps.pas.mch.service.feign.CustService;
import com.epaylinks.efps.pas.mch.service.impl.CustomerAuditInfoServiceImpl;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/**
 * 运营门户管理系统 - 分账商户信息管理
 *
 * <AUTHOR>
 */
@RestController
@Api(value = "MerchantSplitController", description = "商户信息管理")
public class SplitMerchantController {

    @Autowired
    private CustomerAuditInfoServiceImpl customerAuditInfoService;
    @Autowired
    private SplitCustomerService customerService;
    @Autowired
    private CustService custService;
    @Autowired
    private LogService logService;
    @Autowired
	private ReturnCodeUtil returnCodeUtil;

    /**
     * 客户信息: for ADD or UPDATE
     * <p/>
     * 注：获取当前录入的客户信息，如果不存在则新建一条缺省数据并返回给前端
     */
    @PostMapping("CustomerInfo/AddMerchant")
    @Logable(businessTag = "saveSplitMerchant")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "分账子商户录入", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "customerInfoId", value = "客户信息ID", required = false, dataType = "Long", paramType = "query", digit = true),

    })
	public CustomerResponse saveSplitMerchant(@RequestBody CustomerRequest customerRequest,
			@RequestHeader(value = "x-customer-code", required = true) String customerCodeFromHeader) {
    	CustomerResponse res = new CustomerResponse();
    	String nonceStr = UUIDUtils.uuid();
		res.setNonceStr(nonceStr);
		try {
			if( customerRequest.isLatestVersion() // 最新版本加上限制 20190710
					&& StringUtils.isBlank(customerRequest.getCustomerCode())) {
				res.setReturnCode(PasCode.PARENT_CUSTOMER_CODE_NOT_ALLOW_NULL.code);
				res.setReturnMsg(PasCode.PARENT_CUSTOMER_CODE_NOT_ALLOW_NULL.message);
				return res;
			}
			if (StringUtils.isNotBlank(customerRequest.getCustomerCode()) && !StringUtils.equalsIgnoreCase(customerCodeFromHeader, customerRequest.getCustomerCode())) {
				res.setReturnCode(PasCode.CUSTOMER_CODE_DIFF_CERT.code);
				res.setReturnMsg(PasCode.CUSTOMER_CODE_DIFF_CERT.message);
				return res;
			}
			if(StringUtils.isBlank(customerRequest.getSignModel())) {
				customerRequest.setSignModel(Constants.QuickpaySignModel.CUSTOMER_SIGN.code);
			}
			if(!Constants.QuickpaySignModel.contains(customerRequest.getSignModel())) {
				throw new AppException(PasCode.SIGN_MODEL_ERROR.code);
			}
			CommonOuterResponse<Boolean> checkResp = custService.isCustomerCreateFromCust(customerCodeFromHeader);
			if(checkResp != null && checkResp.getData()) { // 服务商从cust进件，走cust会员进件接口
				res = customerService.saveSplitMerchantToCust(customerRequest, customerCodeFromHeader);
			}else {
				res = customerService.saveSplitMerchant(customerRequest, customerCodeFromHeader);
			}

		} catch (Exception ex) {
			logService.printLog(ex);
			returnCodeUtil.buildResponse(res, ex, PasCode.SYSTEM_EXCEPTION.code);
		}
		return res;
	}

    @PostMapping("CustomerInfo/ModifyMerchant")
    @Logable(businessTag = "updateSplitMerchant")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "修改子商户接口", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "customerInfoId", value = "客户信息ID", required = false, dataType = "Long", paramType = "query", digit = true),

    })
    public CustomerResponse updateSplitMerchant(
            @RequestParam(required = false) Long customerInfoId,
            @RequestBody CustomerRequest customerRequest,
            @RequestHeader(value = "x-customer-code", required = true) String customerCodeFromHeader) {

    	CustomerResponse res = new CustomerResponse();
		if( customerRequest.isLatestVersion() ){// 2.0新版本加上限制 20190710
			if(StringUtils.isBlank(customerRequest.getCustomerCode())) {
				res.setReturnCode(PasCode.PARENT_CUSTOMER_CODE_NOT_ALLOW_NULL.code);
				res.setReturnMsg(PasCode.PARENT_CUSTOMER_CODE_NOT_ALLOW_NULL.message);
				return res;
			}
			if(StringUtils.isBlank(customerRequest.getMemberId())) {
				res.setReturnCode(PasCode.SUB_CUSTOMER_CODE_NOT_ALLOW_NULL.code);
				res.setReturnMsg(PasCode.SUB_CUSTOMER_CODE_NOT_ALLOW_NULL.message);
				return res;
			}
			if (!customerRequest.getCustomerCode().equals(customerCodeFromHeader )) {
				res.setReturnCode(PasCode.CUSTOMER_CODE_DIFF_CERT.code);
				res.setReturnMsg(PasCode.CUSTOMER_CODE_DIFF_CERT.message);
				return res;
			}
		}else{ //  非新版的customerRequest.getCustomerCode代表子商户商户编号
			
		}
		
		try {
			CommonOuterResponse<Boolean> checkResp = custService.isCustomerCreateFromCust(customerCodeFromHeader);
			if(checkResp != null && checkResp.getData()) { // 服务商从cust进件，走cust会员进件接口
				return customerService.updateSplitMerchantInCust(customerRequest, customerCodeFromHeader);
			}else {
		    	long infoId = customerService.updateSplitMerchant(customerRequest , customerCodeFromHeader);
		    	res.setCustomerInfoId(infoId + "");
			}
	    	res.setReturnCode("0000");
	    	res.setNonceStr(UUIDUtils.uuid());
		}catch(Exception ex) {
			logService.printLog(ex);
			returnCodeUtil.buildResponse(res, ex, PasCode.SYSTEM_EXCEPTION.code);
		}
    	return res;

    }



    @RequestMapping(value ="CustomerInfo/MerchantAuditQuery", method = RequestMethod.POST)
    @Logable(businessTag = "auditStatusQuery")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "子商户审核结果查询", notes = "子商户审核结果查询", httpMethod = "POST")
    public SplitAuditStatusResponse AuditStatusQuery(
    		@RequestBody SplitAuditStatusRequest splitAuditStatusRequest,
    		@RequestHeader(value = "x-customer-code", required = true) String customerCodeFromHeader) {
    	
        return customerAuditInfoService.auditStatusQuery(
        		splitAuditStatusRequest.getCustomerInfoId(), customerCodeFromHeader) ;
    }


}