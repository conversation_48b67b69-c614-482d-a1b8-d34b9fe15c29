package com.epaylinks.efps.pas.mch.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.log.Logable.Level;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.common.PasConstants;
import com.epaylinks.efps.pas.mch.dao.PasSettCycleRuleMapper;
import com.epaylinks.efps.pas.mch.domain.PasSettCycleRule;
import com.epaylinks.efps.pas.mch.service.PasSettCycleRuleService;
@Service
public class PasSettCycleRuleServiceImpl implements PasSettCycleRuleService{
	@Autowired
	private PasSettCycleRuleMapper pasSettCycleRuleMapper;
	
	@Autowired
	private KafkaTemplate<String, String> kafkaTemplate;
	
	public static final String PAS_TOPIC = "PAS_BasicData";
	
	public static final String ALL_SETTLE_CYCLE_RULES_KEY = "AllSettCycleRules";
	/**
	 * 查询所有有效的结算周期规则
	 * @return
	 */
	@Logable(businessTag = "selectAllValid" , level = Level.DEBUG)
	@Override
	public List<PasSettCycleRule> selectAllValid() {
		// TODO Auto-generated method stub
		return pasSettCycleRuleMapper.selectRuleByStatus(PasConstants.pasSettCycleRuleStatus.VALID.code);
	}

	/**
	 * 同步有效的结算周期规则(往kafka发送数据)
	 */
	@Logable(businessTag = "syncPasSettCycleRule")
	@Override
	public void syncPasSettCycleRule() {
		// TODO Auto-generated method stub
		List<PasSettCycleRule> pasSettCycleRules = pasSettCycleRuleMapper
				.selectRuleByStatus(PasConstants.pasSettCycleRuleStatus.VALID.code);
		String data = JSON.toJSONString(pasSettCycleRules);
		try {
			kafkaTemplate.send(PAS_TOPIC, ALL_SETTLE_CYCLE_RULES_KEY, data);
		} catch (Exception e) {
			// TODO: handle exception
			throw new AppException(PasCode.SEND_KAFKA_EXCEPTION.code, e);
		}
	}

}
