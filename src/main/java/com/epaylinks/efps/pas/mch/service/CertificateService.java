package com.epaylinks.efps.pas.mch.service;

import java.io.IOException;

import org.springframework.web.multipart.MultipartFile;

import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.pas.mch.client.model.Certificate;

public interface CertificateService {

	PageResult<Certificate> pageQueryCertificates(Integer pageNum, Integer pageSize, String customerCode,
			String customerName);

	Certificate viewCertificate(String signSN);

	void deleteCertificate(String signSN);

	Certificate importCertificate(MultipartFile uploadFile);

	void addCertificate(Certificate certificate);
}