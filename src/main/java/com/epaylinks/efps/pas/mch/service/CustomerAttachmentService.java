package com.epaylinks.efps.pas.mch.service;

import java.util.List;

import com.epaylinks.efps.pas.mch.domain.CustomerAttachmentInfo;

/**
 * 业务逻辑层：客户附件信息
 * <AUTHOR>
 */
public interface CustomerAttachmentService {

	Long uploadAttachment(Long attachmentId, Long customerInfoId, String attachmentCode, String attachmentName, String attachmentUrl, Long attachmentSize);

	List<CustomerAttachmentInfo> getAttachmentByCustomerInfoId(Long customerInfoId);

	void deleteAttachmentById(Long attachmentId);
	
	String uploadToken(String businessType);
}