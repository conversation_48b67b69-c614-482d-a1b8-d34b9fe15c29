package com.epaylinks.efps.pas.mch.dao;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.epaylinks.efps.pas.mch.domain.Customer;
import com.epaylinks.efps.pas.mch.model.CustomerResult;

@Mapper
public interface CustomerMapper {
	
    int deleteByPrimaryKey(Long customerId);

    int insert(Customer record);

    Customer selectByPrimaryKey(Long customerId);

    int updateByPrimaryKey(Customer record);

	Long findInputingCustomerInfoId(@Param("curUserId") Long curUserId);

	void updateAfterAddCustomerInfo(@Param("customerId")Long customerId, @Param("newestCustomerInfoId")Long newestCustomerInfoId);

	Customer getByCustomerInfoId(@Param("customerInfoId") Long customerInfoId);

	int countCustomerInfos(@Param("beginCreateTime")Date beginCreateTime, @Param("endCreateTime")Date endCreateTime, @Param("customerCode")String customerCode, @Param("customerName")String customerName,
			@Param("customerState")Long customerState, @Param("businessType")String businessType, @Param("settleState")Long settleState, @Param("areaCodeQueryStr")String areaCodeQueryStr, @Param("sourceChannel")String sourceChannel);
	List<CustomerResult> pageQueryCustomerInfos(@Param("beginRowNo")int beginRowNo, @Param("endRowNo")int endRowNo, @Param("beginCreateTime")Date beginCreateTime,
			@Param("endCreateTime")Date endCreateTime, @Param("customerCode")String customerCode, @Param("customerName")String customerName, @Param("customerState")Long customerState, @Param("businessType")String businessType,
			@Param("settleState")Long settleState, @Param("areaCodeQueryStr")String areaCodeQueryStr, @Param("sourceChannel")String sourceChannel);

	String selectCustomerCodeByCustomerInfoId(@Param("customerInfoId") Long customerInfoId);
	
	boolean checkExistOfMchNameInAuditeds(@Param("customerId") Long customerId, @Param("mchName") String mchName);

	boolean checkExistOfLicenseNoInAuditeds(@Param("customerId") Long customerId, @Param("businessLicenseNo") String businessLicenseNo);
	
	boolean checkExistOfLeaPersonIDNoInAuditeds(@Param("customerId") Long customerId, @Param("leaPersoniDentificationNo") String leaPersoniDentificationNo, @Param("parentCustomerCode") String parentCustomerCode);

	List<Customer> selectAll();
	
	Customer selectByCustomerCode(String customerCode);
}