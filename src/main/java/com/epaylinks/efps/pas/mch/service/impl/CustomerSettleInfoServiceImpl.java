package com.epaylinks.efps.pas.mch.service.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.pas.mch.dao.CustomerSettleInfoMapper;
import com.epaylinks.efps.pas.mch.domain.CustomerSettleInfo;
import com.epaylinks.efps.pas.mch.service.CustomerSettleInfoService;

/**
 * 业务逻辑层：客户结算信息
 * <AUTHOR>
 */
@Service
public class CustomerSettleInfoServiceImpl implements CustomerSettleInfoService {

	@Autowired
	CustomerSettleInfoMapper customerSettleInfoMapper;
	
	/**
	 * 查看客户结算信息
	 */
	@Override
	@Logable(businessTag = "getCustomerSettleInfoByCustomerInfoId")
	public CustomerSettleInfo getCustomerSettleInfoByCustomerInfoId(Long customerInfoId) {
		return customerSettleInfoMapper.getByCustomerInfoId(customerInfoId);
	}
	
	/**
	 * 修改客户结算信息
	 * @return customerInfoId
	 */
	@Override
	@Logable(businessTag = "modifyCustomerSettleInfo")
	public void modifyCustomerSettleInfo(CustomerSettleInfo settleInfo) {
		String bankAccountNo = settleInfo.getBankAccountNo();
		if (StringUtils.isNotBlank(bankAccountNo)) {
			bankAccountNo = bankAccountNo.replaceAll(" ", "");
		}
		settleInfo.setBankAccountNo(bankAccountNo);
		/**
		 * 临时处理，先设置为D模式
		 */
		settleInfo.setSettleMode("D");
		customerSettleInfoMapper.updateByPrimaryKey(settleInfo);
	}
}
