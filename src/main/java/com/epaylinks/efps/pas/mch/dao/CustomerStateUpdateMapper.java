package com.epaylinks.efps.pas.mch.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.epaylinks.efps.pas.mch.domain.CustomerStateUpdate;

@Mapper
public interface CustomerStateUpdateMapper {
	
    int deleteByPrimaryKey(Long updateId);

    int insert(CustomerStateUpdate record);

    int insertSelective(CustomerStateUpdate record);

    CustomerStateUpdate selectByPrimaryKey(Long updateId);

    int updateByPrimaryKeySelective(CustomerStateUpdate record);

    int updateByPrimaryKey(CustomerStateUpdate record);

	List<CustomerStateUpdate> selectStateCommentsByIds(@Param("ids") List<Long> ids);
}