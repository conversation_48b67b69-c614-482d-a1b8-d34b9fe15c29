package com.epaylinks.efps.pas.mch.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.epaylinks.efps.pas.mch.domain.CustomerSyncInfo;

@Mapper
public interface CustomerSyncInfoMapper {
	
    int deleteByPrimaryKey(Long syncId);

    int insert(CustomerSyncInfo record);

    int insertSelective(CustomerSyncInfo record);

    CustomerSyncInfo selectByPrimaryKey(Long syncId);

    int updateByPrimaryKeySelective(CustomerSyncInfo record);

    int updateByPrimaryKey(CustomerSyncInfo record);

	List<CustomerSyncInfo> queryAllSyncInfo();

	int countSyncInfoByCustomerInfoId(@Param("customerInfoId") Long customerInfoId);
}