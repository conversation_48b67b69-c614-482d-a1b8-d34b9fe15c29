package com.epaylinks.efps.pas.mch.service.impl;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.pas.common.InputStreamUtil;
import com.epaylinks.efps.pas.mch.client.UaaClient;
import com.epaylinks.efps.pas.mch.client.model.Certificate;
import com.epaylinks.efps.pas.mch.common.MchConstants;
import com.epaylinks.efps.pas.mch.common.MchError;
import com.epaylinks.efps.pas.mch.service.CertificateService;

@Service
public class CertificateServiceImpl implements CertificateService {
	
	private static final String CERT_TYPE = "X.509";
	
	@Autowired
	private UaaClient uaaClient;
	
	/**
	 * 查询证书
	 */
	@Override
	@Logable(businessTag = "pageQueryCertificates")
	public PageResult<Certificate> pageQueryCertificates(Integer pageNum, Integer pageSize,
			String customerCode, String customerName) {
		
		// TODO 通过customerName到CUM模糊查询符合条件的customerCodeList
		
		return uaaClient.pageQueryCertificates(pageNum, pageSize, customerCode, customerName);
	}

	/**
	 * 查看证书
	 */
	@Override
	@Logable(businessTag = "viewCertificate")
	public Certificate viewCertificate(String signSN) {
		return uaaClient.viewCertificate(signSN);
	}

	/**
	 * 删除证书
	 */
	@Override
	@Logable(businessTag = "deleteCertificate")
	public void deleteCertificate(String signSN) {
		uaaClient.deleteCertificate(signSN);
	}

	/**
	 * 导入证书
	 */
	@Override
	@Logable(businessTag = "importCertificate")
	public Certificate importCertificate(MultipartFile uploadFile) {
		try {
			InputStream inputStream = uploadFile.getInputStream();
			X509Certificate x509Cert = this.getCertificate(inputStream);
	
			Certificate cert = new Certificate();
			cert.setSignSN(x509Cert.getSerialNumber().toString());
			cert.setType(0);//公钥
			cert.setNotBefore(x509Cert.getNotBefore());
			cert.setNotAfter(x509Cert.getNotAfter());
			cert.setIssuerDn(x509Cert.getIssuerDN().toString());
			cert.setSubjectDn(x509Cert.getSubjectDN().toString());
			InputStream inputStream1 = uploadFile.getInputStream();
			cert.setBase64X509(InputStreamUtil.getStrFromInputSteam(inputStream1));
//			cert.setStatus();//根据过期时间判断
			return cert;
        } catch (Exception e) {
            throw new AppException(MchError.CERTIFICATE_IMPORT_FAIL.code);
        }
	}

	/**
	 * 通过证书路径certificatePath获得Certificate
	 */
	private static X509Certificate getCertificate(InputStream inputStream) {
		CertificateFactory certificateFactory;
		java.security.cert.Certificate certificate;
		try {
			certificateFactory = CertificateFactory.getInstance(CERT_TYPE);
			certificate = certificateFactory.generateCertificate(inputStream);
			return (X509Certificate)certificate;
		} catch (CertificateException e) {
			throw new AppException(MchError.CERTIFICATE_IMPORT_FAIL.code);
		} finally {
			try {
				inputStream.close();
			} catch (IOException e) {
				
			}
		}
	}

	/**
	 * 新增证书
	 */
	@Override
	@Logable(businessTag = "addCertificate")
	public void addCertificate(Certificate certificate) {
		uaaClient.addCertificate(certificate);
	}
	
	public static void main(String[] args) {
		InputStream inputStream = null;
		try {
			inputStream = new FileInputStream(new File("C:\\Users\\<USER>\\Desktop\\epaylinks_pfx.cer"));
		} catch (FileNotFoundException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		X509Certificate x509Cert = getCertificate(inputStream);
		
		Certificate cert = new Certificate();
		cert.setSignSN(x509Cert.getSerialNumber().toString());
		cert.setType(0);//公钥
		cert.setNotBefore(x509Cert.getNotBefore());
		cert.setNotAfter(x509Cert.getNotAfter());
		cert.setIssuerDn(x509Cert.getIssuerDN().toString());
		cert.setSubjectDn(x509Cert.getSubjectDN().toString());
		try {
			inputStream = new FileInputStream(new File("C:\\Users\\<USER>\\Desktop\\epaylinks_pfx.cer"));
		} catch (FileNotFoundException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		cert.setBase64X509(InputStreamUtil.getStrFromInputSteam(inputStream));
//		cert.setStatus();//根据过期时间判断
		System.out.println(cert.getBase64X509());
	}
}
