package com.epaylinks.efps.pas.mch.client;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.config.DefaultFeignConfiguration;
import com.epaylinks.efps.pas.clr.domain.HuikuanRequest;
import com.epaylinks.efps.pas.clr.response.HuikuanResponse;
import com.epaylinks.efps.pas.mch.client.model.QuotaChangeResponse;
import com.epaylinks.efps.pas.mch.client.model.ResetWarnNoticeKey;
import com.epaylinks.efps.pas.pas.controller.response.QueryQuotaResponse;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.*;

@FeignClient(value = "ACS", configuration = DefaultFeignConfiguration.class)
public interface AcsClient {

    /**
     * 额度调整（仅适用于同时通过银联、网联开展资金结算业务的机构）
     * 描述：机构主动发起，调减/调增在银联资金结算的映射额度
     *
     * @param transactionNo 订单号
     * @param amount        调整额度
     * @param changeType    调整类型，REDUCE:调减, ADD:调增
     */
    @RequestMapping(value = "/union/fundSett/changeQuota", method = RequestMethod.POST)
    QuotaChangeResponse changeQuota(
            @RequestParam(value = "transactionNo", required = true) String transactionNo,
            @RequestParam(value = "amount", required = true) Long amount,
            @RequestParam(value = "changeType", required = true) String changeType);

    /**
     * 备付金额度查询（仅适用于同时通过银联、网联开展资金结算业务的机构）
     */
    @RequestMapping(value = "/union/fundSett/queryQuota", method = RequestMethod.GET)
    QueryQuotaResponse queryQuota();

    @PostMapping("/union/fundSett/huikuan")
    HuikuanResponse huikuan(@RequestBody HuikuanRequest request);

    @PostMapping("/customerAccount/addUnionQuota")
    CommonOuterResponse addUnionQuota(@RequestParam("amount") Long amount,
                                      @RequestParam("remark") String remark,
                                      @RequestParam("userId") Long userId);

    @PostMapping("/notice/resetWarnNotice")
    void resetWarnNotice(@RequestParam("key")ResetWarnNoticeKey key);
}