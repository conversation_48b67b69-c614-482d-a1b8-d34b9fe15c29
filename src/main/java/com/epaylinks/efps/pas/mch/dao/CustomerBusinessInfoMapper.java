package com.epaylinks.efps.pas.mch.dao;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.epaylinks.efps.pas.mch.domain.CustomerBusinessInfoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.epaylinks.efps.pas.mch.domain.CustomerBusinessInfo;
import com.epaylinks.efps.pas.pas.domain.Business;

@Mapper
public interface CustomerBusinessInfoMapper {

	int deleteByPrimaryKey(Long businessId);

	int insert(CustomerBusinessInfo record);

	int insertSelective(CustomerBusinessInfo record);

	CustomerBusinessInfo selectByPrimaryKey(Long businessId);

	List<CustomerBusinessInfo> selectByBusinessCode(String businessCode);

	int updateByPrimaryKeySelective(CustomerBusinessInfo record);

	int updateByPrimaryKey(CustomerBusinessInfo record);

	List<CustomerBusinessInfo> getByCustomerInfoId(Long customerInfoId);

	int countCustomerBusinessInfos(Long customerInfoId);

	List<CustomerBusinessInfo> pageQueryCustomerBusinessInfos(@Param("beginRowNo") int beginRowNo,
			@Param("endRowNo") int endRowNo, @Param("customerInfoId") Long customerInfoId);

	List<CustomerBusinessInfo> queryListByCustomerInfoId(Long customerInfoId);

	int countCustomerBusinessInfo(Long customerInfoId);

	boolean isBusinessInfoExist(@Param("customerInfoId") Long customerInfoId,
			@Param("businessCode") String businessCode);

	List<CustomerBusinessInfo> selectAll();

	List<CustomerBusinessInfo> selectByCustomerCode(String customerCode);

	List<CustomerBusinessInfo> selectByInfoId(Long customerInfoId);

	List<CustomerBusinessInfo> selectByBusinessCodeAndInfoId(@Param("businessCode") String businessCode,
			@Param("customerInfoId") Long customerInfoId);

	/**
	 * 根据infoId以及业务编码以及日期来查询业务实例
	 * 
	 * @param newestCustomerInfoId
	 * @param code
	 * @param date
	 * @return
	 */
	CustomerBusinessInfo selectByInfoIdAndBusinessCodeAndDate(@Param("infoId") Long infoId,
			@Param("businessCode") String businessCode, @Param("date") Date date);

	int countCustomerBusinessInfosById(Map map);


	List<CustomerBusinessInfo> pageQueryCustomerBusinessInfosById(@Param("beginRowNo") int beginRowNo,
			@Param("endRowNo") int endRowNo, @Param("customerInfoId") Long customerInfoId,@Param("businessId") Long businessId);

	//临时修改数据的
	List<CustomerBusinessInfoVO> selectAllEmptyCategory();
	  //临时修改数据的
	int updateByPkBusiness(CustomerBusinessInfo record);;
}