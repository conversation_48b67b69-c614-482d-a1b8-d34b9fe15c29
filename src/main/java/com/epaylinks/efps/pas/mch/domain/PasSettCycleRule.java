package com.epaylinks.efps.pas.mch.domain;

import java.util.Date;
/**
 * 结算周期规则
 * <AUTHOR>
 *
 */
public class PasSettCycleRule {
	/**
	 * 数据库主键
	 */
    private Long id;
    /**
     * 周期规则的编码，业务主键，唯一索引
     */
    private String code;
    /**
     * 周期规则的名称
     */
    private String name;
    /**
     * 支持以下取值（不要用数字）：D0 D T0 T RealTime
     */
    private String type;
    /**
     * 除RealTime类型外，其他的都必须有参数对于D/T类型：配置一个正整数n，表示T+n；
     * 对于D0/T0类型：配置一串时间点（24小时制），指定结算周期的分隔时间点，例如3:00,9:00,15:00,21:00，必须升序
     */
    private String params;
    /**
     * 状态： 有效：Valid 无效：Invalid
     */
    private String status;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 创建者userId
     */
    private Long creator;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 修改者userId
     */
    private Long updator;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getParams() {
        return params;
    }

    public void setParams(String params) {
        this.params = params;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getCreator() {
        return creator;
    }

    public void setCreator(Long creator) {
        this.creator = creator;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getUpdator() {
        return updator;
    }

    public void setUpdator(Long updator) {
        this.updator = updator;
    }
}