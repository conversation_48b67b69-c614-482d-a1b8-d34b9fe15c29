package com.epaylinks.efps.pas.mch.service.impl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import com.epaylinks.efps.pas.pas.service.BusinessService;
import org.apache.commons.lang3.StringUtils;
import org.codehaus.groovy.util.StringUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.epaylinks.efps.common.business.domain.SettmentCycle;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.log.Logable.Level;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.common.util.Constants.BusinessParamCode;
import com.epaylinks.efps.common.util.TimeInterval;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.common.PasConstants;
import com.epaylinks.efps.pas.common.PasConstants.pasSettCycleRuleType;
import com.epaylinks.efps.pas.mch.client.TxsClient;
import com.epaylinks.efps.pas.mch.common.MchConstants;
import com.epaylinks.efps.pas.mch.common.MchError;
import com.epaylinks.efps.pas.mch.controller.dto.SettCycleRuleDTO;
import com.epaylinks.efps.pas.mch.dao.CustomerBusinessInfoMapper;
import com.epaylinks.efps.pas.mch.dao.CustomerInfoMapper;
import com.epaylinks.efps.pas.mch.dao.CustomerMapper;
import com.epaylinks.efps.pas.mch.dao.CustomerSettleInfoMapper;
import com.epaylinks.efps.pas.mch.dao.PasHolidayMapper;
import com.epaylinks.efps.pas.mch.dao.PasSettCycleRuleInstMapper;
import com.epaylinks.efps.pas.mch.dao.PasSettCycleRuleMapper;
import com.epaylinks.efps.pas.mch.domain.Customer;
import com.epaylinks.efps.pas.mch.domain.CustomerBusinessInfo;
import com.epaylinks.efps.pas.mch.domain.CustomerInfo;
import com.epaylinks.efps.pas.mch.domain.CustomerSettleInfo;
import com.epaylinks.efps.pas.mch.domain.PasSettCycleRule;
import com.epaylinks.efps.pas.mch.domain.PasSettCycleRuleInst;
import com.epaylinks.efps.pas.mch.service.CustomerBusinessInfoService;
import com.epaylinks.efps.pas.mch.vo.BusinessParamInstVo;
import com.epaylinks.efps.pas.mch.vo.CustomerBusinessInfoVo;
import com.epaylinks.efps.pas.mch.vo.MustOpenBusiness;
import com.epaylinks.efps.pas.mch.vo.OpenBusiness;
import com.epaylinks.efps.pas.mch.vo.SettCycleRuleInstVo;
import com.epaylinks.efps.pas.pas.dao.BizPayMethodMapper;
import com.epaylinks.efps.pas.pas.dao.BusinessMapper;
import com.epaylinks.efps.pas.pas.dao.BusinessParamInstMapper;
import com.epaylinks.efps.pas.pas.dao.BusinessSettCycleRuleMapper;
import com.epaylinks.efps.pas.pas.dao.PayMethodMapper;
import com.epaylinks.efps.pas.pas.domain.Business;
import com.epaylinks.efps.pas.pas.domain.BusinessParamInst;
import com.epaylinks.efps.pas.pas.domain.BusinessSettCycleRule;

/**
 * 业务逻辑层：客户业务信息
 */
@Service
public class CustomerBusinessInfoServiceImpl implements CustomerBusinessInfoService {

	private static List<OpenBusiness> openBusinesses = new ArrayList<>();
	@Autowired
	private TxsClient txsClient;
	@Autowired
	private CustomerInfoMapper customerInfoMapper;

	private static final String MEMBER_EXCHANGE_IN = "MemberExchangeIn";

	private static final String MEMBER_EXCHANGE_OUT = "MemberExchangeOut";

	private static final String MEMBER_OPPOSITE_EXCHANGE_IN = "MemberOppositeExchangeIn";

	private static final String MEMBER_OPPOSITE_EXCHANGE_OUT = "MemberOppositeExchangeOut";

	@Autowired
    private BusinessService businessService;

	static {
		//开通分账业务，必须开通分账在途金额退款以及可用金额退款
		OpenBusiness fzOpenBusiness = new OpenBusiness();
		fzOpenBusiness.setBusinessCode(Constants.EfpsFzService.FZ.code);
		List<MustOpenBusiness> fzMustOpenBusinesses = new ArrayList<>();
		MustOpenBusiness fzRefundFloatMustOpenBusiness = new MustOpenBusiness();
		fzRefundFloatMustOpenBusiness.setBusinessCode(Constants.EfpsFzService.FZ_REFUND_USING_FLOAT.code);
		//结算编码设置为null,那么和分账业务的结算编码一致
		MustOpenBusiness fzRefundAvailMustOpenBusiness = new MustOpenBusiness();
		fzRefundAvailMustOpenBusiness.setBusinessCode(Constants.EfpsAccountService.FZ_REFUND_USING_AVAILABLE.code);
		fzRefundAvailMustOpenBusiness.setSettCode("RealTime");
		fzMustOpenBusinesses.add(fzRefundFloatMustOpenBusiness);
		fzMustOpenBusinesses.add(fzRefundAvailMustOpenBusiness);
		fzOpenBusiness.setMustOpenBusiness(fzMustOpenBusinesses);
		openBusinesses.add(fzOpenBusiness);

		//同理 开通被分账业务，必须开通被分账在途退款以及可用金额退款
		OpenBusiness bfzOpenBusiness = new OpenBusiness();
		bfzOpenBusiness.setBusinessCode(Constants.EfpsBfzService.BFZ.code);
		List<MustOpenBusiness> bfzMustOpenBusinesses = new ArrayList<>();
		MustOpenBusiness bfzRefundFloatMustOpenBusiness = new MustOpenBusiness();
		bfzRefundFloatMustOpenBusiness.setBusinessCode(Constants.EfpsBfzService.BFZ_REFUND_USING_FLOAT.code);

		MustOpenBusiness bfzRefundAvailMustOpenBusiness = new MustOpenBusiness();
		bfzRefundAvailMustOpenBusiness.setBusinessCode(Constants.EfpsAccountService.BFZ_REFUND_USING_AVAILABLE.code);
		bfzRefundAvailMustOpenBusiness.setSettCode("RealTime");
		bfzMustOpenBusinesses.add(bfzRefundFloatMustOpenBusiness);
		bfzMustOpenBusinesses.add(bfzRefundAvailMustOpenBusiness);
		bfzOpenBusiness.setMustOpenBusiness(bfzMustOpenBusinesses);
		openBusinesses.add(bfzOpenBusiness);


		/*//开通分账-支付类业务，必须开通，必须开通分账支付-可用金额退款
		OpenBusiness FZAliJSAPIOpenBusiness = new OpenBusiness();
		FZAliJSAPIOpenBusiness.setBusinessCode(Constants.EfpsFzPayService.FZ_ALIJSAPI.code);
		List<MustOpenBusiness> FZAliJSAPIMustOpenBusinesses = new ArrayList<>();
		MustOpenBusiness FZAliJSAPIMustOpenBusiness = new MustOpenBusiness();
		FZAliJSAPIMustOpenBusiness.setBusinessCode(Constants.EfpsAccountService.FZ_PAY_REFUND_USING_AVAILABLE.code);
		FZAliJSAPIMustOpenBusiness.setSettCode("RealTime");
		FZAliJSAPIMustOpenBusinesses.add(FZAliJSAPIMustOpenBusiness);
		FZAliJSAPIOpenBusiness.setMustOpenBusiness(FZAliJSAPIMustOpenBusinesses);
		openBusinesses.add(FZAliJSAPIOpenBusiness);

		// 开通分账-支付类业务，必须开通，必须开通分账支付-可用金额退款
		OpenBusiness FZ_ALIMICROOpenBusiness = new OpenBusiness();
		FZAliJSAPIOpenBusiness.setBusinessCode(Constants.EfpsFzPayService.FZ_ALIMICRO.code);
		List<MustOpenBusiness> FZ_ALIMICROMustOpenBusinesses = new ArrayList<>();
		MustOpenBusiness FZ_ALIMICROMustOpenBusiness = new MustOpenBusiness();
		FZ_ALIMICROMustOpenBusiness.setBusinessCode(Constants.EfpsAccountService.FZ_PAY_REFUND_USING_AVAILABLE.code);
		FZ_ALIMICROMustOpenBusiness.setSettCode("RealTime");
		FZ_ALIMICROMustOpenBusinesses.add(FZ_ALIMICROMustOpenBusiness);
		FZAliJSAPIOpenBusiness.setMustOpenBusiness(FZ_ALIMICROMustOpenBusinesses);
		openBusinesses.add(FZAliJSAPIOpenBusiness);

		// 开通分账-支付类业务，必须开通，必须开通分账支付-可用金额退款
		OpenBusiness FZ_WXMICROOpenBusiness = new OpenBusiness();
		FZ_WXMICROOpenBusiness.setBusinessCode(Constants.EfpsFzPayService.FZ_WXMICRO.code);
		List<MustOpenBusiness> FZ_WXMICROMustOpenBusinesses = new ArrayList<>();
		MustOpenBusiness FZ_WXMICROMustOpenBusiness = new MustOpenBusiness();
		FZ_WXMICROMustOpenBusiness.setBusinessCode(Constants.EfpsAccountService.FZ_PAY_REFUND_USING_AVAILABLE.code);
		FZ_WXMICROMustOpenBusiness.setSettCode("RealTime");
		FZ_WXMICROMustOpenBusinesses.add(FZ_WXMICROMustOpenBusiness);
		FZ_WXMICROOpenBusiness.setMustOpenBusiness(FZ_WXMICROMustOpenBusinesses);
		openBusinesses.add(FZ_WXMICROOpenBusiness);

		// 开通分账-支付类业务，必须开通，必须开通分账支付-可用金额退款
		OpenBusiness FZ_ALINATIVEOpenBusiness = new OpenBusiness();
		FZ_ALINATIVEOpenBusiness.setBusinessCode(Constants.EfpsFzPayService.FZ_ALINATIVE.code);
		List<MustOpenBusiness> FZ_ALINATIVEMustOpenBusinesses = new ArrayList<>();
		MustOpenBusiness FZ_ALINATIVEMustOpenBusiness = new MustOpenBusiness();
		FZ_ALINATIVEMustOpenBusiness.setBusinessCode(Constants.EfpsAccountService.FZ_PAY_REFUND_USING_AVAILABLE.code);
		FZ_ALINATIVEMustOpenBusiness.setSettCode("RealTime");
		FZ_ALINATIVEMustOpenBusinesses.add(FZ_ALINATIVEMustOpenBusiness);
		FZ_ALINATIVEOpenBusiness.setMustOpenBusiness(FZ_ALINATIVEMustOpenBusinesses);
		openBusinesses.add(FZ_ALINATIVEOpenBusiness);

		// 开通分账-支付类业务，必须开通，必须开通分账支付-可用金额退款
		OpenBusiness FZ_QUICKPAYOpenBusiness = new OpenBusiness();
		FZ_QUICKPAYOpenBusiness.setBusinessCode(Constants.EfpsFzPayService.FZ_QUICKPAY.code);
		List<MustOpenBusiness> FZ_QUICKPAYMustOpenBusinesses = new ArrayList<>();
		MustOpenBusiness FZ_QUICKPAYMustOpenBusiness = new MustOpenBusiness();
		FZ_QUICKPAYMustOpenBusiness.setBusinessCode(Constants.EfpsAccountService.FZ_PAY_REFUND_USING_AVAILABLE.code);
		FZ_QUICKPAYMustOpenBusiness.setSettCode("RealTime");
		FZ_QUICKPAYMustOpenBusinesses.add(FZ_QUICKPAYMustOpenBusiness);
		FZ_QUICKPAYOpenBusiness.setMustOpenBusiness(FZ_QUICKPAYMustOpenBusinesses);
		openBusinesses.add(FZ_QUICKPAYOpenBusiness);

		// 开通分账-支付类业务，必须开通，必须开通分账支付-可用金额退款
		OpenBusiness FZ_WXMWEBOpenBusiness = new OpenBusiness();
		FZ_WXMWEBOpenBusiness.setBusinessCode(Constants.EfpsFzPayService.FZ_WXMWEB.code);
		List<MustOpenBusiness> FZ_WXMWEBMustOpenBusinesses = new ArrayList<>();
		MustOpenBusiness FZ_WXMWEBMustOpenBusiness = new MustOpenBusiness();
		FZ_WXMWEBMustOpenBusiness.setBusinessCode(Constants.EfpsAccountService.FZ_PAY_REFUND_USING_AVAILABLE.code);
		FZ_WXMWEBMustOpenBusiness.setSettCode("RealTime");
		FZ_WXMWEBMustOpenBusinesses.add(FZ_WXMWEBMustOpenBusiness);
		FZ_WXMWEBOpenBusiness.setMustOpenBusiness(FZ_WXMWEBMustOpenBusinesses);
		openBusinesses.add(FZ_WXMWEBOpenBusiness);

		// 开通分账-支付类业务，必须开通，必须开通分账支付-可用金额退款
		OpenBusiness FZ_WXJSAPIOpenBusiness = new OpenBusiness();
		FZ_WXJSAPIOpenBusiness.setBusinessCode(Constants.EfpsFzPayService.FZ_WXJSAPI.code);
		List<MustOpenBusiness> FZ_WXJSAPIMustOpenBusinesses = new ArrayList<>();
		MustOpenBusiness FZ_WXJSAPIMustOpenBusiness = new MustOpenBusiness();
		FZ_WXJSAPIMustOpenBusiness.setBusinessCode(Constants.EfpsAccountService.FZ_PAY_REFUND_USING_AVAILABLE.code);
		FZ_WXJSAPIMustOpenBusiness.setSettCode("RealTime");
		FZ_WXJSAPIMustOpenBusinesses.add(FZ_WXJSAPIMustOpenBusiness);
		FZ_WXJSAPIOpenBusiness.setMustOpenBusiness(FZ_WXJSAPIMustOpenBusinesses);
		openBusinesses.add(FZ_WXJSAPIOpenBusiness);

		// 开通分账-支付类业务，必须开通，必须开通分账支付-可用金额退款
		OpenBusiness FZ_WXNATVIEOpenBusiness = new OpenBusiness();
		FZ_WXNATVIEOpenBusiness.setBusinessCode(Constants.EfpsFzPayService.FZ_WXNATVIE.code);
		List<MustOpenBusiness> FZ_WXNATVIEMustOpenBusinesses = new ArrayList<>();
		MustOpenBusiness FZ_WXNATVIEMustOpenBusiness = new MustOpenBusiness();
		FZ_WXNATVIEMustOpenBusiness.setBusinessCode(Constants.EfpsAccountService.FZ_PAY_REFUND_USING_AVAILABLE.code);
		FZ_WXNATVIEMustOpenBusiness.setSettCode("RealTime");
		FZ_WXNATVIEMustOpenBusinesses.add(FZ_WXNATVIEMustOpenBusiness);
		FZ_WXNATVIEOpenBusiness.setMustOpenBusiness(FZ_WXNATVIEMustOpenBusinesses);
		openBusinesses.add(FZ_WXNATVIEOpenBusiness);*/

	}

	@Autowired
	private CustomerBusinessInfoMapper customerBusinessInfoMapper;
	@Autowired
	private BizPayMethodMapper bizPayMethodMapper;
	@Autowired
	private PayMethodMapper payMethodMapper;
	@Autowired
	private SequenceService sequenceService;
	@Autowired
	private CustomerSettleInfoMapper customerSettleInfoMapper;
	@Autowired
	private PasSettCycleRuleMapper pasSettCycleRuleMapper;
	@Autowired
	private BusinessSettCycleRuleMapper businessSettCycleRuleMapper;
	@Autowired
	private PasSettCycleRuleInstMapper pasSettCycleRuleInstMapper;
	@Autowired
	private CustomerMapper customerMapper;
	@Autowired
	private BusinessParamInstMapper businessParamInstMapper;
	@Autowired
	private BusinessMapper businessMapper;
	@Autowired
	private PasHolidayMapper holidayMapper;
	@Autowired
	public CustomerBusinessInfoServiceImpl self;

	private static final int DEFAULT_YEAR = 2099;

	private static final int DEFAULT_MONTH = 01;

	private static final int DEFAULT_DATE = 01;

	private static final int DEFAULT_HOUR = 00;

	private static final int DEFAULT_MINUTE = 00;

	private static final int DEFAULT_SECOND = 00;

	private static final String ADDSETTCYCLERULEINSTCATEGORY = "addSettCycleRuleInst";

	/**
	 * 新增客户业务实例以及对应的结算周期规则实例（保证同一事务中执行）
	 */
	@Override
	@Logable(businessTag = "createCustomerBusinessInfo")
	@Transactional
	public void createCustomerBusinessInfo(CustomerBusinessInfoVo customerBusinessInfoVo) {
		self.validBusinessInfo(customerBusinessInfoVo);
		Long businessInstId = self.addBusinessInfo(customerBusinessInfoVo);
		// 添加结算周期规则实例
		// 现在业务结算周期允许修改，所以这里不做限制
//		if (!self.checkExistSettCycle(customerBusinessInfoVo)) {
//			throw new AppException(MchError.CUM_BIZ_SETT_CYCLE_ERROR.code);
//		}
		List<SettCycleRuleInstVo> settCycleRuleInstList = customerBusinessInfoVo.getSettCycleRuleInstList();
		for (SettCycleRuleInstVo vo : settCycleRuleInstList) {
			PasSettCycleRule pasSettCycleRule = pasSettCycleRuleMapper.selectByCode(vo.getSettCycleRuleCode());
			self.addSettCycleRuleInst(customerBusinessInfoVo.getBusinessExamId(), pasSettCycleRule,
					vo.getValidStartTime(), vo.getValidEndTime(), businessInstId);
		}
		// 添加业务参数实例
		self.addBusinessParamInst(customerBusinessInfoVo, new Date(), businessInstId);
		// 添加配套开通的业务
//		self.createAllBusinessInfoRelated(customerBusinessInfoVo);
	}

	/**
	 * 修改客户业务实例以及对应的结算周期规则实例（保证同一事务中执行）
	 */
	@Override
	@Logable(businessTag = "modifyCustomerBusinessInfo")
	@Transactional
	public void modifyCustomerBusinessInfo(CustomerBusinessInfoVo customerBusinessInfoVo) {
	    customerBusinessInfoVo.setChangeType("MODIFY");
		self.validBusinessInfo(customerBusinessInfoVo);
		self.modifyBusinessInfo(customerBusinessInfoVo);
		Long businessInstId = customerBusinessInfoVo.getBusinessId();

		// 添加结算周期规则实例
		List<SettCycleRuleInstVo> settCycleRuleInstList = customerBusinessInfoVo.getSettCycleRuleInstList();
		for (SettCycleRuleInstVo vo : settCycleRuleInstList) {
			PasSettCycleRule pasSettCycleRule = pasSettCycleRuleMapper.selectByCode(vo.getSettCycleRuleCode());
			self.modifySettCycleRuleInst(customerBusinessInfoVo.getOldBusinessExamId(), customerBusinessInfoVo.getBusinessExamId(), pasSettCycleRule,
					vo.getValidStartTime(), vo.getValidEndTime(), businessInstId);
		}
		// 添加业务参数实例
		self.modifyBusinessParamInst(customerBusinessInfoVo, new Date(), businessInstId);
		// 添加配套开通的业务
//		self.createAllBusinessInfoRelated(customerBusinessInfoVo);
	}

	/**
	 * 添加主业务配套开通的所有业务
	 */
	@Logable(businessTag = "createAllBusinessInfoRelated")
	public void createAllBusinessInfoRelated(CustomerBusinessInfoVo customerBusinessInfoVo) {
		String businessCode = customerBusinessInfoVo.getBusinessCode();
		for (OpenBusiness openBusiness : openBusinesses) {
			if (openBusiness.getBusinessCode().equals(businessCode)) {
				List<MustOpenBusiness> mustOpenBusiness = openBusiness.getMustOpenBusiness();
				for (MustOpenBusiness xBusiness : mustOpenBusiness) {
					// 开通此配套的业务
					CustomerBusinessInfoVo openCustomerBusinessInfoVo = new CustomerBusinessInfoVo();
					BeanUtils.copyProperties(customerBusinessInfoVo, openCustomerBusinessInfoVo);
					openCustomerBusinessInfoVo.setBusinessCode(xBusiness.getBusinessCode());
					openCustomerBusinessInfoVo.setSettCycleRuleInstList(null);
					self.createBusinessInfoRelated(openCustomerBusinessInfoVo);
				}
				break;
			}
		}
	}

	/**
	 * 添加配套开通的业务
	 */
	@Logable(businessTag = "createBusinessInfoRelated")
	public void createBusinessInfoRelated(CustomerBusinessInfoVo customerBusinessInfoVo) {
		self.validBusinessInfo(customerBusinessInfoVo);
		Long businessInstId = self.addBusinessInfo(customerBusinessInfoVo);
		// 添加结算周期规则实例
		List<SettCycleRuleInstVo> settCycleRuleInstList = customerBusinessInfoVo.getSettCycleRuleInstList();
		for (SettCycleRuleInstVo vo : settCycleRuleInstList) {
			PasSettCycleRule pasSettCycleRule = pasSettCycleRuleMapper.selectByCode(vo.getSettCycleRuleCode());
			self.addSettCycleRuleInst(customerBusinessInfoVo.getBusinessExamId(), pasSettCycleRule,
					vo.getValidStartTime(), vo.getValidEndTime(), businessInstId);
		}
		// 添加业务参数实例
		self.addBusinessParamInst(customerBusinessInfoVo, new Date(), businessInstId);
	}

	/**
	 * 合法性校验
	 * 检查结算周期是否与该商户所有已开通的支付类业务的结算周期规则一致（仅校验编码即可，不需校验周期实例的有效期一致）
	 */
	@Logable(businessTag = "checkExistSettCycle")
	public boolean checkExistSettCycle(CustomerBusinessInfoVo customerBusinessInfoVo) {
		// 如果是支付类业务，则进行结算周期一致性检查
		List<SettCycleRuleInstVo> settCycleRuleInstList = customerBusinessInfoVo.getSettCycleRuleInstList();
		if (settCycleRuleInstList.isEmpty()) {
			return true;
		}
		String settCycleRuleCode = settCycleRuleInstList.get(0).getSettCycleRuleCode();
		// 查询所有已开通的支付类业务的结算周期规则(由于数据库里所有结算周期必定相同，这里仅查一条)
		Long mchInfoId = customerBusinessInfoVo.getCustomerInfoId();
		String businessCode = customerBusinessInfoVo.getBusinessCode();
		for (Constants.EfpsAccountService x : Constants.EfpsAccountService.values()) {
			if (x.code.equals(businessCode)) {
				return true;
			}
		}
		String existSettCycleCode = pasSettCycleRuleInstMapper.selectSettCycleCodeInSameBizCategory(mchInfoId, businessCode);
		if (existSettCycleCode != null && !settCycleRuleCode.equals(existSettCycleCode)) {
			return false;
		}
		return true;
	}

	@Logable(businessTag = "addBusinessParamInst")
	public void addBusinessParamInst(CustomerBusinessInfoVo customerBusinessInfoVo, Date nowDate,
			Long businessInstDBId) {
		List<BusinessParamInstVo> businessParamInstList = customerBusinessInfoVo.getBusinessParamInstList();
		if (businessParamInstList != null) {
			for (BusinessParamInstVo vo : businessParamInstList) {
				if (StringUtils.isNotBlank(vo.getValue())) {
					if (vo.getCode().equalsIgnoreCase(BusinessParamCode.AGENT_CUSTOMER_CODE.code)) {
						checkForAgentCustomerCode(customerBusinessInfoVo, vo.getValue());
					}
					if (vo.getCode().equalsIgnoreCase(BusinessParamCode.FZ_CUSTOMER_CODE.code)) {
						/**
						 * 如果业务参数实例中包含分账，校验该客户编码是否开通了分账业务
						 */
						Customer customer = customerMapper.selectByCustomerCode(vo.getValue());
						customerBusinessInfoMapper.isBusinessInfoExist(customer.getNewestCustomerInfoId(),
								customerBusinessInfoVo.getBusinessCode());
					}
					for (Constants.BusinessParamCode businessParamCode : Constants.BusinessParamCode.values()) {
						if (businessParamCode.code.equals(vo.getCode())) {
							BusinessParamInst businessParamInst = new BusinessParamInst(vo.getCode(),
									customerBusinessInfoVo.getBusinessExamId(), businessParamCode.comment, "String", // 这里先写死吧，不过要这个字段干啥呢?
									vo.getValue(), nowDate, null, nowDate, null, businessInstDBId);
							businessParamInstMapper.insert(businessParamInst);
						}
					}
				}
			}
		}
	}
	@Logable(businessTag = "modifyBusinessParamInst")
	@Transactional
	public void modifyBusinessParamInst(CustomerBusinessInfoVo customerBusinessInfoVo, Date nowDate, Long businessInstDBId) {
		// 删除旧业务参数实例
		String oldBusinessExamId = customerBusinessInfoVo.getOldBusinessExamId();
		businessParamInstMapper.deleteByBusinessExamId(oldBusinessExamId);
		// 新增业务参数实例
		List<BusinessParamInstVo> businessParamInstList = customerBusinessInfoVo.getBusinessParamInstList();
		if (businessParamInstList != null) {
			for (BusinessParamInstVo vo : businessParamInstList) {
				if (StringUtils.isNotBlank(vo.getValue())) {
					if (vo.getCode().equalsIgnoreCase(BusinessParamCode.AGENT_CUSTOMER_CODE.code)) {
						checkForAgentCustomerCode(customerBusinessInfoVo, vo.getValue());
					}
					if (vo.getCode().equalsIgnoreCase(BusinessParamCode.FZ_CUSTOMER_CODE.code)) {
						/**
						 * 如果业务参数实例中包含分账，校验该客户编码是否开通了分账业务
						 */
						Customer customer = customerMapper.selectByCustomerCode(vo.getValue());
						customerBusinessInfoMapper.isBusinessInfoExist(customer.getNewestCustomerInfoId(),
								customerBusinessInfoVo.getBusinessCode());
					}
					for (Constants.BusinessParamCode businessParamCode : Constants.BusinessParamCode.values()) {
						if (businessParamCode.code.equals(vo.getCode())) {
							BusinessParamInst businessParamInst = new BusinessParamInst(vo.getCode(),
									customerBusinessInfoVo.getBusinessExamId(), businessParamCode.comment, "String", // 这里先写死吧，不过要这个字段干啥呢?
									vo.getValue(), nowDate, null, nowDate, null, businessInstDBId);
							businessParamInstMapper.insert(businessParamInst);
						}
					}
				}
			}
		}
	}

	@Logable(businessTag = "checkForAgentCustomerCode")
	public void checkForAgentCustomerCode(CustomerBusinessInfoVo customerBusinessInfoVo, String agentCustomerCode) {

		Customer customer = customerMapper.selectByCustomerCode(agentCustomerCode);
		List<CustomerBusinessInfo> agentCustomerBusinessInfoList = customerBusinessInfoMapper
				.selectByInfoId(customer.getNewestCustomerInfoId());
		for (CustomerBusinessInfo agentCustomerBusinessInfo : agentCustomerBusinessInfoList) {
			if (agentCustomerBusinessInfo.getBusinessCode().equals(customerBusinessInfoVo.getBusinessCode())) {
				// 找到拓展商户与代理商相同的业务
				self.checkDlsRate(customerBusinessInfoVo, agentCustomerBusinessInfo);
				// 校验通过，退出循环
				break;
			}
		}
	}

	/**
	 * 校验拓展商户与代理商的费率（拓展商户的费率必须大于代理商的费率）
	 *
	 * @param customerBusinessInfoVo
	 *            拓展商户业务实例对象
	 * @param agentCustomerBusinessInfo
	 *            代理商业务实例对象
	 */
	@Logable(businessTag = "checkDlsRatecheckDlsRate")
	public void checkDlsRate(CustomerBusinessInfoVo customerBusinessInfoVo,
			CustomerBusinessInfo agentCustomerBusinessInfo) {
		if (!agentCustomerBusinessInfo.getRateMode().equals(customerBusinessInfoVo.getRateMode())) {
			throw new AppException(PasCode.DIFFERENT_RATE_MODE.code);
		}
		if (PasConstants.RateMode.FIX_RATE.code.equals(customerBusinessInfoVo.getRateMode())) {
			if(customerBusinessInfoVo.getRateParam() == null|| "".equals(customerBusinessInfoVo.getRateParam() )) {
				customerBusinessInfoVo.setRateParam(String.valueOf(customerBusinessInfoVo.getFeePer()));
			}
			if(agentCustomerBusinessInfo.getRateParam() == null||"".equals(agentCustomerBusinessInfo.getRateParam()))  {
				agentCustomerBusinessInfo.setRateParam(String.valueOf(agentCustomerBusinessInfo.getFeePer()));
			}
			// 如果都是固定费率
			if (Integer.valueOf(customerBusinessInfoVo.getRateParam()) < Integer
					.valueOf(agentCustomerBusinessInfo.getRateParam())) {
				// 如果拓展商户的费率小于等于代理商的费率
				throw new AppException(PasCode.DLS_RATE_PARAM_TOO_LARGE.code);
			}
			return;
		} else if (PasConstants.RateMode.PERCENT_RATE.code.equals(customerBusinessInfoVo.getRateMode())) {
			// 如果是按照百分比费率
			String[] dlsRateArray = agentCustomerBusinessInfo.getRateParam().split("_");
			String[] tzRateArray = customerBusinessInfoVo.getRateParam().split("_");
			if (dlsRateArray.length != tzRateArray.length) {
				throw new AppException(PasCode.DIFFERENT_RATE_MODE.code);
			} else if (dlsRateArray.length == 1) {
				// 都是百分比费率比较无封顶
				if (Integer.valueOf(tzRateArray[0]) < Integer.valueOf(dlsRateArray[0])) {
					throw new AppException(PasCode.DLS_RATE_PARAM_TOO_LARGE.code);
				}
				return;
			} else if (dlsRateArray.length == 2) {
				// 都是百分比费率且有封顶值
				if (Integer.valueOf(tzRateArray[0]) >= Integer.valueOf(dlsRateArray[0])
						&& Integer.valueOf(tzRateArray[1]) >= Integer.valueOf(dlsRateArray[1])) {
					// 拓展商户的费率以及封顶值都比代理商的大才合理
					return;
				}
				throw new AppException(PasCode.DLS_RATE_PARAM_TOO_LARGE.code);
			}
		}
	}

	/**
	 * 添加业务实例
	 *
	 * @param customerBusinessInfoVo
	 * @return 业务实例的主键
	 */
	@Logable(businessTag = "addBusinessInfo")
	public Long addBusinessInfo(CustomerBusinessInfoVo customerBusinessInfoVo) {
		CustomerBusinessInfo customerBusinessInfo = new CustomerBusinessInfo();
		Long businessInfoId = sequenceService.nextValue("addMchBusiness");
		customerBusinessInfo.setBusinessId(businessInfoId);
		customerBusinessInfo.setCustomerInfoId(customerBusinessInfoVo.getCustomerInfoId());
		customerBusinessInfo.setCustomerId(customerBusinessInfoVo.getCustomerId());
		customerBusinessInfo.setCustomerCode(customerBusinessInfoVo.getCustomerCode());
		customerBusinessInfo.setBusinessCode(customerBusinessInfoVo.getBusinessCode());
		Business business = businessMapper.selectByCode(customerBusinessInfoVo.getBusinessCode());
		customerBusinessInfo.setRateName(business.getName());
		customerBusinessInfo.setRateMode(customerBusinessInfoVo.getRateMode());
		customerBusinessInfo.setRateParam(customerBusinessInfoVo.getRateParam());
		customerBusinessInfo.setStatus(customerBusinessInfoVo.getStatus());
		customerBusinessInfo.setFollowWechatAccount(customerBusinessInfoVo.getFollowWechatAccount());
		customerBusinessInfo.setRefundProducureFee(customerBusinessInfoVo.getRefundProducureFee());
		customerBusinessInfo.setRefundRateMode(customerBusinessInfoVo.getRefundRateMode());
		customerBusinessInfo.setRefundRateParam(customerBusinessInfoVo.getRefundRateParam());
		customerBusinessInfo.setPayMethods(null);
		customerBusinessInfo.setPayMethodNames(customerBusinessInfoVo.getPayMethodNames());
		customerBusinessInfo.setBusinessName(customerBusinessInfoVo.getBusinessName());
		customerBusinessInfo.setBusinessExamId(customerBusinessInfoVo.getBusinessExamId());
		customerBusinessInfo.setBeginTime(customerBusinessInfoVo.getBeginTime());
		customerBusinessInfo.setEndTime(customerBusinessInfoVo.getEndTime());
		customerBusinessInfo.setFeePer(customerBusinessInfoVo.getFeePer());
		customerBusinessInfo.setMaxFee(customerBusinessInfoVo.getMaxFee());
		customerBusinessInfo.setMinFee(customerBusinessInfoVo.getMinFee());
		if (StringUtils.isBlank(customerBusinessInfoVo.getNoCreditcards())) {
			//如果禁用信通卡参数是空的，那么默认是允许使用信用卡支付的
			customerBusinessInfo.setNoCreditcards(CustomerBusinessInfo.NoCreditcards.ALLOW.code);
		}else {
			customerBusinessInfo.setNoCreditcards(customerBusinessInfoVo.getNoCreditcards());
		}
		customerBusinessInfo.setCanRefund(customerBusinessInfoVo.getCanRefund()!=null ? customerBusinessInfoVo.getCanRefund():null);
		customerBusinessInfo.setBusinessCategoryCode(customerBusinessInfoVo.getBusinessCategoryCode()!=null ? customerBusinessInfoVo.getBusinessCategoryCode():null);
		customerBusinessInfo.setCanAdvance(customerBusinessInfoVo.getCanAdvance()!=null ? customerBusinessInfoVo.getCanAdvance():null);
		customerBusinessInfo.setSettCycle(customerBusinessInfoVo.getSettCycle());
		/*if (customerBusinessInfoVo.getBusinessCategoryCode() == null) {   // 接口传的可能没有传上来20190109
			String businessCategory = businessMapper.selectBusinessCategoryByCode(customerBusinessInfoVo.getBusinessCode());
			customerBusinessInfo.setBusinessCategoryCode(businessCategory);
		}*/
		customerBusinessInfoMapper.insert(customerBusinessInfo);
		return businessInfoId;
	}
    @Logable(businessTag = "modifyBusinessInfo")
    public void modifyBusinessInfo(CustomerBusinessInfoVo customerBusinessInfoVo) {
        CustomerBusinessInfo oldBusinessInfo = customerBusinessInfoMapper.selectByPrimaryKey(customerBusinessInfoVo.getBusinessId()) ;
        if(oldBusinessInfo==null)
        {
            throw new AppException(MchError.BUSINESS_NOT_EXIST.code,MchError.BUSINESS_NOT_EXIST.message);
        }
        if(oldBusinessInfo.getCustomerInfoId().longValue() != customerBusinessInfoVo.getCustomerInfoId().longValue())
        {
            throw new AppException(MchError.CUSTOMER_BUSINESS_NOT_EXIST.code,MchError.CUSTOMER_BUSINESS_NOT_EXIST.message);
        }
		customerBusinessInfoVo.setOldBusinessExamId(oldBusinessInfo.getBusinessExamId());// 存旧业务实例ID

        CustomerBusinessInfo customerBusinessInfo = new CustomerBusinessInfo();
        customerBusinessInfo.setBusinessId(customerBusinessInfoVo.getBusinessId());
//        customerBusinessInfo.setCustomerInfoId(customerBusinessInfoVo.getCustomerInfoId());
//        customerBusinessInfo.setCustomerId(customerBusinessInfoVo.getCustomerId());
//        customerBusinessInfo.setCustomerCode(customerBusinessInfoVo.getCustomerCode());
//        customerBusinessInfo.setBusinessCode(customerBusinessInfoVo.getBusinessCode());
//        Business business = businessMapper.selectByCode(customerBusinessInfoVo.getBusinessCode());
//        customerBusinessInfo.setRateName(business.getName());

		if (customerBusinessInfoVo.getRateMode() == 1) //1按单笔，2按比例，
		{
			customerBusinessInfoVo.setRateParam("");
			if (customerBusinessInfoVo.getFeePer() == null  ) {
				throw new AppException(MchError.FEE_PER_ERROR.code, MchError.FEE_PER_ERROR.message);
			}
		}
		if (customerBusinessInfoVo.getRateMode() == 2)  //按比例
		{
			customerBusinessInfoVo.setFeePer(null);
			if (customerBusinessInfoVo.getRateParam() == null || "".equals(customerBusinessInfoVo.getRateParam())) {
				throw new AppException(MchError.FEE_PER_ERROR.code, MchError.FEE_PER_ERROR.message);
			}
		}
		customerBusinessInfo.setRateMode(customerBusinessInfoVo.getRateMode());
        customerBusinessInfo.setRateParam(customerBusinessInfoVo.getRateParam());
        customerBusinessInfo.setStatus(customerBusinessInfoVo.getStatus());
        customerBusinessInfo.setFollowWechatAccount(customerBusinessInfoVo.getFollowWechatAccount());
        customerBusinessInfo.setRefundProducureFee(customerBusinessInfoVo.getRefundProducureFee());
        customerBusinessInfo.setRefundRateMode(customerBusinessInfoVo.getRefundRateMode());
        customerBusinessInfo.setRefundRateParam(customerBusinessInfoVo.getRefundRateParam());
//        customerBusinessInfo.setPayMethods(null);
//        customerBusinessInfo.setPayMethodNames(customerBusinessInfoVo.getPayMethodNames());
//        customerBusinessInfo.setBusinessName(customerBusinessInfoVo.getBusinessName());
        customerBusinessInfo.setBusinessExamId(customerBusinessInfoVo.getBusinessExamId());
        customerBusinessInfo.setBeginTime(customerBusinessInfoVo.getBeginTime());
        customerBusinessInfo.setEndTime(customerBusinessInfoVo.getEndTime());
        customerBusinessInfo.setFeePer(customerBusinessInfoVo.getFeePer());
        customerBusinessInfo.setMaxFee(customerBusinessInfoVo.getMaxFee());
        customerBusinessInfo.setMinFee(customerBusinessInfoVo.getMinFee());
        if (StringUtils.isBlank(customerBusinessInfoVo.getNoCreditcards())) {
            //如果禁用信通卡参数是空的，那么默认是允许使用信用卡支付的
            customerBusinessInfo.setNoCreditcards(CustomerBusinessInfo.NoCreditcards.ALLOW.code);
        } else {
            customerBusinessInfo.setNoCreditcards(customerBusinessInfoVo.getNoCreditcards());
        }
        customerBusinessInfo.setCanRefund(customerBusinessInfoVo.getCanRefund()!=null ? customerBusinessInfoVo.getCanRefund():null);
        customerBusinessInfo.setCanAdvance(customerBusinessInfoVo.getCanAdvance()!=null ? customerBusinessInfoVo.getCanAdvance():null);
        customerBusinessInfo.setSettCycle(customerBusinessInfoVo.getSettCycle());
        customerBusinessInfoMapper.updateByPrimaryKeySelective(customerBusinessInfo);
    }

	/**
	 * 添加结算周期实例
	 *
	 * @param businessInfoId
	 *            业务实例的业务键
	 * @param pasSettCycleRule
	 *            结算周期规则
	 * @param beginTime
	 *            插入的结算周期实例的开始时间
	 * @param endTime
	 *            插入的结算周期实例的结束时间
	 * @param businessInstId
	 */
	@Logable(businessTag = "addSettCycleRuleInst")
	public void addSettCycleRuleInst(String businessExamId, PasSettCycleRule pasSettCycleRule, Date beginTime,
			Date endTime, Long businessInstId) {
		PasSettCycleRuleInst pasSettCycleRuleInst = new PasSettCycleRuleInst();
		Long SettCycleRuleInstId = sequenceService.nextValue(ADDSETTCYCLERULEINSTCATEGORY);
		pasSettCycleRuleInst.setId(SettCycleRuleInstId);
		pasSettCycleRuleInst.setCustomerBusinessInfoId(businessExamId);
		pasSettCycleRuleInst.setSettCycleRuleCode(pasSettCycleRule.getCode());
		long now = System.currentTimeMillis();
		Date nowDate = new Date(now);
		pasSettCycleRuleInst.setValidStartTime(beginTime);
		pasSettCycleRuleInst.setValidEndTime(endTime);
		pasSettCycleRuleInst.setCreateTime(nowDate);
		pasSettCycleRuleInst.setUpdateTime(nowDate);
		pasSettCycleRuleInst.setBusinessInstDBId(businessInstId);
		pasSettCycleRuleInstMapper.insert(pasSettCycleRuleInst);
	}
    /**
     * 修改结算周期实例
     *
	 * @param oldBusinessExamId 原业务实例的业务键
     * @param businessExamId
     *            业务实例的业务键
     * @param pasSettCycleRule
     *            结算周期规则
     * @param beginTime
     *            插入的结算周期实例的开始时间
     * @param endTime
     *            插入的结算周期实例的结束时间
     * @param businessInstId
     */
    @Logable(businessTag = "modifySettCycleRuleInst")
    @Transactional
    public void modifySettCycleRuleInst(String oldBusinessExamId, String businessExamId, PasSettCycleRule pasSettCycleRule, Date beginTime,
                                     Date endTime, Long businessInstId) {
        // 先删除原有的
		pasSettCycleRuleInstMapper.deleteByBusinessExamId(oldBusinessExamId);
        // 再新增
        PasSettCycleRuleInst pasSettCycleRuleInst = new PasSettCycleRuleInst();
        Long SettCycleRuleInstId = sequenceService.nextValue(ADDSETTCYCLERULEINSTCATEGORY);
        pasSettCycleRuleInst.setId(SettCycleRuleInstId);
        pasSettCycleRuleInst.setCustomerBusinessInfoId(businessExamId);
        pasSettCycleRuleInst.setSettCycleRuleCode(pasSettCycleRule.getCode());
        long now = System.currentTimeMillis();
        Date nowDate = new Date(now);
        pasSettCycleRuleInst.setValidStartTime(beginTime);
        pasSettCycleRuleInst.setValidEndTime(endTime);
        pasSettCycleRuleInst.setCreateTime(nowDate);
        pasSettCycleRuleInst.setUpdateTime(nowDate);
        pasSettCycleRuleInst.setBusinessInstDBId(businessInstId);
        pasSettCycleRuleInstMapper.insert(pasSettCycleRuleInst);
    }

	/**
	 * 校验用户要开通的业务信息，并设置业务实例ID 结算兼容旧版本 如果入参中未指定结算周期规则实例，则根据客户的结算信息生成一个默认的结算周期规则实例
	 *
	 * @param businessInfo
	 */
	@Logable(businessTag = "validBusinessInfo")
	public void validBusinessInfo(CustomerBusinessInfoVo businessInfo) {
		Date now = new Date();
//		if (businessInfo.getBeginTime() == null) {
			businessInfo.setBeginTime(now);
//		}
//		if (businessInfo.getEndTime() == null) {
			Date date = new Date(DEFAULT_YEAR, DEFAULT_MONTH, DEFAULT_DATE, DEFAULT_HOUR, DEFAULT_MINUTE,
					DEFAULT_SECOND);
			businessInfo.setEndTime(date);
//		}

		if ("ADD".equals(businessInfo.getChangeType())) {
            self.checkExistRepeatBusiness(businessInfo);
        }
//		self.checkMemberService(businessInfo);
		self.checkRatioWithParent(businessInfo);

		String customerCode = customerMapper.selectCustomerCodeByCustomerInfoId(businessInfo.getCustomerInfoId());
		SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
		businessInfo.setBusinessExamId(
				businessInfo.getBusinessCode() + "_" + customerCode + "_" + format.format(businessInfo.getBeginTime()));
		if (StringUtils.isNotBlank(businessInfo.getSettCycle())) {
			// 业务信息的结算周期不为空
			SettCycleRuleInstVo vo = new SettCycleRuleInstVo();
			vo.setSettCycleRuleCode(businessInfo.getSettCycle());
			vo.setValidStartTime(businessInfo.getBeginTime());
			vo.setValidEndTime(businessInfo.getEndTime());
			List<SettCycleRuleInstVo> settCycleRuleInstList = new ArrayList<SettCycleRuleInstVo>();
			settCycleRuleInstList.add(vo);
			businessInfo.setSettCycleRuleInstList(settCycleRuleInstList);
		} else {
			// 业务信息的结算周期为空
			if (businessInfo.getSettCycleRuleInstList() == null || businessInfo.getSettCycleRuleInstList().isEmpty()) {
				String businessCode = businessInfo.getBusinessCode();
				if (this.isRealtimeOfBusiness(businessCode)) {
					// "EFPS分账支付类业务"必须配置为实时结算(原因：产品规定的业务约束)
					SettCycleRuleInstVo vo = new SettCycleRuleInstVo();
					vo.setSettCycleRuleCode("RealTime");
					vo.setValidStartTime(businessInfo.getBeginTime());
					vo.setValidEndTime(businessInfo.getEndTime());
					List<SettCycleRuleInstVo> settCycleRuleInstList = new ArrayList<SettCycleRuleInstVo>();
					settCycleRuleInstList.add(vo);
					businessInfo.setSettCycleRuleInstList(settCycleRuleInstList);
				}else {
					// 如果入参的结算周期规则为空，现有版本下只能是子商户进件接口进来的，则生成和父商户业务一致的一致
					CustomerSettleInfo customerSettleInfo = customerSettleInfoMapper
							.getByCustomerInfoId(businessInfo.getCustomerInfoId());
					String settMode = customerSettleInfo.getSettleMode();
					PasSettCycleRule rule = null;
					if (settMode.equalsIgnoreCase(pasSettCycleRuleType.D0.code)
							|| settMode.equalsIgnoreCase(pasSettCycleRuleType.REALTIME.code)) {
						rule = pasSettCycleRuleMapper.selectByTypeAndStatus(settMode,
								PasConstants.pasSettCycleRuleStatus.VALID.code);

					} else {
						rule = pasSettCycleRuleMapper.selectByTypeAndParamsAndStatus(customerSettleInfo.getSettleMode(),
								customerSettleInfo.getCircle().toString(), PasConstants.pasSettCycleRuleStatus.VALID.code);
					}
					if (rule == null) {
						throw new AppException(PasCode.SETT_CYCLE_PARAM_INVALID.code);
					}

					if(!settMode.equalsIgnoreCase(pasSettCycleRuleType.REALTIME.code))
					{//非实时结算的业务，需要修改业务实例的有效期开始时间为结算周期的整点，否则多个业务合并结算会有问题
						businessInfo.setBeginTime(self.calcCurrentSettCycleBeginTime(rule));
					}

					SettCycleRuleInstVo vo = new SettCycleRuleInstVo();
					vo.setSettCycleRuleCode(rule.getCode());
					vo.setValidStartTime(businessInfo.getBeginTime());
					vo.setValidEndTime(businessInfo.getEndTime());
					List<SettCycleRuleInstVo> settCycleRuleInstList = new ArrayList<SettCycleRuleInstVo>();
					settCycleRuleInstList.add(vo);
					businessInfo.setSettCycleRuleInstList(settCycleRuleInstList);
				}
			}
		}
	}

	/**
	 * 如果当前添加业务的对象是客户会员并且添加的业务是兑换或者反兑换业务，那么需校验父子兑换比例的一致性
	 * @param businessInfo
	 */
	@Logable(businessTag = "checkRatioWithParent")
	public void checkRatioWithParent(CustomerBusinessInfoVo businessInfo) {
		Long infoId = businessInfo.getCustomerInfoId();
		CustomerInfo customerInfo = customerInfoMapper.selectByPrimaryKey(infoId);
		if (Constants.customerCategory.EFPS_CUSTOMER_MEMBER.code.equals(customerInfo.getCustomerCategory()) &&
				(businessInfo.getBusinessCode().equals(Constants.EfpsAccountService.MEMBER_EXCHANGE_IN.code) ||
				 businessInfo.getBusinessCode().equals(Constants.EfpsAccountService.MEMBER_EXCHANGE_OUT.code) ||
				 businessInfo.getBusinessCode().equals(Constants.EfpsAccountService.MEMBER_OPPOSITE_EXCHANGE_IN.code) ||
				 businessInfo.getBusinessCode().equals(Constants.EfpsAccountService.MEMBER_OPPOSITE_EXCHANGE_OUT.code))) {
			//如果当前添加的是客户会员，并且添加的业务是兑换或反兑换，那么需要校验该客户会员与父客户的会员卡兑换业务的兑换比例参数是否一致
			List<BusinessParamInstVo> businessParamInstVos = businessInfo.getBusinessParamInstList();
			String parentCustomerCode = customerInfo.getParentCustomerCode();
			Customer customer = customerMapper.selectByCustomerCode(parentCustomerCode);
			List<CustomerBusinessInfo> customerBusinessInfos = customerBusinessInfoMapper.
					selectByInfoId(customer.getNewestCustomerInfoId());
			for (CustomerBusinessInfo customerBusinessInfo : customerBusinessInfos) {
				if (customerBusinessInfo.getBusinessCode().equals(Constants.EfpsAccountService.MEMBER_EXCHANGE_IN.code) ||
						customerBusinessInfo.getBusinessCode().equals(Constants.EfpsAccountService.MEMBER_EXCHANGE_OUT.code) ||
						customerBusinessInfo.getBusinessCode().equals(Constants.EfpsAccountService.MEMBER_OPPOSITE_EXCHANGE_IN.code) ||
						customerBusinessInfo.getBusinessCode().equals(Constants.EfpsAccountService.MEMBER_OPPOSITE_EXCHANGE_OUT.code)) {
					BusinessParamInst businessParamInst = businessParamInstMapper
							.selectByBusinessInstDBIdAndCode(customerBusinessInfo.getBusinessId(),
									Constants.BusinessParamCode.SUBSCRIPTION_RATIO.code);
					if (businessParamInstVos != null) {
						for (BusinessParamInstVo businessParamInstVo : businessParamInstVos) {
							if (Constants.BusinessParamCode.SUBSCRIPTION_RATIO.code.equals(businessParamInstVo.getCode())) {
								if (!businessParamInst.getValue().equals(businessParamInstVo.getValue())) {
									//如果兑换参数不等，那么抛出异常
									throw new AppException(MchError.CUM_INVALID_PARAM.code);
								}
							}
						}
					}
				}
			}
		}
	}
	@Logable(businessTag="calcCurrentSettCycleBeginTime")
	public Date calcCurrentSettCycleBeginTime(PasSettCycleRule rule) {
		Date now = new Date();
		SettmentCycle cycle = self.calcSettCycle(now, rule);
		return cycle.getStartTime();
	}

	/**
	 * 根据结算周期规则实例和结算周期规则计算某个时间点所属的结算周期 要求结算规则周期实例的有效期必须包含入参time
	 *
	 * @param settCycleRuleInst
	 * @return
	 */
	@Logable(businessTag = "calcSettCycle")
	public SettmentCycle calcSettCycle(Date time, PasSettCycleRule rule) {
		SettmentCycle cycle = new SettmentCycle();
		if (rule.getType().equalsIgnoreCase(pasSettCycleRuleType.REALTIME.code)) {
			cycle.setEndTime(null);
			cycle.setStartTime(null);
			return cycle;
		}
		// 其他类型
		Date startTime = new Date(System.currentTimeMillis()-86400000*7);//最多7天假期，国内
		Date endTime = null;
		while (true) {// 通用性考虑，只能从有效期开始一个个尝试; 前面已经判断time一定落在有效期内
			endTime = self.calcEndTime(startTime, rule);
			if (endTime.getTime() > time.getTime()) {
				cycle.setStartTime(startTime);
				cycle.setEndTime(endTime);
				return cycle;
			}
			startTime = endTime;
		}
	}

	@Logable(businessTag = "calcEndTime", level = Level.DEBUG)
	public Date calcEndTime(Date startTime,PasSettCycleRule rule) {
		if (rule.getType().equals(pasSettCycleRuleType.D.code)) {
			return self.calcEndTimeForD(startTime, rule);
		}
		if (rule.getType().equals(pasSettCycleRuleType.T.code)) {
			return self.calcEndTimeForT(startTime, rule);
		}
		if (rule.getType().equals(pasSettCycleRuleType.D0.code)) {
			return self.calcEndTimeForD0(startTime, rule);
		}
		return null;
	}

	@Logable(businessTag = "calcEndTimeForD0", level = Level.DEBUG)
	protected Date calcEndTimeForD0(Date startTime, PasSettCycleRule rule) {

		 SimpleDateFormat dateTimeFormate = new SimpleDateFormat("yyyyMMdd HH:mm:ss");
		 SimpleDateFormat dateFormate = new SimpleDateFormat("yyyyMMdd");
		String[] splitingTimes = rule.getParams().split(",");// 必须是HH:mm:ss格式
		List<Date> dateList = new LinkedList<>();
		for (String timeStr : splitingTimes) {
			try {
				dateList.add(dateTimeFormate.parse(dateFormate.format(startTime) + " " + timeStr.trim()));
			} catch (ParseException e) {
				throw new AppException(PasCode.SETT_CYCLE_PARAM_INVALID.code);
			}
		}
		if (dateList.size() == 0)
			throw new AppException(PasCode.SETT_CYCLE_PARAM_INVALID.code);
		Collections.sort(dateList);
		Calendar cycleEndTime = Calendar.getInstance();

		if (dateList.get(dateList.size() - 1).getTime() <= startTime.getTime()) {// 入参时间大于等于最后一个时间点，必然是第二天的第一个时间点
			cycleEndTime.setTime(dateList.get(0));
			cycleEndTime.add(Calendar.DATE, 1);
			return cycleEndTime.getTime();
		}
		// 其他情况，则必然是当天的第一个大于startTime的时间点
		for (int i = 1; i < dateList.size(); i++) {
			if (dateList.get(i).getTime() > startTime.getTime()) {
				return dateList.get(i);
			}
		}
		throw new AppException(PasCode.SYSTEM_EXCEPTION.code);
	}

	@Logable(businessTag = "calcEndTimeForT", level = Level.DEBUG)
	protected Date calcEndTimeForT(Date startTime, PasSettCycleRule rule) {
		int cycleDays = Integer.parseInt(rule.getParams().trim());
		Calendar endTime = Calendar.getInstance();
		endTime.setTime(startTime);
		endTime.set(Calendar.HOUR_OF_DAY, 0);
		endTime.set(Calendar.MINUTE, 0);
		endTime.set(Calendar.SECOND, 0);
		endTime.set(Calendar.MILLISECOND, 0);
		int passedWorkDays = 0;
		while(true)
		{
			endTime.add(Calendar.DATE, 1);
			if(!isHoliday(endTime.getTime()))
				passedWorkDays++;
			if(passedWorkDays == cycleDays)
				break;
		}
		return endTime.getTime();
	}

	@Logable(businessTag = "calcEndTimeForD", level = Level.DEBUG)
	protected Date calcEndTimeForD(Date startTime, PasSettCycleRule rule) {
		int cycleDays = Integer.parseInt(rule.getParams().trim());
		Calendar endTime = Calendar.getInstance();
		endTime.setTime(startTime);
		endTime.set(Calendar.HOUR_OF_DAY, 0);
		endTime.set(Calendar.MINUTE, 0);
		endTime.set(Calendar.SECOND, 0);
		endTime.set(Calendar.MILLISECOND, 0);
		endTime.add(Calendar.DATE, cycleDays);
		return endTime.getTime();
	}

	@Logable(businessTag = "isHoliday", level = Level.DEBUG)
	boolean isHoliday(Date date) {
		SimpleDateFormat dateFormate = new SimpleDateFormat("yyyyMMdd");
		String dateStr = dateFormate.format(date);
		return holidayMapper.selectByPrimaryKey(dateStr) != null;
	}

	/**
	 * 检查是否存在重复的业务，存在重复的业务会抛出异常
	 * 所谓重复，是指输入的业务实例的有效期与已经存在的相同businessCode的任何一个业务实例的有效期有重叠
	 *
	 * @param businessInfo
	 */
	@Logable(businessTag = "checkExistRepeatBusiness")
	protected void checkExistRepeatBusiness(CustomerBusinessInfoVo businessInfo) {
		//如果是代理商，检查该业务是否允许重复添加，
		List list = new ArrayList();
		list.add(businessInfo.getBusinessCode());
		List<Business> bList = businessService.getBusinessByCodes(list);
		Business b = bList.get(0);
		//检查是否服务商    ,就是开通代理业务
		boolean isDl = customerBusinessInfoMapper.isBusinessInfoExist(businessInfo.getCustomerInfoId(), Constants.BusinessCode.DL.code);
		if ("1".equals(b.getCanRepeat()) && isDl) {
			//判断是否服务 判断是否同一业务，同一结算周期
			List<CustomerBusinessInfo> businessInfos = customerBusinessInfoMapper.selectByBusinessCodeAndInfoId(businessInfo.getBusinessCode(), businessInfo.getCustomerInfoId());
			if (businessInfos != null && businessInfos.size() > 0) {
				for (CustomerBusinessInfo customerBusinessInfo : businessInfos) {
					if (customerBusinessInfo.getSettCycle() != null && businessInfo.getSettCycle() != null) {
						if (customerBusinessInfo.getSettCycle().equals(businessInfo.getSettCycle())) {
							throw new AppException(PasCode.NOT_ALLOW_REPEATED_SCYLE_BUSINESS.code);
						}
					}
				}
			}
		}
		else  {  //不允许，则走原来流程
			TimeInterval inputTimeInterval = new TimeInterval(businessInfo.getBeginTime(), businessInfo.getEndTime());
			List<CustomerBusinessInfo> existSameBusinessInsts = customerBusinessInfoMapper
					.selectByBusinessCodeAndInfoId(businessInfo.getBusinessCode(), businessInfo.getCustomerInfoId());
			if (existSameBusinessInsts != null && existSameBusinessInsts.size() > 0) {
				for (CustomerBusinessInfo info : existSameBusinessInsts) {
					TimeInterval temp = new TimeInterval(info.getBeginTime(), info.getEndTime());
					if (temp.overlapped(inputTimeInterval))
						throw new AppException(PasCode.NOT_ALLOW_REPEATED_BUSINESS.code);
				}
			}
		}
	}

	/**
	 * 会员业务的校验
	 * @param businessInfo
	 */
	@Logable(businessTag = "checkMemberService")
	protected void checkMemberService(CustomerBusinessInfoVo businessInfo) {
		if (businessInfo.getBusinessCode().equals(MEMBER_EXCHANGE_IN) ||
				businessInfo.getBusinessCode().equals(MEMBER_EXCHANGE_OUT)) {
			//如果添加的是会员兑换业务，那么需要查询该客户的会员的反兑换业务
			List<CustomerBusinessInfo> customerBusinessInfos = customerBusinessInfoMapper
					.selectByBusinessCodeAndInfoId(MEMBER_OPPOSITE_EXCHANGE_IN, businessInfo.getCustomerInfoId());
			self.checkRation(businessInfo, customerBusinessInfos);
			customerBusinessInfos = customerBusinessInfoMapper
					.selectByBusinessCodeAndInfoId(MEMBER_OPPOSITE_EXCHANGE_OUT, businessInfo.getCustomerInfoId());
			self.checkRation(businessInfo, customerBusinessInfos);
			if (businessInfo.getBusinessCode().equals(MEMBER_EXCHANGE_IN)) {
				customerBusinessInfos = customerBusinessInfoMapper
						.selectByBusinessCodeAndInfoId(MEMBER_EXCHANGE_OUT, businessInfo.getCustomerInfoId());
				self.checkRation(businessInfo, customerBusinessInfos);
			}else {
				customerBusinessInfos = customerBusinessInfoMapper
						.selectByBusinessCodeAndInfoId(MEMBER_EXCHANGE_IN, businessInfo.getCustomerInfoId());
				self.checkRation(businessInfo, customerBusinessInfos);
			}
		}
		if (businessInfo.getBusinessCode().equals(MEMBER_OPPOSITE_EXCHANGE_IN) ||
				businessInfo.getBusinessCode().equals(MEMBER_OPPOSITE_EXCHANGE_OUT)) {
			//如果添加的是会员反兑换业务，那么需要校验该客户的会员的兑换业务的兑换比例
			List<CustomerBusinessInfo> customerBusinessInfos = customerBusinessInfoMapper
					.selectByBusinessCodeAndInfoId(MEMBER_EXCHANGE_IN, businessInfo.getCustomerInfoId());
			self.checkRation(businessInfo, customerBusinessInfos);
			customerBusinessInfos = customerBusinessInfoMapper
					.selectByBusinessCodeAndInfoId(MEMBER_EXCHANGE_OUT, businessInfo.getCustomerInfoId());
			self.checkRation(businessInfo, customerBusinessInfos);
			if (businessInfo.getBusinessCode().equals(MEMBER_OPPOSITE_EXCHANGE_IN)) {
				customerBusinessInfos = customerBusinessInfoMapper
						.selectByBusinessCodeAndInfoId(MEMBER_OPPOSITE_EXCHANGE_OUT, businessInfo.getCustomerInfoId());
				self.checkRation(businessInfo, customerBusinessInfos);
			}else {
				customerBusinessInfos = customerBusinessInfoMapper
						.selectByBusinessCodeAndInfoId(MEMBER_OPPOSITE_EXCHANGE_IN, businessInfo.getCustomerInfoId());
				self.checkRation(businessInfo, customerBusinessInfos);
			}
		}
	}

	@Logable(businessTag = "checkRation")
	public void checkRation(CustomerBusinessInfoVo businessInfo, List<CustomerBusinessInfo> customerBusinessInfos) {
		if (customerBusinessInfos != null && customerBusinessInfos.size() > 0) {
			List<BusinessParamInstVo> businessParamInsts = businessInfo.getBusinessParamInstList();
			for (CustomerBusinessInfo info : customerBusinessInfos) {
				BusinessParamInst businessParamInst = businessParamInstMapper
						.selectByBusinessInstDBIdAndCode(info.getBusinessId() ,
								Constants.BusinessParamCode.SUBSCRIPTION_RATIO.code);
				for (BusinessParamInstVo businessParamInstVo : businessParamInsts) {
					if (Constants.BusinessParamCode.SUBSCRIPTION_RATIO.code.equals(businessParamInstVo.getCode())) {
						if (!businessParamInstVo.getValue().equals(businessParamInst.getValue())) {
							//如果入参的兑换比例不一致
							throw new AppException(MchError.CUM_INVALID_PARAM.code);
						}
					}
				}
			}
		}
	}

	/**
	 * 查询客户业务信息
	 */
	@Override
	@Logable(businessTag = "pageQueryCustomerBusinessInfos")
	public PageResult<CustomerBusinessInfo> pageQueryCustomerBusinessInfos(int pageNum, int pageSize,
			Long customerInfoId) {
		int total = customerBusinessInfoMapper.countCustomerBusinessInfos(customerInfoId);
		int beginRowNo = (pageNum - 1) * pageSize + 1;
		int endRowNo = pageNum * pageSize;
		List<CustomerBusinessInfo> list = customerBusinessInfoMapper.pageQueryCustomerBusinessInfos(beginRowNo,
				endRowNo, customerInfoId);
		if (list != null && !list.isEmpty()) {
			list.forEach(customerBusinessInfo -> {
				//对旧数据进行处理  20180917
				if(customerBusinessInfo.getRateMode()==1 && customerBusinessInfo.getFeePer() ==null) //1按单笔，2按比例，
				{
					String[] rates =customerBusinessInfo.getRateParam()!=null?customerBusinessInfo.getRateParam().split("_"):null ;
					if(rates !=null && rates[0]!=null) {
						customerBusinessInfo.setFeePer(new Long(rates[0]));
						customerBusinessInfo.setRateParam(null);
					}
					else {
						customerBusinessInfo.setFeePer(null);
					}
				}
				if(customerBusinessInfo.getRateMode()==2)  //按比例
				{
					String[] rates =customerBusinessInfo.getRateParam()!=null?customerBusinessInfo.getRateParam().split("_"):null ;
					if (rates != null && rates[0] != null) {
						customerBusinessInfo.setRateParam(rates[0]);
					} else {
						customerBusinessInfo.setRateParam(null);
					}
				}
				// 迭代所有的业务实例
				Business business = businessMapper.selectByCode(customerBusinessInfo.getBusinessCode());
				if (business != null) {
					customerBusinessInfo.setBusinessName(business.getName());
					// 查询该业务对应的支付方式
					List<String> payMethodCodes = bizPayMethodMapper
							.selectPayMethods(customerBusinessInfo.getBusinessCode());
					List<String> payMethodNames = null;
					if (payMethodCodes != null && !payMethodCodes.isEmpty()) {
						payMethodNames = payMethodMapper.selectByPayMethodCodes(payMethodCodes);
					}
					if (payMethodNames != null && !payMethodNames.isEmpty()) {
						StringBuffer sb = new StringBuffer();
						for (String payMethodName : payMethodNames) {
							sb.append(payMethodName + ",");
						}
						sb.deleteCharAt(sb.length() - 1);
						customerBusinessInfo.setPayMethodNames(sb.toString());
					}
				}
				// 查询业务实例对应的业务参数实例
				List<BusinessParamInst> businessParamInsts = businessParamInstMapper.selectByBusinessInstDBId(customerBusinessInfo.getBusinessId());
				for(BusinessParamInst bpi:businessParamInsts) {//解析value值
					if(Constants.BusinessParamCode.NOCARD_SEND_MSG.code.equals(bpi.getCode())) {
						if(Constants.NocardSendMsg.need.code.equals(bpi.getValue())) {
							bpi.setExtendedField(Constants.NocardSendMsg.need.comment);
						}else {
							bpi.setExtendedField(Constants.NocardSendMsg.no_need.comment);
						}
					}
					if(Constants.BusinessParamCode.NOCARD_PROFIT_METHOD.code.equals(bpi.getCode())) {
						bpi.setExtendedField("比例");//TODO
					}
				}
				customerBusinessInfo.setBusinessParamInsts(businessParamInsts);
			});
		}

		PageResult<CustomerBusinessInfo> pagingResult = new PageResult<>();
		pagingResult.setTotal(total);
		pagingResult.setRows(list);
		return pagingResult;
	}

	/**
	 * 删除客户业务信息
	 */
	@Override
	@Logable(businessTag = "deleteCustomerBusinessInfo")
	public boolean deleteCustomerBusinessInfo(Long id) {
		CustomerBusinessInfo customerBusinessInfo = customerBusinessInfoMapper.selectByPrimaryKey(id);
		//如果没有产生业务，那么则删除该业务
		boolean used = txsClient.confirmTransactionRecord(customerBusinessInfo.getBusinessCode(), customerBusinessInfo.getBusinessExamId());
		if (!used) {
			customerBusinessInfoMapper.deleteByPrimaryKey(id);
			return true;
		}
		return false;
	}

	@Override
	@Logable(businessTag = "getCustomerBusinessInfos")
	public List<CustomerBusinessInfo> getCustomerBusinessInfos(Long customerInfoId) {
		List<CustomerBusinessInfo> bizInfoList = customerBusinessInfoMapper.queryListByCustomerInfoId(customerInfoId);
		// 设置支付方式编码
		for (CustomerBusinessInfo bizInfo : bizInfoList) {
			List<String> payMethods = bizPayMethodMapper.selectPayMethods(bizInfo.getBusinessCode());
			bizInfo.setPayMethods(payMethods);
			// 查询业务参数实例
			List<BusinessParamInst> businessParamInsts = businessParamInstMapper
					.selectByBusinessInstDBId(bizInfo.getBusinessId());
			bizInfo.setBusinessParamInsts(businessParamInsts);
		}
		return bizInfoList;
	}

	@Override
	@Logable(businessTag = "getCustomerBusinessInfoByPk")
	public CustomerBusinessInfo getCustomerBusinessInfoByPk(Long businessId) {
		return customerBusinessInfoMapper.selectByPrimaryKey(businessId);
	}

	@Override
	@Logable(businessTag = "queryListByCustomerInfoId")
	public List<CustomerBusinessInfo> queryListByCustomerInfoId(Long customerInfoId) {
		return customerBusinessInfoMapper.queryListByCustomerInfoId(customerInfoId);
	}

	@Override
	public int countCustomerBusinessInfo(Long customerInfoId) {
		return customerBusinessInfoMapper.countCustomerBusinessInfo(customerInfoId);
	}

	@Override
	public String checkCustomerBusinessInfoExist(Long customerInfoId, String businessCode) {
		boolean isExist = customerBusinessInfoMapper.isBusinessInfoExist(customerInfoId, businessCode);
		return isExist ? MchConstants.Exist.TRUE.code : MchConstants.Exist.FALSE.code;
	}

	@Override
	public List<CustomerBusinessInfo> getAllCustomerBusinessInfo() {
		return customerBusinessInfoMapper.selectAll();
	}

	@Override
	public List<CustomerBusinessInfo> getCustBusinessInfoByCustomerCode(String customerCode) {
		return customerBusinessInfoMapper.selectByCustomerCode(customerCode);
	}

	/**
	 * 判断业务编码所属的业务类别是否是“实时结算”
	 * @param businessCode
	 * @return
	 * 注："EFPS分账支付类业务"必须配置为实时结算（原因：产品规定的业务约束）；
	 *    "EFPS易票联账户服务"也是实时；
	 * 	      其它几个业务类别为非实时结算（原因：在途金额退款需合并结算）
	 */
	@Logable(businessTag = "isRealtimeOfBusiness")
	public boolean isRealtimeOfBusiness(String businessCode) {
		Business business = businessMapper.selectByCode(businessCode);
		if (business.getBusinessCategory().equals(Constants.BusinessCategory.EFPS_FZ_PAY_SERVICE.code)
			|| business.getBusinessCategory().equals(Constants.BusinessCategory.EFPS_NOCARD_SERVICE.code)) {
			return true;
		}
		if (Constants.BusinessCategory.EFPS_ACCOUNT_SERVICE.code.equals(business.getBusinessCategory())) {
			//如果属于易票联账务服务
			if (!Constants.EfpsAccountService.DL.code.equals(businessCode) &&
				!Constants.EfpsAccountService.RECHARGE.code.equals(businessCode)) {
				//只要不是代理业务和充值业务，那么就是实时结算
				return true;
			}
		}
		return false;
	}

	@Logable(businessTag="addRefundBusiness")
	public void addRefundBusiness(List<CustomerBusinessInfo> customerBusinessInfoVos) {

		for (CustomerBusinessInfo customerBusinessInfoVo : customerBusinessInfoVos) {
			Customer customer = customerMapper.selectByCustomerCode(customerBusinessInfoVo.getCustomerCode());
			if (customer == null) {
				continue;
			}
			CustomerBusinessInfo customerBusinessInfo = new CustomerBusinessInfo();
			Long businessInfoId = sequenceService.nextValue("addMchBusiness");
			customerBusinessInfo.setBusinessId(businessInfoId);
			customerBusinessInfo.setCustomerInfoId(customer.getNewestCustomerInfoId());
			customerBusinessInfo.setCustomerId(null);
			customerBusinessInfo.setCustomerCode(null);
			customerBusinessInfo.setBusinessCode("Refund");
			Business business = businessMapper.selectByCode(customerBusinessInfo.getBusinessCode());
			customerBusinessInfo.setRateName(business.getName());
			customerBusinessInfo.setRateMode(customerBusinessInfoVo.getRateMode());
			customerBusinessInfo.setRateParam(customerBusinessInfoVo.getRateParam());
			customerBusinessInfo.setStatus(Short.valueOf("1"));
			customerBusinessInfo.setFollowWechatAccount(null);
			customerBusinessInfo.setRefundProducureFee(customerBusinessInfoVo.getRefundProducureFee());
			customerBusinessInfo.setRefundRateMode(customerBusinessInfoVo.getRefundRateMode());
			customerBusinessInfo.setRefundRateParam(customerBusinessInfoVo.getRefundRateParam());
			customerBusinessInfo.setPayMethods(null);
			customerBusinessInfo.setPayMethodNames(null);
			customerBusinessInfo.setBusinessName(Constants.EfpsAccountService.REFUND.comment);
			customerBusinessInfo.setBusinessExamId(customerBusinessInfoVo.getBusinessExamId());
			customerBusinessInfo.setBeginTime(customerBusinessInfoVo.getBeginTime());
			customerBusinessInfo.setEndTime(customerBusinessInfoVo.getEndTime());
			customerBusinessInfo.setFeePer(null);
			customerBusinessInfo.setMaxFee(null);
			customerBusinessInfo.setMinFee(null);
			try {
				customerBusinessInfoMapper.insert(customerBusinessInfo);
			} catch (Exception e) {
				// TODO Auto-generated catch block
				throw e;
			}
		}
	}


	/**
	 * 查询客户业务信息
	 */
	@Override
	@Logable(businessTag = "pageQueryCustomerBusinessInfos")
	public PageResult<CustomerBusinessInfo> pageQueryCustomerBusinessInfos(int pageNum, int pageSize,
			Map map) {
		Long customerInfoId  = (Long)map.get("customerInfoId");
		Long businessId  =(Long)map.get("businessId");
		int total = customerBusinessInfoMapper.countCustomerBusinessInfosById(map);
		int beginRowNo = (pageNum - 1) * pageSize + 1;
		int endRowNo = pageNum * pageSize;
		List<CustomerBusinessInfo> list = customerBusinessInfoMapper.pageQueryCustomerBusinessInfosById(beginRowNo,
				endRowNo, customerInfoId,businessId);
		if (list != null && !list.isEmpty()) {
			list.forEach(customerBusinessInfo -> {
				//对旧数据进行处理  20180917
				if(customerBusinessInfo.getRateMode()==1 && customerBusinessInfo.getFeePer() ==null) //1按单笔，2按比例，
				{
					String[] rates =customerBusinessInfo.getRateParam()!=null?customerBusinessInfo.getRateParam().split("_"):null ;
					if(rates !=null && rates[0]!=null) {
						customerBusinessInfo.setFeePer(new Long(rates[0]));
						customerBusinessInfo.setRateParam(null);
					}
					else {
						customerBusinessInfo.setFeePer(null);
					}
				}
				if(customerBusinessInfo.getRateMode()==2)  //按比例
				{
					String[] rates =customerBusinessInfo.getRateParam()!=null?customerBusinessInfo.getRateParam().split("_"):null ;
					if (rates != null && rates[0] != null) {
						customerBusinessInfo.setRateParam(rates[0]);
					} else {
						customerBusinessInfo.setRateParam(null);
					}
				}
				// 迭代所有的业务实例
				Business business = businessMapper.selectByCode(customerBusinessInfo.getBusinessCode());
				if (business != null) {
					customerBusinessInfo.setBusinessName(business.getName());
					// 查询该业务对应的支付方式
					List<String> payMethodCodes = bizPayMethodMapper
							.selectPayMethods(customerBusinessInfo.getBusinessCode());
					List<String> payMethodNames = null;
					if (payMethodCodes != null && !payMethodCodes.isEmpty()) {
						payMethodNames = payMethodMapper.selectByPayMethodCodes(payMethodCodes);
					}
					if (payMethodNames != null && !payMethodNames.isEmpty()) {
						StringBuffer sb = new StringBuffer();
						for (String payMethodName : payMethodNames) {
							sb.append(payMethodName + ",");
						}
						sb.deleteCharAt(sb.length() - 1);
						customerBusinessInfo.setPayMethodNames(sb.toString());
					}
				}
				// 查询业务实例对应的业务参数实例
				List<BusinessParamInst> businessParamInsts = businessParamInstMapper
						.selectByBusinessInstDBId(customerBusinessInfo.getBusinessId());
				customerBusinessInfo.setBusinessParamInsts(businessParamInsts);
			});
		}

		PageResult<CustomerBusinessInfo> pagingResult = new PageResult<>();
		pagingResult.setTotal(total);
		pagingResult.setRows(list);
		return pagingResult;
	}

	@Override
	@Logable(businessTag = "querySupportSettCycleByBusinessCode")
	public List<SettCycleRuleDTO> querySupportSettCycleByBusinessCode(String businessCode) {
		List<SettCycleRuleDTO> settCycleDTOList = new ArrayList<>();
		BusinessSettCycleRule businessSettCycleRule = businessSettCycleRuleMapper.querySupportSettCycleByBusinessCode(businessCode);
		if(businessSettCycleRule==null)
		{
			return settCycleDTOList;
		}
		String settCycles = businessSettCycleRule.getSettCycleRuleCode();

		if (StringUtils.isBlank(settCycles)) {
            return settCycleDTOList;
        }
        String[] settCycleArray = settCycles.split(",");
		for (String code : settCycleArray) {
			SettCycleRuleDTO dto = new SettCycleRuleDTO();
			dto.setCode(code);
			dto.setName(MchConstants.SETT_CYCLE_CODE_MAP.get(code));
			settCycleDTOList.add(dto);
		}
		return settCycleDTOList;
	}
}