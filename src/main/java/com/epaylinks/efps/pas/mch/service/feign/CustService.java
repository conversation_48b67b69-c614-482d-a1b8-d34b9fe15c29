package com.epaylinks.efps.pas.mch.service.feign;

import java.util.Set;

import com.epaylinks.efps.pas.mch.model.AccountNotifyBean;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.*;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.business.cust.request.MemberQueryRequest;
import com.epaylinks.efps.common.business.cust.request.MemberRequest;
import com.epaylinks.efps.common.business.cust.response.MemberApplyResponse;
import com.epaylinks.efps.common.business.cust.response.MemberModifyResponse;
import com.epaylinks.efps.common.business.cust.response.MemberQueryResponse;
import com.epaylinks.efps.common.config.DefaultFeignConfiguration;

@FeignClient( value = "CUST" , configuration = DefaultFeignConfiguration.class)
public interface CustService {


    /**
     * 判断商户是否从cust进件
     * @return
     */
    @GetMapping("/customer/isCustomerCreateFromCust")
    public CommonOuterResponse<Boolean> isCustomerCreateFromCust(@RequestParam("customerCode") String customerCode);


    /**
     * 会员进件
     * @param request
     * @return
     */
	@RequestMapping(value = "/member/apply", method = RequestMethod.POST)
    public MemberApplyResponse applyMember( @RequestBody MemberRequest request );

	/**
	 * 会员审核结果查询接口
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/member/queryAuditInfo", method = RequestMethod.POST)
    public MemberQueryResponse queryAuditInfo(@RequestBody MemberQueryRequest request ) ;

	/**
	 * 会员信息修改
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/member/modify", method = RequestMethod.POST)
    public MemberModifyResponse modify(@RequestBody MemberRequest request,  @RequestHeader("x-customer-code") String parentCustomerCode);

    /**
     * 查询商户开通业务
     * @param request
     * @return
     */
	@RequestMapping(value = "/business/queryCustomerBusinessCodes", method = RequestMethod.GET)
    public Set<String> queryCustomerBusinessCodes(@RequestParam("customerCode") String customerCode);

	/**
	 * 获取客户调账异步地址
	 * @param customerCode
	 * @return
	 */
	@RequestMapping(value = "/merchant/queryAdjustAccountNotifyUrl", method = RequestMethod.GET)
	CommonOuterResponse<AccountNotifyBean> queryAdjustAccountNotifyUrl(@RequestParam("customerCode") String customerCode);
	
	
    /**
     * 
     * @param paramType 参数类型
     * @param paramName 参数名称
     * @return
     */
    @RequestMapping(value = "/param/query", method = RequestMethod.POST)
    public String queryStaticParam(
            @RequestParam(value = "paramType", required = true) String paramType,
            @RequestParam(value = "paramName", required = false) String paramName
            );

	@RequestMapping(value = "/sms/sendManage", method = RequestMethod.POST)
	CommonOuterResponse sendManageData(@RequestParam(value = "mobileNo") String mobileNo,
									   @RequestParam(value = "remarks") String remarks,
									   @RequestParam(value = "shortUrl") String shortUrl);
}
