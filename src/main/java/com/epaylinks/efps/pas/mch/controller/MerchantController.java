package com.epaylinks.efps.pas.mch.controller;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.business.CommonResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.util.BCrypt;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.common.PasConstants;
import com.epaylinks.efps.pas.mch.common.MchConstants;
import com.epaylinks.efps.pas.mch.common.MchError;
import com.epaylinks.efps.pas.mch.controller.dto.CustomerBusinessInstDTO;
import com.epaylinks.efps.pas.mch.controller.dto.SettCycleRuleDTO;
import com.epaylinks.efps.pas.mch.controller.dto.SourceChannelDTO;
import com.epaylinks.efps.pas.mch.controller.dto.SourceChannelResponse;
import com.epaylinks.efps.pas.mch.controller.response.CustomerResponse;
import com.epaylinks.efps.pas.mch.controller.response.PasSettCycleRuleInstResponse;
import com.epaylinks.efps.pas.mch.domain.*;
import com.epaylinks.efps.pas.mch.model.CustomerAuditResult;
import com.epaylinks.efps.pas.mch.model.CustomerResult;
import com.epaylinks.efps.pas.mch.service.CustomerAttachmentService;
import com.epaylinks.efps.pas.mch.service.CustomerBusinessInfoService;
import com.epaylinks.efps.pas.mch.service.CustomerSettleInfoService;
import com.epaylinks.efps.pas.mch.service.feign.FsService;
import com.epaylinks.efps.pas.mch.service.impl.*;
import com.epaylinks.efps.pas.mch.vo.CustomerBusinessInfoVo;

import com.epaylinks.efps.pas.pas.controller.response.HolidayResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 运营门户管理系统 - 商户信息管理
 * <AUTHOR>
 */
@RestController
@Api(value = "MerchantController", description = "商户资料管理")
public class MerchantController<V extends CommonResponse> {

	@Autowired
	private CustomerServiceImpl customerServiceImpl;
	@Autowired
	private CustomerInfoServiceImpl customerInfoService;
	@Autowired
	private CustomerSettleInfoService customerSettleInfoService;
	@Autowired
	private CustomerBusinessInfoService customerBusinessInfoService;
	@Autowired
	private CustomerContactsInfoServiceImpl customerContactsInfoService;
	@Autowired
	private CustomerAuditInfoServiceImpl customerAuditInfoService;
	@Autowired
	private CustomerAttachmentService customerAttachmentService;
	@Autowired
	private FsService fsService;
	@Autowired
	private PasSettCycleRuleInstServiceImpl pasSettCycleRuleInstService;


	/**
	 * 商户信息: for ADD or UPDATE
	 *
	 * 注：获取当前录入的商户信息，如果不存在则新建一条缺省数据并返回给前端
	 */
	@GetMapping("CustomerInfo/init")
    @Logable(businessTag = "getCustomerInfoForAddOrUpdate")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "商户信息录入/修改的初始化", httpMethod = "GET")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "customerInfoId", value = "商户信息ID", required = false, dataType = "Long", paramType = "query", digit = true)
    })
	public CustomerInfo getCustomerInfoForAddOrUpdate(
			@RequestParam(required = false) Long customerInfoId,
			@RequestHeader(value = "x-userid", required = true) Long curUserId) {
		return customerServiceImpl.getCustomerInfoForAddOrUpdate(customerInfoId, curUserId);
	}

	/**
	 * 查看商户信息
	 */
	@GetMapping("CustomerInfo/detail")
    @Logable(businessTag = "viewCustomerInfo")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "查看商户信息", httpMethod = "GET")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "customerInfoId", value = "商户信息ID", required = true, dataType = "Long", paramType = "query", digit = true)
    })
	public CustomerInfo viewCustomerInfo(@RequestParam Long customerInfoId) {
		return customerInfoService.getCustomerInfoById(customerInfoId);
	}

	/**
	 * 查看商户信息(New)
	 */
	@GetMapping("CustomerInfo/detailNew")
	@Logable(businessTag = "viewCustomerInfoNew")
	@Exceptionable
	@Validatable
	@ApiOperation(value = "查看商户信息（新封装）", httpMethod = "GET")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "customerInfoId", value = "商户信息ID", required = true, dataType = "Long", paramType = "query", digit = true)
	})
	public PageResult<CustomerInfo> viewCustomerInfoNew(@RequestParam Long customerInfoId) {

		PageResult<CustomerInfo> pageResult = new PageResult<CustomerInfo>();
		try {
			return customerInfoService.getCustomerInfoByIdNew(customerInfoId);
		} catch (Exception e) {
			if(e instanceof AppException) {
				pageResult.setCode(((AppException) e).getErrorCode());
				pageResult.setMessage(((AppException) e).getErrorMsg());
			} else {
				pageResult.setCode(PasConstants.FAIL);
			}
			return pageResult;
		}
	}

	/**
	 * 修改商户基本信息
	 */
	@PutMapping(value = "/CustomerInfo")
    @Logable(businessTag = "modifyCustomerInfo")
    @Exceptionable
    @ApiOperation(value = "修改商户基本信息", httpMethod = "PUT")
	public V modifyCustomerInfo(
			@RequestBody CustomerInfo customerInfo,
			@RequestHeader(value = "x-userid", required = true) Long curUserId) {
		CommonResponse commonResponse = new CommonResponse();
		try {
			customerInfoService.modifyCustomerInfo(customerInfo, curUserId);
			commonResponse.setCode(CommonResponse.SUCCEE);
			return (V) commonResponse;
		} catch (Exception e) {
			if(e instanceof AppException) {
				commonResponse.setCode(((AppException) e).getErrorCode());
				commonResponse.setMessage(((AppException) e).getErrorMsg());
			} else {
				commonResponse.setCode(PasConstants.FAIL);
			}
			return (V) commonResponse;
		}
	}

	/**
	 * 查看商户结算信息
	 */
	@GetMapping("CustomerSettleInfo/detail")
    @Logable(businessTag = "viewCustomerSettleInfo")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "查看商户结算信息", httpMethod = "GET")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "customerInfoId", value = "商户信息ID", required = true, dataType = "Long", paramType = "query", digit = true)
    })
	public CustomerSettleInfo viewCustomerSettleInfo(@RequestParam Long customerInfoId) {
		return customerSettleInfoService.getCustomerSettleInfoByCustomerInfoId(customerInfoId);
	}


	/**
	 * 修改商户结算信息
	 * @return customerInfoId
	 */
	@PutMapping("CustomerSettleInfo")
    @Logable(businessTag = "modifyCustomerSettleInfo")
    @Exceptionable
    @ApiOperation(value = "修改商户结算信息", httpMethod = "PUT")
	public void modifyCustomerSettleInfo(@RequestBody CustomerSettleInfo settleInfo) {
		customerSettleInfoService.modifyCustomerSettleInfo(settleInfo);
	}

	/**
	 * 新增商户业务信息
	 */
	@PostMapping("CustomerBusinessInfo")
    @Logable(businessTag = "addCustomerBusinessInfo")
    @Exceptionable
    @ApiOperation(value = "新增商户业务信息", httpMethod = "POST")
	public void addCustomerBusinessInfo(@RequestBody CustomerBusinessInfoVo customerBusinessInfoVo) {
		if(customerBusinessInfoVo.getFeePer()!=null && customerBusinessInfoVo.getFeePer() <0) //0929要看看要不要上线
		{
			throw new AppException(MchError.CUM_INVALID_PARAM.code);
		}                                                                                        //0322 long to bigdecimal
		if(!StringUtils.trimToEmpty(customerBusinessInfoVo.getRateParam()).equals("")  &&  new BigDecimal(customerBusinessInfoVo.getRateParam()).doubleValue()<0)
		{
			throw new AppException(MchError.CUM_INVALID_PARAM.code);
		}
		customerBusinessInfoService.createCustomerBusinessInfo(customerBusinessInfoVo);
	}

	@PostMapping("addRefundBusiness")
	@Exceptionable
	public CommonOuterResponse addRefundBusiness(@RequestBody List<CustomerBusinessInfo> customerBusinessInfos) {
		CommonOuterResponse response = new CommonOuterResponse();
		try {
			customerBusinessInfoService.addRefundBusiness(customerBusinessInfos);
		} catch (Exception e) {
			response.setReturnCode("0001");
			response.setReturnMsg(e.getMessage());
		}
		return response;
	}

	/**
	 * 新增商户业务实例信息，替换掉上面的addCustomerBusinessInfo
	 */
	@PostMapping("CustomerBusinessInst")
    @Logable(businessTag = "addCustomerBusinessInst")
    @Exceptionable
    @ApiOperation(value = "新增商户业务实例，目前仅供运营管理门户使用", httpMethod = "POST")
	public CommonResponse addCustomerBusinessInst(@RequestBody CustomerBusinessInstDTO customerBusinessInstDTO,
			@RequestHeader("x-customer-code") String xCustomerCode,
			@RequestHeader(value = "x-user-type") String userType) {
		//目前仅支持x-user-type为运营管理用户
		throw new AppException("UNSUPPORTED NOW");
	}

	/**
	 * 查询商户业务实例，替换掉下面的pageQueryCustomerBusinessInfos
	 */
	@GetMapping("CustomerBusinessInst")
    @Logable(businessTag = "pageQueryCustomerBusinessInsts")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "查询商户业务实例信息，当前仅供运营管理门户使用", httpMethod = "GET")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "pageNum", value = "页码", required = true, dataType = "Integer", paramType = "query", digit = true),
        @ApiImplicitParam(name = "pageSize", value = "每页数量", required = true, dataType = "Integer", paramType = "query", digit = true),
        @ApiImplicitParam(name = "customerInfoId", value = "商户信息ID", required = true, dataType = "Long", paramType = "query", digit = true)
    })
	public PageResult<CustomerBusinessInfo> pageQueryCustomerBusinessInsts(
			@RequestParam Integer pageNum,
			@RequestParam Integer pageSize,
			@RequestParam Long customerInfoId,
			@RequestHeader("x-customer-code") String xCustomerCode,
			@RequestHeader(value = "x-user-type") String userType) {
		//目前仅支持x-user-type为运营管理用户
		throw new AppException("UNSUPPORTED NOW");
	}

	/**
	 * 查询商户业务实例信息
	 */
	@GetMapping("CustomerBusinessInfo")
    @Logable(businessTag = "pageQueryCustomerBusinessInfos")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "查询商户业务信息", httpMethod = "GET")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "pageNum", value = "页码", required = true, dataType = "Integer", paramType = "query", digit = true),
        @ApiImplicitParam(name = "pageSize", value = "每页数量", required = true, dataType = "Integer", paramType = "query", digit = true),
        @ApiImplicitParam(name = "customerInfoId", value = "商户信息ID", required = true, dataType = "Long", paramType = "query", digit = true)
    })
	public PageResult<CustomerBusinessInfo> pageQueryCustomerBusinessInfos(
			@RequestParam Integer pageNum,
			@RequestParam Integer pageSize,
			@RequestParam Long customerInfoId) {
		return customerBusinessInfoService.pageQueryCustomerBusinessInfos(pageNum, pageSize, customerInfoId);
	}



	/**
	 * 商户业务信息数量（用于提交审核前的校验，至少一条）
	 */
	@GetMapping("CustomerBusinessInfo/amount")
    @Logable(businessTag = "checkCustomerBusinessInfoExist")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "商户业务信息数量", httpMethod = "GET")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "customerInfoId", value = "商户信息ID", required = true, dataType = "Long", paramType = "query", digit = true)
    })
	public int countCustomerBusinessInfo(@RequestParam Long customerInfoId) {
		return customerBusinessInfoService.countCustomerBusinessInfo(customerInfoId);
	}

	/**
	 * 检查商户是否存在重复的业务信息（用于新增业务时选择业务信息下拉项的校验）
	 */
	@GetMapping("CustomerBusinessInfo/isExist")
    @Logable(businessTag = "checkCustomerBusinessInfoExist")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "检查是否存在业务信息", httpMethod = "GET")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "customerInfoId", value = "商户信息ID", required = true, dataType = "Long", paramType = "query", digit = true)
    })
	public String checkCustomerBusinessInfoExist(@RequestParam Long customerInfoId, @RequestParam String businessCode) {
		return customerBusinessInfoService.checkCustomerBusinessInfoExist(customerInfoId, businessCode);
	}

	/**
	 * 删除商户业务信息
	 */
	@DeleteMapping("CustomerBusinessInfo")
    @Logable(businessTag = "deleteCustomerBusinessInfo")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "删除商户业务信息", httpMethod = "DELETE")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "商户业务信息ID", required = true, dataType = "Long", paramType = "query", digit = true)
    })
	public CommonResponse deleteCustomerBusinessInfo(@RequestParam Long id) {
		CommonResponse commonResponse = new CommonResponse();
		boolean delete = customerBusinessInfoService.deleteCustomerBusinessInfo(id);
		if (delete) {
			//如果成功删除
			commonResponse.setCode(CommonResponse.SUCCEE);
		}else {
			commonResponse.setCode(MchError.BUSINESS_EXAM_ID_USED.code);
			commonResponse.setMessage(MchError.BUSINESS_EXAM_ID_USED.message);
		}
		return commonResponse;
	}

	/**
	 * 查看商户联系人（管理员）信息
	 */
	@GetMapping("CustomerContactsInfo/detail")
    @Logable(businessTag = "viewCustomerContactsInfo")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "查看商户联系人（管理员）信息", httpMethod = "GET")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "customerInfoId", value = "商户信息ID", required = true, dataType = "Long", paramType = "query", digit = true)
    })
	public CustomerContactsInfo viewCustomerContactsInfo(@RequestParam Long customerInfoId) {
		return customerContactsInfoService.getCustomerContactsInfoByCustomerInfoId(customerInfoId);
	}

	/**
	 * 修改商户联系人（管理员）信息
	 * @return customerInfoId
	 */
	@PutMapping("CustomerContactsInfo")
    @Logable(businessTag = "modifyCustomerContactsInfo")
    @Exceptionable
    @ApiOperation(value = "修改商户联系人信息", httpMethod = "PUT")
	public V modifyCustomerContactsInfo(@RequestBody CustomerContactsInfo contactsInfo) {
		CommonResponse commonResponse = new CommonResponse();
		try {
			customerContactsInfoService.modifyCustomerContactsInfo(contactsInfo);
			commonResponse.setCode(CommonResponse.SUCCEE);
			return (V) commonResponse;
		} catch (Exception e) {
			if(e instanceof AppException) {
				commonResponse.setCode(((AppException) e).getErrorCode());
				commonResponse.setMessage(((AppException) e).getErrorMsg());
			} else {
				commonResponse.setCode(PasConstants.FAIL);
			}
			return (V) commonResponse;
		}
	}
	/**
	 * 商户信息提交审核
	 */
	@PostMapping("CustomerInfo/auditSubmit")
    @Logable(businessTag = "submitCustomerInfoForAudit")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "商户信息提交审核", httpMethod = "POST")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "customerInfoId", value = "商户信息ID", required = true, dataType = "Long", paramType = "query", digit = true)
    })
	public void submitCustomerInfoForAudit(
			@RequestParam Long customerInfoId,
			@RequestParam(required = false) Long auditId,
			@RequestHeader(value = "x-userid", required = true) Long curUserId) {
		customerInfoService.submitCustomerInfoForAudit(customerInfoId, curUserId, auditId);
	}

	/**
	 * 查询待审核列表
	 */
	@GetMapping("CustomerAuditInfo/waitAudit")
    @Logable(businessTag = "pageQueryCustomerAuditInfosOfWaitAudit")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "查询待审核列表", httpMethod = "GET")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "pageNum", value = "页码", required = true, dataType = "Integer", paramType = "query", digit = true),
        @ApiImplicitParam(name = "pageSize", value = "每页数量", required = true, dataType = "Integer", paramType = "query", digit = true)
    })
	public PageResult<CustomerAuditResult> pageQueryCustomerAuditInfosOfWaitAudit(
			@RequestParam(name = "pageNum" , required = true) Integer pageNum,
				@RequestParam(name = "pageSize" , required = true) Integer pageSize,
			@RequestParam(name = "submitTimeBegin" , required = false) String submitTimeBeginStr ,
			@RequestParam(name = "submitTimeEnd" , required = false) String submitTimeEndStr ,
			@RequestParam(name = "customerName" , required = false) String customerName ,
			@RequestParam(name = "operationType" , required = false) String operationType ,
			@RequestParam(name = "auditStatus" , required = false) String auditStatus,
			@RequestParam(name = "sourceChannel" , required = false) String sourceChannel) {
		//待前端修改后，再增加时间格式化代码以及相关校验
		SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
		Date submitTimeBegin = null;
		Date submitTimeEnd = null;
		if (StringUtils.isNotBlank(submitTimeBeginStr)) {
			try {
				submitTimeBegin = formatter.parse(submitTimeBeginStr);
			} catch (ParseException e) {
				// TODO Auto-generated catch block
				throw new AppException(MchError.CUM_INVALID_PARAM.code);
			}
		}
		if (StringUtils.isNotBlank(submitTimeEndStr)) {
			try {
				submitTimeEnd = formatter.parse(submitTimeEndStr);
			} catch (ParseException e) {
				// TODO Auto-generated catch block
				throw new AppException(MchError.CUM_INVALID_PARAM.code);
			}
		}
		return customerAuditInfoService.pageQueryCustomerAuditInfosOfWaitAudit(pageNum, pageSize
				, submitTimeBegin , submitTimeEnd , customerName , operationType , auditStatus,sourceChannel);
	}

	/**
	 * 查看商户审核信息
	 */
	@GetMapping("CustomerAuditInfo/detail")
    @Logable(businessTag = "viewCustomerAuditInfo")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "查看商户审核信息", httpMethod = "GET")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "auditInfoId", value = "商户审核信息ID", required = true, dataType = "Long", paramType = "query", digit = true)
    })
	public CustomerAuditInfo viewCustomerAuditInfo(@RequestParam Long auditInfoId) {
		return customerAuditInfoService.viewCustomerAuditInfo(auditInfoId);
	}

	/**
	 * 商户信息审核（初审/复审）
	 */
	@PostMapping("CustomerAuditInfo/audit")
    @Logable(businessTag = "auditCustomerInfo")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "商户信息审核（初审/复审）", httpMethod = "POST")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "auditInfoId", value = "商户审核信息ID", required = true, dataType = "Long", paramType = "query", digit = true),
        @ApiImplicitParam(name = "auditOption", value = "审核意见（00：通过，01：否决）", required = true, dataType = "String", paramType = "query", valueRange = "{00,01}"),
        @ApiImplicitParam(name = "comment", value = "审核说明", required = false, dataType = "String", paramType = "query", length = 200)
    })
	public void auditCustomerInfo(
			@RequestParam Long auditInfoId,
			@RequestParam String auditOption,// 00同意, 01否决
			@RequestParam(required=false) String comment,
			@RequestHeader(value = "x-userid", required = true) Long curUserId) {
		customerAuditInfoService.auditCustomerInfo(auditInfoId, auditOption, comment, curUserId);
	}

	/**
	 * 发送某个商户进件成功邮件
	 * @param customerCode 客户编码
	 */
	@ApiOperation(value = "重发某个进件成功的商户的通知邮件", httpMethod = "POST")
	@RequestMapping(value = "/sendInletEmail" , method = RequestMethod.POST)
	@Logable(businessTag = "sendInletEmail")
	public V sendInletEmail(@RequestParam("customerCode")String customerCode) {
		CommonResponse commonResponse = new CommonResponse();
		Customer customer = customerServiceImpl.getCustomerByCustomerCode(customerCode);
		if (customer == null) {
			//客户不存在
			throw new AppException(MchError.CUM_NOT_EXIST.code);
		}
		CustomerContactsInfo customerContactsInfo = customerContactsInfoService.getCustomerContactsInfoByCustomerInfoId(customer.getNewestCustomerInfoId());
		CustomerInfo customerInfo = customerInfoService.getCustomerInfoById(customer.getNewestCustomerInfoId());
		//按照商户添加信息来重发邮件
		// 初始登录密码为手机号（密文）
		String initLoginPwd = customerAuditInfoService.getMerchantAdminPhone(customer.getNewestCustomerInfoId());
		String loginPwd = initLoginPwd;

		CustomerSettleInfo customerSettleInfo = customerSettleInfoService.getCustomerSettleInfoByCustomerInfoId(customer.getNewestCustomerInfoId());
		String bankAccountNo = customerSettleInfo.getBankAccountNo();
		String initPayPwd = bankAccountNo.substring(bankAccountNo.length() - 6, bankAccountNo.length());
		String payPwd = initPayPwd;
		if (BCrypt.checkpw(loginPwd, customer.getInitLoginPsw()) && BCrypt.checkpw(payPwd, customer.getInitPayPsw())) {
			//如果校验明文和密文对的上，那么直接发邮件
			customerAuditInfoService.emailForMerchant(MchConstants.CustomerOperateType.INFO_ADD.code,
					customerContactsInfo.getEmail(), customerCode, customerInfo.getName(), customerInfo.getMobile(),
					loginPwd, payPwd);
		}else {
			customer.setInitLoginPsw(BCrypt.hashpw(initLoginPwd, BCrypt.gensalt()));
			customer.setInitPayPsw(BCrypt.hashpw(initPayPwd, BCrypt.gensalt()));
			//发邮件之前先修改用户的支付密码和登录密码，然后更新到db中
			customerServiceImpl.updateCustomer(customer);
			customerServiceImpl.syncCustomerInfo(null, customer.getNewestCustomerInfoId(), MchConstants.CustomerOperateType.INFO_ADD.code);
			//同步好之后则发送邮件
			customerAuditInfoService.emailForMerchant(MchConstants.CustomerOperateType.INFO_ADD.code,
					customerContactsInfo.getEmail(), customerCode, customerInfo.getName(), customerInfo.getMobile(),
					loginPwd, payPwd);
		}
		commonResponse.setMessage(CommonResponse.SUCCEE);
		return (V) commonResponse;
	}

	/**
		 * 商户信息审核（初审/复审）
		 */
		@PostMapping("CustomerAuditInfo/auditForUat")
	    @Logable(businessTag = "auditCustomerInfo")
	    @Exceptionable
	    @Validatable
	    @ApiOperation(value = "商户信息审核（初审/复审）for uat", httpMethod = "POST")
	    @ApiImplicitParams({
	        @ApiImplicitParam(name = "auditInfoId", value = "商户审核信息ID", required = true, dataType = "Long", paramType = "query", digit = true),
	        @ApiImplicitParam(name = "auditOption", value = "审核意见（00：通过，01：否决）", required = true, dataType = "String", paramType = "query", valueRange = "{00,01}"),
	        @ApiImplicitParam(name = "comment", value = "审核说明", required = false, dataType = "String", paramType = "query", length = 200)
	    })
		public void auditCustomerInfoForUat(
				@RequestParam Long auditInfoId,
				@RequestParam String auditOption,// 00同意, 01否决
				@RequestParam(required=false) String comment) {
			Long curUserId  =500L;
			customerAuditInfoService.auditCustomerInfo(auditInfoId, auditOption, comment, curUserId);
		}

	/**
	 * 查询已被拒绝的商户审核信息
	 */
	@GetMapping("CustomerAuditInfo/refusal")
    @Logable(businessTag = "getCustomerAuditInfosOfRefuse")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "查询已被拒绝的商户审核信息", httpMethod = "GET")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "pageNum", value = "页码", required = true, dataType = "Integer", paramType = "query", digit = true),
        @ApiImplicitParam(name = "pageSize", value = "每页数量", required = true, dataType = "Integer", paramType = "query", digit = true),
        @ApiImplicitParam(name = "submitTimeBegin", value = "提交时间起始（示例：2017-10-20）", required = false, dataType = "String", paramType = "query"),
        @ApiImplicitParam(name = "submitTimeEnd", value = "提交时间截止（示例：2017-10-25）", required = false, dataType = "String", paramType = "query"),
        @ApiImplicitParam(name = "operationType", value = "行为类型（2：运营门户-商户资料修改，3：运营门户-商户资料新增）", required = false, dataType = "String", paramType = "query", valueRange = "{2,3}"),
        @ApiImplicitParam(name = "customerName", value = "商户名称", required = false, dataType = "String", paramType = "query", length = 100),
        @ApiImplicitParam(name = "auditStatus", value = "商户信息审核状态（01：初审未通过，03：复审未通过）", required = false, dataType = "String", paramType = "query", valueRange = "{01,03}"),
		@ApiImplicitParam(name = "sourceChannel", value = "商户来源", required = false, dataType = "String", paramType = "query",valueRange = "{1,2,3,4}")
    })
	public PageResult<CustomerAuditResult> getCustomerAuditInfosOfRefuse(
			@RequestParam Integer pageNum,
			@RequestParam Integer pageSize,
			@RequestParam(required = false) String submitTimeBegin,
			@RequestParam(required = false) String submitTimeEnd,
			@RequestParam(required = false) String operationType,
			@RequestParam(required = false) String customerName,
			@RequestParam(required = false) String auditStatus,
			@RequestParam(name = "sourceChannel" , required = false) String sourceChannel) {
		return customerAuditInfoService.getCustomerAuditInfosOfRefuse(pageNum, pageSize, submitTimeBegin, submitTimeEnd, operationType, customerName, auditStatus,sourceChannel);
	}

	/**
	 * 已审核的商户查询
	 */
	@GetMapping("Customer")
    @Logable(businessTag = "pageQueryCustomers")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "已审核的商户查询，仅供运营管理门户调用", httpMethod = "GET")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "pageNum", value = "页码", required = true, dataType = "Integer", paramType = "query", digit = true),
        @ApiImplicitParam(name = "pageSize", value = "每页数量", required = true, dataType = "Integer", paramType = "query", digit = true),
        @ApiImplicitParam(name = "beginCreateTime", value = "创建时间起始（示例：2017-10-20）", required = false, dataType = "String", paramType = "query"),
        @ApiImplicitParam(name = "endCreateTime", value = "创建时间截止（示例：2017-10-25）", required = false, dataType = "String", paramType = "query"),
        @ApiImplicitParam(name = "customerCode", value = "商户编号", required = false, dataType = "String", paramType = "query"),
        @ApiImplicitParam(name = "customerName", value = "商户名称", required = false, dataType = "String", paramType = "query", length = 100),
        @ApiImplicitParam(name = "customerState", value = "商户状态（0：初始化，1：正常，2：冻结）", required = false, dataType = "Long", paramType = "query", valueRange = "{0,1,2}"),
        @ApiImplicitParam(name = "businessType", value = "业务类型（00：线上收单商户，01：线下收单商户，02：综合收单商户）", required = false, dataType = "String", paramType = "query", valueRange = "{00,01,02}"),
        @ApiImplicitParam(name = "settleState", value = "结算状态", required = false, dataType = "Long", paramType = "query"),
        @ApiImplicitParam(name = "province", value = "归属省编码", required = false, dataType = "String", paramType = "query"),
        @ApiImplicitParam(name = "city", value = "归属市编码", required = false, dataType = "String", paramType = "query"),
		@ApiImplicitParam(name = "district", value = "归属区编码", required = false, dataType = "String", paramType = "query"),
		@ApiImplicitParam(name = "sourceChannel", value = "商户来源", required = false, dataType = "String", paramType = "query",valueRange = "{1,2,3,4}")
    })
	public PageResult<CustomerResult> pageQueryCustomers(
			@RequestParam Integer pageNum,
			@RequestParam Integer pageSize,
			@RequestParam(required = false) String beginCreateTime,
			@RequestParam(required = false) String endCreateTime,
			@RequestParam(required = false) String customerCode,
			@RequestParam(required = false) String customerName,
			@RequestParam(required = false) Long customerState,
			@RequestParam(required = false) String businessType,
			@RequestParam(required = false) Long settleState,
			@RequestParam(required = false) String province,
			@RequestParam(required = false) String city,
			@RequestParam(required = false) String district,
			@RequestParam(required = false) String businessCode,
			@RequestParam(required = false) String sourceChannel,
			@RequestHeader("x-customer-code") String xCustomerCode,
			@RequestHeader(value = "x-user-type") String userType) {
		//需支持新增的参数businessCode，并且增加x-user-type的检查

		return customerServiceImpl.pageQueryCustomerInfos(pageNum, pageSize, beginCreateTime, endCreateTime,
				customerCode, customerName, customerState, businessType, settleState, province, city, district,sourceChannel);
	}

	/**
	 * 修改商户状态
	 */
	@PutMapping("Customer/state")
    @Logable(businessTag = "modifyCustomerState")
    @Exceptionable
    @ApiOperation(value = "修改商户状态", httpMethod = "PUT")
	@ApiImplicitParams({
        @ApiImplicitParam(name = "customerId", value = "商户ID", required = true, dataType = "Long", paramType = "query", digit = true),
        @ApiImplicitParam(name = "newState", value = "商户新状态（1：正常，2：冻结）", required = true, dataType = "Long", paramType = "query", valueRange = "{1,2}"),
        @ApiImplicitParam(name = "updateComment", value = "修改说明", required = false, dataType = "String", paramType = "query", length = 200)
    })
	public void modifyCustomerState(
			@RequestParam Long customerId,
			@RequestParam Long newState,
			@RequestParam(required = false) String updateComment,
			@RequestHeader(value = "x-userid", required = true) Long curUserId) {
		customerServiceImpl.modifyCustomerState(customerId, newState, updateComment, curUserId);
	}

	/**
	 * 获取上传文件的token
	 * @param businessType
	 * @return
	 */
	@PostMapping("CustomerAttachment/uploadToken")
	@Logable(businessTag = "getUploadToken")
	@Exceptionable
	@ApiOperation(value = "获取文件上传token", httpMethod = "POST")
	@ApiImplicitParams({
    	@ApiImplicitParam(name = "attachmentId", value = "附件ID", required = false, dataType = "long", paramType = "query", digit = true),
    })
	public String getUploadToken(@RequestParam("businessType")String businessType) {
		return fsService.uploadToken(businessType, "pas", null, null);
	}

	/**
	 * 附件上传
	 */
	@PostMapping("CustomerAttachment/upload")
    @Logable(businessTag = "uploadCustomerAttachment")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "附件上传", httpMethod = "POST")
    @ApiImplicitParams({
    	@ApiImplicitParam(name = "attachmentId", value = "附件ID", required = false, dataType = "long", paramType = "query", digit = true),
    	@ApiImplicitParam(name = "customerInfoId", value = "商户信息ID", required = true, dataType = "long", paramType = "query", digit = true),
        @ApiImplicitParam(name = "attachmentCode", value = "附件类型编码（01:身份证正面, 02:身份证背面, 03:组织机构代码, 04:营业执照, 05:店铺招牌, 0601:店铺内景1, 0602:店铺内景2, 0603:店铺内景3, 0701:合作协议1, 0702:合作协议2, 08:授权委托书, 09:签约银行卡图片, 10:手持证件照, 11:入境证明, 12:银行账户信息, 13:其它证件/文件）",
        	required = true, dataType = "String", paramType = "query", valueRange = "{01,02,03,04,05,0601,0602,0603,0701,0702,08,09,10,11,12,13}") ,
        @ApiImplicitParam(name = "attachmentName", value = "附件名称", required = true, dataType = "String", paramType = "query"),
        @ApiImplicitParam(name = "attachmentUrl", value = "附件上传文件系统后对应的id", required = true, dataType = "String", paramType = "query"),
        @ApiImplicitParam(name = "attachmenSize", value = "附件的大小", required = true, dataType = "long", paramType = "query" , digit = true),
    })
	public Long uploadCustomerAttachment(
			@RequestParam(value = "attachmentId" , required = false) Long attachmentId,
			@RequestParam(value = "customerInfoId") Long customerInfoId,
			@RequestParam(value = "attachmentCode") String attachmentCode,
			@RequestParam(value = "attachmentName") String attachmentName,
			@RequestParam(value = "attachmentUrl") String attachmentUrl,
			@RequestParam(value = "attachmenSize") Long attachmenSize) {
		return customerAttachmentService.uploadAttachment(attachmentId, customerInfoId, attachmentCode,
				attachmentName, attachmentUrl, attachmenSize);
	}

	/**
	 * 附件查询
	 */
	@GetMapping("CustomerAttachment")
    @Logable(businessTag = "queryCustomerAttachment")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "附件查询", httpMethod = "GET")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "customerInfoId", value = "商户信息ID", required = true, dataType = "Long", paramType = "query", digit = true)
    })
	public List<CustomerAttachmentInfo> queryCustomerAttachment(@RequestParam("customerInfoId")Long customerInfoId) {
		return customerAttachmentService.getAttachmentByCustomerInfoId(customerInfoId);
	}

	/**
	 * 附件删除
	 */
	@DeleteMapping("CustomerAttachment")
    @Logable(businessTag = "deleteAttachmentById")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "附件删除", httpMethod = "DELETE")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "attachmentId", value = "附件ID", required = true, dataType = "Long", paramType = "query", digit = true)
    })
	public void deleteAttachmentById(Long attachmentId) {
		customerAttachmentService.deleteAttachmentById(attachmentId);
	}

	/**
	 * 根据客户编码查看商户信息
	 */
	@GetMapping("CustomerInfo/detailByCustomerCode")
    @Logable(businessTag = "viewCustomerInfoByCustomerCode")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "根据客户编码查看商户信息", httpMethod = "GET")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "customerCode", value = "客户编码", required = true, dataType = "String", paramType = "query")
    })
	public CustomerInfo viewCustomerInfoByCustomerCode(@RequestParam String customerCode) {
		return customerInfoService.getCustomerInfoByByCustomerCode(customerCode);
	}

	/**
		 * 查询商户业务实例信息
		 */
		@GetMapping("CustomerInfo/getCustomerBusinessInfoByBusinesId")
	    @Logable(businessTag = "pageQueryCustomerBusinessInfos")
	    @Exceptionable
	    @Validatable
	    @ApiOperation(value = "根据业务信息ID查询商户业务信息", httpMethod = "GET")
	    @ApiImplicitParams({
	        @ApiImplicitParam(name = "pageNum", value = "页码", required = true, dataType = "Integer", paramType = "query", digit = true),
	        @ApiImplicitParam(name = "pageSize", value = "每页数量", required = true, dataType = "Integer", paramType = "query", digit = true),
	        @ApiImplicitParam(name = "customerInfoId", value = "商户信息ID", required = true, dataType = "Long", paramType = "query", digit = true) ,
	        @ApiImplicitParam(name = "businessId", value = "业务信息ID", required = true, dataType = "Long", paramType = "query", digit = true)
	    })
		public PageResult<CustomerBusinessInfo> pageQueryCustomerBusinessInfosById(
				@RequestParam Integer pageNum,
				@RequestParam Integer pageSize,
				@RequestParam Long customerInfoId,
				@RequestParam Long businessId
				) {
			Map map  = new HashMap();
			map.put("customerInfoId",customerInfoId);
			map.put("businessId",businessId);
			return customerBusinessInfoService.pageQueryCustomerBusinessInfos(pageNum, pageSize, map);
		}

    /**
     * 修改商户业务信息
     */
    @PostMapping("CustomerInfo/modifyCustomerBusinessInfo")
    @Logable(businessTag = "modifyCustomerBusinessInfo")
    @Exceptionable
    @ApiOperation(value = "修改商户业务信息", httpMethod = "POST")
    public CommonOuterResponse modifyCustomerBusinessInfo(@RequestBody CustomerBusinessInfoVo customerBusinessInfoVo,
                                           @RequestHeader(value = "x-userid", required = true) Long curUserId) {
        CommonOuterResponse  response = new CommonOuterResponse();
        try {
            if (customerBusinessInfoVo.getFeePer() != null && customerBusinessInfoVo.getFeePer() < 0) //0929要看看要不要上线
            {
                response.setReturnCode(MchError.CUM_INVALID_PARAM.code);
                response.setReturnMsg(MchError.CUM_INVALID_PARAM.message);
                return response;
            }
            if (!StringUtils.trimToEmpty(customerBusinessInfoVo.getRateParam()).equals("") && new BigDecimal(customerBusinessInfoVo.getRateParam()).doubleValue() < 0) {
                response.setReturnCode(MchError.CUM_INVALID_PARAM.code);
                response.setReturnMsg(MchError.CUM_INVALID_PARAM.message);
                return response;
            }
            customerBusinessInfoService.modifyCustomerBusinessInfo(customerBusinessInfoVo);
        } catch (Exception e) {
            response = new HolidayResponse();
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
            return response;
        }
        return response;
    }

    /**
     * 根据业务编码查询该业务可适配的结算周期编码
     */
    @GetMapping("CustomerBusinessInfo/querySupportSettCycleByBusinessCode")
    @Logable(businessTag = "querySupportSettCycleByBusinessCode")
    @Exceptionable
    @Validatable
    public CommonOuterResponse querySupportSettCycleByBusinessCode(@RequestParam String businessCode) {
        List<SettCycleRuleDTO> data = customerBusinessInfoService.querySupportSettCycleByBusinessCode(businessCode);
        CommonOuterResponse response = new CommonOuterResponse();
        response.setData(data);
        return response;
    }

	/**
	 * 检查该商户如有业务开通了T+0结算，需要开通普通代付业务（无论是储蓄卡还是信用卡），才能审核通过。
	 */
	@GetMapping("CustomerBusinessInfo/checkCustomerT0IsOpenDf")
	@Logable(businessTag = "auditCustomerInfo")
	@Exceptionable
	@Validatable
	@ApiOperation(value = "检查该商户如有业务开通了T+0结算，需要开通普通代付业务（无论是储蓄卡还是信用卡），才能审核通过", httpMethod = "GET")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "customerInfoId", value = "商户INFOID", required = true, dataType = "Long", paramType = "query", digit = true)
	})
	public CustomerResponse checkCustomerT0IsOpenDf(
			@RequestParam Long customerInfoId,
			@RequestHeader(value = "x-userid", required = true) Long curUserId) {
		CustomerResponse response = new CustomerResponse();
		try {
			response = customerAuditInfoService.checkT0IsOpenDf(customerInfoId);
		} catch (Exception e) {
			response = new CustomerResponse();
			if (e instanceof AppException) {
				response.setReturnCode(((AppException) e).getErrorCode());
				response.setReturnMsg(((AppException) e).getErrorMsg());
			} else {
				response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
				response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
			}
			return response;
		}
		return response;
	}


	@RequestMapping(value = "/syncCustCustomer", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
	@Logable(businessTag = "MerchantController.searchCustomer")
	@Exceptionable
	@Validatable
	@ApiOperation(value = "同步客户资料", notes = "同步客户资料", httpMethod = "POST")
	public CommonOuterResponse syncCustCustomer(@RequestBody CustomerSyncInfo syncInfo) {
		CommonOuterResponse response = new CommonOuterResponse();
		try {
			customerServiceImpl.syncCustomerSyncInfo(syncInfo);
			response.setReturnCode(CommonResponse.SUCCEE);
			return response;
		} catch (Exception e) {
			if (e instanceof AppException) {
				response.setReturnCode(((AppException) e).getErrorCode());
				response.setReturnMsg(((AppException) e).getErrorMsg());
			} else {
				response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
				response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
			}
			return response;
		}
	}

	@RequestMapping(value = "/updateCustomerBySync", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
	@Logable(businessTag = "MerchantController.searchCustomer")
	@Exceptionable
	@Validatable
	@ApiOperation(value = "同步客户资料", notes = "同步客户资料", httpMethod = "POST")
	public CommonOuterResponse updateCustomerBySync(@RequestBody CustomerSyncInfo syncInfo) {
		CommonOuterResponse response = new CommonOuterResponse();
		try {
			customerServiceImpl.updateByPrimaryKey(syncInfo);
			response.setReturnCode(CommonResponse.SUCCEE);
			return response;
		} catch (Exception e) {
			if (e instanceof AppException) {
				response.setReturnCode(((AppException) e).getErrorCode());
				response.setReturnMsg(((AppException) e).getErrorMsg());
			} else {
				response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
				response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
			}
			return response;
		}
	}

	@RequestMapping(value = "/selectByBusinessInstDBId", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
	@Logable(businessTag = "MerchantController.selectByBusinessInstDBId")
	@Exceptionable
	@Validatable
	@ApiImplicitParams({
			@ApiImplicitParam(name = "businessId", value = "businessId", required = true, dataType = "Long", paramType = "query", digit = true)
	})
	public PasSettCycleRuleInstResponse selectByBusinessInstDBId(@RequestParam Long businessId	) {
		PasSettCycleRuleInstResponse response = new PasSettCycleRuleInstResponse();
		try {
			List<PasSettCycleRuleInst> list = pasSettCycleRuleInstService.selectByBusinessInstDBId(businessId);
			response.setList(list);
			response.setReturnCode(CommonResponse.SUCCEE);
			return response;
		} catch (Exception e) {
			if (e instanceof AppException) {
				response.setReturnCode(((AppException) e).getErrorCode());
				response.setReturnMsg(((AppException) e).getErrorMsg());
			} else {
				response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
				response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
			}
			return response;
		}
	}


	@RequestMapping(value = "/getSourceChannel", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
	@Logable(businessTag = "MerchantController.getSourceChannel")
	@Exceptionable
	@Validatable
	@ApiOperation(value = "商户来源", notes = "商户来源", httpMethod = "GET")
	public SourceChannelResponse getSourceChannel() {
		SourceChannelResponse response = new SourceChannelResponse();
		try {
			ArrayList<SourceChannelDTO> list = new ArrayList<SourceChannelDTO>();
			for (Constants.SourceChannel type : Constants.SourceChannel.values()) {
				SourceChannelDTO vo = new SourceChannelDTO(); //EnumOrderTypeVo
				vo.setCode(type.code);
				vo.setComment(type.comment);
				list.add(vo);
			}
			response.setList(list);
			return response;
		} catch (Exception e) {
			if (e instanceof AppException) {
				response.setReturnCode(((AppException) e).getErrorCode());
				response.setReturnMsg(((AppException) e).getErrorMsg());
			} else {
				response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
				response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
			}
			return response;
		}
	}

	@GetMapping("CustomerBusinessInfo/isRealtimeOfBusiness")
   @Logable(businessTag = "MerchantController_isRealtimeOfBusiness")
   @Exceptionable
   @Validatable
   public boolean isRealtimeOfBusiness(@RequestParam String businessCode) {
      return  customerBusinessInfoService.isRealtimeOfBusiness(businessCode);
   }

	/*public static <T extends SourceChannelDTO> T getEnum(Integer code , Class<T> enumStatus){
   //循环遍历你的枚举类中的所有code值，相等则返回给调用者
   for (T each:enumStatus.getEnumConstants()) {
     if (code.equals(each.getCode())){
       return each;
     }
   }
   return null;
 }*/

}