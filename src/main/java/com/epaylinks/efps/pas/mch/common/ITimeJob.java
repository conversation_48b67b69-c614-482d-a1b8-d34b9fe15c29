package com.epaylinks.efps.pas.mch.common;

import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.util.Constants;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;

/**
 * 描述 ：定时器接口
 * Created by 陈奕丞 on 2017/9/15.
 */
public interface ITimeJob {

    /**
     * 根据Ip， 判断是否执行定时器
     *
     * @return
     */
    @Logable(businessTag = "getLocalIp")
    default List<String> getLocalIp() {
        List<String> list = new ArrayList<String>();
        try {
            Enumeration<NetworkInterface> netInterfaces = NetworkInterface.getNetworkInterfaces();
            InetAddress ip;
            while (netInterfaces.hasMoreElements()) {
                NetworkInterface ni = netInterfaces.nextElement();
                Enumeration<InetAddress> nii = ni.getInetAddresses();
                while (nii.hasMoreElements()) {
                    ip = nii.nextElement();
                    if (ip.getHostAddress().indexOf(":") == -1) {
                        list.add(ip.getHostAddress());
                    }
                }
            }
        } catch (SocketException e) {
            throw new AppException(Constants.FAIL, e);
        }
        return list;
    }

    /**
     * 具体实现，返回数量
     *
     * @return
     */
    public Integer execute();
}
