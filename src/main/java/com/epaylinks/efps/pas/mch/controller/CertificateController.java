package com.epaylinks.efps.pas.mch.controller;

import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.pas.mch.client.model.Certificate;
import com.epaylinks.efps.pas.mch.common.MchConstants;
import com.epaylinks.efps.pas.mch.common.MchError;
import com.epaylinks.efps.pas.mch.service.CertificateService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

import java.io.IOException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 运营门户管理系统 - 商户证书管理
 * <AUTHOR>
 */
@RestController
@Api(value = "CertificateController", description = "商户证书管理")
public class CertificateController {

	@Autowired
	private CertificateService certificateService;
	
	/**
	 * 查询证书
	 */
	@GetMapping("Certificate")
    @Logable(businessTag = "pageQueryCertificates")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "查询证书", httpMethod = "GET")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "pageNum", value = "页码", required = true, dataType = "Integer", paramType = "query", digit = true),
        @ApiImplicitParam(name = "pageSize", value = "每页数量", required = true, dataType = "Integer", paramType = "query", digit = true),
        @ApiImplicitParam(name = "customerCode", value = "商户编号", required = false, dataType = "String", paramType = "query"),
        @ApiImplicitParam(name = "customerName", value = "商户名称", required = false, dataType = "String", paramType = "query")
    })
	public PageResult<Certificate> pageQueryCertificates(
			@RequestParam Integer pageNum,
			@RequestParam Integer pageSize,
			@RequestParam(required = false) String customerCode,
			@RequestParam(required = false) String customerName) {
		return certificateService.pageQueryCertificates(pageNum, pageSize, customerCode, customerName);
	}
	
	/**
	 * 查看证书
	 */
	@GetMapping("Certificate/detail")
    @Logable(businessTag = "viewCertificate")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "查询证书", httpMethod = "GET")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "signSN", value = "证书ID", required = true, dataType = "String", paramType = "query")
    })
	public Certificate viewCertificate(@RequestParam String signSN) {
		return certificateService.viewCertificate(signSN);
	}
	
	/**
	 * 删除证书
	 */
	@DeleteMapping("Certificate")
    @Logable(businessTag = "deleteCertificate")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "删除证书", httpMethod = "DELETE")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "signSN", value = "证书ID", required = true, dataType = "String", paramType = "query")
    })
	public void deleteCertificate(@RequestParam String signSN) {
		certificateService.deleteCertificate(signSN);
	}
	
	/**
	 * 导入证书
	 * 
	 * 调用示例：
		 <form action="http://localhost:8030/Certificate/import" method="POST" enctype="multipart/form-data">
		   	证书文件：<input type="file" name="uploadFile"/><br />
		    <input type="submit"/>
		</form>
	 * @throws IOException 
	 */
	@PostMapping("Certificate/import")
    @Logable(businessTag = "importCertificate")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "导入证书", httpMethod = "POST")
	public Certificate importCertificate(@RequestParam MultipartFile uploadFile){
		if (uploadFile.isEmpty()) {
			throw new AppException(MchError.UPLOAD_FILE_EMPTY.code);
		}
		return certificateService.importCertificate(uploadFile);
	}
	
	/**
	 * 新增证书
	 */
	@PostMapping("Certificate")
    @Logable(businessTag = "addCertificate")
    @Exceptionable
    @ApiOperation(value = "新增证书", httpMethod = "POST")
	public void addCertificate(@RequestBody Certificate certificate) {
		certificateService.addCertificate(certificate);
	}
}