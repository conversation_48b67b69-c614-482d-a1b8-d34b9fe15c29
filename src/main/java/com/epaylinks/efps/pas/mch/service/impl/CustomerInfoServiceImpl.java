package com.epaylinks.efps.pas.mch.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.epaylinks.efps.common.util.page.PageResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.FeignExceptionUtils;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.common.PasConstants;
import com.epaylinks.efps.pas.mch.client.CumClient;
import com.epaylinks.efps.pas.mch.common.MchConstants;
import com.epaylinks.efps.pas.mch.common.MchError;
import com.epaylinks.efps.pas.mch.dao.CustomerAuditInfoMapper;
import com.epaylinks.efps.pas.mch.dao.CustomerBusinessInfoMapper;
import com.epaylinks.efps.pas.mch.dao.CustomerInfoMapper;
import com.epaylinks.efps.pas.mch.dao.CustomerMapper;
import com.epaylinks.efps.pas.mch.domain.Customer;
import com.epaylinks.efps.pas.mch.domain.CustomerAuditInfo;
import com.epaylinks.efps.pas.mch.domain.CustomerBusinessInfo;
import com.epaylinks.efps.pas.mch.domain.CustomerInfo;
import com.epaylinks.efps.pas.mch.service.MchCommonService;
import com.github.pagehelper.StringUtil;

/**
 * 业务逻辑层：客户基本信息
 * <AUTHOR>
 */
@Service
@RefreshScope
public class CustomerInfoServiceImpl {

	@Autowired
	private CustomerInfoMapper customerInfoMapper;
	@Autowired
	private CustomerAuditInfoMapper customerAuditInfoMapper;
	@Autowired
	private CustomerMapper customerMapper;
	@Autowired
	private MchCommonService mchCommonService;
	@Autowired
	private CumClient cumClient;
	@Autowired
	private SequenceService sequenceService;
	@Autowired
	private CustomerBusinessInfoMapper customerBusinessInfoMapper;
	@Value("${INIT_LOGIN_PSW_LEN}")
	private int INIT_LOGIN_PSW_LEN;

	/**
	 * 查看客户信息
	 */

	@Logable(businessTag = "getCustomerInfoById")
	public CustomerInfo getCustomerInfoById(Long infoId) {
		return customerInfoMapper.selectByPrimaryKey(infoId);
	}

	/**
	 * 查看客户信息(New)
	 */

	@Logable(businessTag = "getCustomerInfoByIdNew")
	public PageResult<CustomerInfo> getCustomerInfoByIdNew(Long infoId) {
		PageResult<CustomerInfo> pageResult = new PageResult<CustomerInfo>();
		List<CustomerInfo> customerInfoList = new ArrayList<CustomerInfo>();
		if(null == infoId){
			pageResult.setCode(PasCode.DATA_NOT_EXIST.code);
			pageResult.setMessage(PasCode.DATA_NOT_EXIST.message);
			return pageResult;
		}
		CustomerInfo customerInfo = customerInfoMapper.selectByPrimaryKey(infoId);
		if(customerInfo == null){
			pageResult.setCode(PasCode.DATA_NOT_EXIST.code);
			pageResult.setMessage(PasCode.DATA_NOT_EXIST.message);
			return pageResult;
		}
		Long customerId = customerInfo.getCustomerId();
		Customer customer = customerMapper.selectByPrimaryKey(customerId);
		if(customer == null || StringUtils.isEmpty(customer.getCustomerCode())){
			pageResult.setCode(PasCode.DATA_NOT_EXIST.code);
			pageResult.setMessage(PasCode.DATA_NOT_EXIST.message);
			return pageResult;
		}
		customerInfo.setCustomerCode(customer.getCustomerCode());
		customerInfoList.add(customerInfo);
		pageResult.setRows(customerInfoList);
		pageResult.setTotal(customerInfoList.size());
		pageResult.setCode(PageResult.SUCCEE);
		pageResult.setMessage("查询成功");
		return pageResult;
	}

	/**
	 * 修改客户基本信息
	 */
	@Logable(businessTag = "modifyCustomerInfo")
	public void modifyCustomerInfo(CustomerInfo customerInfo, Long curUserId) {
		// 商户名称重复性检查，20180516与华明沟通后，去掉此检查
//		if (mchCommonService.checkExistOfMchName(customerInfo.getName(), customerInfo.getCustomerId(), customerInfo.getCustomerId())) {
//			throw new AppException(MchConstants.MchError.CUM_NAME_EXIST.code, MchConstants.MchError.CUM_NAME_EXIST.comment);
//		}
		if(customerInfo.getParentCustomerCode()!=null) {// 去掉父级商户编号空格
			customerInfo.setParentCustomerCode(customerInfo.getParentCustomerCode().trim());
		}
		// 营业执照号重复性检查
		if(customerInfo.getCustomerType().equals(CustomerInfo.CustomerType.XIAO_WEI.code))
		{
			if (mchCommonService.checkExistOfLeaPersonIDNo(customerInfo.getLeaPersoniDentificationNo(), customerInfo.getCustomerId(), customerInfo.getCustomerId(), customerInfo.getParentCustomerCode())) {
				throw new AppException(MchError.CUM_LEA_PERSON_ID_EXIST.code, MchError.CUM_LEA_PERSON_ID_EXIST.message);
			}
		}
		else
		{
			if (mchCommonService.checkExistOfLicenseNo(customerInfo.getBusinessLicenseNo(), customerInfo.getCustomerId(), customerInfo.getCustomerId())) {
				throw new AppException(MchError.CUM_LICENSENO_EXIST.code, MchError.CUM_LICENSENO_EXIST.message);
			}
		}
		// 如果是三证合一，需置空以下字段的值
		Integer useUscc = customerInfo.getUseUscc();
		if (useUscc == 1) {
			customerInfo.setOrgStructureCode(null);
			customerInfo.setNatTaxRegCerNo(null);
			customerInfo.setNatTaxRegExpDate(null);
			customerInfo.setLocTaxRegCerNo(null);
			customerInfo.setLocTaxRegCerExpDate(null);
		}
		//检查输入数据，可扩展20190121
//		checkCustomerInfo(customerInfo);
		// 保存
		customerInfoMapper.updateByPrimaryKey(customerInfo);
	}

	/**
	 * 客户信息提交审核
	 *
	 * 注1：每次提交审核都生成一条新审核记录；
	 * 注2：如果customer.getNewestCustomerInfoId()不存在，则表示从来未被审核通过，表示新增，否则修改
	 *
	 * @param customerInfoId 该版本商户资料ID
	 * @param curUserId 当前用户ID
	 * @param oldAuditInfoId 原来的审核信息
	 */
	@Transactional(rollbackFor = RuntimeException.class)
	@Logable(businessTag = "submitCustomerInfoForAudit")
	public String submitCustomerInfoForAudit(Long customerInfoId, Long curUserId, Long oldAuditInfoId) {
		Customer customer = customerMapper.getByCustomerInfoId(customerInfoId);
		if (customer == null) {
			throw new AppException(MchError.CUM_NOT_EXIST.code);
		}
		String customerCode = customer.getCustomerCode();

		// 生成一条新审核记录
		CustomerAuditInfo auditInfo = new CustomerAuditInfo();
		Long auditInfoId = sequenceService.nextValue("addMchAuditInfo");
		auditInfo.setAuditInfoId(auditInfoId);
		auditInfo.setCustomerInfoId(customerInfoId);
		String operationType = (customer.getNewestCustomerInfoId() == null) ? MchConstants.CustomerOperateType.INFO_ADD.code : MchConstants.CustomerOperateType.INFO_MODIFY.code;
		auditInfo.setOperationType(operationType);
		auditInfo.setAuditStatus(MchConstants.CustomerInfoAuditState.WAIT_FIRST_AUDIT.code);
		auditInfo.setCreatorId(curUserId);
		Date now = new Date();
		auditInfo.setCreateTime(now);
		auditInfo.setUpdateTime(now);
		customerAuditInfoMapper.insert(auditInfo);

		// 修改商户信息
		if (customerCode == null) {
			// 如果新增客户 需申请客户编码
			try {
				customerCode = cumClient.newCustomerCode(MchConstants.CumUserType.ENTERPRISE_CUSTOMER.code);
			} catch(Exception e) {
				String errorCode = FeignExceptionUtils.parseException(e, PasCode.FEIGN_CUMEXCEPTION.code);
				throw new AppException(errorCode , e.getCause());
			}
			if (StringUtil.isEmpty(customerCode)) {
				throw new AppException(MchError.GET_CUSTOMER_CODE_FAIL.code);
			}
			customer.setCustomerCode(customerCode);
		}
		customerMapper.updateByPrimaryKey(customer);
		List<CustomerBusinessInfo> customerBusinessInfos = customerBusinessInfoMapper
				.queryListByCustomerInfoId(customer.getNewestCustomerInfoId());
		SimpleDateFormat format=new SimpleDateFormat("yyyyMMddHHmmss");
		for (CustomerBusinessInfo customerBusinessInfo : customerBusinessInfos) {
			customerBusinessInfo.setBusinessId(customerBusinessInfo.getBusinessId());
			String businessExamId = customerBusinessInfo.getBusinessCode() + "_" + customerCode + "_"
			+ format.format(customerBusinessInfo.getBeginTime());
			customerBusinessInfo.setBusinessExamId(businessExamId);
			customerBusinessInfoMapper.updateByPrimaryKey(customerBusinessInfo);
		}

		// 删除原来的已拒绝审核记录
		if(oldAuditInfoId != null) {
			customerAuditInfoMapper.deleteByPrimaryKey(oldAuditInfoId);
		}
		return customer.getCustomerCode();
	}

	public CustomerInfo getCustomerInfoByByCustomerCode(String customerCode) {
		return customerInfoMapper.getCustomerInfoByByCustomerCode(customerCode);
	}

    @Logable(businessTag = "checkCustomerInfo")
    public boolean checkCustomerInfo(CustomerInfo customerInfo) {
        if (!customerInfo.getBusinessLicenseNo().isEmpty() && customerInfo.getBusinessLicenseNo().length() > 30) {
            throw new AppException(PasCode.BUSINESS_LINENSENO_LENGTH_ERROR.code);
        }
        return true;
    }
}