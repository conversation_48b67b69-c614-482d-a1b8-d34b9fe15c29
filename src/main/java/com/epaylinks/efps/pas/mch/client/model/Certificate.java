package com.epaylinks.efps.pas.mch.client.model;

import java.util.Date;

public class Certificate {
	/**
	 * 签名证书序列号，主键
	 */
    private String signSN;
    /**
     * 客户编码，即证书鉴权接口中的商户ID 客户编码为0表示EFPS自身
     */
    private String customerCode;
    /**
     * 证书有效期开始日期
     */
    private Date notBefore;
    /**
     * 证书有效期结束日期
     */
    private Date notAfter;
    /**
     * 签发证书的CA的X.500 DN(DN-Distinguished Name)名字，例如： CN=天威诚信数字认证中心企业证书CA, OU=企业证书, 
     * O=北京天威诚信电子商务服务有限公司, C=CN
     */
    private String issuerDn;
    /**
     * 证书持有者的X.500唯一名字，例如： CN=广州一指通信息科技有限公司, OU=电子商务部, O=天威诚信数字认证中心
     */
    private String subjectDn;
    /**
     * 公钥证书的Base64编码的X509格式字符串，即从浏览器导出的Base64编码X509格式的公钥证书文件内容。
     * 如果该字段不为空，则验签时直接使用该字段构建PublicKey，
     * 否则使用keystorePath指定的密钥管理文件构建keyStore然后由keyStore获取PublicKey
     */
    private String base64X509;
    /**
     * 密钥管理库文件路径
     */
    private String keyStorePath;
    /**
     * 密钥管理库格式：JKS JCEKS PKCS12
     */
    private String keystoreType;
    /**
     * 私钥密码，证书类型为私钥时必选，Bcrypt算法加密后的结果
     */
    private String privateKeyPwd;
    /**
     * 证书类型：0：公钥 1：私钥
     */
    private Integer type;
    /**
     * 证书状态：0：正常 1：过期 2：无效
     */
    private Integer status;
    /**
     * 证书使用状态：0：停用 1：正常使用 
     */
    private Integer useStatus;
    /**
     * 创建时间，YYYYMMDDHH24Miss
     */
    private Date createDateTime;
    /**
     * 修改时间，YYYYMMDDHH24Miss
     */
    private Date updateDateTime;
    /**
     * 备注
     */
    private String mem;

	public String getSignSN() {
		return signSN;
	}

	public void setSignSN(String signSN) {
		this.signSN = signSN;
	}

	public String getCustomerCode() {
		return customerCode;
	}

	public void setCustomerCode(String customerCode) {
		this.customerCode = customerCode;
	}

	public Date getNotBefore() {
		return notBefore;
	}

	public void setNotBefore(Date notBefore) {
		this.notBefore = notBefore;
	}

	public Date getNotAfter() {
		return notAfter;
	}

	public void setNotAfter(Date notAfter) {
		this.notAfter = notAfter;
	}

	public String getIssuerDn() {
		return issuerDn;
	}

	public void setIssuerDn(String issuerDn) {
		this.issuerDn = issuerDn;
	}

	public String getSubjectDn() {
		return subjectDn;
	}

	public void setSubjectDn(String subjectDn) {
		this.subjectDn = subjectDn;
	}

	public String getBase64X509() {
		return base64X509;
	}

	public void setBase64X509(String base64x509) {
		base64X509 = base64x509;
	}

	public String getKeyStorePath() {
		return keyStorePath;
	}

	public void setKeyStorePath(String keyStorePath) {
		this.keyStorePath = keyStorePath;
	}

	public String getKeystoreType() {
		return keystoreType;
	}

	public void setKeystoreType(String keystoreType) {
		this.keystoreType = keystoreType;
	}

	public String getPrivateKeyPwd() {
		return privateKeyPwd;
	}

	public void setPrivateKeyPwd(String privateKeyPwd) {
		this.privateKeyPwd = privateKeyPwd;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Integer getUseStatus() {
		return useStatus;
	}

	public void setUseStatus(Integer useStatus) {
		this.useStatus = useStatus;
	}

	public Date getCreateDateTime() {
		return createDateTime;
	}

	public void setCreateDateTime(Date createDateTime) {
		this.createDateTime = createDateTime;
	}

	public Date getUpdateDateTime() {
		return updateDateTime;
	}

	public void setUpdateDateTime(Date updateDateTime) {
		this.updateDateTime = updateDateTime;
	}

	public String getMem() {
		return mem;
	}

	public void setMem(String mem) {
		this.mem = mem;
	}

}