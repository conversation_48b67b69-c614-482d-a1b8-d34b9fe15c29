package com.epaylinks.efps.pas.mch.controller.dto;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 添加业务实例的入参
 * 以及查询业务实例的响应
 * 替换掉现有的CustomerBusinessInfoVo.java
 * <AUTHOR>
 *
 */
public class CustomerBusinessInstDTO {

    private Long customerInfoId;//从属的客户信息ID，添加业务实例作为入参时必填
    private String customerCode;//客户编码，作为入参时可空
    private String businessCode;//业务编码，入参时必填
    private String rateName;
    private Short rateMode;//按单笔收费1；按比例收费2
    private String rateParam;//费率参数
    private Short status;//作为入参时不用填
    private Boolean refundProcedure;//退款是是否退回手续费
    private String businessName;
    /**
     * 业务实例的业务主键（取值：业务编码_客户编码_有效期开始时间点）
     */
    private String businessInstId;//业务实例ID，入参时不需填写
    /**
     * 业务实例的有效期开始时间
     */
    private Date beginTime;//如不填，默认为当天0点
    /**
     * 业务实例的结束时间
     */
    private Date endTime;//如不填，默认为2099-01-01

    /**
     * 开通的业务对应的结算周期相关配置。
     * 其中key是数据库中结算周期规则的主键，
     * value是生成的结算周期规则实例对应的有效起止时间戳，以英文逗号分隔
     */
    public List<SettCircleRuleInstDTO> settCircleRuleInstList;
    public List<BusinessParamInstDTO> businessParamInst;
}
