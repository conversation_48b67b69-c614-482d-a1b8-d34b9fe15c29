package com.epaylinks.efps.pas.mch.thread;

import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.pas.mch.client.HssClient;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 异步处理程序
 */
@Component
public class AsyncTask {
	
	@Autowired
	private HssClient hssClient;
	
    /**
     * 发邮件异步方式
     */
    @Async("mchDefaultThreadPool")
    @Logable(businessTag = "PAS.MCH.sendEmail")
	public void sendMail(String tos, String ccs, String bccs, String subject, String msg) {
    	hssClient.sendHtmlMail(tos, ccs, bccs, subject, msg);
	}
}