package com.epaylinks.efps.pas.mch.domain;

import java.util.Date;

import com.alibaba.fastjson.JSON;
/**
 * 结算周期实例
 * <AUTHOR>
 *
 */
public class PasSettCycleRuleInst {
	/**
	 * 数据库主键
	 */
    private Long id;
    
    /**
     * 数据库主键，customerBusinessInfoId在新录入商户时其中的customerCode会为空，因此会有可能产生同样的取值
     */
    private Long businessInstDBId;
    /**
     * 所从属的企业客户业务信息记录的数据库业务主键
     */
    private String customerBusinessInfoId;
    /**
     * 结算周期规则实例的编码
     */
    private String settCycleRuleCode;
    /**
     * 结算周期有效期开始时间，精确到秒
     */
    private Date validStartTime;
    /**
     * 结算周期有效期截止时间，注意为前闭后闭区间，如果需设置为一直有效，则设置为20991231235959。
     * 相同Customer_Business_Info_Id可以有多条客户业务结算周期，但是这些记录的有效期不可有任何重叠部分
     */
    private Date validEndTime;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 创建者userId
     */
    private Long creator;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 修改时间
     */
    private Long updator;
    
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCustomerBusinessInfoId() {
		return customerBusinessInfoId;
	}

	public void setCustomerBusinessInfoId(String customerBusinessInfoId) {
		this.customerBusinessInfoId = customerBusinessInfoId;
	}

	public String getSettCycleRuleCode() {
        return settCycleRuleCode;
    }

    public void setSettCycleRuleCode(String settCycleRuleCode) {
        this.settCycleRuleCode = settCycleRuleCode;
    }

    public Date getValidStartTime() {
        return validStartTime;
    }

    public void setValidStartTime(Date validStartTime) {
        this.validStartTime = validStartTime;
    }

    public Date getValidEndTime() {
        return validEndTime;
    }

    public void setValidEndTime(Date validEndTime) {
        this.validEndTime = validEndTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getCreator() {
        return creator;
    }

    public void setCreator(Long creator) {
        this.creator = creator;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getUpdator() {
        return updator;
    }

    public void setUpdator(Long updator) {
        this.updator = updator;
    }
    
    
    public PasSettCycleRuleInst(Long id, String customerBusinessInfoId, String settCycleRuleCode, Date validStartTime,
			Date validEndTime, Date createTime, Long creator, Date updateTime, Long updator) {
		super();
		this.id = id;
		this.customerBusinessInfoId = customerBusinessInfoId;
		this.settCycleRuleCode = settCycleRuleCode;
		this.validStartTime = validStartTime;
		this.validEndTime = validEndTime;
		this.createTime = createTime;
		this.creator = creator;
		this.updateTime = updateTime;
		this.updator = updator;
	}

    
	public PasSettCycleRuleInst() {
		super();
	}

	@Override
    public String toString() {
    	// TODO Auto-generated method stub
    	return JSON.toJSONString(this);
    }

	public Long getBusinessInstDBId() {
		return businessInstDBId;
	}

	public void setBusinessInstDBId(Long businessInstDBId) {
		this.businessInstDBId = businessInstDBId;
	}

}