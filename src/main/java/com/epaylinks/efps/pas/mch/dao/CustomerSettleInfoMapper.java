package com.epaylinks.efps.pas.mch.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.epaylinks.efps.pas.mch.domain.CustomerSettleInfo;

@Mapper
public interface CustomerSettleInfoMapper {
	
    int deleteByPrimaryKey(Long settleId);

    int insert(CustomerSettleInfo record);

    int insertSelective(CustomerSettleInfo record);

    CustomerSettleInfo selectByPrimaryKey(Long settleId);

    int updateByPrimaryKeySelective(CustomerSettleInfo record);

    int updateByPrimaryKey(CustomerSettleInfo record);
    
    CustomerSettleInfo getByCustomerInfoId(Long customerInfoId);

	String selectBankAccountNoByInfoId(@Param("customerInfoId") Long customerInfoId);
}