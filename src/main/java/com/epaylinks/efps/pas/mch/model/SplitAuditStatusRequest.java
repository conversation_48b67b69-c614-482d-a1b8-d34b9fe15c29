package com.epaylinks.efps.pas.mch.model;

import com.alibaba.fastjson.JSON;

public class SplitAuditStatusRequest {

    private Long customerInfoId;

    private String nonceStr;
    
    public Long getCustomerInfoId() {
        return customerInfoId;
    }

    public void setCustomerInfoId(Long customerInfoId) {
        this.customerInfoId = customerInfoId;
    }

    public String getNonceStr() {
		return nonceStr;
	}

	public void setNonceStr(String nonceStr) {
		this.nonceStr = nonceStr;
	}
    
	@Override
	public String toString() {
		// TODO Auto-generated method stub
		return JSON.toJSONString(this);
	}
}
