package com.epaylinks.efps.pas.mch.client.model;

import com.alibaba.fastjson.JSON;

public class Base64FileUploadRequest {

	String fileBaseSixFour;//文件体内容, base64编码
	String fileName;
	String businessType;
	String uploadToken;
	
	
	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}


	public String getFileBaseSixFour() {
		return fileBaseSixFour;
	}


	public void setFileBaseSixFour(String fileBaseSixFour) {
		this.fileBaseSixFour = fileBaseSixFour;
	}


	public String getFileName() {
		return fileName;
	}


	public void setFileName(String fileName) {
		this.fileName = fileName;
	}


	public String getBusinessType() {
		return businessType;
	}


	public void setBusinessType(String businessType) {
		this.businessType = businessType;
	}


	public String getUploadToken() {
		return uploadToken;
	}


	public void setUploadToken(String uploadToken) {
		this.uploadToken = uploadToken;
	}
	
}
