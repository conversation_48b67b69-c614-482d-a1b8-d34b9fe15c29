package com.epaylinks.efps.pas.mch.domain;


public class TradeCategory {
    /**
     * 主键
     */
    private Long id;

    /**
     * 上级类目ID
     */
    private Long parentId;

    /**
     * 行业类别名称
     */
    private String name;

    /**
     * 行业类别编码
     */
    private String code;

    /**
     * 描述
     */
    private String remark;

    /**
     * MCC  默认4816
     */
    private String mcc;

    /**
     * 厦门浦发MCC
     */
    private String xmpf;

    /**
     * 深圳平安MCC
     */
    private String szpa;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getMcc() {
        return mcc;
    }

    public void setMcc(String mcc) {
        this.mcc = mcc;
    }

    public String getXmpf() {  return xmpf; }

    public void setXmpf(String xmpf) {
        this.xmpf = xmpf;
    }

    public String getSzpa() {
        return szpa;
    }

    public void setSzpa(String szpa) {
        this.szpa = szpa;
    }
}