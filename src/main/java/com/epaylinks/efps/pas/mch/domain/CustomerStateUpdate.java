package com.epaylinks.efps.pas.mch.domain;

import java.util.Date;

public class CustomerStateUpdate {
    private Long updateId;

    private Long customerId;

    private Long newState;

    private Long oldState;

    private Long operatorId;

    private Date operateTime;

    private String updateComment;

	public Long getUpdateId() {
		return updateId;
	}

	public void setUpdateId(Long updateId) {
		this.updateId = updateId;
	}

	public Long getCustomerId() {
		return customerId;
	}

	public void setCustomerId(Long customerId) {
		this.customerId = customerId;
	}

	public Long getNewState() {
		return newState;
	}

	public void setNewState(Long newState) {
		this.newState = newState;
	}

	public Long getOldState() {
		return oldState;
	}

	public void setOldState(Long oldState) {
		this.oldState = oldState;
	}

	public Long getOperatorId() {
		return operatorId;
	}

	public void setOperatorId(Long operatorId) {
		this.operatorId = operatorId;
	}

	public Date getOperateTime() {
		return operateTime;
	}

	public void setOperateTime(Date operateTime) {
		this.operateTime = operateTime;
	}

	public String getUpdateComment() {
		return updateComment;
	}

	public void setUpdateComment(String updateComment) {
		this.updateComment = updateComment;
	}
}