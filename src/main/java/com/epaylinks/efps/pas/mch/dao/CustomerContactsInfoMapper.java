package com.epaylinks.efps.pas.mch.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.epaylinks.efps.pas.mch.domain.CustomerContactsInfo;

import java.util.List;

@Mapper
public interface CustomerContactsInfoMapper {
    int deleteByPrimaryKey(Long contactId);

    int insert(CustomerContactsInfo record);

    int insertSelective(CustomerContactsInfo record);

    CustomerContactsInfo selectByPrimaryKey(Long contactId);

    List<CustomerContactsInfo> selectBySelective(CustomerContactsInfo customerContactsInfo);

    int updateByPrimaryKeySelective(CustomerContactsInfo record);

    int updateByPrimaryKey(CustomerContactsInfo record);

    CustomerContactsInfo getByCustomerInfoId(Long customerInfoId);

	CustomerContactsInfo selectByCustomerInfoId(@Param("customerInfoId") Long customerInfoId);
}