package com.epaylinks.efps.pas.mch.service.impl;

import com.epaylinks.efps.pas.mch.dao.TradeCategoryMapper;
import com.epaylinks.efps.pas.mch.domain.TradeCategory;
import com.epaylinks.efps.pas.mch.service.TradeCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> 5m
 * @Date : 2018/1/24 13:48
 * @Description :
 */
@Service
public class TradeCategoryServiceImpl implements TradeCategoryService {

    @Autowired
    TradeCategoryMapper tradeCategoryMapper;


    @Override
    public int deleteByPrimaryKey(Long id) {
        return tradeCategoryMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(TradeCategory record) {
        return tradeCategoryMapper.insert(record);
    }

    @Override
    public int insertSelective(TradeCategory record) {
        return tradeCategoryMapper.insertSelective(record);
    }

    @Override
    public TradeCategory selectByPrimaryKey(Long id) {
        return tradeCategoryMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(TradeCategory record) {
        return tradeCategoryMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(TradeCategory record) {
        return tradeCategoryMapper.updateByPrimaryKey(record);
    }

    @Override
    public List<TradeCategory> selectBySelective(TradeCategory record) {
        return tradeCategoryMapper.selectBySelective(record);
    }
}
