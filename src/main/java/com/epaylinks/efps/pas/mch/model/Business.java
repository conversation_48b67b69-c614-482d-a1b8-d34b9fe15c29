package com.epaylinks.efps.pas.mch.model;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import com.alibaba.fastjson.JSONObject;

public class Business {
	
	public enum InletBusiness{
		/*SPLITTED("SPLITTED" , "SPLITTED"),
		WX_NATIVE_PAY("WX_NATIVE_PAY" , "WX_NATIVE_PAY"),
		ALI_NATIVE_PAY("ALI_NATIVE_PAY" , "ALI_NATIVE_PAY"),
		WX_MICRO_PAY("WX_MICRO_PAY" , "WX_MICRO_PAY")
		public final String code;
		public final String message;
		private InletBusiness(String code, String message) {
			this.code = code;
			this.message = message;
		}*/
		SPLITTED , WX_NATIVE_PAY , ALI_NATIVE_PAY , WX_MICRO_PAY , 
		ALI_MICRO_PAY , ALI_JSAPI_PAY , WX_JSAPI_PAY , WX_MWEB_PAY , 
		ALI_MWEB_PAY , WX_APP_PAY , ALI_APP_PAY , MEM_INNER_TRANS , 
		MEM_EXCHANGE , MEM_REXCHANGE , D0_WITHDRAW , D0_QUICK_PAY,
		FZ_NOCARD_PAY,FZ_NOCARD_PAY_CREDIT,FZ_NOCARD_WITHDRAW,WITHDRAW_CREDIT_CARD;
//		DZ_WITHDRAW_SAVING_CARD,DZ_WITHDRAW_CREDIT_CARD;
	}
	
	
	private static final Map<String, Set<String>> map = new HashMap<>();
	
	static {
		Set<String> bfz = new HashSet<>();
		bfz.add("BFZ");
		map.put("SPLITTED", bfz);
		
		Set<String> wxNative = new HashSet<>();
		wxNative.add("WxNatvie");
		map.put("WX_NATIVE_PAY", wxNative);
		
		Set<String> aliNative = new HashSet<>();
		aliNative.add("AliNative");
		map.put("ALI_NATIVE_PAY", aliNative);
		
		Set<String> wxMicro = new HashSet<>();
		wxMicro.add("WxMicro");
		map.put("WX_MICRO_PAY", wxMicro);
		
		Set<String> aliMicro = new HashSet<>();
		aliMicro.add("AliMicro");
		map.put("ALI_MICRO_PAY", aliMicro);
		
		Set<String> aliJSAPI = new HashSet<>();
		aliJSAPI.add("AliJSAPI");
		map.put("ALI_JSAPI_PAY", aliJSAPI);
		
		Set<String> wxJSAPI = new HashSet<>();
		wxJSAPI.add("WxJSAPI");
		map.put("WX_JSAPI_PAY", wxJSAPI);
		
		Set<String> wxMWEB = new HashSet<>();
		wxMWEB.add("WxMWEB");
		map.put("WX_MWEB_PAY", wxMWEB);
		
		Set<String> withdraw = new HashSet<>();
		withdraw.add("Withdraw");
		map.put("WITHDRAW", withdraw);
		
		Set<String> memberInsidePay = new HashSet<>();
		memberInsidePay.add("MemberInsidePay");
		map.put("MEM_INNER_TRANS", memberInsidePay);
		
		Set<String> memberExchange = new HashSet<>();
		memberExchange.add("MemberExchangeIn");
		memberExchange.add("MemberExchangeOut");
		map.put("MEM_EXCHANGE", memberExchange);
		
		Set<String> memberOppositeExchange = new HashSet<>();
		memberOppositeExchange.add("MemberOppositeExchangeIn");
		memberOppositeExchange.add("MemberOppositeExchangeOut");
		map.put("MEM_REXCHANGE", memberOppositeExchange);
		
		Set<String> memberInsidePayCent = new HashSet<>();
		memberInsidePayCent.add("MemberInsidePayCent");
		map.put("MEMBER_INSIDE_PAY_CENT", memberInsidePayCent);
		
		Set<String> d0WithDraw = new HashSet<>();
		d0WithDraw.add("D0-Withdraw-Auto");
		map.put("D0_WITHDRAW", d0WithDraw);
		
		Set<String> d0QuickPay = new HashSet<>();
		d0QuickPay.add("D0-QuickPay");
		map.put("D0_QUICK_PAY", d0QuickPay);

		Set<String> NocardFzWithdraw = new HashSet<>();
		NocardFzWithdraw.add("FZ-NocardWithdraw");
		map.put("FZ_NOCARD_WITHDRAW", NocardFzWithdraw);

		Set<String> NocardPayCredit = new HashSet<>();
		NocardPayCredit.add("FZ-NocardPayCredit");
		map.put("FZ_NOCARD_PAY_CREDIT", NocardPayCredit);

		Set<String> NocardPay = new HashSet<>();
		NocardPay.add("FZ-NocardPay");
		map.put("FZ_NOCARD_PAY", NocardPay);

		Set<String> withdrawCreditCard = new HashSet<>();
		withdrawCreditCard.add("Withdraw-CreditCard");
		map.put("WITHDRAW_CREDIT_CARD", withdrawCreditCard);

		/*Set<String> DZ_WITHDRAW_SAVING_CARD = new HashSet<>();
		withdrawCreditCard.add("DZ-WITHDRAW-SAVING_CARD");
		map.put("DZ_WITHDRAW_SAVING_CARD", DZ_WITHDRAW_SAVING_CARD);

		Set<String> DZ_WITHDRAW_CREDIT_CARD = new HashSet<>();
		withdrawCreditCard.add("DZ-WITHDRAW-CREDIT_CARD");
		map.put("DZ_WITHDRAW_CREDIT_CARD", DZ_WITHDRAW_CREDIT_CARD);*/
	}
	
	private String code;
	//yyyyMMdd
	private String startDate;
	//yyyyMMdd
	private String endDate;

	private String ratioMode;
	
	private String ratioParam;

	/**
	 * 业务类别：efpsAccountService: efps账务服务, efpsPayService: efps支付服务
	 */
	private String businessCategory;
	
	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getStartDate() {
		return startDate;
	}

	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}

	public String getEndDate() {
		return endDate;
	}

	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}

	public String getRatioMode() {
		return ratioMode;
	}

	public void setRatioMode(String ratioMode) {
		this.ratioMode = ratioMode;
	}

	public String getRatioParam() {
		return ratioParam;
	}

	public void setRatioParam(String ratioParam) {
		this.ratioParam = ratioParam;
	}


	public static Map<String, Set<String>> getMap() {
		return map;
	}

	public Set<String> getBusinessCode() {
		return map.get(code);
	}
	
	@Override
	public String toString() {
		// TODO Auto-generated method stub
		return JSONObject.toJSONString(this);
	}

	public String getBusinessCategory() {
		return businessCategory;
	}

	public void setBusinessCategory(String businessCategory) {
		this.businessCategory = businessCategory;
	}

}
