package com.epaylinks.efps.pas.mch.vo;

import java.util.Date;

import com.alibaba.fastjson.JSON;

/**
 * 运营管理门户界面添加业务实例时传入的结算周期规则实例
 * <AUTHOR>
 *
 */
public class SettCycleRuleInstVo {

    /**
     */
    private String settCycleRuleCode;

    /**
     */
    private Date validStartTime;

    /**
     */
    private Date validEndTime;

	public String getSettCycleRuleCode() {
		return settCycleRuleCode;
	}

	public void setSettCycleRuleCode(String settCycleRuleCode) {
		this.settCycleRuleCode = settCycleRuleCode;
	}

	public Date getValidStartTime() {
		return validStartTime;
	}

	public void setValidStartTime(Date validStartTime) {
		this.validStartTime = validStartTime;
	}

	public Date getValidEndTime() {
		return validEndTime;
	}

	public void setValidEndTime(Date validEndTime) {
		this.validEndTime = validEndTime;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
    
    
}
