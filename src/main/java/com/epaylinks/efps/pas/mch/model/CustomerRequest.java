package com.epaylinks.efps.pas.mch.model;

import java.util.List;

import com.alibaba.fastjson.JSON;

/**
 * Created by adm on 2018/1/23.
 */
public class CustomerRequest {
	
	public static final String DATE_FORMATTER = "yyyy-MM-dd";
	public static final String BUSINESS_DATE_FORMATTER="yyyyMMdd";
	public static final String LONG_TIME_DATE_STR="2099-12-31";
	
	public static final Double LATEST_VERSION = 2.0; // 最新版本
	
	

    private String customerCode; // 2.0版本后，该参数为主商户商户编号；之前则为会员商户编号。
    
    private String memberId; // 分账子商户编号 2.0版本后使用
    
    private String version;
    //商户名称
    private String name;

    //    商户简称
    private String shortName;
    //    商户手机号
    private String mobile;
    //    商户电话号码    可选
    private String telephone;
    /* 商户类别：
         10：个体商户
         20：企业（集团）商户
         30：渠道商户
         40：内部商户
         50：小微商户*/
    private String type;
    //    归属省市区   编号由EFPS提供
    private String areaCode;
    /*  String 1：是  是否三证合一
          0：
          否*/
    private String useUSCC;
    //    文件 营业执照图片
    private String businessLinenseImage;

    private String businessAddress;
    
    
    private String settMode;

    //    营业执照号
    private String businessLicenseNo;

    private String businessLicenesExpiredDate;
    //    法人或经营者姓名
    private String lealPersonName;
    //    法人或经营者证件类型          1：身份证
    private String lealPersonIdentificationType;
    //    法人或经营者证件号码
    private String lealPersonIdentificationNo;
    //    法人或经营者证件有效期截止日期
    private String lealPersonIdentificationExpiredDate;
    //    图片文件 法人身份证正面图片
    private String lealPersonIdentificationImage1;
    //    图片文件 法人身份证反面图片
    private String lealPersonIdentificationImage2;//文档是1,看要不要改？
    //    结算周期    单位为天
    private String settCircle;
    /*  结算账户类型
          1：对公
          2：对私*/
    private String bankAccountType;
    //    结算开户行名称
    private String bankName;
    /*  银行开户名称
          /持卡人姓名*/
    private String customerNameInBank;
    //    银行账号 /卡号
    private String accountNo;
    //    联行号由EFPS提供联行号表
    private String bankLineNumber;
    /* 结算目标 1：结算到银行卡
         2：
         结算到易票联账户
                 目前只能填1*/
    private String settTarget;
    //    异步通知审核结果使用的URL
    private String notifyURL;
//    随机字符串
    private String nonceStr  ;

    private String registeredAddress;
    
    private String bankReservedMobile;
    
    /**
     * 商户开通的业务列表
     */
    private List<Business> businessList;
    
    private List<Contact> contactList;
    
    /**
     * 签约模式，0-商户签约，1-平台签约
     */
    private String signModel;
    
    
    public String getSignModel() {
		return signModel;
	}

	public void setSignModel(String signModel) {
		this.signModel = signModel;
	}

    public String getRegisteredAddress() {
        return registeredAddress;
    }

    public void setRegisteredAddress(String registeredAddress) {
        this.registeredAddress = registeredAddress;
    }

    public String getBusinessAddress() {
        return businessAddress;
    }

    public void setBusinessAddress(String businessAddress) {
        this.businessAddress = businessAddress;
    }

    public String getNonceStr() {
        return nonceStr;
    }

    public void setNonceStr(String nonceStr) {
        this.nonceStr = nonceStr;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getMemberId() {
		return memberId;
	}

	public void setMemberId(String memberId) {
		this.memberId = memberId;
	}

	public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getShortName() {
        return shortName;
    }

    public void setShortName(String shortName) {
        this.shortName = shortName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getUseUSCC() {
        return useUSCC;
    }

    public void setUseUSCC(String useUSCC) {
        this.useUSCC = useUSCC;
    }

    public String getBusinessLinenseImage() {
        return businessLinenseImage;
    }

    public void setBusinessLinenseImage(String businessLinenseImage) {
        this.businessLinenseImage = businessLinenseImage;
    }

    public String getBusinessLicenseNo() {
        return businessLicenseNo;
    }

    public void setBusinessLicenseNo(String businessLicenseNo) {
        this.businessLicenseNo = businessLicenseNo;
    }

    public String getBusinessLicenesExpiredDate() {
        return businessLicenesExpiredDate;
    }

    public void setBusinessLicenesExpiredDate(String businessLicenesExpiredDate) {
        this.businessLicenesExpiredDate = businessLicenesExpiredDate;
    }

    public String getLealPersonName() {
        return lealPersonName;
    }

    public void setLealPersonName(String lealPersonName) {
        this.lealPersonName = lealPersonName;
    }

    public String getLealPersonIdentificationType() {
        return lealPersonIdentificationType;
    }

    public void setLealPersonIdentificationType(String lealPersonIdentificationType) {
        this.lealPersonIdentificationType = lealPersonIdentificationType;
    }

    public String getLealPersonIdentificationNo() {
        return lealPersonIdentificationNo;
    }

    public void setLealPersonIdentificationNo(String lealPersonIdentificationNo) {
        this.lealPersonIdentificationNo = lealPersonIdentificationNo;
    }

    public String getLealPersonIdentificationExpiredDate() {
        return lealPersonIdentificationExpiredDate;
    }

    public void setLealPersonIdentificationExpiredDate(String lealPersonIdentificationExpiredDate) {
        this.lealPersonIdentificationExpiredDate = lealPersonIdentificationExpiredDate;
    }

    public String getLealPersonIdentificationImage1() {
        return lealPersonIdentificationImage1;
    }

    public void setLealPersonIdentificationImage1(String lealPersonIdentificationImage1) {
        this.lealPersonIdentificationImage1 = lealPersonIdentificationImage1;
    }

    public String getLealPersonIdentificationImage2() {
        return lealPersonIdentificationImage2;
    }

    public void setLealPersonIdentificationImage2(String lealPersonIdentificationImage2) {
        this.lealPersonIdentificationImage2 = lealPersonIdentificationImage2;
    }

    public String getSettCircle() {
        return settCircle;
    }

    public void setSettCircle(String settCircle) {
        this.settCircle = settCircle;
    }

    public String getBankAccountType() {
        return bankAccountType;
    }

    public void setBankAccountType(String bankAccountType) {
        this.bankAccountType = bankAccountType;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getCustomerNameInBank() {
        return customerNameInBank;
    }

    public void setCustomerNameInBank(String customerNameInBank) {
        this.customerNameInBank = customerNameInBank;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public String getBankLineNumber() {
        return bankLineNumber;
    }

    public void setBankLineNumber(String bankLineNumber) {
        this.bankLineNumber = bankLineNumber;
    }

    public String getSettTarget() {
        return settTarget;
    }

    public void setSettTarget(String settTarget) {
        this.settTarget = settTarget;
    }

    public String getNotifyURL() {
        return notifyURL;
    }

    public void setNotifyURL(String notifyURL) {
        this.notifyURL = notifyURL;
    }

	public List<Business> getBusinessList() {
		return businessList;
	}

	public void setBusinessList(List<Business> businessList) {
		this.businessList = businessList;
	}
	public List<Contact> getContactList() {
		return contactList;
	}

	public void setContactList(List<Contact> contactList) {
		this.contactList = contactList;
	}
    
	@Override
	public String toString() {
		// TODO Auto-generated method stub
		return JSON.toJSONString(this);
	}

	public String getSettMode() {
		return settMode;
	}

	public void setSettMode(String settMode) {
		this.settMode = settMode;
	}
	
	
	public String getBankReservedMobile() {
		return bankReservedMobile;
	}

	public void setBankReservedMobile(String bankReservedMobile) {
		this.bankReservedMobile = bankReservedMobile;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}


    public boolean isLatestVersion() {
    	if(version == null ) {
    		return false;
    	}
    	try {
    		if(version != null) {
	    		Double v = Double.parseDouble(version);
	    		if(v >= CustomerRequest.LATEST_VERSION  ) {
	    			return true;
	    		}
    		}
    	}catch(Exception e) {
    		
    	}
    	return false;
    }


	public static enum action{
		ADD("Add" , "新增"),
		MODIFY("Modify" , "修改");
		public final String code;
		public final String message;
		private action(String code, String message) {
			this.code = code;
			this.message = message;
		}
		public String getCode() {
			return code;
		}
		public String getMessage() {
			return message;
		}
	}

}

