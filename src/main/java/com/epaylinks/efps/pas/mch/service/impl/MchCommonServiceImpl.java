package com.epaylinks.efps.pas.mch.service.impl;

import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.pas.mch.dao.CustomerAuditInfoMapper;
import com.epaylinks.efps.pas.mch.dao.CustomerMapper;
import com.epaylinks.efps.pas.mch.service.MchCommonService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 业务逻辑层：商户公共逻辑
 * <AUTHOR>
 */
@Service
public class MchCommonServiceImpl implements MchCommonService {

	@Autowired
	private CustomerMapper customerMapper;
	@Autowired
	private CustomerAuditInfoMapper customerAuditInfoMapper;
	
	@Override
	@Logable(businessTag = "checkExistOfMchName")
	public boolean checkExistOfMchName(String mchName, Long customerId, Long customerInfoId) {
		boolean isExist = false;
		// 在已审核商户中检查商户名称是否已存在
		isExist = customerMapper.checkExistOfMchNameInAuditeds(customerId, mchName);
		if (!isExist) {
			// 在待审核商户中检查商户名称是否已存在
			isExist = customerAuditInfoMapper.checkExistOfMchNameInWaitAuditeds(customerInfoId, mchName);
		}
		return isExist;
	}

	@Override
	@Logable(businessTag = "checkExistOfLicenseNo")
	public boolean checkExistOfLicenseNo(String businessLicenseNo, Long customerId, Long customerInfoId) {
		boolean isExist = false;
		// 在已审核商户中检查营业执照号是否已存在
		isExist = customerMapper.checkExistOfLicenseNoInAuditeds(customerId, businessLicenseNo);
		if (!isExist) {
			// 在待审核商户中检查商户名称是否已存在
			isExist = customerAuditInfoMapper.checkExistOfLicenseNoInWaitAuditeds(customerInfoId, businessLicenseNo);
		}
		return isExist;
	}
	
	@Override
	@Logable(businessTag = "checkExistOfLeaPersonIDNo")
	public boolean checkExistOfLeaPersonIDNo(String leaPersoniDentificationNo, Long customerId, Long customerInfoId, String parentCustomerCode) {
		boolean isExist = false;
		// 在已审核商户中检查是否已存在
		isExist = customerMapper.checkExistOfLeaPersonIDNoInAuditeds(customerId, leaPersoniDentificationNo, parentCustomerCode);
		if (!isExist) {
			// 在待审核商户中检查是否已存在
			isExist = customerAuditInfoMapper.checkExistOfLeaPersonIDNoInWaitAuditeds(customerInfoId, leaPersoniDentificationNo, parentCustomerCode);
		}
		return isExist;
	}
}