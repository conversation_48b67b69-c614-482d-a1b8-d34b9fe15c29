package com.epaylinks.efps.pas.mch.job;

import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.pas.mch.common.ITimeJob;
import com.epaylinks.efps.pas.mch.common.MchConstants;
import com.epaylinks.efps.pas.mch.dao.CustomerStateUpdateMapper;
import com.epaylinks.efps.pas.mch.dao.CustomerSyncInfoMapper;
import com.epaylinks.efps.pas.mch.domain.CustomerStateUpdate;
import com.epaylinks.efps.pas.mch.domain.CustomerSyncInfo;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.epaylinks.efps.pas.mch.service.impl.CustomerServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@RefreshScope
public class CustomerSyncJob implements ITimeJob {
	
	@Value("${localIp}")
	String localIp;

	@Autowired
	private CustomerSyncInfoMapper customerSyncInfoMapper;
	@Autowired
	private CustomerStateUpdateMapper customerStateUpdateMapper;
	@Autowired
	private CustomerServiceImpl customerServiceImpl;
	
	@Autowired
	public CustomerSyncJob self;
	
    /**
     * 定时同步到客户子系统
     * 
     * 异常处理：如任何一条记录调用客户管理子系统接口失败，需重试三次（通过ribbon.MaxAutoRetries设置），
     * 			三次都失败则本轮处理中忽略掉该记录所属客户的所有通知记
     * 			录，继续处理其他客户的记录
     */
    @Scheduled(fixedDelay = 60000)// 每60秒
    @Logable(businessTag = "CustomerSyncJob.execute")
    public Integer execute() {
        if (!self.getLocalIp().contains(localIp)) {
            return -1;
        }
        
    	// 读取所有“待同步状态更改记录”（按创建时间从小到大排序）
    	List<CustomerSyncInfo> customerSyncInfoList = customerSyncInfoMapper.queryAllSyncInfo();
    	Set<String> customerCodeSet = new HashSet<>();
    	for (CustomerSyncInfo syncInfo : customerSyncInfoList) {
    		if(!customerCodeSet.contains(syncInfo.getCustomerCode())) {
    			String result = this.syncCustomer(syncInfo);
    			if (Constants.SUCCESS.equals(result)) {
    				// 同步成功：删除记录
	    			customerSyncInfoMapper.deleteByPrimaryKey(syncInfo.getSyncId());
        		} else {
        			// 同步失败：标记并忽略掉本轮该记录所属客户的所有其它通知
        			customerCodeSet.add(syncInfo.getCustomerCode());
        		}
    		}
    	}
    	return -1;
    }
    
    @Logable(businessTag = "syncCustomer")
	private String syncCustomer(CustomerSyncInfo syncInfo) {
		String operateType = syncInfo.getSyncType();
		Long recordId = syncInfo.getRecordId();
		String result = null;
		/**
		 * OperateType：新增/修改客户信息
		 */
		if (MchConstants.CustomerOperateType.INFO_ADD.code.equals(operateType) || MchConstants.CustomerOperateType.INFO_MODIFY.code.equals(operateType)) {
			result = customerServiceImpl.syncCustomerInfo(syncInfo, recordId, operateType);
		}
		/**
		 * OperateType：修改客户状态
		 */
		else if (MchConstants.CustomerOperateType.STATE_UPDATE.code.equals(operateType)) {
			CustomerStateUpdate stateUpdateRecord = customerStateUpdateMapper.selectByPrimaryKey(recordId);
			result = customerServiceImpl.syncCustomerState(syncInfo, stateUpdateRecord, syncInfo.getCustomerCode());
		}
		return result;
	}
}
