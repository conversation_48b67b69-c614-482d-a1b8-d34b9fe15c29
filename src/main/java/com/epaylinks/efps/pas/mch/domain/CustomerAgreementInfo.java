package com.epaylinks.efps.pas.mch.domain;

import java.util.Date;

public class CustomerAgreementInfo {
    private Long agreementId;

    private Long customerId;

    private String customerCode;

    private Long customerSource;

    private String agreementCode;

    private Date agreementExpireDate;

    private String agreementRemarks;

	public void copyDataFromCustomer(Customer customer) {
		this.customerCode = customer.getCustomerCode();
		this.customerSource = 2L;//?
		this.agreementCode = "协议编号";//?
		this.agreementExpireDate = new Date();
//		this.agreementRemarks = agreementRemarks;//不必填
	}

    public Long getAgreementId() {
        return agreementId;
    }

    public void setAgreementId(Long agreementId) {
        this.agreementId = agreementId;
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public Long getCustomerSource() {
        return customerSource;
    }

    public void setCustomerSource(Long customerSource) {
        this.customerSource = customerSource;
    }

    public String getAgreementCode() {
        return agreementCode;
    }

    public void setAgreementCode(String agreementCode) {
        this.agreementCode = agreementCode;
    }

    public Date getAgreementExpireDate() {
        return agreementExpireDate;
    }

    public void setAgreementExpireDate(Date agreementExpireDate) {
        this.agreementExpireDate = agreementExpireDate;
    }

    public String getAgreementRemarks() {
        return agreementRemarks;
    }

    public void setAgreementRemarks(String agreementRemarks) {
        this.agreementRemarks = agreementRemarks;
    }
}