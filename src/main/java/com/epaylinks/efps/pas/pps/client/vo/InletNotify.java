package com.epaylinks.efps.pas.pps.client.vo;

import com.alibaba.fastjson.JSON;

public class InletNotify {
	private String customerInfoId;
	/**
	 * Add表示新增，Modify表示修改
	 */
	private String type;
	
	private String name;
	
	private String shortName;
	
	private Long auditResult;
	
	private String auditMsg;
	
	private String auditDateTime;
	/**
	 * auditResult为0时必填，type为Modify时必填
	 */
	private String customerCode;
	
	private String nonceStr;

	public String getCustomerInfoId() {
		return customerInfoId;
	}

	public void setCustomerInfoId(String customerInfoId) {
		this.customerInfoId = customerInfoId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getShortName() {
		return shortName;
	}

	public void setShortName(String shortName) {
		this.shortName = shortName;
	}

	public Long getAuditResult() {
		return auditResult;
	}

	public void setAuditResult(Long auditResult) {
		this.auditResult = auditResult;
	}

	public String getAuditMsg() {
		return auditMsg;
	}

	public void setAuditMsg(String auditMsg) {
		this.auditMsg = auditMsg;
	}

	public String getAuditDateTime() {
		return auditDateTime;
	}

	public void setAuditDateTime(String auditDateTime) {
		this.auditDateTime = auditDateTime;
	}

	public String getCustomerCode() {
		return customerCode;
	}

	public void setCustomerCode(String customerCode) {
		this.customerCode = customerCode;
	}

	public String getNonceStr() {
		return nonceStr;
	}

	public void setNonceStr(String nonceStr) {
		this.nonceStr = nonceStr;
	}
	
	
	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	@Override
	public String toString() {
		// TODO Auto-generated method stub
		return JSON.toJSONString(this);
	}
	
}
