package com.epaylinks.efps.pas.pps.dao;



import java.util.List;

import com.epaylinks.efps.pas.pps.model.RefundApply;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;



@Mapper
public interface RefundApplyMapper {
    int insert(RefundApply record);
    int insertSelective(RefundApply record);
    /**
     * 根据原订单编号以及商家管理员以及易票联管理员的审核状态来查询退款记录
     * @param orgiTransactionNo 原交易订单号
     * @param auditState 商家管理员审核状态
     * @param refundState 易票联管理员审核状态
     * @return
     */
  /*  Long selectRefundAmountByOrgiTransactionNoAndAdmitWithOperatorAuditState(@Param("orgiTransactionNo") String orgiTransactionNo
			, @Param("auditState") Integer auditState, @Param("refundState") Integer refundState);
    */
    /**
     * 根据原订单号来查询已退款的金额
     * @param orgiTransactionNo
     * @return
     */
   /* Long selectRefundAmountByOrgiTransactionNo(@Param("orgiTransactionNo") String orgiTransactionNo
			, @Param("auditState") Integer auditState, @Param("OperatorAuditStates") List<Integer> OperatorAuditStates);*/
    
    /**
     * 根据退款申请号来更新该退款单的审核人用户id，审核时间，审核意见以及审核状态
     * @param refundApply 封装的商户审核参数
     */
	int updateAdmitAuditUserIdAndDateTimeAndRemarkAndStateByRefundApplyNo(RefundApply refundApply);
	/**
	 * 根据退款申请号以及客户编码来查询退款记录
	 * @param refundApplyNo 退款申请号
	 * @param customerCode 客户编码
	 * @return
	 */
	RefundApply selectByRefundApplyNoAndCustomerCode(@Param("refundApplyNo") String refundApplyNo,
													 @Param("customerCode") String customerCode);

	/**
     * 根据原订单号来查询商户已退款的金额
     * @param orgiTransactionNo
     * @return
     */
    Long selectRefundAmountByCustomerOrgiTransactionNo(@Param("orgiTransactionNo") String orgiTransactionNo, @Param("customerCode") String customerCode);


	//退款记录查询
		List<RefundApply> pageQueryRefundApply(@Param("beginRowNo") Integer beginRowNo, @Param("endRowNo") Integer endRowNo, @Param("beginCreateTime") String beginCreateTime, @Param("endCreateTime") String endCreateTime,
											   @Param("customerCode") String customerCodeFromHeader,  @Param("refundTransactionNo") String refundTransactionNo,
											   @Param("orgiOutTradeNo")String orgiOutTradeNo, @Param("orgiTransactionNo")String orgiTransactionNo,
											    @Param("auditState") String audiState, @Param("refundState") String refundState, @Param("refundApplyNo") String refundApplyNo) ;

		Integer countPageQueryRefundApply(@Param("beginRowNo") Integer beginRowNo,
										  @Param("endRowNo") Integer endRowNo,
										  @Param("beginCreateTime") String beginCreateTime,
										  @Param("endCreateTime") String endCreateTime,
										  @Param("customerCode") String customerCodeFromHeader,
										  @Param("refundTransactionNo") String refundTransactionNo,
										  @Param("orgiOutTradeNo")String orgiOutTradeNo,
										  @Param("orgiTransactionNo")String orgiTransactionNo,
										   @Param("auditState") Long audiState,
										  @Param("refundState") Long refundState,
										  @Param("refundApplyNo") String refundApplyNo) ;
		;
	int updateByRefundApplyNo(RefundApply refundApply);


}