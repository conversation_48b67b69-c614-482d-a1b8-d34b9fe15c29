package com.epaylinks.efps.pas.pps.client.vo;

import java.io.Serializable;
/**
 * 退款请求数据
 * <AUTHOR>
 *
 */
public class RefundRequest implements Serializable {
	
	private String outTradeNo;
	
	private String outRefundNo;
	
	private String customerCode;
	
	private String terminalNo;
	
	private String refundDesc;
	
	private Long totalFee;
	
	private Long refundFee;
	
	private String refundCurrency;
	
	private String channelType;
	
	private String mchUserId;
	
	private String appId;
	
	private String notifyUrl;
	
	private String redirectUrl;

	public String getOutTradeNo() {
		return outTradeNo;
	}

	public void setOutTradeNo(String outTradeNo) {
		this.outTradeNo = outTradeNo;
	}

	public String getOutRefundNo() {
		return outRefundNo;
	}

	public void setOutRefundNo(String outRefundNo) {
		this.outRefundNo = outRefundNo;
	}

	public String getCustomerCode() {
		return customerCode;
	}

	public void setCustomerCode(String customerCode) {
		this.customerCode = customerCode;
	}

	public String getTerminalNo() {
		return terminalNo;
	}

	public void setTerminalNo(String terminalNo) {
		this.terminalNo = terminalNo;
	}

	public String getRefundDesc() {
		return refundDesc;
	}

	public void setRefundDesc(String refundDesc) {
		this.refundDesc = refundDesc;
	}

	public Long getTotalFee() {
		return totalFee;
	}

	public void setTotalFee(Long totalFee) {
		this.totalFee = totalFee;
	}

	public Long getRefundFee() {
		return refundFee;
	}

	public void setRefundFee(Long refundFee) {
		this.refundFee = refundFee;
	}

	public String getRefundCurrency() {
		return refundCurrency;
	}

	public void setRefundCurrency(String refundCurrency) {
		this.refundCurrency = refundCurrency;
	}

	public String getChannelType() {
		return channelType;
	}

	public void setChannelType(String channelType) {
		this.channelType = channelType;
	}

	public String getMchUserId() {
		return mchUserId;
	}

	public void setMchUserId(String mchUserId) {
		this.mchUserId = mchUserId;
	}

	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	public String getNotifyUrl() {
		return notifyUrl;
	}

	public void setNotifyUrl(String notifyUrl) {
		this.notifyUrl = notifyUrl;
	}

	public String getRedirectUrl() {
		return redirectUrl;
	}

	public void setRedirectUrl(String redirectUrl) {
		this.redirectUrl = redirectUrl;
	}
	
	@Override
    public String toString() {
        return "RefundRequest{" +
                "outTradeNo='" + outTradeNo + '\'' +
                ", outRefundNo='" + outRefundNo + '\'' +
                ", customerCode='" + customerCode + '\'' +
                ", terminalNo='" + terminalNo + '\'' +
                ", refundDesc='" + refundDesc + '\'' +
                ", totalFee='" + totalFee + '\'' +
                ", refundFee='" + refundFee + '\'' +
                ", refundCurrency='" + refundCurrency + '\'' +
                ", channelType='" + channelType + '\'' +
                ", mchUserId='" + mchUserId + '\'' +
                ", appId='" + appId + '\'' +
                ", notifyUrl='" + notifyUrl + '\'' +
                ", redirectUrl='" + redirectUrl + '\'' +
                '}';
    }
}
