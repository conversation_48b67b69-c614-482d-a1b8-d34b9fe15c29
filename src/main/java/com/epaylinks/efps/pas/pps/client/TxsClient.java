package com.epaylinks.efps.pas.pps.client;

import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.pas.pps.client.vo.RefundRequest;
import com.epaylinks.efps.pas.pps.client.vo.RefundResponse;
import com.epaylinks.efps.pas.txs.domain.TxsPayTradeOrder;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient("txs")
public interface TxsClient {

    //退款接口
    @RequestMapping(value = "/pay/Refund", method = RequestMethod.POST)
    //商户订单号,商户退款单号,客户编码,终端号     订单金额
    RefundResponse refundRes(@RequestParam("outTradeNo") String outTradeNo,
                             @RequestParam("outRefundNo") String outRefundNo,
                             @RequestParam("transactionNo") String transactionNo,
                             @RequestParam("customerCode") String customerCode,
                             @RequestParam("terminalNo") String terminalNo,
                             @RequestParam(value = "refundDesc", required = false) String refundDesc,
                             @RequestParam("totalFee") String totalFee,
                             @RequestParam("refundFee") String refundFee,
                             @RequestParam("refundCurrency") String refundCurrency,
                             @RequestParam("channelType") String channelType,
                             @RequestParam(value = "mchUserId", required = false) String mchUserId,
                             @RequestParam(value = "appId", required = false) String appId,
                             @RequestParam("notifyUrl") String notifyUrl,
                             @RequestParam("redirectUrl") String redirectUrl);


    //异步通知接口
    @RequestMapping(value="/asynNotify", method = RequestMethod.POST)
     public String asynNotify(@RequestParam("url") String url, @RequestParam("orderId") String orderId, @RequestParam("orderType") String orderType, @RequestParam("content") String content) ;


}