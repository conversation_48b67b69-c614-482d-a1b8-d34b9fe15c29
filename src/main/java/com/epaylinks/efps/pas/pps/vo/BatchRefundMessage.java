package com.epaylinks.efps.pas.pps.vo;
/**
 * 批量退款消息
 * <AUTHOR>
 *
 */

import com.epaylinks.efps.pas.pps.model.RefundApply;

import java.util.List;

public class BatchRefundMessage {
	/**
	 * 客户编码
	 */
	private String customerCode;
	/**
	 * 退款申请列表
	 */
	private List<RefundApplyMessage> refundApplyList;
	public String getCustomerCode() {
		return customerCode;
	}
	public void setCustomerCode(String customerCode) {
		this.customerCode = customerCode;
	}
	public List<RefundApplyMessage> getRefundApplyList() {
		return refundApplyList;
	}
	public void setRefundApplyList(List<RefundApplyMessage> refundApplyList) {
		this.refundApplyList = refundApplyList;
	}
}
