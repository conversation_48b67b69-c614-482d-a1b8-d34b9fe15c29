package com.epaylinks.efps.pas.pps.controller;


import com.alibaba.fastjson.JSONObject;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.pas.pps.service.impl.RefundServiceImpl;
import com.epaylinks.efps.pas.pps.vo.RefundNotifyRequest;
import com.epaylinks.efps.pas.txs.domain.TxsPayTradeOrder;
import com.epaylinks.efps.pas.txs.service.TransactionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import org.springframework.web.bind.annotation.*;


import com.epaylinks.efps.pas.pps.model.RefundApply;
import com.epaylinks.efps.pas.pps.vo.RefundApplyVo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

import javax.servlet.http.HttpServletResponse;

/**
 * 退款控制器
 *
 * <AUTHOR>
 */
@Controller
@Api(value = "RefundApplyController", description = "退款控制器")
public class RefundController {
    @Autowired
    private RefundServiceImpl refundServiceImpl;

    /*@Autowired
    private TxsClient txsClient; */
    @Autowired
    private TransactionService txsClient;
    @Autowired
    private TransactionService transactionService;

    @RequestMapping(value = "/refund/approve", method = RequestMethod.POST)
    @ResponseBody
    @Logable(businessTag = "refundApprove")
    @Exceptionable
    @ApiImplicitParams({
    })
    @ApiOperation(value = "退款审核", notes = "退款审核", httpMethod = "POST")
    public RefundApplyVo refundApprove(
            @RequestBody RefundApply refundApply,
            @RequestHeader(value = "x-customer-code", required = false) String customerCodeFromHeader,
            @RequestAttribute(value = "x-userid", required = false) Long userId
    ) {

        return refundServiceImpl.refundBatchApprove(refundApply, userId, customerCodeFromHeader);
    }

    @RequestMapping(value = "/refund/refund", method = RequestMethod.POST)
    @ResponseBody
    @Logable(businessTag = "refund")
    @Exceptionable
    @ApiImplicitParams({
            @ApiImplicitParam(name = "customerCode", value = "商户编号", required = true, dataType = "String", paramType = "query")
    })
    @ApiOperation(value = "退款", notes = "退款", httpMethod = "POST")
    public RefundApplyVo refund(
            @RequestBody RefundApply refundApply,
            @RequestParam(required = true) String customerCode,
            @RequestAttribute(value = "x-userid", required = true) Long userId
    ) {
        return refundServiceImpl.refund(refundApply, userId, customerCode);
    }

    @GetMapping("refundQuery")
    @ResponseBody
    @Logable(businessTag = "RefundApplyController.pageQueryRefundApply")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "退款查询", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页码", required = true, dataType = "Integer", paramType = "query", digit = true),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", required = true, dataType = "Integer", paramType = "query", digit = true),
            @ApiImplicitParam(name = "beginCreateTime", value = "创建时间起始（示例：2017-10-20）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endCreateTime", value = "创建时间截止（示例：2017-10-25）", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "refundApplyNo", value = "退款申请号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "refundTransactionNo", value = "退款交易单号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgiOutTradeNo", value = "原商户订单号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgiTransactionNo", value = "原付款交易订单号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "customerCode", value = "商户编号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "customerName", value = "商户名称", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "refundState", value = "退款状态( 0：未执行 1：退款失败 2：退款成功 3：退款中)", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "auditState", value = "退款审核状态( 0：待商户管理员审核 1：商户管理员审核通过待运营管理审核 2：商户管理员审核拒绝 3：运营管理审核通过 3：运营管理审核拒绝)", required = false, dataType = "String", paramType = "query")
    })
    public PageResult<RefundApply> pageQueryRefundApply(
            @RequestParam Integer pageNum,
            @RequestParam Integer pageSize,
            @RequestParam(required = false) String beginCreateTime,
            @RequestParam(required = false) String endCreateTime,
            @RequestAttribute(value = "x-userid", required = false) Long userId,
            @RequestParam(required = false) String refundApplyNo,
            @RequestParam(required = false) String refundTransactionNo,
            @RequestParam(required = false) String orgiOutTradeNo,
            @RequestParam(required = false) String orgiTransactionNo,
            @RequestParam(required = false) String customerCode,
            @RequestParam(required = false) String customerName,
            @RequestParam(required = false) String refundState,
            @RequestParam(required = false) String auditState) {
        return refundServiceImpl.pageQueryRefundApply(pageNum, pageSize, beginCreateTime, endCreateTime,
                customerCode, customerName, userId, refundTransactionNo, orgiOutTradeNo, orgiTransactionNo, auditState, refundState, refundApplyNo);
    }

    //这里最后看看哪里用到1226
    @GetMapping("getTotalRefundAmount")
    @ResponseBody
    @Logable(businessTag = "pageQueryRefundApply")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "查询已退款金额", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "customerCode", value = "客户编号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgiOutTradeNo", value = "原商户订单号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgiTransactionNo", value = "原付款交易订单号", required = false, dataType = "String", paramType = "query"),
    })
    public Long getTotalRefundAmount(
//            @RequestHeader(value = "x-customer-code", required = false) String customerCode,
            @RequestParam(required = true) String customerCode,
            @RequestAttribute(value = "x-userid", required = false) Long userId,
            @RequestParam(required = false) String orgiOutTradeNo,
            @RequestParam(required = false) String orgiTransactionNo
    ) {
        return refundServiceImpl.selectRefundAmountByCustomerOrgiTransactionNo(customerCode, orgiOutTradeNo, orgiTransactionNo);
    }


    //1225
    @RequestMapping(value = "/RefundNotify", method = RequestMethod.POST)
    @ResponseBody
    @Logable(businessTag = "RefundNotify.Notify")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "异步接收退款通知", httpMethod = "POST")
    public void Notify(@RequestBody RefundNotifyRequest req, HttpServletResponse response ) {
        //SUCCESS("00", "成功"),  FAIL("01", "失败"),PENDING("02", "未知"),  NOT_PROCESS("03", "未处理"),  IN_PROCESS("04", "处理中"),    OVERTIME_CLOSE("05", "超时关闭");
        String returnCode = refundServiceImpl.refundNotify(req);
        //通知不用再发
        if ("0".equals(returnCode)) {
           String returnMsg =  getReturnMsg();
            try {
                response.getWriter().print(returnMsg);
                response.getWriter().close();
            } catch (Exception e) {
            }
        }
    }

    @Logable(businessTag = "RefundController.getReturnMsg")
    @Exceptionable
    public String getReturnMsg() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("returnCode", "0000");
        jsonObject.put("returnMsg", "");
        return jsonObject.toJSONString();
    }


    @GetMapping("testPayQuery")
    @ResponseBody
    @Logable(businessTag = "testPayQuery")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "退款查询", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgiOutTradeNo", value = "原商户订单号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orgiTransactionNo", value = "原付款交易订单号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageNum", value = "页码", required = true, dataType = "Integer", paramType = "query", digit = true),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", required = true, dataType = "Integer", paramType = "query", digit = true)
    })
    public PageResult<TxsPayTradeOrder> testPayQuery(
    		@RequestHeader(value = "x-user-type", required = false) String userType,
			@RequestHeader(value = "x-userid", required = false) String userId,
			@RequestHeader(value = "x-customer-code", required = false) String customerCodeHead,
            @RequestParam(required = false) String refundTransactionNo,
            @RequestParam(required = false) String orgiOutTradeNo,
            @RequestParam Integer pageNum,
            @RequestParam Integer pageSize
    ) {
        PageResult<TxsPayTradeOrder> s = txsClient.payQuery(userType,userId,customerCodeHead,null, null, null, null, null, null, pageNum, pageSize, "ZF", null, null, null, null,null,null);
        return s;
    }

    @RequestMapping(value = "/refund/refundApproveForUat", method = RequestMethod.POST)
        @ResponseBody
        @Logable(businessTag = "refundApprove")
        @Exceptionable
        @ApiImplicitParams({
        })
        @ApiOperation(value = "退款审核FOR UAT 不用userId", notes = "退款审核FOR UAT 不用userId", httpMethod = "POST")
        public RefundApplyVo refundApproveForUat(
                @RequestBody RefundApply refundApply

        ) {
            return refundServiceImpl.refundApproveForUat(refundApply);
        }
}
