package com.epaylinks.efps.pas.pps.vo;

import java.util.Date;

/**
 * Created by adm on 2017/12/5.
 */

import java.util.Date;

public class RefundApplyRequest {
    //	原商户订单号
    private String transactionNo;
    private String refundAmount;
    private String applyRemark;

    public String getTransactionNo() {
        return transactionNo;
    }

    public void setTransactionNo(String transactionNo) {
        this.transactionNo = transactionNo;
    }

    public String getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(String refundAmount) {
        this.refundAmount = refundAmount;
    }

    public String getApplyRemark() {
        return applyRemark;
    }

    public void setApplyRemark(String applyRemark) {
        this.applyRemark = applyRemark;
    }

    //退款开始时间
    private String beginCreateTime;
    //退款结束时间（申请）
    private String endCreateTime;
    private Long userId;
    private String customerCode;
    //	退款交易单号
    private String refundTransactionNo;
    //	原付款交易订单号
    private String orgiTransactionNo;
    //	退款申请号
    private String refundApplyNo;
    //	退款执行状态   ,0：未执行  1：退款失败  2：退款成功   3：退款中
    private Long refundState;


    public String getBeginCreateTime() {
        return beginCreateTime;
    }

    public void setBeginCreateTime(String beginCreateTime) {
        this.beginCreateTime = beginCreateTime;
    }

    public String getEndCreateTime() {
        return endCreateTime;
    }

    public void setEndCreateTime(String endCreateTime) {
        this.endCreateTime = endCreateTime;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getRefundTransactionNo() {
        return refundTransactionNo;
    }

    public void setRefundTransactionNo(String refundTransactionNo) {
        this.refundTransactionNo = refundTransactionNo;
    }

    public String getOrgiTransactionNo() {
        return orgiTransactionNo;
    }

    public void setOrgiTransactionNo(String orgiTransactionNo) {
        this.orgiTransactionNo = orgiTransactionNo;
    }

    public String getRefundApplyNo() {
        return refundApplyNo;
    }

    public void setRefundApplyNo(String refundApplyNo) {
        this.refundApplyNo = refundApplyNo;
    }

    public Long getRefundState() {
        return refundState;
    }

    public void setRefundState(Long refundState) {
        this.refundState = refundState;
    }
}