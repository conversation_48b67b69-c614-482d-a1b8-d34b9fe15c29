package com.epaylinks.efps.pas.pps.model;

import java.util.Date;

public class RefundApply {
	/**
	 * 主键
	 */
    private Long id;
    /**
     * 客户编码
     */
    private String customerCode;

    private String customerName;

	public String getCustomerName() {
		return customerName;
	}

	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	private String orgiOutTradeNo;
    private Date refundEndTime;

	public Date getRefundEndTime() {
		return refundEndTime;
	}

	public void setRefundEndTime(Date refundEndTime) {
		this.refundEndTime = refundEndTime;
	}

	public String getOrgiOutTradeNo() {
		return orgiOutTradeNo;
	}

	public void setOrgiOutTradeNo(String orgiOutTradeNo) {
		this.orgiOutTradeNo = orgiOutTradeNo;
	}

	/**
     * 原付款交易订单号
     */
    private String orgiTransactionNo;
    /**
     * 退款交易单号
     */
    private String refundTransactionNo;
    /**
     * 退款申请号
     */
    private String refundApplyNo;
    /**
     * 退款申请人用户ID
     */
    private Long refundApplyUserId;
    /**
     * 退款申请时间
     */
    private Date applyDateTime;
    /**
     * 退款申请说明
     */
    private String applyRemark;
    /**
     * 申请退款金额
     */
    private Long refundAmount;
    /**
     * 商户管理员审核人的UserId
     */
    private Long adminAuditUserId;
    /**
     * 商户管理员审核时间
     */
    private Date adminAuditDateTime;
    /**
     * 商户管理员审核意见
     */
    private String adminAuditRemark;
    /**
     * 审核状态：
     0：待商户管理员审核
     1：商户管理员审核通过待运营管理审核
     2：商户管理员审核拒绝
     3：运营管理审核通过
     4：运营管理审核拒绝
     */
    private Integer auditState;

	private Long operatorAuditUserId;
    /**
     * 易票联运营管理员审核时间
     */
    private Date operatorAuditDateTime;
    /**
     * 易票联运营管理员审核意见
     */
    private String operatorAuditRemark;
    /**
     * 退款执行状态
     0：未执行
     1：退款失败
     2：退款成功
     3：退款中
     */
    private Integer refundState;
	private Long refundMethod     ;
	private Long  refundProcedureFee    ;
	private Long refundFee ;
	private Long oriAmount   ;
	private Long oriProcedureFee ;
	private String oriPayMethod   ;
	private String oriPayState     ;


	private String sourceChannel;

	public String getSourceChannel() {
		return sourceChannel;
	}

	public void setSourceChannel(String sourceChannel) {
		this.sourceChannel = sourceChannel;
	}



	public Long getRefundMethod() {
		return refundMethod;
	}

	public void setRefundMethod(Long refundMethod) {
		this.refundMethod = refundMethod;
	}

	public Long getRefundProcedureFee() {
		return refundProcedureFee;
	}

	public void setRefundProcedureFee(Long refundProcedureFee) {
		this.refundProcedureFee = refundProcedureFee;
	}

	public Long getRefundFee() {
		return refundFee;
	}

	public void setRefundFee(Long refundFee) {
		this.refundFee = refundFee;
	}

	public Long getOriAmount() {
		return oriAmount;
	}

	public void setOriAmount(Long oriAmount) {
		this.oriAmount = oriAmount;
	}

	public Long getOriProcedureFee() {
		return oriProcedureFee;
	}

	public void setOriProcedureFee(Long oriProcedureFee) {
		this.oriProcedureFee = oriProcedureFee;
	}

	public String getOriPayMethod() {
		return oriPayMethod;
	}

	public void setOriPayMethod(String oriPayMethod) {
		this.oriPayMethod = oriPayMethod;
	}

	public String getOriPayState() {
		return oriPayState;
	}

	public void setOriPayState(String oriPayState) {
		this.oriPayState = oriPayState;
	}

	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getCustomerCode() {
		return customerCode;
	}
	public void setCustomerCode(String customerCode) {
		this.customerCode = customerCode;
	}
	public String getOrgiTransactionNo() {
		return orgiTransactionNo;
	}
	public void setOrgiTransactionNo(String orgiTransactionNo) {
		this.orgiTransactionNo = orgiTransactionNo;
	}
	public String getRefundTransactionNo() {
		return refundTransactionNo;
	}
	public void setRefundTransactionNo(String refundTransactionNo) {
		this.refundTransactionNo = refundTransactionNo;
	}
	public String getRefundApplyNo() {
		return refundApplyNo;
	}
	public void setRefundApplyNo(String refundApplyNo) {
		this.refundApplyNo = refundApplyNo;
	}
	public Long getRefundApplyUserId() {
		return refundApplyUserId;
	}
	public void setRefundApplyUserId(Long refundApplyUserId) {
		this.refundApplyUserId = refundApplyUserId;
	}
	public Date getApplyDateTime() {
		return applyDateTime;
	}
	public void setApplyDateTime(Date applyDateTime) {
		this.applyDateTime = applyDateTime;
	}
	public String getApplyRemark() {
		return applyRemark;
	}
	public void setApplyRemark(String applyRemark) {
		this.applyRemark = applyRemark;
	}
	public Long getRefundAmount() {
		return refundAmount;
	}
	public void setRefundAmount(Long refundAmount) {
		this.refundAmount = refundAmount;
	}
	public Long getAdminAuditUserId() {
		return adminAuditUserId;
	}
	public void setAdminAuditUserId(Long adminAuditUserId) {
		this.adminAuditUserId = adminAuditUserId;
	}
	public Date getAdminAuditDateTime() {
		return adminAuditDateTime;
	}
	public void setAdminAuditDateTime(Date adminAuditDateTime) {
		this.adminAuditDateTime = adminAuditDateTime;
	}
	public String getAdminAuditRemark() {
		return adminAuditRemark;
	}
	public void setAdminAuditRemark(String adminAuditRemark) {
		this.adminAuditRemark = adminAuditRemark;
	}

	public Integer getAuditState() {
		return auditState;
	}

	public void setAuditState(Integer auditState) {
		this.auditState = auditState;
	}

	public Date getOperatorAuditDateTime() {
		return operatorAuditDateTime;
	}
	public void setOperatorAuditDateTime(Date operatorAuditDateTime) {
		this.operatorAuditDateTime = operatorAuditDateTime;
	}
	public String getOperatorAuditRemark() {
		return operatorAuditRemark;
	}
	public void setOperatorAuditRemark(String operatorAuditRemark) {
		this.operatorAuditRemark = operatorAuditRemark;
	}

	public Integer getRefundState() {
		return refundState;
	}

	public void setRefundState(Integer refundState) {
		this.refundState = refundState;
	}

	@Override
	public String toString() {
		return "RefundApply{" +
				"id=" + id +
				", customerCode='" + customerCode + '\'' +
				", orgiOutTradeNo='" + orgiOutTradeNo + '\'' +
				", refundEndTime=" + refundEndTime +
				", orgiTransactionNo='" + orgiTransactionNo + '\'' +
				", refundTransactionNo='" + refundTransactionNo + '\'' +
				", refundApplyNo='" + refundApplyNo + '\'' +
				", refundApplyUserId=" + refundApplyUserId +
				", applyDateTime=" + applyDateTime +
				", applyRemark='" + applyRemark + '\'' +
				", refundAmount=" + refundAmount +
				", adminAuditUserId=" + adminAuditUserId +
				", adminAuditDateTime=" + adminAuditDateTime +
				", adminAuditRemark='" + adminAuditRemark + '\'' +
				", auditState=" + auditState +
				", operatorAuditDateTime=" + operatorAuditDateTime +
				", operatorAuditRemark='" + operatorAuditRemark + '\'' +
				", refundState=" + refundState +
				", refundMethod=" + refundMethod +
				", refundProcedureFee=" + refundProcedureFee +
				", refundFee=" + refundFee +
				", oriAmount=" + oriAmount +
				", oriProcedureFee=" + oriProcedureFee +
				", oriPayMethod='" + oriPayMethod + '\'' +
				", oriPayState='" + oriPayState + '\'' +
				'}';
	}

	public Long getOperatorAuditUserId() {
		return operatorAuditUserId;
	}

	public void setOperatorAuditUserId(Long operatorAuditUserId) {
		this.operatorAuditUserId = operatorAuditUserId;
	}
}