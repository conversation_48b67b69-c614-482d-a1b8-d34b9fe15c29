package com.epaylinks.efps.pas.pps.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.pas.cum.domain.CumCustomerInfo;
import com.epaylinks.efps.pas.cum.service.impl.CumCumCustomerInfoServiceImpl;
import com.epaylinks.efps.pas.pps.client.TxsClient;
import com.epaylinks.efps.pas.pps.client.vo.RefundResponse;
import com.epaylinks.efps.pas.pps.dao.RefundApplyMapper;
import com.epaylinks.efps.pas.pps.model.RefundApply;
import com.epaylinks.efps.pas.pps.util.PasConstants;
import com.epaylinks.efps.pas.pps.vo.BatchRefundMessage;
import com.epaylinks.efps.pas.pps.vo.RefundApplyMessage;
import com.epaylinks.efps.pas.pps.vo.RefundApplyVo;
import com.epaylinks.efps.pas.pps.vo.RefundNotifyRequest;
import org.apache.commons.lang.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.concurrent.ListenableFuture;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

@Service
public class RefundServiceImpl {
    @Autowired
    private TxsClient txsClient;

    @Autowired
    RefundServiceImpl self;

    @Autowired
    private CumCumCustomerInfoServiceImpl cumCumCustomerInfoServiceImpl;
    @Autowired
    private RefundApplyMapper refundApplyMapper;
    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;
    private static final long LEGALDAY = 15552000000L;
    @Autowired
    private SequenceService sequenceService;

    private static final String CATEGORY = "ppsRefundApply";

    private static final String TOPIC = "PPS_Apply";//"012RefundNotice" modifytoo PPS_Apply0315
    private static final String TOPIC_OPERATOR_APPROVE = "PAS_ApplyResult";

    private static final String FIRST_APPROVE_KEY = "FirstAuditNotice";
    private static final String OPERATOR_APPROVE_KEY = "OperatorAuditStateNotice";
    private static final String REFUND_STATE_KEY = "RefundStateNotice";

    @Value("${refundNotify.url}")
    private String refundNotifyUrl;


    private static final Logger log = LoggerFactory.getLogger(RefundServiceImpl.class);


    /**
     * 根据原订单号来查询商户已退款的金额
     *
     * @param orgiTransactionNo
     * @return
     */
    public Long selectRefundAmountByCustomerOrgiTransactionNo(String customerCodeFromHeader, String orgiOutTradeNo, String orgiTransactionNo) {
        Long totalRefundAmount = refundApplyMapper.selectRefundAmountByCustomerOrgiTransactionNo(orgiTransactionNo, customerCodeFromHeader);
        if (totalRefundAmount == null) totalRefundAmount = 0L;
        return totalRefundAmount;
    }

     //退款记录查询
     @Logable(businessTag = "pas.pageQueryRefundApply")
     public PageResult<RefundApply> pageQueryRefundApply(Integer pageNum, Integer pageSize, String beginCreateTime, String endCreateTime,
                                                           String customerCode, String customerName, Long userId, String refundTransactionNo,
                                                           String orgiOutTradeNo, String orgiTransactionNo, String auditState, String refundState, String refundApplyNo) {
      log.info("pageQueryRefundApply"+customerCode+",customerName:"+customerName+"refundTransactionNo:"+refundTransactionNo+"orgiOutTradeNo:"+orgiOutTradeNo);
         //根据商户名称获得商户号
         if (StringUtils.isBlank(customerCode) && StringUtils.isNotBlank(customerName)) {
             CumCustomerInfo cumCustomerInfo = new CumCustomerInfo();
             cumCustomerInfo.setName(customerName);
             List<CumCustomerInfo> list = cumCumCustomerInfoServiceImpl.selectBySelective(cumCustomerInfo);

             if (list.size() > 0) {
                 customerCode = list.get(0).getCustomerCode();
             }else{
                 customerCode = "-";
             }
         }
        Long refState = null;
        Long audiState = null;
        if (auditState != null && !"".equals(auditState)) {
            audiState = Long.parseLong(auditState);
        }
        if (refundState != null && !"".equals(refundState)) {
            refState = Long.parseLong(refundState);
        }
        int total = refundApplyMapper.countPageQueryRefundApply(pageNum, pageSize, beginCreateTime, endCreateTime,
                customerCode, refundTransactionNo,orgiOutTradeNo,orgiTransactionNo,  audiState, refState, refundApplyNo);
        int beginRowNo = (pageNum - 1) * pageSize + 1;
        int endRowNo = pageNum * pageSize;
        List<RefundApply> list = refundApplyMapper.pageQueryRefundApply(beginRowNo, endRowNo, beginCreateTime, endCreateTime,
                customerCode, refundTransactionNo,orgiOutTradeNo,orgiTransactionNo, auditState, refundState, refundApplyNo);
//         List<RefundApply> rows = pageResult.getRows();
         PageResult<RefundApply> pagingResult = new PageResult<>();
                if(list.size()>0){
                    //获取所有商户号
                    List<String> customerCodeList = new ArrayList<String>();
                    for (RefundApply row : list) {
                        if(!customerCodeList.contains(row.getCustomerCode())){
                            customerCodeList.add(row.getCustomerCode());
                        }
                    }

                    List<CumCustomerInfo> cumCustomerInfoList = cumCumCustomerInfoServiceImpl.getCustomerInfoByCodes(customerCodeList);
                    if(cumCustomerInfoList.size()>0){
                        HashMap<String, String> hp = new HashMap<String, String>();
                        for (CumCustomerInfo cumCustomerInfo : cumCustomerInfoList) {
                            if(!hp.containsKey(cumCustomerInfo.getCustomerCode())){
                                hp.put(cumCustomerInfo.getCustomerCode(), cumCustomerInfo.getName());
                            }
                        }

                        List<RefundApply> rowsNew = new ArrayList<RefundApply>();
                        //给商户名称赋值
                        for (RefundApply row : list) {
                            row.setCustomerName(hp.get(row.getCustomerCode())!=null ? hp.get(row.getCustomerCode()):"");
                            rowsNew.add(row);
                        }
                        pagingResult.setRows(rowsNew);
                    }
                }
        pagingResult.setTotal(total);
//        pagingResult.setRows(rowsNew);
        return pagingResult;
    }


    //退款审核
    
    @Transactional(isolation = Isolation.DEFAULT, rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    @Logable(businessTag = "pas.refundBatchApprove")
    public RefundApplyVo refundBatchApprove(RefundApply refundApply,long userId, String customerCodeFromHeader) {
        RefundApplyVo refundApplyVo = new RefundApplyVo();
        // TODO Auto-generated method stub
        List<RefundApplyMessage> refundApplyMessages = new ArrayList<RefundApplyMessage>();
        //查询退款订单
        PageResult<RefundApply> pg = pageQueryRefundApply(1, 1, null, null, null, null, null,null, null,null, null, null, refundApply.getRefundApplyNo());
        if (pg == null || pg.getRows() == null || pg.getRows().size() != 1) {
            refundApplyVo.setResultCode(PasConstants.PasCode.TRANSACTIONNO_NOT_EXIT.code);
            refundApplyVo.setMessage(PasConstants.PasCode.TRANSACTIONNO_NOT_EXIT.message);
            return refundApplyVo;
        }
        RefundApply vo = pg.getRows().get(0);
        if (vo.getAuditState() != 1) {     //订单审核状态：  0：待商户管理员审核  1：商户管理员审核通过待运营管理审核   2：商户管理员审核拒绝 3：运营管理审核通过 4：运营管理审核拒绝
            refundApplyVo.setResultCode(PasConstants.PasCode.AUDIT_STATE.code);
            refundApplyVo.setMessage(PasConstants.PasCode.AUDIT_STATE.message);
            return refundApplyVo;//不是它的订单
        }
        if (refundApply.getAuditState() != 3 && refundApply.getAuditState() != 4) {   //传 进来只有通过或拒绝
            refundApplyVo.setResultCode(PasConstants.PasCode.PARAM_STATE.code);
            refundApplyVo.setMessage(PasConstants.PasCode.PARAM_STATE.message);
            return refundApplyVo;//不是它的订单
        }
       refundApply.setOperatorAuditUserId(userId);   //是不是要加字段，运营的？
        refundApply.setOperatorAuditDateTime(new Date());
        boolean legalAuditState = self.legalAuditState(refundApply);
        if (legalAuditState) {
            //更亲审核状态
            refundApplyMapper.updateAdmitAuditUserIdAndDateTimeAndRemarkAndStateByRefundApplyNo(refundApply);
            if (refundApply.getAuditState() == PasConstants.RefundApplyAdmitAuditState.MANAGE_THROUGH_AUDIT.status) {  ///???????
//                        审核通过，调用交易的退款接口
                try {
                   self.txsRefund(vo);
                }catch(Exception e)
                {
                }
            }
        } else {
            //如果前端传递的审核状态既不是通过也不是拒绝
        }
//        发送“退款申请状态通知消息”
        RefundApply refundApplyInDb = refundApplyMapper.selectByRefundApplyNoAndCustomerCode(refundApply.getRefundApplyNo()
                , vo.getCustomerCode());
        RefundApplyMessage refundApplyMessage = new RefundApplyMessage();
        refundApplyMessage.setOperatorAuditRemark(refundApply.getOperatorAuditRemark());
        refundApplyMessage.setOrgiTransactionNo(refundApplyInDb.getOrgiTransactionNo());
        refundApplyMessage.setRefundAmount(refundApplyInDb.getRefundAmount());
        refundApplyMessage.setRefundApplyNo(refundApplyInDb.getRefundApplyNo());
        refundApplyMessage.setAuditState(refundApplyInDb.getAuditState());
        refundApplyMessage.setOperatorAuditDateTime(refundApply.getOperatorAuditDateTime());
        refundApplyMessages.add(refundApplyMessage);


        //如果不为空
        BatchRefundMessage batchRefundMessage = new BatchRefundMessage();
        batchRefundMessage.setCustomerCode(vo.getCustomerCode()); //customerCodeFromHeader
        batchRefundMessage.setRefundApplyList(refundApplyMessages);
        ListenableFuture<SendResult<String, String>> future = kafkaTemplate.send(TOPIC_OPERATOR_APPROVE, OPERATOR_APPROVE_KEY, JSON.toJSONString(batchRefundMessage));
       try {
            SendResult<String, String> result = future.get(5, TimeUnit.SECONDS);
            if (result != null) {
                log.info("消息：" + JSON.toJSONString(batchRefundMessage) + "发送成功");
            }
        } catch (InterruptedException e) {
            // TODO Auto-generated catch block
            log.info("消息：" + JSON.toJSONString(batchRefundMessage) + "发送失败");
        } catch (ExecutionException e) {
            // TODO Auto-generated catch block
            log.info("消息：" + JSON.toJSONString(batchRefundMessage) + "发送失败");
        } catch (TimeoutException e) {
            // TODO Auto-generated catch block
            log.info("消息：" + JSON.toJSONString(batchRefundMessage) + "发送失败");
        }

        if (refundApply.getAuditState() == 3) { //审核通过
            refundApplyVo.setResultCode(Constants.SUCCESS);
            refundApplyVo.setMessage("您已通过该笔退款申请!");  //处理成功
        } else if (refundApply.getAuditState() == 4) {    //审核不通过
            refundApplyVo.setResultCode(Constants.SUCCESS);
            refundApplyVo.setMessage("您未通过该笔退款申请!");  //处理成功
        }
        return refundApplyVo;
    }

    @Logable(businessTag = "txsRefund")
    public RefundResponse txsRefund(RefundApply vo) {
        return txsClient.refundRes(vo.getOrgiOutTradeNo(), vo.getRefundApplyNo(),vo.getOrgiTransactionNo(), vo.getCustomerCode(), "PPS", null, vo.getOriAmount().toString(), vo.getRefundAmount().toString(), "CNY", "01", null, null, refundNotifyUrl, "www.epaylinks.cn");
    }

    @Logable(businessTag = "legalAuditState")
    public boolean legalAuditState(RefundApply refundApply) {
        return (PasConstants.RefundApplyAdmitAuditState.MANAGE_THROUGH_AUDIT.status  //??????
                == refundApply.getAuditState()) || (PasConstants.RefundApplyAdmitAuditState.MANAGE_REJECT_AUDIT.status
                == refundApply.getAuditState());
    }


    //退款结果通知
    @Logable(businessTag = "pas.refundNotify")
    public String refundNotify(RefundNotifyRequest req) {
        if("".equals(StringUtils.trimToEmpty(req.getPayState()))||"".equals(StringUtils.trimToEmpty(req.getTransactionNo()))||"".equals(StringUtils.trimToEmpty(req.getOutRefundNo()))||"".equals(StringUtils.trimToEmpty(req.getCustomerCode())) )
        {
            return "-2";
        }
//        SUCCESS("00", "成功"), FAIL("01", "失败"),PENDING("02", "未知"), NOT_PROCESS("03", "未处理"),   IN_PROCESS("04", "处理中"),   OVERTIME_CLOSE("05", "超时关闭");
        RefundApply refund = new RefundApply();
        Integer refundState = 0;
//        退款执行状态             0：未执行   1：退款失败  2：退款成功  3：退款中
        if (req.getPayState().equals("00")) {
            refundState = 2;   //  2：退款成功
        } else if (req.getPayState().equals("01")) {
            refundState = 1;
        }
        PageResult<RefundApply> pg = pageQueryRefundApply(1, 1, null, null, req.getCustomerCode(), null, null, null, null, null, null, null, req.getOutRefundNo());
        if (pg == null || pg.getRows() == null || pg.getRows().size() != 1) {
            log.info( req.getOutRefundNo() + "receive notice is data not found..." + req.getPayState() + "exist this pas refundNotify ");
            return "-1";
        }
        RefundApply refundApplyInDb = pg.getRows().get(0);
        if (refundApplyInDb.getRefundState() == 0) {   //如果是原始状态，接受通知，如果不是，不执行  1227
            refund.setRefundState(refundState);
            refund.setRefundApplyNo( req.getOutRefundNo());
            refund.setRefundTransactionNo(req.getTransactionNo());  //////////?0105要有返回才能写进去 ??????
            if (refundState == 2) //成功才写时间
            {
                refund.setRefundEndTime(new Date());
            }
            refundApplyMapper.updateByRefundApplyNo(refund);
            log.info(req.getOutRefundNo() + "refundNotify data update success ，refundNotify pg:" + pg + "pg.row:" + pg.getRows() + " pg.getRows().size:" + pg.getRows().size() + "start to kafka message  notice");
//            RefundApply refundApplyInDb = pg.getRows().get(0);
            List<RefundApplyMessage> refundApplyMessages = new ArrayList<RefundApplyMessage>();
            RefundApplyMessage refundApplyMessage = new RefundApplyMessage();
            refundApplyMessage.setOrgiTransactionNo(refundApplyInDb.getOrgiTransactionNo());
            refundApplyMessage.setRefundAmount(refundApplyInDb.getRefundAmount());
            refundApplyMessage.setRefundApplyNo(refundApplyInDb.getRefundApplyNo());
            refundApplyMessage.setRefundState(refund.getRefundState());
            refundApplyMessage.setRefundTransactionNo(refund.getRefundTransactionNo());
            refundApplyMessages.add(refundApplyMessage);
            //如果不为空
            BatchRefundMessage batchRefundMessage = new BatchRefundMessage();
            batchRefundMessage.setCustomerCode(refundApplyInDb.getCustomerCode()); //customerCodeFromHeader
            batchRefundMessage.setRefundApplyList(refundApplyMessages);
            ListenableFuture<SendResult<String, String>> future = kafkaTemplate.send(TOPIC, REFUND_STATE_KEY, JSON.toJSONString(batchRefundMessage));
            try {
                SendResult<String, String> result = future.get(5, TimeUnit.SECONDS);
                if (result != null) {
                    log.info(TOPIC+","+REFUND_STATE_KEY+"消息：" + JSON.toJSONString(batchRefundMessage) + "发送成功");
                }
            } catch (InterruptedException e) {
                // TODO Auto-generated catch block
                log.info(TOPIC+","+REFUND_STATE_KEY+"消息：" + JSON.toJSONString(batchRefundMessage) + "发送失败");
            } catch (ExecutionException e) {
                // TODO Auto-generated catch block
                log.info(TOPIC+","+REFUND_STATE_KEY+"消息：" + JSON.toJSONString(batchRefundMessage) + "发送失败");
            } catch (TimeoutException e) {
                // TODO Auto-generated catch block
                log.info(TOPIC+","+REFUND_STATE_KEY+"消息：" + JSON.toJSONString(batchRefundMessage) + "发送失败");
            }
           return "0";
        }
        return "1";

    }

    //PPS商户审核通过，kafka写PAS数据  然后运营人员可以从此表查数据进行审核
    @Logable(businessTag = "insertPasRefundApply")
    @KafkaListener(topics = {"012RefundNotice","PPS_Apply"},containerFactory = "customContainerFactory")   //need modify   012RefundApproveNotice  012RefundNotice1
    public int insertPasRefundApply(ConsumerRecord<?, ?> record) {
        log.info("收到消息通知，下面要看日志有没执行") ;
        Optional<?> kafkaMessage = Optional.ofNullable(record.value());
        if (kafkaMessage.isPresent()) {
            Object message = kafkaMessage.get();
            String key = (String) record.key();
            RefundApply vo = new RefundApply();
            if (FIRST_APPROVE_KEY.equals(key)) {
                log.info("insertPasRefundApply,商户审核通过，插入数据到这PAS");
                //如果是商户门户变更通知
                JSONObject jsonObject = JSONObject.parseObject((String) message);
                JSONArray jsonArray =jsonObject.getJSONArray("refundApplyList");
                JSONObject refundApply = JSONObject.parseObject(jsonArray.get(0).toString());
                String customerCode = refundApply.getString("customerCode");
                String orgiOutTradeNo = refundApply.getString("orgiOutTradeNo");
                String orgiTransactionNo = refundApply.getString("orgiTransactionNo");
                String refundTransactionNo = refundApply.getString("refundTransactionNo");
                String refundApplyNo = refundApply.getString("refundApplyNo");
                String refundApplyUserId = refundApply.getString("refundApplyUserId");
//                String applyDateTime = refundApply.getString("applyDateTime");
                Date applyDateTime = refundApply.getDate("applyDateTime");
                String applyRemark = refundApply.getString("applyRemark");
                long refundAmount = refundApply.getLong("refundAmount");
                String adminAuditUserId = refundApply.getString("adminAuditUserId");
//                String adminAuditDateTime = refundApply.getString("adminAuditDateTime");
                Date adminAuditDateTime = refundApply.getDate("adminAuditDateTime");
                String adminAuditRemark = refundApply.getString("adminAuditRemark");
                Integer auditState = refundApply.getInteger("auditState");
//                Date operatorAuditDateTime = refundApply.getDate("operatorAuditDateTime");
//                String operatorAuditRemark = refundApply.getString("operatorAuditRemark");
                Integer refundState = refundApply.getInteger("refundState");
                long refundMethod = refundApply.getLong("refundMethod");
                long refundProcedureFee = refundApply.getLong("refundProcedureFee");
                long refundFee = refundApply.getLong("refundFee");
                long oriAmount = refundApply.getLong("oriAmount");
                long oriProcedureFee = refundApply.getLong("oriProcedureFee");
                String oriPayMethod = refundApply.getString("oriPayMethod");
                String oriPayState = refundApply.getString("oriPayState");
                String sourceChannel = refundApply.getString("sourceChannel");
                log.info("insertPasRefundApply,商户审核通过，插入数据到这PAS"+refundApplyNo);
                PageResult<RefundApply> pg = pageQueryRefundApply(1, 1, null, null, null, null, null, null, null,null, null, null, refundApplyNo);
                if (pg == null || pg.getRows() == null || pg.getRows().size() == 0) {   //kafka通知，如果pas没有记录，才进行插入，有就不执行了。
                    log.info(orgiTransactionNo+"insertPasRefundApply,商户审核通过，插入数据到这PAS"+refundApplyNo+"refundTransactionNo:"+refundTransactionNo);
                    vo.setId(sequenceService.nextValue("PAS_REFUND_APPLY"));
                    vo.setCustomerCode(customerCode);
                    vo.setOrgiOutTradeNo(orgiOutTradeNo);
                    vo.setOrgiTransactionNo(orgiTransactionNo);
                    vo.setRefundTransactionNo(refundTransactionNo);
                    vo.setRefundApplyNo(refundApplyNo);
                    if(! "".equals(StringUtils.trimToEmpty(refundApplyUserId))) {
                        vo.setRefundApplyUserId(new Long(refundApplyUserId));
                    }
                    vo.setApplyDateTime(applyDateTime);
                    vo.setApplyRemark(applyRemark);
                    vo.setRefundAmount(refundAmount);
                    if(!"".equals(StringUtils.trimToEmpty(adminAuditUserId)) ) {
                        vo.setAdminAuditUserId(new Long(adminAuditUserId));
                    }
                    vo.setAdminAuditDateTime(adminAuditDateTime);
                    vo.setAdminAuditRemark(adminAuditRemark);
                    vo.setAuditState(auditState);
//                vo.setOperatorAuditDateTime(operatorAuditDateTime);
//                vo.setOperatorAuditRemark(operatorAuditRemark);
                    vo.setRefundState(refundState);
                    vo.setRefundMethod(refundMethod);
                    vo.setRefundProcedureFee(refundProcedureFee);
                    vo.setRefundFee(refundFee);
                    vo.setOriAmount(oriAmount);
                    vo.setOriProcedureFee(oriProcedureFee);
                    vo.setOriPayMethod(oriPayMethod);
                    vo.setOriPayState(oriPayState);
                    vo.setSourceChannel(sourceChannel);
              /*  if (!"".equals(applyDateTime)) {
                    try {
                        vo.setApplyDateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(applyDateTime));
                    } catch (Exception e) {

                    }
                }*/
                    return refundApplyMapper.insert(vo);
                }
            }
        }
        return 0;
    }

    //w人工退款
      
      @Logable(businessTag = "pas.refund.refund")
      @Transactional(isolation = Isolation.DEFAULT, rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
      public RefundApplyVo refund(RefundApply refundApply,long userId, String customerCodeFromHeader) {
          RefundApplyVo refundApplyVo = new RefundApplyVo();
          // TODO Auto-generated method stub
          String resultCode = null;
          List<RefundApplyMessage> refundApplyMessages = new ArrayList<RefundApplyMessage>();
          //查询退款订单
          log.info("to approve:refundApplyNo:" + refundApply.getRefundApplyNo() + "refundApply.getOrgiTransactionNo():" + refundApply.getOrgiTransactionNo());
          PageResult<RefundApply> pg = pageQueryRefundApply(1, 1, null, null, customerCodeFromHeader, null,null,null, null,null, null, null, refundApply.getRefundApplyNo());
          log.info("pg:" + pg + "pg.row:" + pg.getRows() + " pg.getRows().size:" + pg.getRows().size());
          if (pg == null || pg.getRows() == null || pg.getRows().size() != 1) {
              refundApplyVo.setResultCode(PasConstants.PasCode.TRANSACTIONNO_NOT_EXIT.code);
              refundApplyVo.setMessage(PasConstants.PasCode.TRANSACTIONNO_NOT_EXIT.message);
              return refundApplyVo;
          }
          RefundApply vo = pg.getRows().get(0);
          if (vo.getAuditState() != 3 && vo.getRefundState() !=0) {     //订单审核状态：  0：待商户管理员审核  1：商户管理员审核通过待运营管理审核   2：商户管理员审核拒绝 3：运营管理审核通过 4：运营管理审核拒绝
              refundApplyVo.setResultCode(PasConstants.PasCode.AUDIT_STATE.code);
              refundApplyVo.setMessage(PasConstants.PasCode.AUDIT_STATE.message);
              return refundApplyVo;//不是它的订单
          }

          boolean legalAuditState = (PasConstants.RefundApplyAdmitAuditState.MANAGE_THROUGH_AUDIT.status  == vo.getAuditState()) ;
          if (legalAuditState) {
              //更亲审核状态
//              if (vo.getAuditState() == PasConstants.RefundApplyAdmitAuditState.MANAGE_THROUGH_AUDIT.status) {  ///???????
  //            审核通过，调用交易的退款接口
                  try {
                    RefundResponse res= txsClient.refundRes(vo.getOrgiTransactionNo(), vo.getRefundApplyNo(),vo.getOrgiTransactionNo(), vo.getCustomerCode(), "PPS", null, vo.getOriAmount().toString(), vo.getRefundAmount().toString(), "CNY", "01", null, null, refundNotifyUrl, "www.epaylinks.cn");
                      log.info(vo.getRefundApplyNo()+"txsClient.refundRes"+res.toString());
                  }catch(Exception e)
                  {
                      log.error(vo.getRefundApplyNo()+"txsClient.refund异常",e);
                  }
//              }
          } else {
              //如果前端传递的审核状态既不是通过也不是拒绝
          }
  //        发送“退款申请状态通知消息”
          RefundApply refundApplyInDb = refundApplyMapper.selectByRefundApplyNoAndCustomerCode(refundApply.getRefundApplyNo()
                  , vo.getCustomerCode());
          RefundApplyMessage refundApplyMessage = new RefundApplyMessage();
          refundApplyMessage.setOperatorAuditRemark(refundApply.getOperatorAuditRemark());
          refundApplyMessage.setOrgiTransactionNo(refundApplyInDb.getOrgiTransactionNo());
          refundApplyMessage.setRefundAmount(refundApplyInDb.getRefundAmount());
          refundApplyMessage.setRefundApplyNo(refundApplyInDb.getRefundApplyNo());
          refundApplyMessage.setAuditState(refundApplyInDb.getAuditState());
          refundApplyMessage.setOperatorAuditDateTime(refundApply.getOperatorAuditDateTime());
          refundApplyMessages.add(refundApplyMessage);


          //如果不为空
          BatchRefundMessage batchRefundMessage = new BatchRefundMessage();
          batchRefundMessage.setCustomerCode(vo.getCustomerCode()); //customerCodeFromHeader
          batchRefundMessage.setRefundApplyList(refundApplyMessages);
          ListenableFuture<SendResult<String, String>> future = kafkaTemplate.send(TOPIC_OPERATOR_APPROVE, OPERATOR_APPROVE_KEY, JSON.toJSONString(batchRefundMessage));
         try {
              SendResult<String, String> result = future.get(5, TimeUnit.SECONDS);
              if (result != null) {
                  log.info("消息：" + JSON.toJSONString(batchRefundMessage) + "发送成功");
              }
          } catch (InterruptedException e) {
              // TODO Auto-generated catch block
              log.info("消息：" + JSON.toJSONString(batchRefundMessage) + "发送失败");
          } catch (ExecutionException e) {
              // TODO Auto-generated catch block
              log.info("消息：" + JSON.toJSONString(batchRefundMessage) + "发送失败");
          } catch (TimeoutException e) {
              // TODO Auto-generated catch block
              log.info("消息：" + JSON.toJSONString(batchRefundMessage) + "发送失败");
          }

          refundApplyVo.setResultCode(Constants.SUCCESS);
          refundApplyVo.setMessage(Constants.ReturnCode.SUCCESS.comment);  //处理成功
          return refundApplyVo;
      }

  /*    用来测试PPS商户管理员审核通过通知PAS插入数据给运营管理人员查出来进行审核 insertPasRefundApply
    @Transactional(isolation = Isolation.DEFAULT, rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public RefundApplyVo refundBatchApprove(RefundApply refundApply,long userId, String customerCodeFromHeader) {
        RefundApplyVo refundApplyVo = new RefundApplyVo();
        // TODO Auto-generated method stub
        String resultCode = null;
        List<RefundApply> refundApplyMessages = new ArrayList<RefundApply>();
        //查询退款订单
        log.info("to approve:refundApplyNo:" + refundApply.getRefundApplyNo() + "refundApply.getOrgiTransactionNo():" + refundApply.getOrgiTransactionNo());
        PageResult<RefundApply> pg = pageQueryRefundApply(1, 1, null, null, null, null,null, null,null, null, null, refundApply.getRefundApplyNo());
        log.info("pg:" + pg + "pg.row:" + pg.getRows() + " pg.getRows().size:" + pg.getRows().size());
        if (pg == null || pg.getRows() == null || pg.getRows().size() != 1) {
            refundApplyVo.setResultCode(PasConstants.PasCode.TRANSACTIONNO_NOT_EXIT.code);
            refundApplyVo.setMessage(PasConstants.PasCode.TRANSACTIONNO_NOT_EXIT.message);
            return refundApplyVo;
        }
        RefundApply vo = pg.getRows().get(0);
        if (vo.getAuditState() != 1) {     //订单审核状态：  0：待商户管理员审核  1：商户管理员审核通过待运营管理审核   2：商户管理员审核拒绝 3：运营管理审核通过 4：运营管理审核拒绝
            refundApplyVo.setResultCode(PasConstants.PasCode.AUDIT_STATE.code);
            refundApplyVo.setMessage(PasConstants.PasCode.AUDIT_STATE.message);
            return refundApplyVo;//不是它的订单
        }
        if (refundApply.getAuditState() != 3 && refundApply.getAuditState() != 4) {   //传 进来只有通过或拒绝
            refundApplyVo.setResultCode(PasConstants.PasCode.PARAM_STATE.code);
            refundApplyVo.setMessage(PasConstants.PasCode.PARAM_STATE.message);
            return refundApplyVo;//不是它的订单
        }
       refundApply.setOperatorAuditUserId(userId);   //是不是要加字段，运营的？
        refundApply.setOperatorAuditDateTime(new Date());
        boolean legalAuditState = (PasConstants.RefundApplyAdmitAuditState.MANAGE_THROUGH_AUDIT.status  //??????
                == refundApply.getAuditState()) || (PasConstants.RefundApplyAdmitAuditState.MANAGE_REJECT_AUDIT.status
                == refundApply.getAuditState());
        if (legalAuditState) {
            //更亲审核状态
            refundApplyMapper.updateAdmitAuditUserIdAndDateTimeAndRemarkAndStateByRefundApplyNo(refundApply);
            if (refundApply.getAuditState() == PasConstants.RefundApplyAdmitAuditState.MANAGE_THROUGH_AUDIT.status) {  ///???????
//                        审核通过，调用交易的退款接口
//                txsClient.refund(vo.getOrgiOutTradeNo(), vo.getRefundTransactionNo(), vo.getCustomerCode(), "PPS", vo.getOriAmount().toString(), vo.getRefundAmount().toString(), "CNY", "01", refundNotifyUrl, "");
            }
        } else {
            //如果前端传递的审核状态既不是通过也不是拒绝
        }
//        发送“退款申请状态通知消息”
        RefundApply refundApplyInDb = refundApplyMapper.selectByRefundApplyNoAndCustomerCode(refundApply.getRefundApplyNo()
                , vo.getCustomerCode());
        refundApplyMessages.add(refundApplyInDb);


        //如果不为空
        BatchRefundMessage batchRefundMessage = new BatchRefundMessage();
        batchRefundMessage.setCustomerCode(vo.getCustomerCode()); //customerCodeFromHeader
        batchRefundMessage.setRefundApplyList(refundApplyMessages);
//        ListenableFuture<SendResult<String, String>> future = kafkaTemplate.send(TOPIC_APPROVE, OPERATOR_APPROVE_KEY, JSON.toJSONString(batchRefundMessage));
        ListenableFuture<SendResult<String, String>> future = kafkaTemplate.send(TOPIC_APPROVE_TEST, FIRST_APPROVE_KEY, JSON.toJSONString(batchRefundMessage));
        try {
            SendResult<String, String> result = future.get(5, TimeUnit.SECONDS);
            if (result != null) {
                log.info("消息：" + JSON.toJSONString(batchRefundMessage) + "发送成功");
            }
        } catch (InterruptedException e) {
            // TODO Auto-generated catch block
            log.info("消息：" + JSON.toJSONString(batchRefundMessage) + "发送失败");
        } catch (ExecutionException e) {
            // TODO Auto-generated catch block
            log.info("消息：" + JSON.toJSONString(batchRefundMessage) + "发送失败");
        } catch (TimeoutException e) {
            // TODO Auto-generated catch block
            log.info("消息：" + JSON.toJSONString(batchRefundMessage) + "发送失败");
        }

        refundApplyVo.setResultCode(Constants.SUCCESS);
        refundApplyVo.setMessage(Constants.ReturnCode.SUCCESS.comment);  //处理成功
        return refundApplyVo;
    }*/

    //退款审核这个是自动测试用的，需留意20180402
       
       @Transactional(isolation = Isolation.DEFAULT, rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
       @Logable(businessTag = "pas.refundBatchApprove FOR UAT")
       public RefundApplyVo refundApproveForUat(RefundApply refundApply) {
           RefundApplyVo refundApplyVo = new RefundApplyVo();
           // TODO Auto-generated method stub
           List<RefundApplyMessage> refundApplyMessages = new ArrayList<RefundApplyMessage>();
           //查询退款订单
           PageResult<RefundApply> pg = pageQueryRefundApply(1, 1, null, null, null, null, null,null, null,null, null, null, refundApply.getRefundApplyNo());
           if (pg == null || pg.getRows() == null || pg.getRows().size() != 1) {
               refundApplyVo.setResultCode(PasConstants.PasCode.TRANSACTIONNO_NOT_EXIT.code);
               refundApplyVo.setMessage(PasConstants.PasCode.TRANSACTIONNO_NOT_EXIT.message);
               return refundApplyVo;
           }
           RefundApply vo = pg.getRows().get(0);
           if (vo.getAuditState() != 1) {     //订单审核状态：  0：待商户管理员审核  1：商户管理员审核通过待运营管理审核   2：商户管理员审核拒绝 3：运营管理审核通过 4：运营管理审核拒绝
               refundApplyVo.setResultCode(PasConstants.PasCode.AUDIT_STATE.code);
               refundApplyVo.setMessage(PasConstants.PasCode.AUDIT_STATE.message);
               return refundApplyVo;//不是它的订单
           }
           if (refundApply.getAuditState() != 3 && refundApply.getAuditState() != 4) {   //传 进来只有通过或拒绝
               refundApplyVo.setResultCode(PasConstants.PasCode.PARAM_STATE.code);
               refundApplyVo.setMessage(PasConstants.PasCode.PARAM_STATE.message);
               return refundApplyVo;//不是它的订单
           }
          refundApply.setOperatorAuditUserId(1L);   //userId 20180402是不是要加字段，运营的？
           refundApply.setOperatorAuditDateTime(new Date());
           boolean legalAuditState = self.legalAuditState(refundApply);
           if (legalAuditState) {
               //更亲审核状态
               refundApplyMapper.updateAdmitAuditUserIdAndDateTimeAndRemarkAndStateByRefundApplyNo(refundApply);
               if (refundApply.getAuditState() == PasConstants.RefundApplyAdmitAuditState.MANAGE_THROUGH_AUDIT.status) {  ///???????
   //                        审核通过，调用交易的退款接口
                   try {
                      self.txsRefund(vo);
                   }catch(Exception e)
                   {
                   }
               }
           } else {
               //如果前端传递的审核状态既不是通过也不是拒绝
           }
   //        发送“退款申请状态通知消息”
           RefundApply refundApplyInDb = refundApplyMapper.selectByRefundApplyNoAndCustomerCode(refundApply.getRefundApplyNo()
                   , vo.getCustomerCode());
           RefundApplyMessage refundApplyMessage = new RefundApplyMessage();
           refundApplyMessage.setOperatorAuditRemark(refundApply.getOperatorAuditRemark());
           refundApplyMessage.setOrgiTransactionNo(refundApplyInDb.getOrgiTransactionNo());
           refundApplyMessage.setRefundAmount(refundApplyInDb.getRefundAmount());
           refundApplyMessage.setRefundApplyNo(refundApplyInDb.getRefundApplyNo());
           refundApplyMessage.setAuditState(refundApplyInDb.getAuditState());
           refundApplyMessage.setOperatorAuditDateTime(refundApply.getOperatorAuditDateTime());
           refundApplyMessages.add(refundApplyMessage);


           //如果不为空
           BatchRefundMessage batchRefundMessage = new BatchRefundMessage();
           batchRefundMessage.setCustomerCode(vo.getCustomerCode()); //customerCodeFromHeader
           batchRefundMessage.setRefundApplyList(refundApplyMessages);
           ListenableFuture<SendResult<String, String>> future = kafkaTemplate.send(TOPIC_OPERATOR_APPROVE, OPERATOR_APPROVE_KEY, JSON.toJSONString(batchRefundMessage));
          try {
               SendResult<String, String> result = future.get(5, TimeUnit.SECONDS);
               if (result != null) {
                   log.info("消息：" + JSON.toJSONString(batchRefundMessage) + "发送成功");
               }
           } catch (InterruptedException e) {
               // TODO Auto-generated catch block
               log.info("消息：" + JSON.toJSONString(batchRefundMessage) + "发送失败");
           } catch (ExecutionException e) {
               // TODO Auto-generated catch block
               log.info("消息：" + JSON.toJSONString(batchRefundMessage) + "发送失败");
           } catch (TimeoutException e) {
               // TODO Auto-generated catch block
               log.info("消息：" + JSON.toJSONString(batchRefundMessage) + "发送失败");
           }

           if (refundApply.getAuditState() == 3) { //审核通过
               refundApplyVo.setResultCode(Constants.SUCCESS);
               refundApplyVo.setMessage("您已通过该笔退款申请!");  //处理成功
           } else if (refundApply.getAuditState() == 4) {    //审核不通过
               refundApplyVo.setResultCode(Constants.SUCCESS);
               refundApplyVo.setMessage("您未通过该笔退款申请!");  //处理成功
           }
           return refundApplyVo;
       }

}



