package com.epaylinks.efps.pas.pps.client.vo;

public class RefundResponse {
    /**
     * 返回状态码
     */
    private String returnCode;
    /**
     * 错误信息
     */
    private String returnMsg;
    /**
     * 退款结果
     */
    private String refundResult;
    /**
     * 支付失败原因
     */
    private String payError;
	public String getReturnCode() {
		return returnCode;
	}
	public void setReturnCode(String returnCode) {
		this.returnCode = returnCode;
	}
	public String getReturnMsg() {
		return returnMsg;
	}
	public void setReturnMsg(String returnMsg) {
		this.returnMsg = returnMsg;
	}
	public String getRefundResult() {
		return refundResult;
	}
	public void setRefundResult(String refundResult) {
		this.refundResult = refundResult;
	}
	public String getPayError() {
		return payError;
	}
	public void setPayError(String payError) {
		this.payError = payError;
	}

	@Override
	public String toString() {
		return "RefundResponse{" +
				"returnCode='" + returnCode + '\'' +
				", returnMsg='" + returnMsg + '\'' +
				", refundResult='" + refundResult + '\'' +
				", payError='" + payError + '\'' +
				'}';
	}
}
