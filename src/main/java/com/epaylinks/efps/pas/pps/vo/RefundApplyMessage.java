package com.epaylinks.efps.pas.pps.vo;
/**
 * 退款消息对象
 * <AUTHOR>
 *
 */

import java.util.Date;

public class RefundApplyMessage {
	/**
	 * 退款申请号
	 */
	private String refundApplyNo;
	/**
  * 退款交易单号
  */
 private String refundTransactionNo;

	public String getRefundTransactionNo() {
		return refundTransactionNo;
	}

	public void setRefundTransactionNo(String refundTransactionNo) {
		this.refundTransactionNo = refundTransactionNo;
	}

	/**
	 * 退款申请时间
	 */
	private Date applyDateTime;
	private Date operatorAuditDateTime;
	/**
	 * 退款申请说明
	 */
	private String applyRemark;
	private String operatorAuditRemark;

	private Integer auditState;
	private Integer refundState;
	/**
	 * 申请退款金额
	 */
	private Long refundAmount;
	/**
	 * 原付款交易订单号
	 */
	private String orgiTransactionNo;
	public String getRefundApplyNo() {
		return refundApplyNo;
	}
	public void setRefundApplyNo(String refundApplyNo) {
		this.refundApplyNo = refundApplyNo;
	}
	public Date getApplyDateTime() {
		return applyDateTime;
	}
	public void setApplyDateTime(Date applyDateTime) {
		this.applyDateTime = applyDateTime;
	}
	public String getApplyRemark() {
		return applyRemark;
	}
	public void setApplyRemark(String applyRemark) {
		this.applyRemark = applyRemark;
	}
	public Long getRefundAmount() {
		return refundAmount;
	}
	public void setRefundAmount(Long refundAmount) {
		this.refundAmount = refundAmount;
	}
	public String getOrgiTransactionNo() {
		return orgiTransactionNo;
	}
	public void setOrgiTransactionNo(String orgiTransactionNo) {
		this.orgiTransactionNo = orgiTransactionNo;
	}

	public Integer getAuditState() {
		return auditState;
	}

	public void setAuditState(Integer auditState) {
		this.auditState = auditState;
	}

	public Integer getRefundState() {
		return refundState;
	}

	public void setRefundState(Integer refundState) {
		this.refundState = refundState;
	}

	public String getOperatorAuditRemark() {
		return operatorAuditRemark;
	}

	public void setOperatorAuditRemark(String operatorAuditRemark) {
		this.operatorAuditRemark = operatorAuditRemark;
	}

	public Date getOperatorAuditDateTime() {
		return operatorAuditDateTime;
	}

	public void setOperatorAuditDateTime(Date operatorAuditDateTime) {
		this.operatorAuditDateTime = operatorAuditDateTime;
	}
}
