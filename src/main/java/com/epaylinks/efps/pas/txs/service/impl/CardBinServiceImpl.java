package com.epaylinks.efps.pas.txs.service.impl;

import com.alibaba.fastjson.JSON;
import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.util.IOUtils;
import com.epaylinks.efps.pas.txs.dao.CardBinMapper;
import com.epaylinks.efps.pas.txs.domain.CardBin;
import com.epaylinks.efps.pas.txs.dto.CardBinDTO;
import com.epaylinks.efps.pas.txs.service.CardBinService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: <EMAIL>
 * @Date: 2019/12/25 15:22
 * @Description 银行卡规则匹配业务数据服务
 * @Version 1.0
 */
@Service
public class CardBinServiceImpl implements CardBinService {

    private static final String FILE_HEADER = "define('cardBin', [], function () {window.global_card_bins = {";
    private static final String FILE_BOTTOM = "}})";

    @Autowired
    private CardBinMapper cardBinMapper;

    @Override
    public String queryCardBinJsonData() {
        String fileContent = "{";
        try {
            fileContent += getCardBinJsFileContent(cardBinMapper.selectRecords());
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            return fileContent + "}";
        }
    }

    @Override
    public void downloadCardBinJsFile(HttpServletResponse response) {
        try {
            response.addHeader("Content-Disposition", "attachment;fileName=cardBin.js");
            response.setContentType("application/octet-stream");
            OutputStream outputStream = response.getOutputStream();
            String fileContent = getCardBinJsFileContent(cardBinMapper.selectRecords());
            String cardBinJsFile = FILE_HEADER + fileContent + FILE_BOTTOM;
            IOUtils.decrypt(new ByteArrayInputStream(cardBinJsFile.getBytes("UTF-8")), outputStream);
        } catch (Exception e) {
            response.setStatus(404);
        }
    }

    @Override
    public String getCardBinJsFileContent(List<CardBin> cardBins) {
        StringBuffer buffer = new StringBuffer();
        Map<String, CardBin> map = cardBins.stream().collect(Collectors.toMap(CardBin::getCardNoRange, Function.identity(), (existing, replacement) -> existing));
        cardBins = map.entrySet().stream().sorted(Map.Entry.<String, CardBin>comparingByKey().reversed()).map(x -> x.getValue()).collect(Collectors.toList());
        cardBins.forEach(cardBin -> {
            CardBinDTO dto = new CardBinDTO();
            BeanUtils.copyProperties(cardBin, dto);
            buffer.append("\"" + cardBin.getCardNoRange() + "\":" + JSON.toJSONString(dto));
            buffer.append(",");
        });
        try {
            buffer.deleteCharAt(buffer.lastIndexOf(","));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return buffer.toString();
    }
}
