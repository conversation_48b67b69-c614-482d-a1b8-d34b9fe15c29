package com.epaylinks.efps.pas.txs.service.impl;

import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.common.PasConstants;
import com.epaylinks.efps.pas.cum.domain.CumCustomerInfo;
import com.epaylinks.efps.pas.cum.service.impl.CumCumCustomerInfoServiceImpl;
import com.epaylinks.efps.pas.txs.domain.TxsPayTradeOrder;
import com.epaylinks.efps.pas.txs.domain.TxsWithdrawTradeOrder;
import com.epaylinks.efps.pas.txs.service.TransactionService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 5m
 * @Date : 2017/12/25 17:31
 * @Description :
 */
@Service
public class TransactionHandleServiceImpl {

    @Autowired
    private TransactionService transactionService;

    @Autowired
    private CumCumCustomerInfoServiceImpl cumCumCustomerInfoServiceImpl;

    
    public PageResult<TxsPayTradeOrder> payQuery(String userType,String userId,String customerCodeHead,String transactionNo, String outTradeNo, String customerCode, String customerName , String payState, String beginTime, String endTime, Integer pageNum, Integer pageSize, String transactionType, String payMethod,
    		String businessCode, String channelName, Boolean download,String fileName,String type) {

        //根据商户名称获得商户号
        if(StringUtils.isBlank(customerCode) && StringUtils.isNotBlank(customerName)){
            CumCustomerInfo cumCustomerInfo = new CumCustomerInfo();
            cumCustomerInfo.setName(customerName);
            List<CumCustomerInfo> list = cumCumCustomerInfoServiceImpl.selectBySelective(cumCustomerInfo);

            if(list.size()>0){
                customerCode = list.get(0).getCustomerCode();
            }else{
                customerCode = "-";
            }
        }
		if (download != null && download) {
			if (StringUtils.isBlank(fileName) || StringUtils.isBlank(type)) {
				throw new AppException(PasCode.FILENAME_TYPE_MUSTHAS.code);
			}
		}
        PageResult<TxsPayTradeOrder> pageResult = transactionService.payQuery(userType,userId,customerCodeHead, transactionNo, outTradeNo, customerCode, payState, beginTime, endTime, pageNum, pageSize, transactionType, payMethod,
        		businessCode, channelName, download,fileName, type);
        List<TxsPayTradeOrder> rows = pageResult.getRows();
        if(rows.size()>0){
            //获取所有商户号
            List<String> customerCodeList = new ArrayList<String>();
            for (TxsPayTradeOrder row : rows) {
                if(!customerCodeList.contains(row.getCustomerCode())){
                    customerCodeList.add(row.getCustomerCode());
                }
            }

            List<CumCustomerInfo> cumCustomerInfoList = cumCumCustomerInfoServiceImpl.getCustomerInfoByCodes(customerCodeList);
            if(cumCustomerInfoList.size()>0){
                HashMap<String, String> hp = new HashMap<String, String>();
                for (CumCustomerInfo cumCustomerInfo : cumCustomerInfoList) {
                    if(!hp.containsKey(cumCustomerInfo.getCustomerCode())){
                        hp.put(cumCustomerInfo.getCustomerCode(), cumCustomerInfo.getName());
                    }
                }

                List<TxsPayTradeOrder> rowsNew = new ArrayList<TxsPayTradeOrder>();
                //给商户名称赋值
                for (TxsPayTradeOrder row : rows) {
                    row.setCustomerName(hp.get(row.getCustomerCode())!=null ? hp.get(row.getCustomerCode()):"");
                    rowsNew.add(row);
                }
                pageResult.setRows(rowsNew);
            }
        }
        List<Map<String, String>> statistics = new ArrayList<>();
        Map<String, String> map = new HashMap<>(3);
        map.put("procedureFee", "手续费总额");
        map.put("amount", "订单金额总额");
        statistics.add(map);
        pageResult.setStatistics(statistics);
        return pageResult;
    }


    
    public PageResult<TxsWithdrawTradeOrder> withdrawQuery(String transactionNo, String outTradeNo, String customerCode, String customerName , String payState, String beginTime, String endTime, Integer pageNum, Integer pageSize,
    		Boolean download,String fileName,String type) {

        //根据商户名称获得商户号
        if(StringUtils.isBlank(customerCode) && StringUtils.isNotBlank(customerName)){
            CumCustomerInfo cumCustomerInfo = new CumCustomerInfo();
            cumCustomerInfo.setName(customerName);
            List<CumCustomerInfo> list = cumCumCustomerInfoServiceImpl.selectBySelective(cumCustomerInfo);

            if(list.size()>0){
                customerCode = list.get(0).getCustomerCode();
            }else{
                customerCode = "-";
            }
        }

        PageResult<TxsWithdrawTradeOrder> pageResult = transactionService.withdrawQuery(transactionNo, outTradeNo, customerCode, payState, beginTime, endTime, pageNum, pageSize,
        		download,fileName, type);
        List<TxsWithdrawTradeOrder> rows = pageResult.getRows();
        if(rows.size()>0){
            //获取所有商户号
            List<String> customerCodeList = new ArrayList<String>();
            for (TxsWithdrawTradeOrder row : rows) {
                if(!customerCodeList.contains(row.getCustomerCode())){
                    customerCodeList.add(row.getCustomerCode());
                }
            }

            List<CumCustomerInfo> cumCustomerInfoList = cumCumCustomerInfoServiceImpl.getCustomerInfoByCodes(customerCodeList);
            if(cumCustomerInfoList.size()>0){
                HashMap<String, String> hp = new HashMap<String, String>();
                for (CumCustomerInfo cumCustomerInfo : cumCustomerInfoList) {
                    if(!hp.containsKey(cumCustomerInfo.getCustomerCode())){
                        hp.put(cumCustomerInfo.getCustomerCode(), cumCustomerInfo.getName());
                    }
                }

                List<TxsWithdrawTradeOrder> rowsNew = new ArrayList<TxsWithdrawTradeOrder>();
                //给商户名称赋值
                for (TxsWithdrawTradeOrder row : rows) {
                    row.setCustomerName(hp.get(row.getCustomerCode())!=null ? hp.get(row.getCustomerCode()):"");
                    rowsNew.add(row);
                }
                pageResult.setRows(rowsNew);
            }
        }

        List<Map<String, String>> statistics = new ArrayList<>();
        Map<String, String> map = new HashMap<>(3);
        map.put("procedureFee", "手续费总额");
        map.put("totalFee", "提现总额");
        statistics.add(map);
        pageResult.setStatistics(statistics);
        return pageResult;
    }
}
