package com.epaylinks.efps.pas.txs.dto;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * @Author: <EMAIL>
 * @Date: 2019/12/25 16:18
 * @Description
 * @Version 1.0
 */
public class CardBinDTO {
    @JSONField(name = "T")
    private String cardType;
    @JSONField(name = "I")
    private String bankIcon;
    @JSONField(name = "N")
    private String issueBankName;

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getBankIcon() {
        return bankIcon;
    }

    public void setBankIcon(String bankIcon) {
        this.bankIcon = bankIcon;
    }

    public String getIssueBankName() {
        return issueBankName;
    }

    public void setIssueBankName(String issueBankName) {
        this.issueBankName = issueBankName;
    }

}
