package com.epaylinks.efps.pas.txs.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/5/20 15:46
 */
@Mapper
public interface TxsRefundPreOrderMapper {
    @Select("        select count(1)\n" +
            "        from TXS_REFUND_PRE_ORDER\n" +
            "        where PAY_TRANSACTIONNO=#{payTransactionNo,jdbcType=VARCHAR} and PAY_STATE=#{payState,jdbcType=CHAR}")
    Long countByPayTransactionNoAndPayState(@Param("payTransactionNo") String payTransactionNo, @Param("payState") String payState);
}
