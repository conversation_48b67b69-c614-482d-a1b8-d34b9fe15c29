package com.epaylinks.efps.pas.txs.domain;

import com.epaylinks.efps.common.datadownload.annotation.FieldAnnotation;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

public class TxsPayTradeOrder {
    private Long id;

	@JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@FieldAnnotation(fieldName="交易时间", dateFormat="yyyy-MM-dd HH:mm:ss")
	private Date createTime;

	@FieldAnnotation(fieldName="商户编号")
	private String customerCode;

	@FieldAnnotation(fieldName="商户名称")
	private String customerName;

	@FieldAnnotation(fieldName="商户订单号")
	private String outTradeNo;

    @FieldAnnotation(fieldName="易票联订单号")
	private String transactionNo;

	@FieldAnnotation(fieldName="手续费（分）")
	private Long procedureFee;

	@FieldAnnotation(fieldName="订单金额（分）")
	private Long amount;

	/**
	 * 渠道返回的订单
	 */
	@FieldAnnotation(fieldName="上游渠道订单号")
	private String channelOrder;

	@FieldAnnotation(fieldName="支付方式",dictionaries="0:账号支付,1:微信公众号支付,2:个人网银_贷记卡,3:个人网银_借记卡,"
			+ "4:支付宝生活号支付,5:代付,6:微信扫码支付,7:支付宝扫码支付,8:快捷支付,9:微信app支付,10:微信H5支付,13:微信被扫,14:支付宝被扫,20:手机银联支付")
	private String payMethod;

	/**
	 * 渠道名字
	 */
	@FieldAnnotation(fieldName = "上游渠道")
	private String channelName;

	private String terminalNo;

	private String srcChannelType;

	private String notifyUrl;

	private String redirectUrl;

	private String payState;

	@JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date updateTime;

	private String payer;
	private String payerType;
	private String payerId;
	private String clientIp;
	private String transactionType;
	private String currencyType;

	private String attachdata;
	private Date transactionStartTime;
	private Date transactionEndTime;

	@JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date endTime;
	private String orderInfo;
	private String remark;
	private String bankCardNo;



	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getTransactionNo() {
		return transactionNo;
	}

	public void setTransactionNo(String transactionNo) {
		this.transactionNo = transactionNo;
	}

	public String getOutTradeNo() {
		return outTradeNo;
	}

	public void setOutTradeNo(String outTradeNo) {
		this.outTradeNo = outTradeNo;
	}

	public String getCustomerCode() {
		return customerCode;
	}

	public void setCustomerCode(String customerCode) {
		this.customerCode = customerCode;
	}

	public String getTerminalNo() {
		return terminalNo;
	}

	public void setTerminalNo(String terminalNo) {
		this.terminalNo = terminalNo;
	}

	public String getPayMethod() {
		return payMethod;
	}

	public void setPayMethod(String payMethod) {
		this.payMethod = payMethod;
	}

	public String getSrcChannelType() {
		return srcChannelType;
	}

	public void setSrcChannelType(String srcChannelType) {
		this.srcChannelType = srcChannelType;
	}

	public Long getProcedureFee() {
		return procedureFee;
	}

	public void setProcedureFee(Long procedureFee) {
		this.procedureFee = procedureFee;
	}

	public String getNotifyUrl() {
		return notifyUrl;
	}

	public void setNotifyUrl(String notifyUrl) {
		this.notifyUrl = notifyUrl;
	}

	public String getRedirectUrl() {
		return redirectUrl;
	}

	public void setRedirectUrl(String redirectUrl) {
		this.redirectUrl = redirectUrl;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getPayState() {
		return payState;
	}

	public void setPayState(String payState) {
		this.payState = payState;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public String getPayer() {
		return payer;
	}

	public void setPayer(String payer) {
		this.payer = payer;
	}

	public String getPayerType() {
		return payerType;
	}

	public void setPayerType(String payerType) {
		this.payerType = payerType;
	}

	public String getPayerId() {
		return payerId;
	}

	public void setPayerId(String payerId) {
		this.payerId = payerId;
	}

	public String getClientIp() {
		return clientIp;
	}

	public void setClientIp(String clientIp) {
		this.clientIp = clientIp;
	}

	public String getTransactionType() {
		return transactionType;
	}

	public void setTransactionType(String transactionType) {
		this.transactionType = transactionType;
	}

	public String getCurrencyType() {
		return currencyType;
	}

	public void setCurrencyType(String currencyType) {
		this.currencyType = currencyType;
	}

	public Long getAmount() {
		return amount;
	}

	public void setAmount(Long amount) {
		this.amount = amount;
	}

	public String getAttachdata() {
		return attachdata;
	}

	public void setAttachdata(String attachdata) {
		this.attachdata = attachdata;
	}

	public Date getTransactionStartTime() {
		return transactionStartTime;
	}

	public void setTransactionStartTime(Date transactionStartTime) {
		this.transactionStartTime = transactionStartTime;
	}

	public Date getTransactionEndTime() {
		return transactionEndTime;
	}

	public void setTransactionEndTime(Date transactionEndTime) {
		this.transactionEndTime = transactionEndTime;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public String getOrderInfo() {
		return orderInfo;
	}

	public void setOrderInfo(String orderInfo) {
		this.orderInfo = orderInfo;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getBankCardNo() {
		return bankCardNo;
	}

	public void setBankCardNo(String bankCardNo) {
		this.bankCardNo = bankCardNo;
	}

	public String getCustomerName() { return customerName;}

	public void setCustomerName(String customerName) {	this.customerName = customerName;}

	public String getChannelOrder() {
		return channelOrder;
	}

	public void setChannelOrder(String channelOrder) {
		this.channelOrder = channelOrder;
	}

	public String getChannelName() {
		return channelName;
	}

	public void setChannelName(String channelName) {
		this.channelName = channelName;
	}
}