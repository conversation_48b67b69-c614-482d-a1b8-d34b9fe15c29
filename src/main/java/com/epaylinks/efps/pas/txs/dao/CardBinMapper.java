package com.epaylinks.efps.pas.txs.dao;

import com.epaylinks.efps.pas.txs.domain.CardBin;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface CardBinMapper {
    int deleteByPrimaryKey(Long id);

    int insert(CardBin record);

    int insertSelective(CardBin record);

    CardBin selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CardBin record);

    int updateByPrimaryKey(CardBin record);

    List<CardBin> selectRecords();
}