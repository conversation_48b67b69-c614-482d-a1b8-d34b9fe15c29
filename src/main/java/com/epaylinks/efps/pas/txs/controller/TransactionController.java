package com.epaylinks.efps.pas.txs.controller;

import com.epaylinks.efps.common.datadownload.annotation.DownloadAble;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.pas.txs.domain.TxsPayTradeOrder;
import com.epaylinks.efps.pas.txs.domain.TxsWithdrawTradeOrder;
import com.epaylinks.efps.pas.txs.service.impl.TransactionHandleServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> 5m
 * @Date : 2017/12/25 11:36
 * @Description : 交易查询管理
 */
@RestController
@RequestMapping("/txs")
@Api(value = "TransactionController", description = "交易查询")
public class TransactionController {

    @Autowired
    private TransactionHandleServiceImpl transactionHandleService;

    /**
     * 交易查询(支付/充值)
     * payMethod=ZF 支付交易
     * payMethod=CZ 充值交易
     */
    @SuppressWarnings({ "unchecked", "rawtypes"})
    @Logable(businessTag = "receivePayResult")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "交易查询(支付/充值)", notes = "交易查询(支付/充值)", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "transactionNo", value = "交易订单号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "outTradeNo", value = "商户订单号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "customerCode", value = "商户号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "customerName", value = "商户名称", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "payState", value = "支付状态", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "beginTime", value = "开始时间", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "结束时间", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageNum", value = "查询开始页", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "查询的页面大小", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "transactionType", value = "交易类型", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "payMethod", value = "支付方式", required = false, dataType = "String", paramType = "query") })
    @RequestMapping("/payquery")
    @DownloadAble
    public PageResult<TxsPayTradeOrder> payQuery(
    		@RequestHeader(value = "x-user-type", required = false) String userType,
			@RequestHeader(value = "x-userid", required = false) String userId,
			@RequestHeader(value = "x-customer-code", required = false) String customerCodeHead,
            @RequestParam(value = "transactionNo", required = false) String transactionNo,
            @RequestParam(value = "outTradeNo", required = false) String outTradeNo,
            @RequestParam(value = "customerCode", required = false) String customerCode,
            @RequestParam(value = "customerName", required = false) String customerName,
            @RequestParam(value = "payState", required = false) String payState,
            @RequestParam(value = "beginTime", required = false) String beginTime,
            @RequestParam(value = "endTime", required = false) String endTime,
            @RequestParam(value = "pageNum", required = false) Integer pageNum,
            @RequestParam(value = "pageSize", required = false) Integer pageSize,
            @RequestParam(value = "transactionType", required = false) String transactionType,
            @RequestParam(value = "payMethod", required = false) String payMethod,
			@RequestParam(value = "businessCode", required = false) String businessCode,
			@RequestParam(value = "channelName", required = false) String channelName,
            @RequestParam(value = "download", required = false) Boolean download,
			@RequestParam(value = "fileName", required = false) String fileName,
			@RequestParam(value = "type", required = false) String type) {

        return transactionHandleService.payQuery(userType,userId,customerCodeHead, transactionNo, outTradeNo, customerCode, customerName, payState, beginTime, endTime, pageNum, pageSize, transactionType, payMethod,
        		businessCode, channelName, download,fileName, type);
    }


    /**
     * 账户提现交易查询接口
     */
    @Logable(businessTag = "withdrawquery")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "提现交易查询接口", notes = "提现交易查询接口", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "transactionNo", value = "交易订单号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "outTradeNo", value = "商户订单号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "customerCode", value = "商户号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "customerName", value = "商户名称", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "payState", value = "提现状态", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "beginTime", value = "开始时间", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "结束时间", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageNum", value = "查询开始页", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "查询的页面大小", required = false, dataType = "int", paramType = "query") })
    @RequestMapping("/withdrawquery")
    @DownloadAble
    public PageResult<TxsWithdrawTradeOrder> withdrawQuery(@RequestParam(value = "transactionNo", required = false) String transactionNo,
                                                           @RequestParam(value = "outTradeNo", required = false) String outTradeNo,
                                                           @RequestParam(value = "customerCode", required = false) String customerCode,
                                                           @RequestParam(value = "customerName", required = false) String customerName,
                                                           @RequestParam(value = "payState", required = false) String payState,
                                                           @RequestParam(value = "beginTime", required = false) String beginTime,
                                                           @RequestParam(value = "endTime", required = false) String endTime,
                                                           @RequestParam(value = "pageNum", required = false) Integer pageNum,
                                                           @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                                           @RequestParam(value = "download", required = false) Boolean download,
                                               			   @RequestParam(value = "fileName", required = false) String fileName,
                                               			   @RequestParam(value = "type", required = false) String type) {

        return transactionHandleService.withdrawQuery( transactionNo, outTradeNo, customerCode, customerName, payState, beginTime, endTime, pageNum, pageSize,
        		download,fileName, type);
    }


}
