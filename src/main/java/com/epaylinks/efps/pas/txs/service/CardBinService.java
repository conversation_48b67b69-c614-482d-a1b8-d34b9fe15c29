package com.epaylinks.efps.pas.txs.service;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.pas.txs.domain.CardBin;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2019/12/25 15:22
 * @Description 银行卡规则匹配业务数据服务
 * @Version 1.0
 */
public interface CardBinService {

    /**
     * 查询银行卡规则匹配业务“JSON”数据
     *
     * @return 通用响应对象
     */
    String queryCardBinJsonData();

    /**
     * 下载银行卡规则匹配业务“cardBin.js”文件
     *
     * @param response 客户响应对象
     */
    void downloadCardBinJsFile(HttpServletResponse response);

    /**
     * 生成 CardBinJsFile 内容
     *
     * @param cardBins
     * @return
     */
    String getCardBinJsFileContent(List<CardBin> cardBins);
}
