package com.epaylinks.efps.pas.txs.domain;

import com.epaylinks.efps.common.datadownload.annotation.FieldAnnotation;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * 账户提现交易订单表
 * <AUTHOR>
 *
 */
public class TxsWithdrawTradeOrder {

	/**
	 * 订单创建时间
	 */
	@JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@FieldAnnotation(fieldName="交易时间", dateFormat="yyyy-MM-dd HH:mm:ss")
	private Date createTime;

	/**
	 * 客户编号
	 */
	@FieldAnnotation(fieldName="客户编号")
	private String customerCode;

	/**
	 * 客户名称
	 */
	@FieldAnnotation(fieldName="商户名称")
	private String customerName;

	/**
	 * 商户订单号
	 */
	@FieldAnnotation(fieldName="商户订单号")
	private String outTradeNo;

	/**
	 * 交易订单号
	 */
	@FieldAnnotation(fieldName="易票联订单号")
	private String transactionNo;

	/**
	 * 费用（手续费）
	 */
	@FieldAnnotation(fieldName="手续费（分）")
	private Long procedureFee;

	/**
	 * 提现金额
	 */
	@FieldAnnotation(fieldName="提现金额（分）")
	private Long totalFee;

	/**
	 * 渠道返回的订单
	 */
	@FieldAnnotation(fieldName="上游渠道订单号")
	private String channelOrder;

	/**
	 * 渠道名字
	 */
	@FieldAnnotation(fieldName="上游渠道")
	private String channelName;


    private Long id;

    /**
     * 终端号
     */
    private String terminalNo;



    /**
     * 提现币种
     */
    private String payCurrency;
    
    /**
     * 到账类型
     */
    private String arrivalType;
    
    /**
     * 实际到账金额
     */
    private Long actualFee;
    

    
    /**
     * 费率
     */
    private String produceRate;
    
    /**
     * 卡ID
     */
    private Long cardId;
    
    /**
     * 卡号
     */
    private String cardNo;
    

    
    /**
     * 查询条件-交易开始时间
     */
	@JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date beginTime;
    
    /**
     * 查询条件-交易结束时间
     */
	@JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date endTime;
    
    /**
     * 支付状态
     */
    private String payState;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 渠道类型
     */
    private String channelType;

    /**
     * 提现结果通知地址
     */
    private String notifyUrl;

    /**
     * 商家提现结果地址
     */
    private String redirectUrl;



	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getTransactionNo() {
		return transactionNo;
	}

	public void setTransactionNo(String transactionNo) {
		this.transactionNo = transactionNo;
	}

	public String getOutTradeNo() {
		return outTradeNo;
	}

	public void setOutTradeNo(String outTradeNo) {
		this.outTradeNo = outTradeNo;
	}

	public String getCustomerCode() {
		return customerCode;
	}

	public void setCustomerCode(String customerCode) {
		this.customerCode = customerCode;
	}

	public String getTerminalNo() {
		return terminalNo;
	}

	public void setTerminalNo(String terminalNo) {
		this.terminalNo = terminalNo;
	}

	public Long getTotalFee() {
		return totalFee;
	}

	public void setTotalFee(Long totalFee) {
		this.totalFee = totalFee;
	}

	public String getPayCurrency() {
		return payCurrency;
	}

	public void setPayCurrency(String payCurrency) {
		this.payCurrency = payCurrency;
	}

	public String getArrivalType() {
		return arrivalType;
	}

	public void setArrivalType(String arrivalType) {
		this.arrivalType = arrivalType;
	}

	public Long getActualFee() {
		return actualFee;
	}

	public void setActualFee(Long actualFee) {
		this.actualFee = actualFee;
	}

	public Long getProcedureFee() {
		return procedureFee;
	}

	public void setProcedureFee(Long procedureFee) {
		this.procedureFee = procedureFee;
	}

	public String getProduceRate() {
		return produceRate;
	}

	public void setProduceRate(String produceRate) {
		this.produceRate = produceRate;
	}

	public Long getCardId() {
		return cardId;
	}

	public void setCardId(Long cardId) {
		this.cardId = cardId;
	}

	public String getCardNo() {
		return cardNo;
	}

	public void setCardNo(String cardNo) {
		this.cardNo = cardNo;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getBeginTime() {
		return beginTime;
	}

	public void setBeginTime(Date beginTime) {
		this.beginTime = beginTime;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public String getPayState() {
		return payState;
	}

	public void setPayState(String payState) {
		this.payState = payState;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getChannelType() {
		return channelType;
	}

	public void setChannelType(String channelType) {
		this.channelType = channelType;
	}

	public String getNotifyUrl() {
		return notifyUrl;
	}

	public void setNotifyUrl(String notifyUrl) {
		this.notifyUrl = notifyUrl;
	}

	public String getRedirectUrl() {
		return redirectUrl;
	}

	public void setRedirectUrl(String redirectUrl) {
		this.redirectUrl = redirectUrl;
	}

	public String getCustomerName() { return customerName;}

	public void setCustomerName(String customerName) {	this.customerName = customerName;}

	public String getChannelOrder() {
		return channelOrder;
	}

	public void setChannelOrder(String channelOrder) {
		this.channelOrder = channelOrder;
	}

	public String getChannelName() {
		return channelName;
	}

	public void setChannelName(String channelName) {
		this.channelName = channelName;
	}
}