package com.epaylinks.efps.pas.txs.controller;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.pas.txs.service.CardBinService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * @Author: <EMAIL>
 * @Date: 2019/12/25 15:10
 * @Description 银行卡规则匹配业务数据管理
 * @Version 1.0
 */
@RestController
@RequestMapping("/CardBin")
@Api(value = "CardBinController", description = "获取银行卡规则匹配业务数据管理")
public class CardBinController {

    @Autowired
    private CardBinService cardBinService;

    @GetMapping("/queryCardBinJsonData")
    @Logable(businessTag = "queryCardBinJsonData")
    @ApiOperation(value = "查询银行卡规则匹配业务“JSON”数据", notes = "查询银行卡规则匹配业务“JSON”数据", httpMethod = "GET")
    @ResponseBody
    public String queryCardBinJsonData() {
        return cardBinService.queryCardBinJsonData();
    }

    @GetMapping("/downloadCardBinJsFile")
    @Logable(businessTag = "downloadCardBinJsFile")
    @ApiOperation(value = "下载银行卡规则匹配业务“cardBin.js”文件", notes = "下载银行卡规则匹配业务“cardBin.js”文件", httpMethod = "GET")
    public void downloadCardBinJsFile(HttpServletResponse response) {
        cardBinService.downloadCardBinJsFile(response);
    }
}
