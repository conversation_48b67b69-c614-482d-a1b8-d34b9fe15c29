package com.epaylinks.efps.pas.txs.domain;

import java.math.BigDecimal;

public class CardBin {
    private Long id;

    private Long recordId;

    private String cardNoRange;

    private BigDecimal cardNoRangeLen;

    private String issueBankNo;

    private String bankIcon;

    private String issueBankName;

    private String cardName;

    private BigDecimal applyRange;

    private BigDecimal cardNoLen;

    private String cardType;

    private String issueBankAccount;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getRecordId() {
        return recordId;
    }

    public void setRecordId(Long recordId) {
        this.recordId = recordId;
    }

    public String getCardNoRange() {
        return cardNoRange;
    }

    public void setCardNoRange(String cardNoRange) {
        this.cardNoRange = cardNoRange;
    }

    public BigDecimal getCardNoRangeLen() {
        return cardNoRangeLen;
    }

    public void setCardNoRangeLen(BigDecimal cardNoRangeLen) {
        this.cardNoRangeLen = cardNoRangeLen;
    }

    public String getIssueBankNo() {
        return issueBankNo;
    }

    public void setIssueBankNo(String issueBankNo) {
        this.issueBankNo = issueBankNo;
    }

    public String getBankIcon() {
        return bankIcon;
    }

    public void setBankIcon(String bankIcon) {
        this.bankIcon = bankIcon;
    }

    public String getIssueBankName() {
        return issueBankName;
    }

    public void setIssueBankName(String issueBankName) {
        this.issueBankName = issueBankName;
    }

    public String getCardName() {
        return cardName;
    }

    public void setCardName(String cardName) {
        this.cardName = cardName;
    }

    public BigDecimal getApplyRange() {
        return applyRange;
    }

    public void setApplyRange(BigDecimal applyRange) {
        this.applyRange = applyRange;
    }

    public BigDecimal getCardNoLen() {
        return cardNoLen;
    }

    public void setCardNoLen(BigDecimal cardNoLen) {
        this.cardNoLen = cardNoLen;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getIssueBankAccount() {
        return issueBankAccount;
    }

    public void setIssueBankAccount(String issueBankAccount) {
        this.issueBankAccount = issueBankAccount;
    }
}