package com.epaylinks.efps.pas.txs.service;

import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.pas.txs.domain.TxsPayTradeOrder;
import com.epaylinks.efps.pas.txs.domain.TxsWithdrawTradeOrder;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR> 5m
 * @Date : 2017/12/25 11:37
 * @Description :
 */
@FeignClient("txs")
public interface TransactionService {

    @RequestMapping(value = "/pay/payQueryForPas", method= RequestMethod.GET)
    PageResult<TxsPayTradeOrder> payQuery(
    		@RequestHeader(value = "x-user-type", required = false) String userType,
			@RequestHeader(value = "x-userid", required = false) String userId,
			@RequestHeader(value = "x-customer-code", required = false) String customerCodeHead,
    		@RequestParam(value = "transactionNo", required = false) String transactionNo,
                                          @RequestParam(value = "outTradeNo", required = false) String outTradeNo,
                                          @RequestParam(value = "customerCode", required = false) String customerCode,
                                          @RequestParam(value = "payState", required = false) String payState,
                                          @RequestParam(value = "beginTime", required = false) String beginTime,
                                          @RequestParam(value = "endTime", required = false) String endTime,
                                          @RequestParam(value = "pageNum", required = false) Integer pageNum,
                                          @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                          @RequestParam(value = "transactionType", required = false) String transactionType,
                                          @RequestParam(value = "payMethod", required = false) String payMethod,
                                          @RequestParam(value = "businessCode", required = false) String businessCode,
                              			  @RequestParam(value = "channelName", required = false) String channelName,
                                          @RequestParam(value = "download", required = false) Boolean download,
                              			  @RequestParam(value = "fileName", required = false) String fileName,
                              			  @RequestParam(value = "type", required = false) String type);

    @RequestMapping(value = "/pay/withdrawqueryForPas", method= RequestMethod.GET)
    public PageResult<TxsWithdrawTradeOrder> withdrawQuery(@RequestParam(value = "transactionNo", required = false) String transactionNo,
                                                           @RequestParam(value = "outTradeNo", required = false) String outTradeNo,
                                                           @RequestParam(value = "customerCode", required = false) String customerCode,
                                                           @RequestParam(value = "payState", required = false) String payState,
                                                           @RequestParam(value = "beginTime", required = false) String beginTime,
                                                           @RequestParam(value = "endTime", required = false) String endTime,
                                                           @RequestParam(value = "pageNum", required = false) Integer pageNum,
                                                           @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                                           @RequestParam(value = "download", required = false) Boolean download,
                                               			   @RequestParam(value = "fileName", required = false) String fileName,
                                               			   @RequestParam(value = "type", required = false) String type);
}
