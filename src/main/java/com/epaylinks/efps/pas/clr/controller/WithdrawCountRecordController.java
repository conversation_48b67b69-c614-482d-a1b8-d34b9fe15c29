package com.epaylinks.efps.pas.clr.controller;

import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.pas.clr.domain.WithdrawCountRecord;
import com.epaylinks.efps.pas.clr.response.WithdrawCountRecordResp;
import com.epaylinks.efps.pas.clr.service.WithdrawCountService;
import com.epaylinks.efps.pas.common.PageResult;
import com.epaylinks.efps.pas.common.PasCode;
import com.github.pagehelper.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: Liuq
 * @Date: 2018/11/22 10:43
 * @描述: 渤海代付交易汇总
 */
@RestController
@RequestMapping("/WithdrawCountRecord")
@Api(value = "WithdrawCountRecordController", description = "渤海代付交易汇总统计")
public class WithdrawCountRecordController {

    @Autowired
    private WithdrawCountService withdrawCountService;

    @RequestMapping(value ="/queryList", method = RequestMethod.GET)
    @Logable(businessTag = "queryList")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "渤海代付交易汇总查询", notes = "渤海代付交易汇总查询", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startTime", value = "统计开始日期（示例：20180905）", required = false, dataType = "String", length =50, paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "统计结束日期（示例：20180905）", required = false, dataType = "String", length =20, paramType = "query"),
            @ApiImplicitParam(name = "pageNum", value = "查询开始页", required = true, dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "查询的页面大小不需要分页则把此值填大一点", required = true, dataType = "Integer", paramType = "query")})
    public PageResult<WithdrawCountRecordResp> queryRecord(@RequestParam(value = "startTime", required = false) String startTime,
                                                           @RequestParam(value = "endTime", required = false) String endTime,
                                                           @RequestParam(value = "pageNum", required = true) Integer pageNum,
                                                           @RequestParam(value = "pageSize", required = true) Integer pageSize){
        PageResult<WithdrawCountRecordResp> pageResult = null;
        try {
            return withdrawCountService.queryList(startTime,endTime,pageNum,pageSize);
        }  catch (Exception e) {
            pageResult = new PageResult<WithdrawCountRecordResp>();
            if (e instanceof AppException) {
                pageResult.setReturnCode(((AppException) e).getErrorCode());
                pageResult.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                pageResult.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                pageResult.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
            return pageResult;
        }
    }
}
