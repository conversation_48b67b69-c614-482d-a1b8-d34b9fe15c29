package com.epaylinks.efps.pas.clr.service;

import com.epaylinks.efps.pas.clr.domain.WithdrawCountRecord;
import com.epaylinks.efps.pas.clr.response.WithdrawCountRecordResp;
import com.epaylinks.efps.pas.common.PageResult;

/**
 * @Author: Liuq
 * @Date: 2018/11/22 11:28
 */
public interface WithdrawCountService {

    /**
     * 统计汇总代付交易列表
     */
    public PageResult<WithdrawCountRecordResp> queryList(String startTime, String endTime, Integer pageNum, Integer pageSize);
}
