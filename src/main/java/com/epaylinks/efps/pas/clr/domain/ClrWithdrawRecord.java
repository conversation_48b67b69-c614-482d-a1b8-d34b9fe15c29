package com.epaylinks.efps.pas.clr.domain;

import java.util.Date;

public class ClrWithdrawRecord {
    /**
     * 主键
     */
    private Long id;

    /**
     * 下游交易订单号
     */
    private String transactionNo;

    /**
     * 由本系统生成，作为订单号传输给上游
     */
    private String reqSerialno;

    /**
     * 支付金额
     */
    private Long payAmount;

    /**
     * 支付币种
     */
    private String payCurrency;

    /**
     * 渠道类型 01：互联网 02：移动
     */
    private String channelType;

    /**
     * 服务代码id
     */
    private String serviceId;

    /**
     * 支付渠道
     */
    private Long channelId;

    /**
     * 上游通道返回的流水号
     */
    private String channelTradeNo;

    /**
     * 上游通道返回的此笔交易的交易日期
     */
    private String channelTradeTime;

    /**
     * 上游通道返回此笔交易的清算日期
     */
    private String channelClearDate;

    /**
     * 上游通道返回的响应码
     */
    private String channelRespCode;

    /**
     * 上游通道返回的响应信息
     */
    private String channelRespMsg;

    /**
     * 上游通道扩展信息,格式为json格式
     */
    private String channelExtend;

    /**
     * 收款方银行卡ID
     */
    private String payeeBankcardId;

    /**
     * 收款方银行机构编码
     */
    private String payeeBankcode;

    /**
     * 收款方姓名
     */
    private String payeeCustomer;

    /**
     * 商家号
     */
    private String customerCode;

    /**
     * 提现执行状态；00：成功、01：失败、02上游处理中, 03:排队中（初始状态）
     */
    private String state;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 计划发送至上游时间
     */
    private Date planSendTime;

    /**
     * 实际发送至上游时间
     */
    private Date actualSendTime;

    /**
     * 要求上游执行提现时间, 现阶段是当前插入时间+5分钟
     */
    private Date askExecuteTime;

    /**
     * 已经向通道发起查询次数
     */
    private Short queryCount;

    /**
     * 机构表主键
     */
    private Long institutionId;

    /**
     * 支付方式
     */
    private String payMethod;

    /**
     * 机构名称
     */
    private String institutionName;

    /**
     * 渠道商户号
     */
    private String channelMchtNo;

    /**
     * 机构号
     */
    private String channelInstCode;

    /**
     * 路由规则，0-商户路由，1-人工路由，2-智能路由
     */
    private String routeType;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTransactionNo() {
        return transactionNo;
    }

    public void setTransactionNo(String transactionNo) {
        this.transactionNo = transactionNo;
    }

    public String getReqSerialno() {
        return reqSerialno;
    }

    public void setReqSerialno(String reqSerialno) {
        this.reqSerialno = reqSerialno;
    }

    public Long getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(Long payAmount) {
        this.payAmount = payAmount;
    }

    public String getPayCurrency() {
        return payCurrency;
    }

    public void setPayCurrency(String payCurrency) {
        this.payCurrency = payCurrency;
    }

    public String getChannelType() {
        return channelType;
    }

    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }

    public String getServiceId() {
        return serviceId;
    }

    public void setServiceId(String serviceId) {
        this.serviceId = serviceId;
    }

    public Long getChannelId() {
        return channelId;
    }

    public void setChannelId(Long channelId) {
        this.channelId = channelId;
    }

    public String getChannelTradeNo() {
        return channelTradeNo;
    }

    public void setChannelTradeNo(String channelTradeNo) {
        this.channelTradeNo = channelTradeNo;
    }

    public String getChannelTradeTime() {
        return channelTradeTime;
    }

    public void setChannelTradeTime(String channelTradeTime) {
        this.channelTradeTime = channelTradeTime;
    }

    public String getChannelClearDate() {
        return channelClearDate;
    }

    public void setChannelClearDate(String channelClearDate) {
        this.channelClearDate = channelClearDate;
    }

    public String getChannelRespCode() {
        return channelRespCode;
    }

    public void setChannelRespCode(String channelRespCode) {
        this.channelRespCode = channelRespCode;
    }

    public String getChannelRespMsg() {
        return channelRespMsg;
    }

    public void setChannelRespMsg(String channelRespMsg) {
        this.channelRespMsg = channelRespMsg;
    }

    public String getChannelExtend() {
        return channelExtend;
    }

    public void setChannelExtend(String channelExtend) {
        this.channelExtend = channelExtend;
    }

    public String getPayeeBankcardId() {
        return payeeBankcardId;
    }

    public void setPayeeBankcardId(String payeeBankcardId) {
        this.payeeBankcardId = payeeBankcardId;
    }

    public String getPayeeBankcode() {
        return payeeBankcode;
    }

    public void setPayeeBankcode(String payeeBankcode) {
        this.payeeBankcode = payeeBankcode;
    }

    public String getPayeeCustomer() {
        return payeeCustomer;
    }

    public void setPayeeCustomer(String payeeCustomer) {
        this.payeeCustomer = payeeCustomer;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getPlanSendTime() {
        return planSendTime;
    }

    public void setPlanSendTime(Date planSendTime) {
        this.planSendTime = planSendTime;
    }

    public Date getActualSendTime() {
        return actualSendTime;
    }

    public void setActualSendTime(Date actualSendTime) {
        this.actualSendTime = actualSendTime;
    }

    public Date getAskExecuteTime() {
        return askExecuteTime;
    }

    public void setAskExecuteTime(Date askExecuteTime) {
        this.askExecuteTime = askExecuteTime;
    }

    public Short getQueryCount() {
        return queryCount;
    }

    public void setQueryCount(Short queryCount) {
        this.queryCount = queryCount;
    }

    public Long getInstitutionId() {
        return institutionId;
    }

    public void setInstitutionId(Long institutionId) {
        this.institutionId = institutionId;
    }

    public String getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(String payMethod) {
        this.payMethod = payMethod;
    }

    public String getInstitutionName() {
        return institutionName;
    }

    public void setInstitutionName(String institutionName) {
        this.institutionName = institutionName;
    }

    public String getChannelMchtNo() {
        return channelMchtNo;
    }

    public void setChannelMchtNo(String channelMchtNo) {
        this.channelMchtNo = channelMchtNo;
    }

    public String getChannelInstCode() {
        return channelInstCode;
    }

    public void setChannelInstCode(String channelInstCode) {
        this.channelInstCode = channelInstCode;
    }

    public String getRouteType() {
        return routeType;
    }

    public void setRouteType(String routeType) {
        this.routeType = routeType;
    }
}