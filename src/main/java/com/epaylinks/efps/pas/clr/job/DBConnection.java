package com.epaylinks.efps.pas.clr.job;

import java.math.BigDecimal;
import java.sql.*;

import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.pas.provisions.epcc.util.BackendUtils;
import oracle.jdbc.driver.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

/**
 * @Author: Liuq
 * @Date: 2018/11/26 16:36
 */
@Service
@RefreshScope
public class DBConnection {
    @Value("${bohaiDF.datasource.driver}")
    private String driver;
    @Value("${bohaiDF.datasource.url}")
    private String URL;
    @Value("${bohaiDF.datasource.username}")
    private String username;
    @Value("${bohaiDF.datasource.owner}")
    private String owner;
    @Value("${bohaiDF.datasource.password}")
    private String password;
    @Value("${bohaiDF.operator}")
    private String operator;
    @Value("${bohaiDF.storeId}")
    private String storeId;

    public  Statement stmt = null;
    public  ResultSet rs = null;
    public  Connection conn = null;

    public static Logger logger = Logger.getLogger(DBConnection.class.getName());

    @Logable(businessTag = "DBConnection.connect")
    public List<String> connect(String billNo, Long money){
        List<String> strList = new ArrayList<>();
        try {
            // 设置主机ip，端口，用户名，密码
            Class.forName(driver);
            conn = DriverManager.getConnection(URL, username, password);
            CallableStatement proc = null;
            proc = conn.prepareCall("{ call "+owner+".PTSTORE_BALANCE_ADD_SYS(?,?,?,?,?,?,?) }"); //存储过程 ecard下的
            BigDecimal totalFee = new BigDecimal(money);
            BigDecimal d100 = new BigDecimal(100);
            totalFee = totalFee.divide(d100,2,2);//小数点2位
            // 设置输入参数
            proc.setString(1, storeId); // 设置第一个输入参数，商戶號
            proc.setString(2, billNo); // 设置第2个输入参数bill_no 流水號20位
            proc.setBigDecimal(3,totalFee); // 设置第3个输入参数,訂單金額
            proc.setString(4, operator); // 设置第4个输入参数，operator ：默認system
            proc.setString(5, " "); // 设置第5个输入参数，空
            proc.registerOutParameter(6,12);
            proc.registerOutParameter(7,12);//设置输出参数是一个游标.第一个参数,游标类型
            proc.execute();//执行
            String result1 = (String)proc.getObject(6); //获得第一个参数v_resp_code；返回代码 0000：成功，其他失败
            String result2 = (String)proc.getObject(7);  //返回信息v_resp_msg

            if (result1 != null && !result1.trim().equals("")){
                strList.add(result1);
                strList.add(result2);
                logger.info("调用OSS存储过程返回码：" + result1+";返回信息："+result2);
                System.out.println("调用OSS存储过程返回码：" + result1+";返回信息："+result2);
            }
        }
        catch (SQLException ex2) {
            ex2.printStackTrace();
        }
        catch (Exception ex2) {
            ex2.printStackTrace();
        }
        finally{
            try {
                if(rs != null){
                    rs.close();
                }
                if(stmt!=null){
                    stmt.close();
                }
                if(conn!=null){
                    conn.close();
                }
            }
            catch (SQLException ex1) {
            }
        }
        return strList;
    }
}
