package com.epaylinks.efps.pas.clr.service;

import com.epaylinks.efps.pas.clr.domain.Institution;
import com.epaylinks.efps.pas.cum.domain.CumCustomerInfo;

import java.util.List;

/**
 * <AUTHOR> 5m
 * @Date : 2018/1/11 9:47
 * @Description :
 */
public interface InstitutionService {

    /**
     * 查询范围内的机构号对应的机构
     */
    public List<Institution> getInstitutionaByCodes(List<String> codes);


    public List<Institution> selectBySelective(Institution institution);

}
