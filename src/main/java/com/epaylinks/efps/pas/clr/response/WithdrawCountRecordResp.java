package com.epaylinks.efps.pas.clr.response;

import java.util.Date;

public class WithdrawCountRecordResp {
    /**
     * 主键
     */
    private Long id;

    /**
     * 机构表主键id
     */
    private Long institutionId;

    /**
     * 机构名字
     * */
    private String institutionName;

    /**
     * 渠道表主键id
     */
    private Long channelId;

    /**
     * 渠道名字
     */
    private String channelName;

    /**
     * 统计金额
     */
    private Long countAmount;

    /**
     * 统计方式，默认：自动统计
     */
    private String countType;

    /**
     * 统计日期
     */
    private String countTime;

    /**
     * 创建时间
     */
    private String createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getInstitutionId() {
        return institutionId;
    }

    public void setInstitutionId(Long institutionId) {
        this.institutionId = institutionId;
    }

    public Long getChannelId() {
        return channelId;
    }

    public void setChannelId(Long channelId) {
        this.channelId = channelId;
    }

    public Long getCountAmount() {
        return countAmount;
    }

    public void setCountAmount(Long countAmount) {
        this.countAmount = countAmount;
    }

    public String getCountType() {
        return countType;
    }

    public void setCountType(String countType) {
        this.countType = countType;
    }

    public String getCountTime() {
        return countTime;
    }

    public void setCountTime(String countTime) {
        this.countTime = countTime;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getInstitutionName() {
        return institutionName;
    }

    public void setInstitutionName(String institutionName) {
        this.institutionName = institutionName;
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }
}