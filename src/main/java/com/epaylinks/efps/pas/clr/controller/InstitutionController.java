package com.epaylinks.efps.pas.clr.controller;

import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.pas.clr.domain.Institution;
import com.epaylinks.efps.pas.clr.service.InstitutionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 * <AUTHOR> 5m
 * @Date : 2018/1/11 17:28
 * @Description :
 */
@RestController
@RequestMapping("/Institution")
@Api(value = "InstitutionController", description = "机构信息")
public class InstitutionController {
    
    @Autowired
    private InstitutionService institutionService;

    @RequestMapping(value ="/select", method = RequestMethod.GET)
    @Logable(businessTag = "select")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "搜索机构信息数据", notes = "搜索机构信息数据", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "institutionCode", value = "机构信息编码", required = false, dataType = "String", length =50, paramType = "query"),
            @ApiImplicitParam(name = "institutionName", value = "机构名称", required = false, dataType = "String", length =20, paramType = "query") })
    public List<Institution> select(@ApiIgnore Institution record ) {
        return institutionService.selectBySelective(record) ;

    }
}
