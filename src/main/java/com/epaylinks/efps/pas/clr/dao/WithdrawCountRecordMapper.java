package com.epaylinks.efps.pas.clr.dao;

import com.epaylinks.efps.pas.clr.domain.WithdrawCountRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface WithdrawCountRecordMapper {
    int insert(WithdrawCountRecord record);

    int insertSelective(WithdrawCountRecord record);

    /*
     * 通过日期查询
     */
    List<WithdrawCountRecord> queryList(@Param("beginCountTime") Date beginCountTime,@Param("endCountTime") Date endCountTime,
                                        @Param("beginRowNo") Integer beginRowNo,@Param("endRowNo") Integer endRowNo);

    /**
     * 查询符合条件的总数量
     * @param: record
     * @return
     */
    int countWithdrawList(@Param("beginCountTime") Date beginCountTime,@Param("endCountTime") Date endCountTime,
                       @Param("beginRowNo") Integer beginRowNo,@Param("endRowNo") Integer endRowNo);
}