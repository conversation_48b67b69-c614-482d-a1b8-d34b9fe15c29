package com.epaylinks.efps.pas.clr.domain;

import java.util.Date;

public class WithdrawCountRecord {
    /**
     * 主键
     */
    private Long id;

    /**
     * 机构表主键id
     */
    private Long institutionId;

    /**
     * 渠道表主键id
     */
    private Long channelId;

    /**
     * 统计金额
     */
    private Long countAmount;

    /**
     * 关联id
     */
    private String billNo;

    /**
     * 统计方式，默认：自动统计
     */
    private String countType;

    /**
     * 统计日期
     */
    private Date countTime;

    /**
     * 创建时间
     */
    private Date createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getInstitutionId() {
        return institutionId;
    }

    public void setInstitutionId(Long institutionId) {
        this.institutionId = institutionId;
    }

    public Long getChannelId() {
        return channelId;
    }

    public void setChannelId(Long channelId) {
        this.channelId = channelId;
    }

    public Long getCountAmount() {
        return countAmount;
    }

    public void setCountAmount(Long countAmount) {
        this.countAmount = countAmount;
    }

    public String getCountType() {
        return countType;
    }

    public void setCountType(String countType) {
        this.countType = countType;
    }

    public Date getCountTime() {
        return countTime;
    }

    public void setCountTime(Date countTime) {
        this.countTime = countTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }

}