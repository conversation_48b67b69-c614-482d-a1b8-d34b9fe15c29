package com.epaylinks.efps.pas.clr.response;

import java.util.List;

/**
 * 描述：机构列表response对象，前端用
 */
public class InstitutionResp {
    /**
     * 主键
     */
    private Long id;

    /**
     * 上游机构名称
     */
    private String institutionName;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 修改时间
     */
    private String updateTime;
    /**
     * 操作人
     */
    private String updator;

    /**
     * 上游机构编号
     */
    private String institutionCode;

    /**
     * 上游机构联系人
     */
    private String contacts;

    /**
     * 上游机构联系电话
     */
    private String phone;

    /**
     * 易票联联系人
     */
    private String epayContacts;

    /**
     * 上游机构简称
     */
    private String institutionShortName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 渠道大类列表list
     * @return
     */
    private List<String> categoryList;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getInstitutionName() {
        return institutionName;
    }

    public void setInstitutionName(String institutionName) {
        this.institutionName = institutionName;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getUpdator() {
        return updator;
    }

    public void setUpdator(String updator) {
        this.updator = updator;
    }

    public String getInstitutionCode() {
        return institutionCode;
    }

    public void setInstitutionCode(String institutionCode) {
        this.institutionCode = institutionCode;
    }

    public String getContacts() {
        return contacts;
    }

    public void setContacts(String contacts) {
        this.contacts = contacts;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEpayContacts() {
        return epayContacts;
    }

    public void setEpayContacts(String epayContacts) {
        this.epayContacts = epayContacts;
    }

    public String getInstitutionShortName() {
        return institutionShortName;
    }

    public void setInstitutionShortName(String institutionShortName) {
        this.institutionShortName = institutionShortName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public List<String> getCategoryList() {
        return categoryList;
    }

    public void setCategoryList(List<String> categoryList) {
        this.categoryList = categoryList;
    }
}