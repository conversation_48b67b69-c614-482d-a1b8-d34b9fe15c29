package com.epaylinks.efps.pas.clr.dao;

import com.epaylinks.efps.pas.clr.domain.ClrWithdrawRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface ClrWithdrawRecordMapper {
    /**
     * 查詢前一天00點至24點的匯總記錄
     * @param: 匯總日期，機構號
     */
    List<ClrWithdrawRecord> queryPreDayList(@Param("beginCountTime") Date beginCountTime,
                                            @Param("endCountTime") Date endCountTime,
                                            @Param("institutionId") Long institutionId,
                                            @Param("state") String state);
}