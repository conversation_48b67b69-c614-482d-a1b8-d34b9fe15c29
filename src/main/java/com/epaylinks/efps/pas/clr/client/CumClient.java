package com.epaylinks.efps.pas.clr.client;

import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.pas.clr.response.InstitutionResp;
import com.epaylinks.efps.pas.clr.response.PayChannel;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @Author: Liuq
 * @Date: 2018/11/22 16:50
 */
@FeignClient("CUM")
public interface CumClient {

    /**
     * 机构信息列表
     */
    @RequestMapping(value="/Institution/query", method = RequestMethod.GET)
    public PageResult<InstitutionResp> query(@RequestParam(value = "institutionCode",required = false) String institutionCode,
                                             @RequestParam(value = "institutionName",required = false) String institutionName,
                                             @RequestParam(value = "startTime",required = false) String startTime,
                                             @RequestParam(value = "endTime",required = false) String endTime,
                                             @RequestParam(value = "pageNo",required = true) int pageNo,
                                             @RequestParam(value = "pageNum",required = true) int pageNum,
                                             @RequestParam(value = "type",required = false) String type);

    /**
     * 渠道列表
     */
    @RequestMapping(value="/channelRouteRule/queryPayChannelList", method = RequestMethod.GET)
    public PageResult<PayChannel> queryPayChannelList(@RequestParam(value = "payMethod", required = false) String payMethod,
                                                      @RequestParam(value = "institutionId", required = false) Long institutionId);
}


