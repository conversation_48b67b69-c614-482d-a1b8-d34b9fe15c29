package com.epaylinks.efps.pas.clr.domain;

/**
 * 资金结算.回款
 */
public class HuikuanRequest {

    /**
     * 订单号
     */
    private String transactionNo;

    /**
     * 金额
     */
    private Long txnAmt;

    /**
     * 头寸
     */
    private String insSeq;

    /**
     * 结转用途
     */
    private String cfdPurpose;

    /**
     * 备注
     */
    private String remark;

    /**
     * 当前操作用户ID
     */
    private String curUserId;

    public String getTransactionNo() {
        return transactionNo;
    }

    public void setTransactionNo(String transactionNo) {
        this.transactionNo = transactionNo;
    }

    public Long getTxnAmt() {
        return txnAmt;
    }

    public void setTxnAmt(Long txnAmt) {
        this.txnAmt = txnAmt;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getInsSeq() {
        return insSeq;
    }

    public void setInsSeq(String insSeq) {
        this.insSeq = insSeq;
    }

    public String getCfdPurpose() {
        return cfdPurpose;
    }

    public void setCfdPurpose(String cfdPurpose) {
        this.cfdPurpose = cfdPurpose;
    }

    public String getCurUserId() {
        return curUserId;
    }

    public void setCurUserId(String curUserId) {
        this.curUserId = curUserId;
    }
}
