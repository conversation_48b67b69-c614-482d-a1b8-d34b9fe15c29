package com.epaylinks.efps.pas.clr.dao;

import com.epaylinks.efps.pas.clr.domain.Institution;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


@Mapper
@Transactional
public interface InstitutionMapper {
//    int deleteByPrimaryKey(Long id);
//
//    int insert(com.epaylinks.efps.clr.channel.model.Institution record);
//
//    int insertSelective(com.epaylinks.efps.clr.channel.model.Institution record);
//
//    @Transactional(readOnly = true)
//    com.epaylinks.efps.clr.channel.model.Institution selectByPrimaryKey(Long id);
//
//    @Transactional(readOnly = true)
//    List<com.epaylinks.efps.clr.channel.model.Institution> selectBySelective(com.epaylinks.efps.clr.channel.model.Institution record);
//
//    @Transactional(readOnly = true)
//    com.epaylinks.efps.clr.channel.model.Institution selectByChannelId(Long channelId);
//
//    int updateByPrimaryKeySelective(com.epaylinks.efps.clr.channel.model.Institution record);
//
//    int updateByPrimaryKey(com.epaylinks.efps.clr.channel.model.Institution record);

    List<Institution> getInstitutionaByCodes(List<String> codes);

    List<Institution> selectBySelective(Institution institution);
}