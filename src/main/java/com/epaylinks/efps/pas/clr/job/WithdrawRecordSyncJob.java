package com.epaylinks.efps.pas.clr.job;

import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.pas.clr.dao.ClrWithdrawRecordMapper;
import com.epaylinks.efps.pas.clr.dao.WithdrawCountRecordMapper;
import com.epaylinks.efps.pas.clr.domain.ClrWithdrawRecord;
import com.epaylinks.efps.pas.clr.domain.WithdrawCountRecord;
import com.epaylinks.efps.pas.common.PasCode;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.logging.Logger;

/**
 * @Author: Liuq
 * @Date: 2018/11/22 14:59
 * @描述: 渤海代付交易汇总记录定时查询
 */
@Component
@RefreshScope
public class WithdrawRecordSyncJob {

    @Autowired
    private ClrWithdrawRecordMapper clrWithdrawRecordMapper;
    @Autowired
    private SequenceService sequenceService;
    @Autowired
    private WithdrawCountRecordMapper withdrawCountRecordMapper;
    @Autowired
    private DBConnection dbConnection;

    public static Logger logger = Logger.getLogger(DBConnection.class.getName());

    @Scheduled(cron = "0 0 2 * * ?")// 每天凌晨2点执行
//    @Scheduled(cron = "0 0/5 * * * ?")// 隔1分钟执行
    @Logable(businessTag = "WithdrawRecordSyncJob.execute")
    public void execute(){
        //1.先從clr表匯總查詢
        List<ClrWithdrawRecord> clrWithdrawRecordList = new ArrayList<>();
        List<WithdrawCountRecord> withdrawCountRecordList = new ArrayList<>();
        Calendar ca = Calendar.getInstance();//得到一个Calendar的实例
        ca.setTime(new Date()); //设置时间为当前时间
        ca.add(Calendar.DATE, -1);//前一天
        Date beforeDate = ca.getTime(); //结果
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        String dateString = formatter.format(beforeDate);
        Date beginCountTime = null;  //當前日期前一天 00点
        Date endCountTime = null;  //當前日期前一天  24点
        try{
            beginCountTime = DateUtils.parseDate(dateString + " 00:00:00", "yyyy-MM-dd HH:mm:ss");
            endCountTime = DateUtils.parseDate(dateString + " 23:59:59", "yyyy-MM-dd HH:mm:ss");
        }catch (Exception e){
            //抛出异常
            throw new AppException(PasCode.SYSTEM_EXCEPTION.code);
        }
        List<String> resultList = new ArrayList<>();  //調用存儲過程返回list
        String returnCode = "";  //返回代码 0000：成功，其他失败
        String returnMsg ="";   //返回信息
        Long institutionId = 19L;   //渤海银行股份有限公司 19
        String billNo = "";   //关联单号
        Long amount1 = 0L; //D0代付到借记卡金額
        Long amount2 = 0L;  //D0代付到贷记卡金額
        try{
            clrWithdrawRecordList = clrWithdrawRecordMapper.queryPreDayList(beginCountTime,endCountTime,institutionId,"00".trim());
        }catch (Exception e){
            //抛出异常
            throw new AppException(PasCode.SYSTEM_EXCEPTION.code);
        }
        //2.插入到本地表
        if(clrWithdrawRecordList != null && clrWithdrawRecordList.size()>0){
            int result = 0;
            for(ClrWithdrawRecord record:clrWithdrawRecordList){
                if(record.getPayAmount() != null && record.getChannelId() != null && record.getChannelId()==38L){//D0代付到借记卡
                    amount1 = amount1 + record.getPayAmount();
                }
                if(record.getPayAmount() != null && record.getChannelId() != null && record.getChannelId()==37L){//D0代付到贷记卡
                    amount2 = amount2 + record.getPayAmount();
                }
            }
            String dateString2 = DateFormatUtils.format(new Date(), "yyyyMMddHHmmss");
            billNo = "EPSPBH" + dateString2;
            WithdrawCountRecord withdrawCountRecord = new WithdrawCountRecord();
            withdrawCountRecord.setId(sequenceService.nextValue("CUM.WITHDRAW_COUNT_RECORD"));
            withdrawCountRecord.setCreateTime(new Date());
            withdrawCountRecord.setCountTime(beginCountTime);
            withdrawCountRecord.setInstitutionId(institutionId);
            withdrawCountRecord.setCountType("1");
            withdrawCountRecord.setCountAmount(amount1);  //匯總交易金額
            withdrawCountRecord.setChannelId(38L);    //渠道id
            withdrawCountRecord.setBillNo(billNo);
            try{
                result = withdrawCountRecordMapper.insert(withdrawCountRecord);
                withdrawCountRecord.setId(sequenceService.nextValue("CUM.WITHDRAW_COUNT_RECORD"));
                withdrawCountRecord.setCountAmount(amount2);  //匯總交易金額
                withdrawCountRecord.setChannelId(37L);    //渠道id
                result += withdrawCountRecordMapper.insert(withdrawCountRecord);
            }catch (Exception e){
                //抛出异常
                throw new AppException(PasCode.SYSTEM_EXCEPTION.code);
            }
            //3.調用oss接口同步
            try{
                Long money = amount1+amount2;
                if(billNo != null && !billNo.equals("") && money > 0L){
                    resultList = dbConnection.connect(billNo,money);
                }
            }catch (Exception e){
                //拋出異常
                throw new AppException(PasCode.OSS_CONNECT_ERROR.code);
            }
        }
        if(resultList != null && resultList.size()>0){
            returnCode = resultList.get(0);
            returnMsg = resultList.get(1);
            if(null != returnCode && !returnCode.equals("0000")){
                //調用oss存儲過程返回不為空
                logger.info("渤海代付，调用oss存储过程返回码[returnCode]:"+returnCode+",返回信息[returnMsg]:"+returnMsg);
            }
        }
    }
}
