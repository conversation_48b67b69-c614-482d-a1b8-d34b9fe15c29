package com.epaylinks.efps.pas.clr.service.impl;

import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.pas.clr.client.CumClient;
import com.epaylinks.efps.pas.clr.dao.WithdrawCountRecordMapper;
import com.epaylinks.efps.pas.clr.domain.WithdrawCountRecord;
import com.epaylinks.efps.pas.clr.response.InstitutionResp;
import com.epaylinks.efps.pas.clr.response.PayChannel;
import com.epaylinks.efps.pas.clr.response.WithdrawCountRecordResp;
import com.epaylinks.efps.pas.clr.service.WithdrawCountService;
import com.epaylinks.efps.pas.common.PageResult;
import com.epaylinks.efps.pas.common.PasCode;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @Author: Liuq
 * @Date: 2018/11/22 11:28
 */
@Service
public class WithdrawCountServiceImpl implements WithdrawCountService {

    @Autowired
    private WithdrawCountRecordMapper withdrawCountRecordMapper;
    @Autowired
    private CumClient cumClient;

    @Override
    @Logable(businessTag = "WithdrawCountServiceImpl.queryList")
    public PageResult<WithdrawCountRecordResp> queryList(String startTime, String endTime, Integer pageNum, Integer pageSize) {

        PageResult<WithdrawCountRecordResp> withdrawRespPageResult = new PageResult<>();
        List<WithdrawCountRecord> withdrawCountRecordList = new ArrayList<>();
        List<WithdrawCountRecordResp> withdrawRespList = new ArrayList<>();
        int total = 0;
        Date beginCreateTime = null;
        Date endCreateTime = null;
        try {
            int endRowNo = pageNum * pageSize;
            int beginRowNo = (pageNum - 1) * pageSize + 1;
            if (startTime != null && !startTime.equals("")) {
                if(startTime.length() == 8){
                    beginCreateTime = DateUtils.parseDate(startTime+"000000", "yyyyMMddHHmmss");
                } else if(endTime.length() == 14) {
                    beginCreateTime = DateUtils.parseDate(startTime, "yyyyMMddHHmmss");
                }else{
                    withdrawRespPageResult.setRows(withdrawRespList);
                    withdrawRespPageResult.setTotal(total);
                    withdrawRespPageResult.setReturnCode(com.epaylinks.efps.common.util.page.PageResult.SUCCEE);
                    withdrawRespPageResult.setReturnMsg("开始时间格式有误");
                    return withdrawRespPageResult;
                }
            }
            if (endTime != null && !endTime.equals("")) {
                if(endTime.length() == 8){
                    endCreateTime = DateUtils.parseDate(endTime+"235959", "yyyyMMddHHmmss");
                }else if(endTime.length() == 14){
                    endCreateTime = DateUtils.parseDate(endTime, "yyyyMMddHHmmss");
                }else{
                    withdrawRespPageResult.setRows(withdrawRespList);
                    withdrawRespPageResult.setTotal(total);
                    withdrawRespPageResult.setReturnCode(com.epaylinks.efps.common.util.page.PageResult.SUCCEE);
                    withdrawRespPageResult.setReturnMsg("结束时间格式有误");
                    return withdrawRespPageResult;
                }
            }
            withdrawCountRecordList = withdrawCountRecordMapper.queryList(beginCreateTime,endCreateTime,beginRowNo,endRowNo);
            total = withdrawCountRecordMapper.countWithdrawList(beginCreateTime,endCreateTime,beginRowNo,endRowNo);
        }catch (Exception e){
            //抛出异常
            throw new AppException(PasCode.SYSTEM_EXCEPTION.code);
        }
        for(WithdrawCountRecord record : withdrawCountRecordList){
            WithdrawCountRecordResp withdrawResp = new WithdrawCountRecordResp();
            BeanUtils.copyProperties(record, withdrawResp);
            withdrawResp.setCountType("自动统计");
            withdrawResp.setInstitutionName(getInstitutionInfo(record.getInstitutionId()).getInstitutionName());
            withdrawResp.setChannelName(getPayChannel(record.getChannelId()).getChannelName());
            withdrawResp.setCountTime(DateFormatUtils.format(record.getCountTime(), "yyyy-MM-dd HH:mm:ss"));
            withdrawResp.setCreateTime(DateFormatUtils.format(record.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
            withdrawRespList.add(withdrawResp);
        }
        withdrawRespPageResult.setRows(withdrawRespList);
        withdrawRespPageResult.setTotal(total);
        withdrawRespPageResult.setReturnCode(com.epaylinks.efps.common.util.page.PageResult.SUCCEE);
        withdrawRespPageResult.setReturnMsg("查询成功");
        return withdrawRespPageResult;
    }

    /**
     * 查找机构信息
     */
    @Logable(businessTag = "WithdrawCountServiceImpl.getInstitutionInfo")
    public InstitutionResp getInstitutionInfo(Long institutionId){
        List<InstitutionResp> institutionRespList = new ArrayList<>();
        InstitutionResp institution  = new InstitutionResp();
        com.epaylinks.efps.common.util.page.PageResult<InstitutionResp> pageResult = null;
        if(institutionId == null){
            return  institution;
        }
        try{
            pageResult =  cumClient.query(null,null,null,null,1,200,null);
            if(pageResult.getRows() != null && pageResult.getRows().size()>0){
                institutionRespList = pageResult.getRows();
            }
        }catch (Exception e){
            //抛出异常
            throw new AppException(PasCode.SYSTEM_EXCEPTION.code);
        }
        for(InstitutionResp resp:institutionRespList){
            if(null != resp.getId() && resp.getId().equals(institutionId)){
                BeanUtils.copyProperties(resp, institution);
            }
        }
        if(institution == null || institution.getInstitutionName().isEmpty()){
            //抛出异常
            throw new AppException(PasCode.SYSTEM_EXCEPTION.code);
        }
        return institution;
    }
    /**
     * 查找渠道信息（渠道小类）
     */
    @Logable(businessTag = "WithdrawCountServiceImpl.getPayChannel")
    public PayChannel getPayChannel(Long payChannelId){
        PayChannel payChannel = new PayChannel();
        com.epaylinks.efps.common.util.page.PageResult<PayChannel> pageResult = null;
        if(payChannelId == null){
            return payChannel;
        }
        List<PayChannel> channelList = new ArrayList<>();
        try{
            pageResult = cumClient.queryPayChannelList(null,null);
            if(pageResult.getRows() != null && pageResult.getRows().size()>0){
                channelList = pageResult.getRows();
            }
        }catch (Exception e){
            //抛出异常
            throw new AppException(PasCode.SYSTEM_EXCEPTION.code);
        }
        for(PayChannel channel:channelList){
            if(null != channel.getId() && channel.getId().equals(payChannelId)){
                BeanUtils.copyProperties(channel,payChannel);
            }
        }
        if(channelList == null && channelList.size() == 0){
            //抛出异常
            throw new AppException(PasCode.SYSTEM_EXCEPTION.code);
        }
        return payChannel;
    }
}
