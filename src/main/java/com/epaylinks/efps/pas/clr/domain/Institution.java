package com.epaylinks.efps.pas.clr.domain;

import java.util.Date;

public class Institution {
    /**
     * 主键
     */
    private Long id;

    /**
     * 机构名称
     */
    private String institutionName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 修改人
     */
    private String updator;

    /**
     * 机构编码
     */
    private String institutionCode;

    /**
     * 对账方式: 0: 平台， 1：FTP
     */
    private String reconciliationMethod;

    /**
     * 对账周期: 0:T+0,  1:T+1
     */
    private String reconciliationCycle;

    /**
     * 合作机构应收账款会计科目ID
     */
    private Long accountsReceivableId;

    /**
     * 合作机构银行存款会计科目ID
     */
    private Long accountsBankId;

    /**
     * 合作机构联系人
     */
    private String contacts;

    /**
     * 合作机构联系电话
     */
    private String phone;

    /**
     * EPAY联系人
     */
    private String epayContacts;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getInstitutionName() {
        return institutionName;
    }

    public void setInstitutionName(String institutionName) {
        this.institutionName = institutionName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdator() {
        return updator;
    }

    public void setUpdator(String updator) {
        this.updator = updator;
    }

    public String getInstitutionCode() {
        return institutionCode;
    }

    public void setInstitutionCode(String institutionCode) {
        this.institutionCode = institutionCode;
    }

    public String getReconciliationMethod() {
        return reconciliationMethod;
    }

    public void setReconciliationMethod(String reconciliationMethod) {
        this.reconciliationMethod = reconciliationMethod;
    }

    public String getReconciliationCycle() {
        return reconciliationCycle;
    }

    public void setReconciliationCycle(String reconciliationCycle) {
        this.reconciliationCycle = reconciliationCycle;
    }

    public Long getAccountsReceivableId() {
        return accountsReceivableId;
    }

    public void setAccountsReceivableId(Long accountsReceivableId) {
        this.accountsReceivableId = accountsReceivableId;
    }

    public Long getAccountsBankId() {
        return accountsBankId;
    }

    public void setAccountsBankId(Long accountsBankId) {
        this.accountsBankId = accountsBankId;
    }

    public String getContacts() {
        return contacts;
    }

    public void setContacts(String contacts) {
        this.contacts = contacts;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEpayContacts() {
        return epayContacts;
    }

    public void setEpayContacts(String epayContacts) {
        this.epayContacts = epayContacts;
    }
}