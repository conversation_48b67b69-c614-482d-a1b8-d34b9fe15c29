package com.epaylinks.efps.pas.clr.service.impl;

import com.epaylinks.efps.pas.clr.dao.InstitutionMapper;
import com.epaylinks.efps.pas.clr.domain.Institution;
import com.epaylinks.efps.pas.clr.service.InstitutionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR> 5m
 * @Date : 2017/11/20 10:24
 * @Description :
 */
@Service
public class InstitutionServiceImpl implements InstitutionService {

    @Autowired
    private InstitutionMapper institutionMapper;

//    public int deleteByPrimaryKey(Long id){
//        return clrInstitutionMapper.deleteByPrimaryKey(id);
//    }
//
//    public int insert(com.epaylinks.efps.clr.channel.model.Institution record){
//        return clrInstitutionMapper.insert(record);
//    }
//
//    public int insertSelective(com.epaylinks.efps.clr.channel.model.Institution record){
//        return clrInstitutionMapper.insertSelective(record);
//    }
//
//    @Transactional(readOnly = true)
//    public com.epaylinks.efps.clr.channel.model.Institution selectByPrimaryKey(Long id){
//        return clrInstitutionMapper.selectByPrimaryKey(id);
//    }
//
//    @Transactional(readOnly = true)
//    public List<com.epaylinks.efps.clr.channel.model.Institution> selectBySelective(com.epaylinks.efps.clr.channel.model.Institution record){
//        return clrInstitutionMapper.selectBySelective(record);
//    }
//
//    @Transactional(readOnly = true)
//    public com.epaylinks.efps.clr.channel.model.Institution selectByChannelId(Long channelId){
//        return clrInstitutionMapper.selectByChannelId(channelId);
//    }
//
//    public int updateByPrimaryKeySelective(com.epaylinks.efps.clr.channel.model.Institution record){
//        return clrInstitutionMapper.updateByPrimaryKeySelective(record);
//    }
//
//    public int updateByPrimaryKey(com.epaylinks.efps.clr.channel.model.Institution record){
//        return clrInstitutionMapper.updateByPrimaryKey(record);
//    }

    /**
     * 查询范围内的机构号对应的机构
     */
    @Override
    public List<Institution> getInstitutionaByCodes(List<String> codes){
        return institutionMapper.getInstitutionaByCodes(codes);
    }

    @Override
    public List<Institution> selectBySelective(Institution institution){
        return institutionMapper.selectBySelective(institution);
    }
}
