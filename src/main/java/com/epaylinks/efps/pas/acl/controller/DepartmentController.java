package com.epaylinks.efps.pas.acl.controller;

import java.util.List;
import com.epaylinks.efps.common.log.CommonLogger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.pas.acl.controller.response.DepartmentResponse;
import com.epaylinks.efps.pas.acl.controller.response.DepartmentTreeResponse;
import com.epaylinks.efps.pas.acl.dto.DepartmentNode;
import com.epaylinks.efps.pas.acl.po.Department;
import com.epaylinks.efps.pas.acl.service.DepartmentService;
import com.epaylinks.efps.pas.common.PasCode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import springfox.documentation.annotations.ApiIgnore;

@RestController
@RequestMapping("/Department")
@Api(value = "DepartmentController", description = "部门管理")
public class DepartmentController {
	@Autowired
	private DepartmentService departmentService;

    @Autowired
    private CommonLogger logger;

    @ExceptionHandler(Exception.class)
    public CommonOuterResponse handleException(Exception ex) {
        if (ex instanceof AppException) {
            return CommonOuterResponse.fail(((AppException) ex).getErrorCode(),((AppException) ex).getErrorMsg());
        } else {
            logger.printMessage("DepartmentController error:" + ex.getMessage());
            logger.printLog(ex);
            return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code,PasCode.SYSTEM_EXCEPTION.message);
        }
    }

    @Validatable
    @Exceptionable
    @GetMapping("/all")
    @Logable(businessTag = "Department.getDepartmentTree")
    @ApiOperation(value = "部门管理列表", notes = "部门管理列表", httpMethod = "GET")
    public DepartmentTreeResponse getDepartmentTree() {
        DepartmentTreeResponse response = new DepartmentTreeResponse();
        List<DepartmentNode> departmentNodeList = departmentService.getDepartmentTree();
        response.setList(departmentNodeList);
        return response;
    }

    @Validatable
    @Exceptionable
    @GetMapping("/detail")
    @Logable(businessTag = "Department.viewDepartment")
    @ApiOperation(value = "查看部门", notes = "查看部门", httpMethod = "GET")
    public DepartmentResponse viewDepartment(@RequestParam Long id) {
        DepartmentResponse response = new DepartmentResponse();
        Department department = departmentService.viewDepartment(id);
        BeanUtils.copyProperties(department, response);
        return response;
    }

    @Validatable
    @Exceptionable
    @PostMapping("/add")
    @Logable(businessTag = "Department.addDepartment")
    @ApiOperation(value = "新增部门", notes = "新增部门", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "name", value = "部门名称", required = true, dataType = "String", paramType = "query", length = 25),
            @ApiImplicitParam(name = "parentId", value = "父级部门ID", required = false, dataType = "Long", paramType = "query", digit = true),
            @ApiImplicitParam(name = "remark", value = "备注", required = false, dataType = "String", paramType = "query", length = 100)
    })
    public CommonOuterResponse addDepartment(@ApiIgnore Department department,
                                             @RequestHeader(value = "x-userid",required = false) Long userId) {
        departmentService.addDepartment(department,userId);
        return CommonOuterResponse.success();
    }

    @Validatable
    @Exceptionable
    @PutMapping("/modify")
    @Logable(businessTag = "Department.modifyDepartment")
    @ApiOperation(value = "修改部门", notes = "修改部门", httpMethod = "PUT")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "部门ID", required = true, dataType = "Long", paramType = "query"),
            @ApiImplicitParam(name = "name", value = "部门名称", required = true, dataType = "String", paramType = "query", length = 25),
            @ApiImplicitParam(name = "parentId", value = "父级部门ID", required = false, dataType = "Long", paramType = "query", digit = true),
            @ApiImplicitParam(name = "remark", value = "备注", required = false, dataType = "String", paramType = "query", length = 100)
    })
    public CommonOuterResponse modifyDepartment(@ApiIgnore Department department,
                                                @RequestHeader(value = "x-userid",required = false) Long userId) {
        departmentService.modifyDepartment(department,userId);
        return CommonOuterResponse.success();
    }

    @Validatable
    @Exceptionable
    @DeleteMapping("/delete")
    @Logable(businessTag = "Department.deleteDepartment")
    @ApiOperation(value = "删除部门", notes = "删除部门", httpMethod = "DELETE")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "部门ID", required = true, dataType = "Long", paramType = "query")
    })
    public CommonOuterResponse deleteDepartment(@RequestParam Long id) {
        departmentService.deleteDepartment(id);
        return CommonOuterResponse.success();
    }
}
