package com.epaylinks.efps.pas.acl.service;

import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.epaylinks.efps.pas.acl.vo.SmsResponse;
import com.epaylinks.efps.pas.common.SmsSignUtil;
@Service
public class SMS {

	@Value("${smsVersion}")
	private String smsVesrion;

	@Value("${smsUserNo}")
	private String smsUserNo;

	@Value("${smsEncrypt}")
	private String smsEncrypt;
	@Autowired
	private SmsService smsService;
	
	public SmsResponse send(String mobileNo, String modelId, Map<String, String> map) {
		SmsSignUtil smsUtil = new SmsSignUtil();
		//用户ID
		smsUtil.setParameter("user_no", smsUserNo);
		//要发送到的手机号
		smsUtil.setParameter("mobile_no", mobileNo);
		//指定的短信模板ID，EPL002模板内容为：尊敬的用户，您的验证码为：[[checkcode]]，请勿告知他人。如非本人操作，请忽略本短信
		smsUtil.setParameter("model_id", modelId);
		//时间戳
		smsUtil.setParameter("timestamp", System.nanoTime());
		//设置MAP参数
		smsUtil.setParameter("data", map);
		//请求时的秘钥，每个用户唯一
		smsUtil.setParameter("encrypt", smsEncrypt);
		//使用封装好的代码发送请求，签名会在其中自动生成
		Map<String, Object> request = smsUtil.buildRequest();
		SmsResponse response = smsService.send(request);
		return response;
	}
	
    /**
     * 登录密码错误提醒（一般多次错误）
     * @param mobileNo
     * @param customercode
     * @return
     */
    public SmsResponse sendLoginWarningMsg(String mobileNo, String customercode) {
        String modelId = "YPL_MB_DLMMTX";
        Map<String, String> map = new HashMap<String, String>();
        map.put("customercode", customercode);
        return send(mobileNo, modelId, map);
    }
    
}

