package com.epaylinks.efps.pas.acl.service;

import java.util.List;
import java.util.Map;
import com.epaylinks.efps.pas.acl.dto.CompanyNode;
import com.epaylinks.efps.pas.acl.dto.DepartmentNode;
import com.epaylinks.efps.pas.acl.po.Company;
import com.epaylinks.efps.pas.acl.po.Department;

public interface CompanyService {
	Company queryById(Long companyId);

	void add(Company company);

	void addDepartment(Department department,Long userId);

	void updateDepartment(Department department,Long userId);

	void deleteDepartment(Long deptId);
	
	void update(Company company);

	void delete(Long companyId);

	/**
	 * 查询树
	 * 注：不返回根结点，而是第一层所有部门
	 */
	List<CompanyNode> getTree(Long userId);

	List<DepartmentNode> getDepartmentTree();

	/**
	 * 搜索分公司
	 * @param map
	 * @param userId
	 * @return
	 */
	List<Company> searchCompany(Map map, Long userId);
	
	/**
	 * 
	 * 查询所有叶子节点公司
	 * @return
	 */
    List<Company> queryLeafCompany();

	/**
	 * 查询用户所属分公司及下级分公司列表
	 * @param userId
	 * @return
	 */
	List<Long> queryCompanysByUserId(Long userId);



}
