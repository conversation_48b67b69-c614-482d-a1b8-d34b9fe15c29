package com.epaylinks.efps.pas.acl.controller;

import java.util.*;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.pas.acl.po.User;
import com.epaylinks.efps.pas.acl.service.UserService;
import com.epaylinks.efps.pas.common.LogService;
import com.epaylinks.efps.pas.common.PasConstants;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.oplog.OpLog;
import com.epaylinks.efps.common.oplog.OpLogHandle;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.pas.acl.controller.request.RoleRequest;
import com.epaylinks.efps.pas.acl.controller.response.RoleListResponse;
import com.epaylinks.efps.pas.acl.controller.response.RoleResponse;
import com.epaylinks.efps.pas.acl.po.Role;
import com.epaylinks.efps.pas.acl.service.PermService;
import com.epaylinks.efps.pas.acl.service.RoleService;
import com.epaylinks.efps.pas.common.PageResult;
import com.epaylinks.efps.pas.common.PasCode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping("/role")
@Api(value = "RoleController", description = "系统角色管理")
public class RoleController {
	@Autowired
	private RoleService roleService;
	
	@Autowired
    private PermService permService;

	@Autowired
	private UserService userService;

	@Autowired
	private LogService logService;


	@RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
	@ResponseBody
	public List<Role> getRoles() {
        return roleService.getRoles();
	}
	
	@RequestMapping(value="/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
	@ResponseBody
	public RoleResponse getRoleById(@PathVariable Long id) {
		RoleResponse response = new RoleResponse();
		Role role = this.roleService.getRoleById(id);
		if (role == null) {
			response.setReturnCode(PasCode.DATA_NOT_EXIST.code);
			response.setReturnMsg(PasCode.DATA_NOT_EXIST.message);
			return response;
		}
		BeanUtils.copyProperties(role, response);
		return response;
	}

	@RequestMapping(value ="/createRole", method = RequestMethod.POST)
	@Logable(businessTag = "createRole")
	@Exceptionable
	@Validatable
	@ApiOperation(value = "新增角色管理", notes = "新增角色管理", httpMethod = "POST")
    @OpLog(opModule = "系统管理-角色管理",opMethod = "新增角色")
	public RoleResponse create(
			@RequestBody RoleRequest roleRequest,
			@RequestHeader(value = "x-userid", required = true) Long userId) {
		RoleResponse response = new RoleResponse();
		Role role = new Role();
		role.setName(roleRequest.getName());
		role.setRemark(roleRequest.getRemark());
		role.setPermIds(roleRequest.getIds());
		role.setDataAuth(roleRequest.getDataAuth());
		role.setCompanyIds(roleRequest.getCompanyIds());
		role.setOperateTime(new Date());
		User user = userService.selectUserById(userId);
		if(user != null){
			role.setOperater(user.getRealName());
		}
		try {
			roleService.create(role);
			String permNames = permService.queryPermNamesById(roleRequest.getIds());
            OpLogHandle.setOpContent("新增角色："+roleRequest.getName() +"。角色菜单: " + permNames);   
		} catch (Exception e) {
			logService.printLog(e);
			e.printStackTrace();
			response = new RoleResponse();
			if (e instanceof AppException) {
				response.setReturnCode(((AppException) e).getErrorCode());
				response.setReturnMsg(((AppException) e).getErrorMsg());
			} else {
				response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
				response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
			}
			return response;
		}
		return response;
	}

	@RequestMapping(value ="/updateRole", method = RequestMethod.POST)
	@Logable(businessTag = "RoleController.updateRole")
	@Exceptionable
	@Validatable
	@ApiOperation(value = "修改角色信息", notes = "修改角色信息", httpMethod = "POST")
    @OpLog(opModule = "系统管理-角色管理",opMethod = "修改角色")
	public RoleResponse update(
			@RequestBody RoleRequest roleRequest,
			@RequestHeader(value = "x-userid", required = true) Long userId) {
		RoleResponse response = new RoleResponse();
		Role role = new Role();
		role.setUid(roleRequest.getUid());
		role.setName(roleRequest.getName());
		role.setRemark(roleRequest.getRemark());
		role.setPermIds(roleRequest.getIds());
		role.setDataAuth(roleRequest.getDataAuth());
		role.setCompanyIds(roleRequest.getCompanyIds());
		role.setOperateTime(new Date());
		User user = userService.selectUserById(userId);
		if(user != null){
			role.setOperater(user.getRealName());
		}
		try {
		    Role record = roleService.selectById(role.getUid());
			if ((Objects.equals(role.getUid(),10L) || Objects.equals(role.getUid(),11L))
					&& (!Objects.equals(role.getName(),record.getName()) || !Objects.equals(role.getDataAuth(),record.getDataAuth()))) { // 业务员/分公司管理员角色不能修改名称和数据查看权限
				throw new AppException(PasCode.ERROR_PARAM.code,Objects.equals(role.getUid(),11L) ? "业务员" : "分公司管理员" + "角色不允许修改名称与数据查看权限");
			}
			if ((Objects.equals(role.getUid(),10L) || Objects.equals(role.getUid(),11L)) && StringUtils.isNotBlank(role.getCompanyIds())) { // 业务员/分公司管理员角色不能选择分公司数据权限
				throw new AppException(PasCode.ERROR_PARAM.code,Objects.equals(role.getUid(),11L) ? "业务员" : "分公司管理员" + "不能选择分公司数据权限");
			}
			if(PasConstants.RoleDataAuth.CUSTOMIZE.code.equals(role.getDataAuth()) && StringUtils.isBlank(role.getCompanyIds())){
				throw new AppException(PasCode.DATA_NOT_EXIST.code, "自定义权限角色请选择分公司");
			}
            String roleName = record != null ? record.getName() : "";
			roleService.update(role);
			if (!roleName.equals(roleRequest.getName())) {
			    roleName = roleName + "(改为：" + roleRequest.getName()+")";
			}
            String permNames = permService.queryPermNamesById(roleRequest.getIds());
            OpLogHandle.setOpContent("修改角色："+roleName+"。角色菜单: " + permNames);  
		} catch (Exception e) {
			response = new RoleResponse();
			if (e instanceof AppException) {
				response.setReturnCode(((AppException) e).getErrorCode());
				response.setReturnMsg(((AppException) e).getErrorMsg());
			} else {
				response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
				response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
			}
			return response;
		}
		return response;
	}

    @DeleteMapping("deleteRole")
	@Logable(businessTag = "deleteRole")
	@Exceptionable
	@Validatable
	@ApiOperation(value = "删除角色信息", notes = "删除角色信息", httpMethod = "DELETE")
    @OpLog(opModule = "系统管理-角色管理",opMethod = "删除角色")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "uid", value = "uid", required = true, dataType = "Long", length = 20, paramType = "query")})
	public RoleResponse delete(@RequestParam Long uid) {
		RoleResponse response = new RoleResponse();
		try {
		    Role role = roleService.selectById(uid);
		    String roleName = role != null ? role.getName() : null;
			roleService.delete(uid);
            OpLogHandle.setOpContent("删除角色："+ roleName);   
		} catch (Exception e) {
			response = new RoleResponse();
			if (e instanceof AppException) {
				response.setReturnCode(((AppException) e).getErrorCode());
				response.setReturnMsg(((AppException) e).getErrorMsg());
			} else {
				response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
				response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
			}
			return response;
		}
		return response;
	}

	@RequestMapping(value ="/selectList", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
	@Logable(businessTag = "selectList")
	@Exceptionable
	@Validatable
	@ApiOperation(value = "查询角色信息", notes = "查询角色信息", httpMethod = "GET")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "name", value = "名称", required = false, dataType = "String", length =50, paramType = "query"),
			@ApiImplicitParam(name = "dataAuth", value = "数据权限范围（0：全量，1：非全量）", required = false, dataType = "String", length =50, paramType = "query"),
			@ApiImplicitParam(name = "beginTime", value = "开始时间,yyyyMMddhhmmss", required = false, dataType = "String", paramType = "query"),
			@ApiImplicitParam(name = "endTime", value = "结束时间,yyyyMMddhhmmss", required = false, dataType = "String", paramType = "query"),
			@ApiImplicitParam(name = "pageNum", value = "查询开始页", required = true, dataType = "int", paramType = "query"),
			@ApiImplicitParam(name = "pageSize", value = "查询的页面大小", required = true, dataType = "int", paramType = "query" )})
	public PageResult<Role> selectByPage(@RequestParam(value = "name", required = false) String name,
										 @RequestParam(value = "dataAuth", required = false) String dataAuth,
										 @RequestParam(value = "beginTime", required = false) String beginTime,
										 @RequestParam(value = "endTime", required = false) String endTime,
										 @RequestParam(value = "pageNum", required = true) Integer pageNum,
										 @RequestParam(value = "pageSize", required = true) Integer pageSize) {
		PageResult<Role> page = new PageResult<>();
		try {
			  Map map = new HashMap();
			  map.put("name", name);
			  map.put("dataAuth",dataAuth);
			  map.put("beginTime", beginTime);
			  map.put("endTime", endTime);
			  List<Role> roleNoPage = roleService.selectRolesBySelective(map);
			  if (pageNum == null) {
				  pageNum = 1;
			  }
			  if (pageSize == null) {
				  pageSize = roleNoPage.size();
			  }

			  int endRowNo = pageNum * pageSize;
			  int beginRowNo = (pageNum - 1) * pageSize + 1;


			  map.put("beginRowNo", beginRowNo);
			  map.put("endRowNo", endRowNo);

			  List<Role> rolePage = roleService.selectRolesByPage(map);

			  page.setRows(rolePage);
			  page.setTotal(roleNoPage.size());
		  } catch (Exception e) {
			page = new PageResult<Role>();
			if (e instanceof AppException) {
				page.setReturnCode(((AppException) e).getErrorCode());
				page.setReturnMsg(((AppException) e).getErrorMsg());
			} else {
				page.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
				page.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
			}
			return page;
		}
		return page;
	}

	@RequestMapping(value ="/selectAllRoles", method = RequestMethod.GET)
	@Logable(businessTag = "selectAllRoles")
	@Exceptionable
	@Validatable
	@ApiOperation(value = "获取角色下拉框内容", notes = "获取角色下拉框内容", httpMethod = "GET")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "dataAuth", value = "数据权限范围（不传：所有角色；0：全量；1：自定义；2：本部门；3：本人）", required = false, dataType = "String", length = 50, paramType = "query")
	})
	public RoleListResponse selectAllRoles(@RequestParam(required = false) String dataAuth) {
		RoleListResponse response = new RoleListResponse();
		List<Role> list = roleService.selectAllRoles(dataAuth);
		if (list == null) {
			response.setReturnCode(PasCode.DATA_NOT_EXIST.code);
			response.setReturnMsg(PasCode.DATA_NOT_EXIST.message);
			return response;
		}
		response.setRoleList(list);
		return response;
	}

	@RequestMapping(value ="/selectById", method = RequestMethod.GET)
	@Logable(businessTag = "selectById")
	@Exceptionable
	@Validatable
	@ApiOperation(value = "查询角色信息", notes = "查询角色信息", httpMethod = "GET")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "uid", value = "uid", required = false, dataType = "Long", length =20, paramType = "query")})
	public RoleResponse selectById(@RequestParam(value = "uid", required = true) Long uid) {
		RoleResponse response = new RoleResponse();
		try {
			Role role = this.roleService.selectById(uid);
			if (role == null) {
				response.setReturnCode(PasCode.DATA_NOT_EXIST.code);
				response.setReturnMsg(PasCode.DATA_NOT_EXIST.message);
				return response;
			}
			BeanUtils.copyProperties(role, response);

		} catch (Exception e) {
			response = new RoleResponse();
			if (e instanceof AppException) {
				response.setReturnCode(((AppException) e).getErrorCode());
				response.setReturnMsg(((AppException) e).getErrorMsg());
			} else {
				response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
				response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
			}
			return response;
		}
		return response;
	}

	@GetMapping("/getRoleInfo")
	@ApiOperation(value = "根据用户查询角色列表", notes = "根据用户查询角色列表", httpMethod = "GET")
	public CommonOuterResponse getRoleInfo(@RequestParam("userId") Long userId) {
		CommonOuterResponse response = new CommonOuterResponse();
		try {
			response = CommonOuterResponse.success(roleService.getRoleListByUId(userId));
		} catch (Exception e) {
			System.out.println("获取角色错误：" + e.getMessage());
			logService.printLog("获取角色错误：" + e.getMessage());
			response = new RoleResponse();
			if (e instanceof AppException) {
				response.setReturnCode(((AppException) e).getErrorCode());
				response.setReturnMsg(((AppException) e).getErrorMsg());
			} else {
				response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
				response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
			}
		}
		return response;
	}
}
