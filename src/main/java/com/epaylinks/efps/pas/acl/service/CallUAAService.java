package com.epaylinks.efps.pas.acl.service;

import com.epaylinks.efps.common.config.DefaultFeignConfiguration;
import com.epaylinks.efps.common.util.Param;
import com.epaylinks.efps.pas.acl.dto.BusinessToken;
import com.epaylinks.efps.pas.acl.dto.SaveServiceRoleAuthDTO;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.*;


@FeignClient(value = "UAA" , configuration = DefaultFeignConfiguration.class)
public interface CallUAAService {

	@PostMapping("Token")
	String applyToken(
			@RequestParam("businessType") String businessType,
			@RequestParam("userId") String userId,
			@RequestParam("customerCode") String customerCode,
			@RequestParam(value = "roles", required = false) String roles,// 逗号隔开
			@RequestParam(value = "extra", required = false) String extra);
	
    /**
     * 校验令牌
     * @param token
     * @return
     */
    @GetMapping(value = "/Token")
    public BusinessToken verifyToken(@RequestParam("token") String token);
    
//	@PostMapping("VerificationController/VerificationCode")
//	String validateCode(@RequestParam("authcode") String authcode);

	@PostMapping("ServiceRoleAuth/save")
	String saveServiceRoleAuth(@RequestBody SaveServiceRoleAuthDTO dto);

	/**
	 * 校验验证码接口
	 * @param uid
	 * @param authcode
	 * @param serviceCode
	 * @return
	 */
	@RequestMapping(value = "/VerificationController/VerificationCode",method = RequestMethod.POST)
	public String validateCode(@RequestParam("uid") String uid, @RequestParam("authCode") String authcode, @RequestParam("serviceCode") String serviceCode);

	//发送短信验证码
	@RequestMapping(value = "/VerificationController/ApplyVerificationCode",method = RequestMethod.POST)
	public String sendVerifyCode(@RequestParam("uid") String uid, @RequestParam("serviceCode") String serviceCode);

	@PostMapping(value = "/VerificationController/ApplyVerificationCode")
	public String sendCode(@RequestParam(value = "uid",required = false) String uid,
						   @RequestParam(value = "serviceCode",required = true) String serviceCode,
						   @RequestParam(value = "modelCode",required = false) String modelCode);
	
//	/**
//	 * 根据KeyType 解密接口（RSA）
//	 * @param map
//	 * @return
//	 */
//	@PostMapping("/decryptByKeyType")
//	public Map<String, String> decryptByKeyType(@RequestBody Map<String ,String > map, @RequestParam("keyType") String keyType);

}
