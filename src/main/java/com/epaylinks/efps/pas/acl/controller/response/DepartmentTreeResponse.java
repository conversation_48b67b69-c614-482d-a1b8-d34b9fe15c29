package com.epaylinks.efps.pas.acl.controller.response;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.pas.acl.dto.DepartmentNode;

import java.util.List;

/**
 * 部门树 - 响应类
 */
public class DepartmentTreeResponse extends CommonOuterResponse {

    private List<DepartmentNode> list;

    public List<DepartmentNode> getList() {
        return list;
    }

    public void setList(List<DepartmentNode> list) {
        this.list = list;
    }
}
