package com.epaylinks.efps.pas.acl.controller;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.pas.acl.po.User;
import com.epaylinks.efps.pas.common.PasConstants;
import ocx.GetRandom;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.util.IPAddressUtils;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.pas.acl.controller.response.LoginResponse;
import com.epaylinks.efps.pas.acl.service.GraphicService;
import com.epaylinks.efps.pas.acl.service.SessionService;
import com.epaylinks.efps.pas.common.LogService;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.pas.service.LoginLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

@RestController
@RefreshScope
@RequestMapping("/login")
@Api(value = "LoginController", description = "登录管理")
public class LoginController {

    @Autowired
    private SessionService sessionService;

    @Autowired
    private GraphicService graphicService;
    
    @Autowired
    private LoginLogService loginLogService;
    
    @Autowired
    private LogService logService;

    @Autowired
    private CommonLogger logger;

    @PostMapping("v2/login")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "运营用户登录V2", notes = "运营用户登录V2", httpMethod = "POST")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "name", value = "用户名", required = true, dataType = "String", paramType = "query"),
        @ApiImplicitParam(name = "passWord", value = "密码(RAS加密)", required = true, dataType = "String", paramType = "query"),
        @ApiImplicitParam(name = "uid", value = "图形验证码uid", required = true, dataType = "String", paramType = "query"),
        @ApiImplicitParam(name = "graphicCode", value = "图形验证码", required = true, dataType = "String", paramType = "query")
    })
    public LoginResponse loginV2(
                               @RequestParam("name") String name,
                               @RequestParam("passWord") String passWord,
                               @RequestParam("uid") String uid,
                               @RequestParam("graphicCode") String graphicCode,
                               HttpServletRequest request,
                               HttpServletResponse response) {
        
        LoginResponse loginResponse = new LoginResponse();
        try {
            if (!graphicService.validate(uid, graphicCode)) {
                loginResponse.setReturnCode(PasCode.GRAPHIC_CODE_INVALID.code);
                loginResponse.setReturnMsg(PasCode.GRAPHIC_CODE_INVALID.message);
                // 登录失败日志 增加验证码错误日志 2020.11.25
                loginLogService.savePasLoginLog(name, "0", IPAddressUtils.getRealIp(request), loginResponse.getReturnMsg(), false);
                return loginResponse; // 验证码错误不应该记录登录日志
            }
            passWord = sessionService.decryptPwd(passWord);
            loginResponse = sessionService.login(name, passWord);
            
            // 登录成功日志
            loginLogService.savePasLoginLog(name, "1", IPAddressUtils.getRealIp(request), "登录成功", false);
       
        } catch (Exception e) {
            boolean pwdErrow = false;
            if (e instanceof AppException) {
                if (PasCode.LOGIN_PWD_ERORR.code.equals(((AppException) e).getErrorCode())) {
                    pwdErrow = true;
                }
                loginResponse.setReturnCode(((AppException) e).getErrorCode());
                loginResponse.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                logger.printMessage("登录错误：" + e.getMessage());
                logger.printLog(e);
                loginResponse.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                loginResponse.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
            // 登录失败日志
            loginLogService.savePasLoginLog(name, "0", IPAddressUtils.getRealIp(request), loginResponse.getReturnMsg(), pwdErrow);
            
            return loginResponse;
        }

        // token和userName写入Cookie  前端特殊要求，待确定是否可去掉
        Cookie cookie = new Cookie("token", loginResponse.getToken());
        cookie.setPath("/");
//        cookie.setHttpOnly(true);
        cookie.setSecure(true);
        response.addCookie(cookie);
        Cookie cookie2 = new Cookie("userName", loginResponse.getUserName());
//        cookie2.setHttpOnly(true);
        cookie2.setSecure(true);
        response.addCookie(cookie2);

        return loginResponse;
    }
    
    

    /**
     * 单点登录通过用户名获取登录信息
     */
    @PostMapping("sso/getLoginResponse")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "单点登录通过token获取登录信息", notes = "单点登录通过token获取登录信息(CUST调用)", httpMethod = "POST")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "token", value = "前系统登录token", required = true, dataType = "String", paramType = "query"),

    })
    public LoginResponse getLoginResponse(
                               @RequestParam("token") String token) {
        
        LoginResponse loginResponse = new LoginResponse();
        try {
            loginResponse = sessionService.ssoLogin(token);
        } catch (Exception e) {
            if (e instanceof AppException) {
                loginResponse.setReturnCode(((AppException) e).getErrorCode());
                loginResponse.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                logService.printLog(e);
                loginResponse.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                loginResponse.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
        }
        return loginResponse;

    }

    @PostMapping("/businessLogin")
    @ApiOperation(value = "PC版业务员进件平台登录",notes = "PC版业务员进件平台登录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "name",value = "用户名",required = true,dataType = "String",paramType = "query"),
            @ApiImplicitParam(name = "password",value = "密码（控件加密sm2）",required = true,dataType = "String",paramType = "query"),
            @ApiImplicitParam(name = "mcryptKey",value = "32位随机数（sm2加密，无控件加密时不传）",required = false,dataType = "String",paramType = "query"),
            @ApiImplicitParam(name = "verifyCode",value = "短信验证码",required = true,dataType = "String",paramType = "query")
    })
    @Logable(businessTag = "LoginController.businessLogin")
    public LoginResponse businessLogin(@RequestParam("name") String name,
                                       @RequestParam("password") String password,
                                       @RequestParam(value = "mcryptKey",required = false) String mcryptKey,
                                       @RequestParam("verifyCode") String verifyCode,
                                       HttpServletRequest request,
                                       HttpServletResponse response) {
        LoginResponse loginResponse = new LoginResponse();
        try {
            // 密码控件解密 sm2
//            password = sessionService.decryptPassword(password,mcryptKey);
            if (StringUtils.isNotBlank(mcryptKey)) {
                password = sessionService.decryptSM2(password,mcryptKey);
            } else {
                password = sessionService.decryptPwd(password);
            }
            // 检验登录
            loginResponse = sessionService.checkBusinessLogin(name,password,verifyCode);
            // 登录成功日志
            loginLogService.savePasLoginLog(name, "1", IPAddressUtils.getRealIp(request), "PC业务员进件平台-登录成功", false);
            // 写入cookie（前端使用）
            Cookie tokenCookie = new Cookie("token", loginResponse.getToken());
            tokenCookie.setPath("/");
            tokenCookie.setSecure(true);
            response.addCookie(tokenCookie);
            Cookie userNameCookie = new Cookie("userName", loginResponse.getUserName());
            userNameCookie.setSecure(true);
            response.addCookie(userNameCookie);
        } catch (Exception e) {
            logger.printLog(e);
            boolean pwdErrorFlag = false;
            if (e instanceof AppException) {
                if (PasCode.LOGIN_PWD_ERORR.code.equals(((AppException) e).getErrorCode())) {
                    // 当前版本密码错误不计入次数和锁定
//                    pwdErrorFlag = true;
                }
                loginResponse.setReturnCode(((AppException) e).getErrorCode());
                loginResponse.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                loginResponse.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                loginResponse.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
            // 登录失败日志
            loginLogService.savePasLoginLog(name, "0", IPAddressUtils.getRealIp(request), "PC业务员进件平台-" + loginResponse.getReturnMsg(), pwdErrorFlag);
        }
        return loginResponse;
    }

    @PostMapping("/sendBusinessLoginSMSCaptcha")
    @ApiOperation(value = "业务员登录发送短信验证码",notes = "业务员登录发送短信验证码")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "name",value = "用户名",required = true,dataType = "String",paramType = "query"),
            @ApiImplicitParam(name = "password",value = "密码（控件加密sm2）",required = true,dataType = "String",paramType = "query"),
            @ApiImplicitParam(name = "mcryptKey",value = "32位随机数（sm2加密，无控件加密时不传）",required = false,dataType = "String",paramType = "query"),
            @ApiImplicitParam(name = "uid",value = "图形验证码uid",required = true,dataType = "String",paramType = "query"),
            @ApiImplicitParam(name = "graphicCode",value = "图形验证码",required = true,dataType = "String",paramType = "query")
    })
    @Logable(businessTag = "LoginController.sendBusinessLoginSMSCaptcha")
    public CommonOuterResponse sendBusinessLoginSMSCaptcha(@RequestParam("name") String name,
                                                           @RequestParam("password") String password,
                                                           @RequestParam(value = "mcryptKey",required = false) String mcryptKey,
                                                           @RequestParam(value = "uid") String uid,
                                                           @RequestParam(value = "graphicCode") String graphicCode) {
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            // 业务员角色校验
            User user = sessionService.checkBusinessRole(name);
            // 图形验证码校验
            if (!graphicService.validate(uid, graphicCode)) {
                throw new AppException(PasCode.GRAPHIC_CODE_INVALID.code,PasCode.GRAPHIC_CODE_INVALID.message);
            }
            // 密码控件解密 sm2
//            password = sessionService.decryptPassword(password,mcryptKey);
            if (StringUtils.isNotBlank(mcryptKey)) {
                password = sessionService.decryptSM2(password,mcryptKey);
            } else {
                password = sessionService.decryptPwd(password);
            }
            // 登录密码校验
            sessionService.checkPassword(user,password,false);
            // 发送短信验证码
            sessionService.sendSMS(user.getMobile(), PasConstants.serviceCode.BUSINESSLOGIN.code,name,"业务员登录");
        } catch (AppException e) {
            response.setReturnCode(e.getErrorCode());
            response.setReturnMsg(e.getErrorMsg());
        } catch (Exception e) {
            logger.printMessage("业务员登录发送短信验证码error：" + e.getMessage());
            logger.printLog(e);
            response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
            response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
        }
        return response;
    }

    @ApiOperation(value = "获取控件加密随机数", notes = "获取加密随机数", httpMethod = "GET")
    @GetMapping("/generateString")
    public CommonOuterResponse generateString(@RequestParam(value = "len",required = false,defaultValue = "32") Integer len) {
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            String sKey = GetRandom.generateString(len);
            return CommonOuterResponse.success("success",sKey);
        } catch (Exception e) {
            response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
            response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
        }
        return response;
    }
}
