package com.epaylinks.efps.pas.acl.service;

import java.util.*;
import com.alibaba.fastjson.JSON;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.pas.acl.dao.*;
import com.epaylinks.efps.pas.acl.dto.UserRoleDataAuthDTO;
import com.epaylinks.efps.pas.acl.po.*;
import com.epaylinks.efps.pas.common.PasConstants;
import com.epaylinks.efps.pas.pas.dao.RoleCompanyMapper;
import com.epaylinks.efps.pas.pas.domain.RoleCompany;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.pas.acl.dto.SaveServiceRoleAuthDTO;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.pas.common.PasConstant;

@Service
public class RoleService {

	@Autowired
	private RoleMapper roleMapper;

    @Autowired
    private RoleUserMapper roleUserMapper;

	@Autowired
	private RolePermMapper rolePermMapper;

	@Autowired
	private RoleCompanyMapper roleCompanyMapper;

	@Autowired
	private CompanyMapper companyMapper;
	
	@Autowired
	private PermServiceMapper permServiceMapper;

	@Autowired
	private SequenceService sequenceService;

	@Autowired
	private CallUAAService callUAAService;

	@Autowired
	private RoleService self;

	@Autowired
	private CommonLogger logger;

	public List<UserRoleDataAuthDTO> selectRoleDataAuthByUserId(Long userId,String dataAuth) {
		 return roleMapper.selectRoleDataAuthByUserId(userId,dataAuth);
	}

	@Logable(businessTag = "RoleServiceImpl.getRoles")
	public List<Role> getRoles() {
		return roleMapper.queryList();
	}

	@Logable(businessTag = "RoleServiceImpl.getRoleById")
	public Role getRoleById(Long uid) {
		return roleMapper.selectByPrimaryKey(uid);
	}

	@Transactional
	@Logable(businessTag = "RoleServiceImpl.create")
	public void create(Role role) {
		// 判断是否存在同名角色
		boolean isExistRoleName = roleMapper.checkExistOfRoleName(role.getName());
		if (isExistRoleName) {
			throw new AppException(PasCode.ROLE_NAME_EXIST.code, PasCode.ROLE_NAME_EXIST.message);
		}
		// 新增角色
		Long roleId = sequenceService.nextValue("pas_role");
		role.setUid(roleId);
		role.setCode(String.valueOf(sequenceService.nextValue("pas_role_code")));
		roleMapper.insert(role);

		if("1".equals(role.getDataAuth()) && StringUtils.isBlank(role.getCompanyIds())){
			throw new AppException(PasCode.DATA_NOT_EXIST.code, "自定义权限角色请选择分公司");
		}
		//新增角色数据权限对应分公司
		if("1".equals(role.getDataAuth()) && StringUtils.isNotBlank(role.getCompanyIds())){
			List<String> companyIds = Arrays.asList(role.getCompanyIds().split(","));
			for (int i = 0; i < companyIds.size(); i++) {
				if (StringUtils.isNotBlank(companyIds.get(i))) {
					Long companyId = Long.valueOf(companyIds.get(i));
					Company company = companyMapper.selectByPrimaryKey(companyId);
					if(company == null){
						throw new AppException(PasCode.COMPANY_NAME_EXIST.code, companyId+"分公司不存在");
					}
					RoleCompany roleCompany = new RoleCompany();
					roleCompany.setRoleId(roleId);
					roleCompany.setRoleName(role.getName());
					roleCompany.setCompanyId(companyId);
					roleCompany.setCompanyName(company.getName());
					roleCompanyMapper.insert(roleCompany);
				}
			}
		}

		// 新增角色对应权限
		if(StringUtils.isNotBlank(role.getPermIds())){
			List<String> permIds = Arrays.asList(role.getPermIds().split(","));
			for (int i = 0; i < permIds.size(); i++) {
				if (StringUtils.isNotBlank(permIds.get(i))) {
					RolePerm rolePerm = new RolePerm();
					rolePerm.setId(sequenceService.nextValue("pas_role_perm"));
					rolePerm.setPermId(Long.valueOf(permIds.get(i)));
					rolePerm.setRoleId(role.getUid());
					rolePermMapper.insert(rolePerm);
				}
			}
			self.syncRolePerm(role.getCode(), role.getPermIds());
		}
	}

	@Transactional
	@Logable(businessTag = "RoleServiceImpl.update")
	public void update(Role role) {
		Long roleId = role.getUid();
		// 1.数据校验
		// 判断是否存在同名角色
		boolean isExistRoleName = roleMapper.checkExistOfRoleNameWhenModify(role.getName(), roleId);
		if (isExistRoleName) {
			throw new AppException(PasCode.ROLE_NAME_EXIST.code, PasCode.ROLE_NAME_EXIST.message);
		}
		// 判断当前设置角色的用户，所对应的角色列表是否与当前设置有冲突
		List<Long> associateUserIdList = roleUserMapper.selectUerIdsByRoleId(roleId);
		associateUserIdList.forEach(userId -> {
			List<UserRoleDataAuthDTO> dataAuthDTOS = roleMapper.selectRoleDataAuthByUserId(userId,role.getDataAuth());
			if (!dataAuthDTOS.isEmpty()) {
				logger.printMessage("dataAuthDTOS:" + JSON.toJSONString(dataAuthDTOS));
				throw new AppException(PasCode.UPDATE_ROLE_FIAL.code,PasCode.UPDATE_ROLE_FIAL.message + "：已被使用的角色不可跨数据权限类型修改");
			}
		});
		// 2.更新角色信息
		Role oldRole = roleMapper.selectByPrimaryKey(roleId);
		oldRole.setName(role.getName());
		oldRole.setRemark(role.getRemark());
		oldRole.setPermIds(role.getPermIds());
		oldRole.setOperater(role.getOperater());
		oldRole.setOperateTime(role.getOperateTime());
		roleMapper.updateByPrimaryKey(oldRole);

		// 更新角色与分公司的关联（先全删后全加）
		roleCompanyMapper.deleteByRoleId(roleId);
		if(PasConstants.RoleDataAuth.CUSTOMIZE.code.equals(role.getDataAuth())) {
			List<String> companyIds = Arrays.asList(role.getCompanyIds().split(","));
			for (int i = 0; i < companyIds.size(); i++) {
				if (StringUtils.isNotBlank(companyIds.get(i))) {
					Long companyId = Long.valueOf(companyIds.get(i));
					Company company = companyMapper.selectByPrimaryKey(companyId);
					if(company == null){
						throw new AppException(PasCode.COMPANY_NAME_EXIST.code, companyId+"分公司不存在");
					}
					RoleCompany roleCompany = new RoleCompany();
					roleCompany.setRoleId(roleId);
					roleCompany.setRoleName(role.getName());
					roleCompany.setCompanyId(companyId);
					roleCompany.setCompanyName(company.getName());
					roleCompanyMapper.insert(roleCompany);
				}
			}
			/*
			//查询该角色的用户是否包含多角色
			List<Long> userIdList = roleUserMapper.selectUerIdsByRoleId(roleId);
			if(userIdList != null && userIdList.size() >0){
				for(Long userId : userIdList){
					List<RoleUser> tmpRoleList = roleUserMapper.selectRoleIdsByUser(userId);
					if(tmpRoleList != null && tmpRoleList.size() > 1){
						throw new AppException(PasCode.COMPANY_NAME_EXIST.code, "该分公司权限角色存在用户分配了多角色");
					}
				}
			}
			 */
		}

		// 3.更新角色与权限的关联（先全删后全加）
		rolePermMapper.deleteByRoleId(roleId);
		if (StringUtils.isNotBlank(role.getPermIds())) {
			List<String> permIds = Arrays.asList(role.getPermIds().split(","));
			for (int i = 0; i < permIds.size(); i++) {
				RolePerm rolePerm = new RolePerm();
				rolePerm.setId(sequenceService.nextValue("pas_role_perm"));
				rolePerm.setPermId(Long.valueOf(permIds.get(i)));
				rolePerm.setRoleId(roleId);
				rolePermMapper.insert(rolePerm);
			}
		}
		// 4.同步到UAA
		self.syncRolePerm(oldRole.getCode(), role.getPermIds());
	}

	@Transactional
	@Logable(businessTag = "RoleServiceImpl.delete")
	public void delete(Long roleId) {
        // 删除角色
		if(PasConstant.MarketRole.COMPANY.code.equals(String.valueOf(roleId))
				|| PasConstant.MarketRole.SALES.code.equals(String.valueOf(roleId))) {
			throw new AppException(PasCode.ROLE_CAN_NOT_DELETE.code, PasCode.ROLE_CAN_NOT_DELETE.message);
		}
        int n = roleMapper.deleteByPrimaryKey(roleId);
        if (n != 1) {
            throw new AppException(PasCode.ROLE_DELETE_FAIL.code, PasCode.ROLE_DELETE_FAIL.message);
        }
        // 删除角色用户关联
        roleUserMapper.deleteByRoleId(roleId);
        // 删除角色权限关联
        rolePermMapper.deleteByRoleId(roleId);
	}

	@Logable(businessTag = "RoleServiceImpl.addPermissons")
	public int addPermissons(Long roleId, List<Long> permissionIds) {
		Map<String, Object> paramMap = new HashMap<>();
		paramMap.put("roleId", roleId);
		paramMap.put("permissionIds", permissionIds);
		return roleMapper.addPermissons(paramMap);
	}

	@Logable(businessTag = "RoleServiceImpl.selectById")
	public Role selectById(Long uid) {
		Role role = roleMapper.selectByPrimaryKey(uid);
		if(role==null)
			throw new AppException(PasCode.DATA_NOT_EXIST.code, PasCode.DATA_NOT_EXIST.message);
		List<Long> list = rolePermMapper.selectByRoleId(role.getUid());
		if(list.size()>0){
			StringBuffer sb = new StringBuffer();
			for (int i = 0; i < list.size(); i++) {
				sb.append(",");
				sb.append(java.lang.String.valueOf(list.get(i)));
			}
			role.setPermIds(sb.substring(1));
		}
		List<Long> companyIdList = roleCompanyMapper.selectCompanyIdByRoleId(role.getUid());
		if(companyIdList != null && companyIdList.size() > 0){
			StringBuffer sb = new StringBuffer();
			for (int i = 0; i < companyIdList.size(); i++) {
				sb.append(",");
				sb.append(java.lang.String.valueOf(companyIdList.get(i)));
			}
			role.setCompanyIds(sb.substring(1));
		}
		return role;
	}

	@Logable(businessTag = "RoleServiceImpl.selectByPage")
	public List<Role> selectByPage(Map map) {
		return roleMapper.selectByPage(map);
	}

	@Logable(businessTag = "RoleServiceImpl.selectBySelective")
	public List<Role> selectBySelective(Role role) {
		return roleMapper.selectBySelective(role);
	}

	public List<Role> selectAllRoles(String dataAuth) {
		return roleMapper.selectAllRoles(dataAuth);
	}

	@Logable(businessTag = "RoleServiceImpl.selectRolesByPage")
	public List<Role> selectRolesByPage(Map map) {
		return roleMapper.selectRolesByPage(map);
	}

	@Logable(businessTag = "RoleServiceImpl.selectRolesBySelective")
	public List<Role> selectRolesBySelective(Map map) {
		return roleMapper.selectRolesBySelective(map);
	}

	@Logable(businessTag = "RoleServiceImpl.syncRolePerm")
	public void syncRolePerm(String roleCode, String permIds) {
		List<String> permIdList = Arrays.asList(permIds.split(","));
		List<String> serviceCodeList = self.selectPermServiceByParam(permIdList);
		SaveServiceRoleAuthDTO dto = new SaveServiceRoleAuthDTO();
		dto.setRoleCode(roleCode);
		dto.setServiceCodeList(serviceCodeList);
		try {
			callUAAService.saveServiceRoleAuth(dto);
		} catch (Exception e) {
			throw new AppException(PasCode.SYNC_ROLE_SERVICECODE_ERROR.code, PasCode.SYNC_ROLE_SERVICECODE_ERROR.message);
		}
	}

	@Logable(businessTag = "RoleServiceImpl.selectPermServiceByParam")
	public List<String> selectPermServiceByParam(List<String> permList) {
		return permServiceMapper.selectPermServiceByParam(permList);
	}
	
	
	public String queryRoleNames(String ids) {
        if (ids == null || ids.split(",").length < 1) {
            return null;
        }
        try {
            List<String> idList = Arrays.asList(ids.split(","));
            StringBuffer sb = new StringBuffer();
            idList.forEach( idStr -> {
                Long id = Long.parseLong(idStr);
                Role role = roleMapper.selectByPrimaryKey(id);
                if (role != null) {
                    sb.append("," + role.getName());  
                }
            });
            
            return sb.length() > 0 ? sb.substring(1) : sb.toString();
        } catch(Exception e) {
            return null;
        }
    }
	
	public List<Role> getRoleListByUId(Long userId) {
		List<Role> roleList = new ArrayList<>();
		String roleCodes = roleUserMapper.selectRoleCodeByUserId(userId);
		if (roleCodes == null) {
			throw new AppException(PasCode.USER_ROLE_NOT_EXIST.code, PasCode.USER_ROLE_NOT_EXIST.message);
		}
		List<String> roleCodeList = Arrays.asList(roleCodes.split(","));
		roleCodeList.forEach(rolecode -> {
			Role role = roleMapper.selectByRoleCode(rolecode);
			roleList.add(role);
		});
		return roleList;
	}

	public Boolean judgeRoleDataByRoleIds(List<Long> roleIds,String dataAuth) {
		return roleMapper.judgeRoleDataByRoleIds(roleIds,dataAuth);
	}

}
