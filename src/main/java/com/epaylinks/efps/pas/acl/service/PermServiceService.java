package com.epaylinks.efps.pas.acl.service;

import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.pas.acl.dao.PermServiceMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class PermServiceService {

	@Autowired
	private PermServiceMapper permServiceMapper;

	@Logable(businessTag = "queryPermService")
	public List<com.epaylinks.efps.pas.acl.po.PermService> queryPermService(Long permId) {
		return permServiceMapper.queryPermService(permId);
	}

	@Logable(businessTag = "addPermService")
	public void addPermService(Long permId, String serviceCode) {
		Long maxId = permServiceMapper.selectMaxId(permId);
		if (maxId == null) {
			maxId = permId * 100;
		}
		Long id = maxId + 1L;
		com.epaylinks.efps.pas.acl.po.PermService record = new com.epaylinks.efps.pas.acl.po.PermService();
		record.setId(id);
		record.setPermId(permId);
		record.setServiceCode(serviceCode);
		permServiceMapper.insert(record);
	}

	@Logable(businessTag = "deletePermService")
	public void deletePermService(Long permServiceId) {
		permServiceMapper.deleteByPrimaryKey(permServiceId);
	}

	@Logable(businessTag = "modifyServiceCode")
	public void modifyServiceCode(String origServiceCode, String newServiceCode) {
		permServiceMapper.updateServiceCode(origServiceCode, newServiceCode);
	}

	@Logable(businessTag = "deleteByServiceCode")
	public void deleteByServiceCode(String serviceCode) {
		permServiceMapper.deleteByServiceCode(serviceCode);
	}
}
