package com.epaylinks.efps.pas.acl.service;

import java.util.List;
import com.epaylinks.efps.pas.acl.po.Company;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.pas.acl.dto.DepartmentNode;
import com.epaylinks.efps.pas.acl.po.Department;
import com.epaylinks.efps.pas.common.PasCode;

@Service
public class DepartmentService {
	@Autowired
	private CompanyService companyService;

	public Department viewDepartment(Long id) {
		Company company = companyService.queryById(id);
        if (company == null) {
            throw new AppException(PasCode.DEPT_NOT_EXIST.code, PasCode.DEPT_NOT_EXIST.message);
        }
		Department department = new Department();
		department.setId(company.getCompanyId());
		department.setName(company.getName());
		department.setParentId(company.getParentId());
		department.setRemark(company.getRemark());
		return department;
	}

	public void addDepartment(Department department,Long userId) {
		companyService.addDepartment(department,userId);
	}

    @Transactional
	public void modifyDepartment(Department department,Long userId) {
		companyService.updateDepartment(department,userId);
	}

	public void deleteDepartment(Long deptId) {
		companyService.deleteDepartment(deptId);
	}

    public List<DepartmentNode> getDepartmentTree() {
		List<DepartmentNode> departmentNodeList = companyService.getDepartmentTree();
		return departmentNodeList;
	}
}
