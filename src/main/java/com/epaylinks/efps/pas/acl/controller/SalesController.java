package com.epaylinks.efps.pas.acl.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.util.PageResult;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.pas.acl.po.User;
import com.epaylinks.efps.pas.acl.service.UserService;
import com.epaylinks.efps.pas.acl.vo.PieceLinkVo;
import com.epaylinks.efps.pas.acl.vo.SalesmanVo;
import com.epaylinks.efps.pas.acl.vo.UserVo;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.pas.common.PasConstant;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 业务员管理控制器
 */
@RestController
@RequestMapping("/sales")
@Api(value = "SalesController", description = "业务员管理")
public class SalesController {
	
	@Autowired
	private UserService userService;
   
    /**
     * 分页查询业务员列表
     */
    @GetMapping("/page")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "分页查询业务员列表", notes = "分页查询业务员列表", httpMethod = "GET")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "name", value = "用户名", required = false, dataType = "String", paramType = "query"),
        @ApiImplicitParam(name = "realName", value = "业务员姓名", required = false, dataType = "String", paramType = "query"),
        @ApiImplicitParam(name = "companyId", value = "分公司ID", required = false, dataType = "Long", paramType = "query"),
        @ApiImplicitParam(name = "status", value = "状态：Y:启用，N：禁用", required = false, dataType = "String", paramType = "query", valueRange = "{Y,N}"),
        @ApiImplicitParam(name = "display", value = "是否显示明文：0:否； 1：是", required = false, dataType = "String", length = 1, paramType = "query"),
        @ApiImplicitParam(name = "pageNum", value = "查询开始页", required = true, dataType = "int", paramType = "query"),
        @ApiImplicitParam(name = "pageSize", value = "查询的页面大小", required = true, dataType = "int", paramType = "query")})
    public PageResult<UserVo> page(@RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "realName", required = false) String realName,
            @RequestParam(value = "companyId", required = false) Long companyId,
            @RequestParam(value = "status", required = false) String status,
            @RequestParam(value = "display", required = false) String display,
            @RequestParam(value = "salesCode", required = false) String salesCode,
            @RequestParam(value = "pageNum", required = true) Integer pageNum,
            @RequestParam(value = "pageSize", required = true) Integer pageSize,
            @RequestHeader(value = "x-userid", required = true) Long userId) {
        
        try {
            // 校验明文显示权限
            if ("1".equals(display) && !userService.checkDecryptPermission(userId)) {
                PageResult<UserVo> userPage = new PageResult<UserVo>();
                userPage.setReturnCode(PasCode.NO_RIGHT_TO_QUERY.code);
                userPage.setReturnMsg(PasCode.NO_RIGHT_TO_QUERY.message);
                return userPage;
            }
            
            int endRowNo = pageNum * pageSize;
            int beginRowNo = (pageNum - 1) * pageSize + 1;
            
            Map map = new HashMap();
            map.put("name", name);
            map.put("realName", realName);
            map.put("companyId", companyId);
            map.put("status", status);
            map.put("userType", PasConstant.UserType.SALES.code);  // 业务员
            map.put("beginRowNo", beginRowNo);
            map.put("endRowNo", endRowNo);
            map.put("operId", userId);
            map.put("display", display);
            map.put("salesCode",salesCode);

            com.epaylinks.efps.pas.common.PageResult<UserVo> page = userService.pageQuery(map);
            return PageResult.success(page.getRows(), page.getTotal());
        } catch (Exception e) {
            if (e instanceof AppException) {
            	return PageResult.fail(((AppException) e).getErrorCode(), ((AppException) e).getErrorMsg());
            } else {
            	return PageResult.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
            }
        }
    }


    /**
     * 查询业务员
     */
    @GetMapping("/query")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "查询业务员", notes = "查询业务员", httpMethod = "GET")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "uid", value = "业务员ID", required = true, dataType = "Long", paramType = "query"),
        @ApiImplicitParam(name = "display", value = "是否显示明文：0:否； 1：是", required = false, dataType = "String", length = 1, paramType = "query"),
    })
    public CommonOuterResponse query(
            @RequestParam(value = "uid", required = true)Long uid,
            @RequestParam(value = "display", required = false)String display
    ) {
        try {
            Map map = new HashMap();
            map.put("uid", uid);
            map.put("beginRowNo", 1);
            map.put("endRowNo", 1);
            map.put("display", display);

            com.epaylinks.efps.pas.common.PageResult<UserVo> userPage = userService.pageQuery(map);
            if (userPage != null && userPage.getRows() != null && userPage.getRows().size() > 0) {
            	return CommonOuterResponse.success(userPage.getRows().get(0));
            }
            return CommonOuterResponse.fail(PasCode.USER_NOT_EXIST.code, PasCode.USER_NOT_EXIST.message);
        } catch (Exception e) {
			if (e instanceof AppException) {
				return CommonOuterResponse.fail(((AppException) e).getErrorCode(), ((AppException) e).getErrorMsg());
			} else {
			 	return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
			}
        }
    }

    /**
     * 修改业务员
     */
    @PostMapping("/update")
    @Exceptionable
    @Validatable
    @Logable(businessTag = "SalesController.update")
    @ApiOperation(value = "修改业务员", notes = "修改业务员", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uid", value = "业务员ID", required = true, dataType = "Long", paramType = "query"),
            @ApiImplicitParam(name = "realName", value = "业务员姓名", required = true, dataType = "String", paramType = "query", length = 25),
            @ApiImplicitParam(name = "companyId", value = "分公司ID", required = true, dataType = "Long", paramType = "query", digit = true),
            @ApiImplicitParam(name = "mobile", value = "手机", required = true, dataType = "String", paramType = "query", digit = true),
            @ApiImplicitParam(name = "remark", value = "备注", required = false, dataType = "String", paramType = "query", length = 100)
    })
    public CommonOuterResponse update(@ApiIgnore UserVo userVo,
    		@RequestHeader(value = "x-userid", required = true) Long userId) {
        CommonOuterResponse response = new CommonOuterResponse();
        try {
        	userVo.setUserType(PasConstant.UserType.SALES.code);
        	userService.modifyCompanyUser(userVo, userId);
        } catch (Exception e) {
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
            return response;
        }
        return response;
    }

    /**
     * 启用
     */
    @PostMapping("/active")
    @Exceptionable
    @Validatable
    @Logable(businessTag = "SalesController.active")
    @ApiOperation(value = "启用", notes = "启用", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uid", value = "业务员ID", required = true, dataType = "Long", paramType = "query")
    })
    public CommonOuterResponse active(@RequestParam(value="uid" , required = true) Long uid,
    		@RequestHeader(value = "x-userid", required = true) Long userId) {
        try {
        	userService.changeStatus(uid, "Y", userId,null);
        	return CommonOuterResponse.success();
        } catch (Exception e) {
    		if (e instanceof AppException) {
				return CommonOuterResponse.fail(((AppException) e).getErrorCode(), ((AppException) e).getErrorMsg());
			} else {
			 	return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
			}
        }
    }

    /**
     * 禁用
     */
    @PostMapping("/frozen")
    @Exceptionable
    @Validatable
    @Logable(businessTag = "SalesController.frozen")
    @ApiOperation(value = "禁用", notes = "禁用", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uid", value = "业务员ID", required = true, dataType = "Long", paramType = "query")
    })
    public CommonOuterResponse frozen(@RequestParam(value="uid" , required = true) Long uid,
    		@RequestHeader(value = "x-userid", required = true) Long userId) {
        try {
        	userService.changeStatus(uid, "N", userId,"salesFrozen");
        	return CommonOuterResponse.success();
        } catch (Exception e) {
    		if (e instanceof AppException) {
				return CommonOuterResponse.fail(((AppException) e).getErrorCode(), ((AppException) e).getErrorMsg());
			} else {
			 	return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
			}
        }
    }
    
    /**
     * 业务员搜索
     */
    @GetMapping("/search")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "搜索业务员", notes = "搜索业务员", httpMethod = "GET")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "key", value = "用户名/名称", required = false, dataType = "String",  paramType = "query"),
        @ApiImplicitParam(name = "pageNum", value = "查询开始页", required = true, dataType = "int", paramType = "query"),
        @ApiImplicitParam(name = "pageSize", value = "查询的页面大小", required = true, dataType = "int", paramType = "query")})
    public CommonOuterResponse<List<User>> search(
    	@RequestParam(value = "key", required = false) String key,
        @RequestParam(value = "pageNum", required = true) Integer pageNum,
        @RequestParam(value = "pageSize", required = true) Integer pageSize,
        @RequestHeader(value = "x-userid", required = true) Long userId) {
    	
        try {
            int endRowNo = pageNum * pageSize;
            int beginRowNo = (pageNum - 1) * pageSize + 1;
            Map map = new HashMap();
            map.put("key", key);
            map.put("beginRowNo", beginRowNo);
            map.put("endRowNo", endRowNo);
            map.put("userType", PasConstant.UserType.SALES.code);
            map.put("status", "Y");
        	
            return CommonOuterResponse.success(userService.searchUser(map, userId));
        } catch (Exception e) {
            if (e instanceof AppException) {
            	return PageResult.fail(((AppException) e).getErrorCode(), ((AppException) e).getErrorMsg());
            } else {
            	return PageResult.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
            }
        }
    }    

    /**
     * 查询分公司有效业务员
     */
    @GetMapping("/listByCompany")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "查询分公司有效业务员", notes = "查询分公司有效业务员", httpMethod = "GET")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "companyId", value = "分公司Id", required = true, dataType = "Long",  paramType = "query")
        })
    public CommonOuterResponse<List<User>> listByCompany(
        @RequestParam(value = "companyId", required = true) Long companyId) {
        
        try {
            return CommonOuterResponse.success(userService.listCompanyEffectiveUser(companyId));
        } catch (AppException e) {
                return CommonOuterResponse.fail(((AppException) e).getErrorCode(), ((AppException) e).getErrorMsg());
        } catch (Exception e) {
             return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
        }
    }
    
    /**
     * 查询分公司有效业务员
     */
    @GetMapping("/randomOneByCompany")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "随机查找分公司下一有效业务员", notes = "随机查找分公司下一有效业务员", httpMethod = "GET")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "companyId", value = "分公司Id", required = true, dataType = "Long",  paramType = "query")
        })
    public User randomOneByCompany(
        @RequestParam(value = "companyId", required = true) Long companyId) {
        
        try {
            return userService.randomOneByCompany(companyId);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
    
    
    
    /**
     * 生成或刷新业务员自助进件链接
     */
    @PostMapping("/refreshPieceLink")
    @Exceptionable
    @Validatable
    @Logable(businessTag = "SalesController.refreshPieceLink")
    @ApiOperation(value = "生成或刷新业务员自助进件链接", notes = "生成或刷新业务员自助进件链接", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uid", value = "业务员ID", required = true, dataType = "Long", paramType = "query")
    })
    public CommonOuterResponse<PieceLinkVo> refreshPieceLink(
            @RequestParam(value="uid" , required = true) Long uid,
            @RequestHeader(value = "x-userid", required = true) Long userId) {
        try {
            return CommonOuterResponse.success(userService.refreshPieceLink(uid, userId));
        } catch (Exception e) {
            if (e instanceof AppException) {
                return CommonOuterResponse.fail(((AppException) e).getErrorCode(), ((AppException) e).getErrorMsg());
            } else {
                return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
            }
        }
    }
    
    
    /**
     * 查询商户自助进件H5链接
     */
    @GetMapping("/queryPieceLink")
    @Exceptionable
    @Validatable
    @Logable(businessTag = "SalesController.queryPieceLink")
    @ApiOperation(value = "查询业务员自助进件链接", notes = "查询业务员自助进件链接", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uid", value = "业务员ID", required = true, dataType = "Long", paramType = "query")
    })
    public CommonOuterResponse<PieceLinkVo> queryPieceLink(
            @RequestParam(value="uid" , required = true) Long uid,
            @RequestHeader(value = "x-userid", required = true) Long userId) {
        try {
            return CommonOuterResponse.success(userService.queryPieceLink(uid));
        } catch (Exception e) {
            if (e instanceof AppException) {
                return CommonOuterResponse.fail(((AppException) e).getErrorCode(), ((AppException) e).getErrorMsg());
            } else {
                return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
            }
        }
    }
    
    
    /**
     * 修改业务员自助进件链接状态
     */
    @PostMapping("/updatePieceLinkStatus")
    @Exceptionable
    @Validatable
    @Logable(businessTag = "SalesController.updatePieceLinkStatus")
    @ApiOperation(value = "修改业务员自助进件链接状态", notes = "修改业务员自助进件链接状态", httpMethod = "POST")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "uid", value = "业务员ID", required = true, dataType = "Long", paramType = "query"),
        @ApiImplicitParam(name = "status", value = "状态：0：无效；1：有效", required = true, dataType = "Long", paramType = "query")
    })
    public CommonOuterResponse<PieceLinkVo> updatePieceLinkStatus(
            @RequestParam(value="uid" , required = true) Long uid,
            @RequestParam(value="status" , required = true) Short status,
            @RequestHeader(value = "x-userid", required = true) Long userId) {
        try {
            userService.updatePieceLinkStatus(uid, status, userId);
            return CommonOuterResponse.success();
        } catch (Exception e) {
            if (e instanceof AppException) {
                return CommonOuterResponse.fail(((AppException) e).getErrorCode(), ((AppException) e).getErrorMsg());
            } else {
                return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
            }
        }
    }
    
    
    /**
     * 查询业务员信息
     */
    @GetMapping("/querySalesman")
    @Exceptionable
    @Validatable
    @Logable(businessTag = "SalesController.querySalesman")
    @ApiOperation(value = "查询业务员信息", notes = "查询业务员信息", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uid", value = "业务员ID", required = true, dataType = "Long", paramType = "query")
    })
    public CommonOuterResponse<SalesmanVo> querySalesman(
            @RequestParam(value="uid" , required = true) Long uid) {
        try {
            return CommonOuterResponse.success(userService.querySalesman(uid));
        } catch (Exception e) {
            if (e instanceof AppException) {
                return CommonOuterResponse.fail(((AppException) e).getErrorCode(), ((AppException) e).getErrorMsg());
            } else {
                return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
            }
        }
    }
    
}
