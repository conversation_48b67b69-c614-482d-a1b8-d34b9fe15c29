package com.epaylinks.efps.pas.acl.service;

import java.net.MalformedURLException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;
import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.business.CommonResponse;
import com.epaylinks.efps.common.business.sms.service.VerificationCodeService;
import com.epaylinks.efps.common.dataimport.util.CustReturnCode;
import com.epaylinks.efps.common.hessian.HessianService;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.pas.acl.dao.RoleMapper;
import com.epaylinks.efps.pas.acl.dto.UserRoleDataAuthDTO;
import com.epaylinks.efps.pas.pas.common.PasConstant;
import com.epaylinks.efps.pas.pas.dao.RoleCompanyMapper;
import ocx.AESWithJCE;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import com.epaylinks.efps.common.business.pay.request.UserType;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.hessian.EncryptService;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.common.util.DateTimeUtil;
import com.epaylinks.efps.common.util.DateUtils;
import com.epaylinks.efps.common.util.MD5Utils;
import com.epaylinks.efps.pas.acl.controller.response.LoginResponse;
import com.epaylinks.efps.pas.acl.dao.RoleUserMapper;
import com.epaylinks.efps.pas.acl.dao.UserMapper;
import com.epaylinks.efps.pas.acl.dto.BusinessToken;
import com.epaylinks.efps.pas.acl.dto.PermNode;
import com.epaylinks.efps.pas.acl.po.User;
import com.epaylinks.efps.pas.common.LogService;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.common.PasConstants;
import com.epaylinks.efps.pas.mch.service.feign.CustService;
import com.epaylinks.efps.pas.pas.domain.PasLoginInfo;
import com.epaylinks.efps.pas.pas.service.LoginInfoService;
import org.springframework.util.StringUtils;

@Service
public class SessionService {

	@Autowired
	private UserMapper userMapper;

	@Autowired
	private RoleUserMapper roleUserMapper;

    @Autowired
    private RoleMapper roleMapper;

    @Autowired
    private RoleService roleService;

    @Autowired
    private RoleCompanyMapper roleCompanyMapper;

	@Autowired
	private PermService permService;

	@Autowired
	private CallUAAService callUAA;

	@Autowired
	private EncryptService encryptService;
	
	@Autowired
	private UserService userService;
	
	@Autowired
    private LogService logService;
	
	@Autowired
	private LoginInfoService loginInfoService;
	
	@Autowired
	private CustService custService;
	
	@Autowired
	private SMS sms;

    @Autowired
    private HessianService hessianService;

	@Value("${passWord.salt}")
	private String passWordSalt;

    @Autowired
    private VerificationCodeService verificationCodeService;

    @Autowired
    private CommonLogger logger;
    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private CompanyService companyService;
    
	/**
	 * 用户登录
	 *
	 * @param name 		用户名
	 * @param password 登录密码，明文
	 */
//	@Logable(businessTag = "UserService.login")
	public LoginResponse login(String name, String password) throws Exception {
		User user = userMapper.selectByName(name);
		// 用户不存在
		if (user == null) {
			throw new AppException(PasCode.LOGIN_USER_NOT_EXIST.code, PasCode.USER_OR_PASSWORD_UNCORRECT.message);
		}

        if (user.getUid().intValue() <= 0) {
            throw new AppException(PasCode.USER_DISABLE_LOGIN.code, PasCode.USER_DISABLE_LOGIN.message);
        }

        String sm3Pwd = encryptService.encryptData(password, "A02");

		String pwd = user.getPassword();
		boolean checkSuccess = false;
        if (pwd.indexOf("$b") == 0){
            String sm4Pwd = encryptService.symmetricEncryptData(sm3Pwd);
            if (pwd.equals(sm4Pwd)) {
                checkSuccess = true;
            }
        } else if (pwd.indexOf("$A02") == 0) {
			if (sm3Pwd.equals(pwd)) {
				checkSuccess = true;
			}
		} else {
			// 旧版MD5加密
			String md5Pwd = MD5Utils.getMD5(passWordSalt + password);
			if (md5Pwd.equals(pwd)) {
				checkSuccess = true;
			}
		}

		// 用户状态为锁定（第二天自动解除锁定）
        if (user.getStatus().equals(PasConstants.UserStatus.LOCK.code)) {
            throw new AppException(PasCode.LOGIN_USER_STATE_LOCK_ERROR.code, PasCode.LOGIN_USER_STATE_LOCK_ERROR.message);
        }
        
        // 用户状态为禁用
        if (user.getStatus().equals(PasConstants.UserStatus.NO.code)) {
            throw new AppException(PasCode.LOGIN_USER_STATE_DISABLE_ERROR.code, PasCode.LOGIN_USER_STATE_DISABLE_ERROR.message);
        }
        
		// 密码错误
		if (!checkSuccess) {
		    // 校验登录错误次数          
            int errorCount = this.queryLoginErrorCount(name);
            int pwdMaxErrorCount = Integer.parseInt(custService.queryStaticParam("RC_PARAM", "PWD_MAX_ERROR_COUNT"));
            int pwdWarningErrorCount = Integer.parseInt(custService.queryStaticParam("RC_PARAM", "PWD_WARNING_COUNT"));
            if (errorCount + 1 >= pwdMaxErrorCount) {
                // 锁定用户非事务处理
                user.setUpdateTime(new Date());
                user.setStatus(PasConstants.UserStatus.LOCK.code);
                userService.modifyUser(user);
                throw new AppException(PasCode.LOGIN_PWD_ERORR.code, PasCode.USER_STATUS_LOCK_EXCEPTION.message);
            } else {
                if (errorCount + 1 == pwdWarningErrorCount) {// 达到告警次数发短信告警
                    // 密码超过n次发送告警短信
                    String mobile = "";
                    try {
                        mobile = encryptService.newDecryptData(user.getEnMobile());
                    } catch (Exception e) {
                        e.printStackTrace();
                        throw new AppException(PasCode.LOGIN_PWD_ERORR.code, "mobile解密异常");
                    }
                    sms.sendLoginWarningMsg(mobile, user.getName());
                }
                throw new AppException(PasCode.LOGIN_PWD_ERORR.code, PasCode.USER_OR_PASSWORD_UNCORRECT.message);
            }			
		}
		
		// 检查登录密码是否过期(暂无)
        Date now = new Date();
        if (user.getPasswordExpiredTime() != null && DateUtils.compareTo(now, user.getPasswordExpiredTime()) > 0) {
            throw new AppException(PasCode.PASSWORD_EXPIRED.code, PasCode.PASSWORD_EXPIRED.message);
        }

		// 查看用户角色
		String roleCodes = roleUserMapper.selectRoleCodeByUserId(user.getUid());
		if (roleCodes == null) {
			throw new AppException(PasCode.USER_ROLE_NOT_EXIST.code, PasCode.USER_ROLE_NOT_EXIST.message);
		}

        // 判断用户的所有角色数据查看权限是否冲突
        List<UserRoleDataAuthDTO> dataAuthDTOS = roleMapper.selectRoleDataAuthByUserId(user.getUid(),null);
        if (dataAuthDTOS.stream().map(dto -> dto.getDataAuth()).distinct().count() > 1 && !Objects.equals(user.getUid(),0L)) {
            logger.printMessage("dataAuthDTOS:" + JSON.toJSONString(dataAuthDTOS));
            throw new AppException(PasCode.DATA_ERROR.code,"数据查看权限冲突，请重新分配角色");
        }

	    // 返回结果
        LoginResponse loginResponse = new LoginResponse();
		
        if (user.getPasswordUpdateTime() == null) { // 密码最新修改时间为空（从未修改过），强制用户需要修改密码
            loginResponse.setLoginPwdChangeSuggest(PasConstants.PwdChangeSuggest.MUST_CHANGE.code); 
        } else {// 根据密码过期时间来判断
            if (user.getPasswordExpiredTime() == null) { // 过期时间为空，表示不过期
                loginResponse.setLoginPwdChangeSuggest(PasConstants.PwdChangeSuggest.NO_NEED_TO_CHANGE.code);
            } else {
                int pwdChangeDate = Integer.parseInt(custService.queryStaticParam("RC_PARAM", "PWD_CHANGE_DATE"));
                Date suggestTime = DateTimeUtil.addDate(now , pwdChangeDate); // 建议修改提示时间
                if (DateUtils.compareTo(suggestTime, user.getPasswordExpiredTime()) > 0) {
                    loginResponse.setLoginPwdChangeSuggest(PasConstants.PwdChangeSuggest.SUGGEST_CHANGE.code);
                } else {
                    loginResponse.setLoginPwdChangeSuggest(PasConstants.PwdChangeSuggest.NO_NEED_TO_CHANGE.code);
                }
            }
        }
		
		// 申请token
		String token = callUAA.applyToken(
				"0",// businessType
				String.valueOf(user.getUid()),// userId 仅标识
				String.valueOf(user.getUid()),// customerCode 仅标识
				roleCodes,
				"extra"
		);

        // 删除上次登录所设置的分公司列表缓存
        String redisKey = "PAS_USER:authCompanyIds:" + user.getUid();
        if(redisTemplate.opsForValue().get(redisKey) != null) {
            redisTemplate.delete(redisKey);
        }

        if (!dataAuthDTOS.isEmpty() && !Objects.equals(user.getUid(),0L)) {
            String dataAuth = dataAuthDTOS.get(0).getDataAuth();
            // 用户所有角色的分公司列表
            List<Long> companyIdList = new ArrayList<>();
            if (PasConstants.RoleDataAuth.CUSTOMIZE.code.equals(dataAuth)) {
                List<Long> roleIds = dataAuthDTOS.stream().map(data -> data.getRoleId()).collect(Collectors.toList());
                companyIdList = roleCompanyMapper.selectCompanyIdByRoleIds(roleIds);
            } else if (PasConstants.RoleDataAuth.THIS_DEPARTMENT.code.equals(dataAuth)) {
                companyIdList = companyService.queryCompanysByUserId(user.getUid());
            } else if (PasConstants.RoleDataAuth.SELF.code.equals(dataAuth) && PasConstant.UserType.SALES.code.equals(user.getUserType())) {

            } else if (PasConstants.RoleDataAuth.SELF.code.equals(dataAuth) && !PasConstant.UserType.SALES.code.equals(user.getUserType())) {
                throw new AppException(PasCode.DATA_ERROR.code,"用户获取权限失败，仅业务员支持数据查看权限为本人");
            }
            if(!companyIdList.isEmpty()) {
                String companyIdStr = companyIdList.stream().map(Object::toString).collect(Collectors.joining(","));
                logger.printMessage(user.getUid() + "-" + user.getName() + "-companyIdStr:" + companyIdStr);
                redisTemplate.opsForValue().set(redisKey, companyIdStr,72,TimeUnit.HOURS);
            }
        }

		// 查询当前用户的权限
		PermNode root = permService.getPermOfCurUser(user.getUid());

		loginResponse.setUserId(user.getUid());
		loginResponse.setToken(token);
		loginResponse.setUserName(name);
		loginResponse.setPermTreeRoot(root);
		loginResponse.setUserRealName(user.getRealName());
		loginResponse.setUserType(user.getUserType());
		loginResponse.setCanDecrypt(user.getCanDecrypt());
		
		return loginResponse;
	}
	
	
   public LoginResponse getLoginRespons(User user) {
        // 用户不存在
        if (user == null) {
            throw new AppException(PasCode.LOGIN_USER_NOT_EXIST.code, PasCode.USER_OR_PASSWORD_UNCORRECT.message);
        }

        // 用户状态为锁定（第二天自动解除锁定）
        if (user.getStatus().equals(PasConstants.UserStatus.LOCK.code)) {
            throw new AppException(PasCode.LOGIN_USER_STATE_LOCK_ERROR.code, PasCode.LOGIN_USER_STATE_LOCK_ERROR.message);
        }
        
        // 用户状态为禁用
        if (user.getStatus().equals(PasConstants.UserStatus.NO.code)) {
            throw new AppException(PasCode.LOGIN_USER_STATE_DISABLE_ERROR.code, PasCode.LOGIN_USER_STATE_DISABLE_ERROR.message);
        }
        // 查看用户角色
        String roleCodes = roleUserMapper.selectRoleCodeByUserId(user.getUid());
        if (roleCodes == null) {
            throw new AppException(PasCode.USER_ROLE_NOT_EXIST.code, PasCode.USER_ROLE_NOT_EXIST.message);
        }

        // 返回结果
        LoginResponse loginResponse = new LoginResponse();
        if (user.getPasswordUpdateTime() == null) { // 密码最新修改时间为空（从未修改过），强制用户需要修改密码
            loginResponse.setLoginPwdChangeSuggest(PasConstants.PwdChangeSuggest.MUST_CHANGE.code); 
        } else {// 根据密码过期时间来判断
            if (user.getPasswordExpiredTime() == null) { // 过期时间为空，表示不过期
                loginResponse.setLoginPwdChangeSuggest(PasConstants.PwdChangeSuggest.NO_NEED_TO_CHANGE.code);
            } else {
                int pwdChangeDate = Integer.parseInt(custService.queryStaticParam("RC_PARAM", "PWD_CHANGE_DATE"));
                Date suggestTime = DateTimeUtil.addDate(new Date() , pwdChangeDate); // 建议修改提示时间
                if (DateUtils.compareTo(suggestTime, user.getPasswordExpiredTime()) > 0) {
                    loginResponse.setLoginPwdChangeSuggest(PasConstants.PwdChangeSuggest.SUGGEST_CHANGE.code);
                } else {
                    loginResponse.setLoginPwdChangeSuggest(PasConstants.PwdChangeSuggest.NO_NEED_TO_CHANGE.code);
                }
            }
        }
        
        // 申请token
        String token = callUAA.applyToken(
                "0",// businessType
                String.valueOf(user.getUid()),// userId 仅标识
                String.valueOf(user.getUid()),// customerCode 仅标识
                roleCodes,
                "extra"
        );

        // 查询当前用户的权限
        PermNode root = permService.getPermOfCurUser(user.getUid());

        loginResponse.setUserId(user.getUid());
        loginResponse.setToken(token);
        loginResponse.setUserName(user.getName());
        loginResponse.setPermTreeRoot(root);
        loginResponse.setUserRealName(user.getRealName());
        loginResponse.setUserType(user.getUserType());
        loginResponse.setCanDecrypt(user.getCanDecrypt());
        
        return loginResponse;
    }


	
    public String decryptPwd(String encrytPwd) {

        Map<String, String> map = new HashMap<String, String>();
        map.put("password", encrytPwd);
        try {
            // 机密后结果设置到对应key中去
            Map<String, String> resultMap = encryptService.decryptByKeyType(map, Constants.RsaKeyType.FRONT.code);
            if (resultMap != null) {
                return resultMap.get("password");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        throw new AppException(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
    }
	
    /**
     * 查询登录错误次数
     * @param username
     * @return
     */
    private int queryLoginErrorCount(String username) {
        
        PasLoginInfo pl = new PasLoginInfo();
        pl.setUsername(username);
        pl.setUsertype(UserType.PAS_USER.code);
        PasLoginInfo loginInfo = loginInfoService.searchLoginInfo(pl); 
        if (loginInfo != null ) {
            try {
                return Integer.parseInt(loginInfo.getError_count());
            } catch (Exception e) {
                logService.printLog(e);
            }
        }
        return 0;
    }


    /**
     * 单点登录获取认证信息
     * @param
     * @param token
     * @return
     */
    public LoginResponse ssoLogin(String token) {
        
        // token为系统A登录后生成token，获取epsp登录信息，后续校验token是否有效
        BusinessToken tokenInfo = callUAA.verifyToken(token);
        
        // 返回结果
        LoginResponse loginResponse = new LoginResponse();
        User user = userService.queryUserById(tokenInfo.getUserId());
                
        if (user.getPasswordUpdateTime() == null) { // 密码最新修改时间为空（从未修改过），强制用户需要修改密码
            loginResponse.setLoginPwdChangeSuggest(PasConstants.PwdChangeSuggest.MUST_CHANGE.code); 
        } else {// 根据密码过期时间来判断
            if (user.getPasswordExpiredTime() == null) { // 过期时间为空，表示不过期
                loginResponse.setLoginPwdChangeSuggest(PasConstants.PwdChangeSuggest.NO_NEED_TO_CHANGE.code);
            } else {
                int pwdChangeDate = Integer.parseInt(custService.queryStaticParam("RC_PARAM", "PWD_CHANGE_DATE"));
                Date suggestTime = DateTimeUtil.addDate(new Date() , pwdChangeDate); // 建议修改提示时间
                if (DateUtils.compareTo(suggestTime, user.getPasswordExpiredTime()) > 0) {
                    loginResponse.setLoginPwdChangeSuggest(PasConstants.PwdChangeSuggest.SUGGEST_CHANGE.code);
                } else {
                    loginResponse.setLoginPwdChangeSuggest(PasConstants.PwdChangeSuggest.NO_NEED_TO_CHANGE.code);
                }
            }
        }
        
        // 查询当前用户的权限
        PermNode root = permService.getPermOfCurUser(user.getUid());

        loginResponse.setUserId(user.getUid());
        loginResponse.setToken(token);
        loginResponse.setUserName(user.getName());
        loginResponse.setPermTreeRoot(root);
        loginResponse.setUserRealName(user.getRealName());
        loginResponse.setUserType(user.getUserType());
        loginResponse.setCanDecrypt(user.getCanDecrypt());
        
        return loginResponse;
    }

    /**
     * 校验是否为业务员账号登录
     * @param name

     * @return
     */
    public User checkBusinessRole(String name) throws MalformedURLException {
        if (StringUtils.isEmpty(name)) {
            throw new AppException(PasCode.DATA_ERROR.code,PasCode.DATA_ERROR.message + "：不能为空");
        }
        User user = userMapper.selectByName(name);
        if (user == null) {
            throw new AppException(PasCode.SALES_NOT_EXIST.code, PasCode.SALES_NOT_EXIST.message);
        }
        if (PasConstant.UserType.SALES.code.compareTo(user.getUserType()) != 0) {
            throw new AppException(PasCode.SALES_NOT_EXIST.code,PasCode.SALES_NOT_EXIST.message);
        }
        if (!StringUtils.isEmpty(user.getEnMobile())) {
            user.setMobile(hessianService.symmetricDecryptData(user.getEnMobile()));
        }
        return user;
    }

    /**
     * 业务员进件平台登录发送短信验证码前-密码检验
     * @param user
     * @param password 明文密码
     * @param errorFlag 密码错误是否计入次数或锁定用户
     * @return
     * @throws Exception
     */
    public CommonOuterResponse checkPassword(User user,String password,boolean errorFlag) throws Exception{
        if (StringUtils.isEmpty(password)) {
            throw new AppException(PasCode.DATA_ERROR.code,PasCode.DATA_ERROR.message + "：密码不能为空");
        }
        // 密码校验
        String pwd = user.getPassword();
        Boolean checkSuccess = false;
        if (pwd.indexOf("$b") == 0){
            String sm3Pwd = encryptService.encryptData(password, "A02");
            String sm4Pwd = encryptService.symmetricEncryptData(sm3Pwd);
            if (pwd.equals(sm4Pwd)) {
                checkSuccess = true;
            }
        } else if (pwd.indexOf("$A02") == 0) {
            String sm3Pwd = encryptService.encryptData(password, "A02");
            if (sm3Pwd.equals(pwd)) {
                checkSuccess = true;
            }
        } else {
            // 旧版MD5加密
            String md5Pwd = MD5Utils.getMD5(passWordSalt + password);
            if (md5Pwd.equals(pwd)) {
                checkSuccess = true;
            }
        }
        // 用户状态为锁定（第二天自动解除锁定）
        if (user.getStatus().equals(PasConstants.UserStatus.LOCK.code)) {
            throw new AppException(PasCode.LOGIN_USER_STATE_LOCK_ERROR.code, PasCode.LOGIN_USER_STATE_LOCK_ERROR.message);
        }
        // 用户状态为禁用
        if (user.getStatus().equals(PasConstants.UserStatus.NO.code)) {
            throw new AppException(PasCode.LOGIN_USER_STATE_DISABLE_ERROR.code, PasCode.LOGIN_USER_STATE_DISABLE_ERROR.message);
        }
        if (!checkSuccess) {
            // 当前版本密码校验不更新错误次数和锁定  20221230
            if (errorFlag) {

            } else {
                throw new AppException(PasCode.LOGIN_PWD_ERORR.code,PasCode.LOGIN_PWD_ERORR.message);
            }
        }
        return CommonOuterResponse.success();
    }

    /**
     * 发送短信验证码
     * @param mobile 手机号码
     * @param serviceCode 业务代码
     * @return
     */
    public CommonOuterResponse sendSMSCaptcha(String mobile,String serviceCode) {
//        callUAA.sendVerifyCode(mobile, serviceCode);
        // 使用新SMS
        callUAA.sendCode(mobile,serviceCode,"EPL_MB_YZMTY");
        return CommonOuterResponse.success();
    }

    public void checkSMSCode(String phone,String serviceCode,String captcha) {
        CommonOuterResponse outerResponse = verificationCodeService.verificationCode(phone,serviceCode,captcha);
        if (outerResponse == null || !CustReturnCode.SUCCESS.code.equals(outerResponse.getReturnCode())) {
            throw new AppException(PasCode.VALIDATECODE_ERROR.code,PasCode.VALIDATECODE_ERROR.message);
        }
    }

    public CommonOuterResponse sendSMS(String mobile,String serviceCode,String username,String desc) {
        CommonOuterResponse response = new CommonOuterResponse();
        CommonOuterResponse outerResponse = verificationCodeService.applyVerificationCode(mobile,serviceCode,desc,
                "EPL_MB_YZMTY",username,"pas-SessionService-sendSMS");
        if(null != outerResponse && CustReturnCode.SUCCESS.code.equals(outerResponse.getReturnCode())){
            response.setReturnCode(CommonResponse.SUCCEE);
            response.setReturnMsg("短信验证码发送成功");
            return response;
        }else{
            logger.printMessage("短信请求，返回码：" + outerResponse.getReturnCode() + ",返回说明：" + outerResponse.getReturnMsg());
            response.setReturnCode(CustReturnCode.SYSTEM_EXCEPTION.code);
            response.setReturnMsg(outerResponse.getReturnMsg());
            return response;
        }
    }

    /**
     * 业务员登录校验
     * @param name
     * @param password
     * @param verifyCode
     * @return
     * @throws Exception
     */
    public LoginResponse checkBusinessLogin(String name,String password,String verifyCode) throws Exception {
        // 角色校验
        User user = checkBusinessRole(name);
        // 密码校验
        checkPassword(user,password,false);
        // 验证码校验
//        if (!PasConstants.SUCCESS.equals(callUAA.validateCode(user.getMobile(),verifyCode,PasConstants.serviceCode.BUSINESSLOGIN.code))) {
//            throw new AppException(PasCode.VALIDATECODE_ERROR.code, PasCode.VALIDATECODE_ERROR.message);
//        }
        checkSMSCode(user.getMobile(),PasConstants.serviceCode.BUSINESSLOGIN.code,verifyCode);
        // 查看用户角色
        String roleCodes = roleUserMapper.selectRoleCodeByUserId(user.getUid());
        if (roleCodes == null) {
            throw new AppException(PasCode.USER_ROLE_NOT_EXIST.code, PasCode.USER_ROLE_NOT_EXIST.message);
        }
        // 申请token
        String token = callUAA.applyToken(
                "0",// businessType
                String.valueOf(user.getUid()),// userId 仅标识
                String.valueOf(user.getUid()),// customerCode 仅标识
                roleCodes,
                "extra"
        );
        // 返回用户信息
        LoginResponse loginResponse = new LoginResponse();
        loginResponse.setUserId(user.getUid());
        loginResponse.setToken(token);
        loginResponse.setUserName(name);
        loginResponse.setUserRealName(user.getRealName());
        loginResponse.setUserType(user.getUserType());
        loginResponse.setCanDecrypt(user.getCanDecrypt());
        return loginResponse;
    }

    /**
     * 控件密码解密 sm2+sm4
     * @param password
     * @param mcryptKey
     * @return
     * @throws Exception
     */
    public String decryptPassword(String password,String mcryptKey) throws Exception {
        String deMcryptKey = decryptPwd(mcryptKey);
        logService.printLog("32位随机数：" + deMcryptKey);
        String sm2CipherText =  encryptService.sm4DecryptData(password,deMcryptKey);
        logService.printLog("sm2密文：" + sm2CipherText);
        String result = decryptPwd(sm2CipherText);
        logService.printLog("密码明文：" + result);
        return result;
    }

    /**
     * 控件密码解密 sm2
     * @param password
     * @param mcryptKey
     * @return
     * @throws Exception
     */
    public String decryptSM2(String password,String mcryptKey){
        String enMcryptKey = decryptPwd(mcryptKey);
        String enPwd = decryptPwd(password);
        password = AESWithJCE.getResult(enMcryptKey, enPwd);
        return password;
    }
}
