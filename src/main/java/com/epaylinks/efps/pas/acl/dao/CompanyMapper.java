package com.epaylinks.efps.pas.acl.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.epaylinks.efps.pas.acl.dto.CompanyUserAmount;
import com.epaylinks.efps.pas.acl.po.Company;

@Mapper
public interface CompanyMapper {
    int deleteByPrimaryKey(Long companyId);

    int insert(Company record);

    int insertSelective(Company record);

    Company selectByPrimaryKey(Long companyId);

    int updateByPrimaryKeySelective(Company record);

    int updateByPrimaryKey(Company record);
    
    List<Company> selectAll(@Param("companyId")Long companyId);

	Company queryByNameAndParentId(@Param("name")String name, @Param("parentId")Long parentId);

	Boolean queryByName(@Param("name")String name);

    boolean checkExistOfNameWhenModify(@Param("name") String name, @Param("id") Long id);

	List<CompanyUserAmount> selectUserAmountByCompanyIds(@Param("companyIdList") List<Long> companyIdList);
	
	//搜索分公司列表
	List<Company> searchCompany(Map map);
	
	List<Company> queryLeafCompany();

    int deleteCompanyFromUser(@Param("companyId") Long companyId);

    List<Long> queryJuniorCompanyId(@Param("companyId") Long companyId);

    // 查询用户所属分公司及下级分公司列表
    List<Long> queryCompanysByUserId(@Param("userId") Long userId);

}