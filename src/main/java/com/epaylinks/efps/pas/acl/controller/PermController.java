package com.epaylinks.efps.pas.acl.controller;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.pas.acl.controller.response.BasicPermTreeResponse;
import com.epaylinks.efps.pas.acl.po.Perm;
import com.epaylinks.efps.pas.acl.service.PermService;
import com.epaylinks.efps.pas.common.PasCode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 权限管理控制器/即功能管理
 * 描述：导航、一级菜单、二级菜单、功能按钮
 */
@RestController
@RequestMapping("/Perm")
@Api(value = "PermController", description = "权限管理")
public class PermController {

	@Autowired
	private PermService permService;

    /**
     * 基础权限树
     */
    @GetMapping("basicTree")
    @Exceptionable
    @Validatable
    @Logable(businessTag = "Perm.basicTree")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "不传：默认全部；1：已配置数据权限列表", required = false, dataType = "String", paramType = "query")
    })
    @ApiOperation(value = "权限菜单树", notes = "权限菜单树", httpMethod = "GET")
    public BasicPermTreeResponse getBasicTree(String type) {
        BasicPermTreeResponse response;
        try {
            response = permService.getBasicTree(type);
        } catch (Exception e) {
            response = new BasicPermTreeResponse();
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);

            }
            return response;
        }
        return response;
    }

    /**
     * 查看某功能结点信息
     */
    @GetMapping("viewPerm")
    @Exceptionable
    @Validatable
    @Logable(businessTag = "viewPerm")
    public CommonOuterResponse viewPerm(@RequestParam Long permId) {
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            Perm perm = permService.viewPerm(permId);
            response.setData(perm);
        } catch (Exception e) {
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
            return response;
        }
        return response;
    }

    /**
     * 修改某功能结点信息
     */
    @PutMapping("/modifyPerm")
    @Exceptionable
    @Validatable
    @Logable(businessTag = "modifyPerm")
    @ApiOperation(value = "修改功能", notes = "修改功能", httpMethod = "PUT")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "permId", value = "功能ID", required = true, dataType = "Long", paramType = "query"),
            @ApiImplicitParam(name = "name", value = "功能名称(英文)", required = true, dataType = "String", paramType = "query", length = 25),
            @ApiImplicitParam(name = "alias", value = "功能别称(中文)", required = true, dataType = "String", paramType = "query", length = 25),
            @ApiImplicitParam(name = "url", value = "菜单URL(菜单时必填)", required = false, dataType = "String", paramType = "query", length = 100)
    })
    public CommonOuterResponse modifyPerm(@ApiIgnore Perm perm) {
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            permService.modifyPerm(perm);
        } catch (Exception e) {
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
            return response;
        }
        return response;
    }

    /**
     * 删除某功能结点
     */
    @DeleteMapping("/deletePerm")
    @Exceptionable
    @Validatable
    @Logable(businessTag = "deletePerm")
    @ApiOperation(value = "删除功能", notes = "删除功能", httpMethod = "DELETE")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "permId", value = "功能ID", required = true, dataType = "Long", paramType = "query")
    })
    public CommonOuterResponse deletePerm(@RequestParam Long permId) {
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            permService.deletePerm(permId);
        } catch (Exception e) {
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
            return response;
        }
        return response;
    }

    /**
     * 新增子功能
     */
    @PostMapping("/addSubPerm")
    @Exceptionable
    @Validatable
    @Logable(businessTag = "addSubPerm")
    @ApiOperation(value = "新增子功能", notes = "新增子功能", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "parentId", value = "父功能ID", required = true, dataType = "Long", paramType = "query"),
            @ApiImplicitParam(name = "name", value = "功能名称(英文)", required = true, dataType = "String", paramType = "query", length = 25),
            @ApiImplicitParam(name = "alias", value = "功能别称(中文)", required = true, dataType = "String", paramType = "query", length = 25),
            @ApiImplicitParam(name = "url", value = "菜单URL(菜单时必填)", required = false, dataType = "String", paramType = "query", length = 100)
    })
    public CommonOuterResponse addSubPerm(@ApiIgnore Perm subPerm) {
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            permService.addSubPerm(subPerm);
        } catch (Exception e) {
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
            return response;
        }
        return response;
    }
}
