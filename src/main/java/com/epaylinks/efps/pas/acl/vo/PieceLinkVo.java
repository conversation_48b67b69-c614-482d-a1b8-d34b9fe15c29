package com.epaylinks.efps.pas.acl.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 业务员进件链接信息
 * <AUTHOR>
 * @date 2021-05-08
 *
 */
@ApiModel
public class PieceLinkVo {
    
    @ApiModelProperty(value="用户ID", dataType = "String")
    private Long uid;
    
    @ApiModelProperty(value="自助进件H5链接", dataType = "String")
    private String pieceLink;

    @ApiModelProperty(value="链接状态：0：无效；1：有效", dataType = "Short")
    private Short picecLinkStatus;

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public String getPieceLink() {
        return pieceLink;
    }

    public void setPieceLink(String pieceLink) {
        this.pieceLink = pieceLink;
    }

    public Short getPicecLinkStatus() {
        return picecLinkStatus;
    }

    public void setPicecLinkStatus(Short picecLinkStatus) {
        this.picecLinkStatus = picecLinkStatus;
    }
    

}
