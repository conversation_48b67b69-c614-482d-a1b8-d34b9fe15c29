package com.epaylinks.efps.pas.acl.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.epaylinks.efps.pas.acl.po.User;

@Mapper
public interface UserMapper {

	int deleteByPrimaryKey(Long uid);

	int insert(User record );

	int insertSelective(User record);

	User selectByPrimaryKey(Long uid);

	int updateByPrimaryKeySelective(User record);

	int updateByPrimaryKey(User record);
	
	User selectByName(String name);

	List<User> selectBySelective(User user);

	List<User> getUserByIds(List<Long> ids);

	String selectEmail(@Param("userId") Long userId);

	List<User> selectNamesByIds(@Param("ids") List<Long> ids);

	//分页查询用户列表
	Integer selectByParam(Map Map);

	List<User> selectByParamByPage(Map map);

	List<User> selectRoleBySelective(User user);

	boolean checkExistOfName(@Param("name") String name);
	
	/**
	 * 判断正式姓名是否已存在
	 * @param realName
	 * @param selfUserId 自身id
	 * @return
	 */
	boolean checkExistOfRealName(@Param("realName") String realName, @Param("selfUserId") Long selfUserId);

	boolean checkExistOfPhone(@Param("phone") String phone);

	boolean checkExistOfPhoneWhenModify(@Param("phone") String phone, @Param("userId") Long userId);

	List<User> selectUserByParam(Map Map);

    List<User> queryByUserIdList(@Param("userIdList") List<Long> userIdList);

	List<User> selectListByName(@Param("name") String name);
	
	List<User> selectUserByRealParam(@Param("realName") String realName);
	   
	//搜索用户列表
	List<User> searchUser(Map map);
	
	List<User> listCompanyUser(@Param("companyId") Long companyId);
	
	User randomOneByCompany(@Param("companyId") Long companyId);

    String queryCustLoginName(Long userId);

    List<User> queryLockedUserList(@Param(value = "count") Long count);
    
    List<Long> queryUserIdsByPermId(@Param(value = "permId") Long permId, @Param(value = "userId") Long userId);

    int countAllCustUser();

	List<User> queryAllCustUserByPage(Map<String, Object> paramMap);

	List<Map> queryUserByName(@Param("name") String name);

	List<User> selectUserBySalesCode(@Param("salesCode") Long salesCode);

	String queryCompanyUrlByUserId(@Param("userId") Long userId);
}