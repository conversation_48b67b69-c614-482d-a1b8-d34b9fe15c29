package com.epaylinks.efps.pas.acl.controller.response;

import com.epaylinks.efps.common.business.CommonOuterResponse;

/**
 * 部门信息 - 响应类
 */
public class DepartmentResponse extends CommonOuterResponse {

    /**
     * ID
     */
    private Long id;

    /**
     * 父级部门ID
     */
    private Long parentId;

    /**
     * 部门名称
     */
    private String name;

    /**
     * 备注
     */
    private String remark;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
