package com.epaylinks.efps.pas.acl.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.util.PageResult;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.pas.acl.dto.CompanyNode;
import com.epaylinks.efps.pas.acl.po.Company;
import com.epaylinks.efps.pas.acl.service.CompanyService;
import com.epaylinks.efps.pas.acl.service.UserService;
import com.epaylinks.efps.pas.acl.vo.UserVo;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.pas.common.PasConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/**
 * 分公司管理控制器
 */
@RestController
@RequestMapping("/company")
@Api(value = "CompanyController", description = "分公司管理")
public class CompanyController {
	
	@Autowired
	private CompanyService companyService;
	@Autowired
	private UserService userService;

    /**
     * 分公司结构树
     */
    @GetMapping("/listTree")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "查询分公司结构树", notes = "查询分公司结构树", httpMethod = "GET")
    public CommonOuterResponse listTree(@RequestHeader(value = "x-userid", required = true) Long userId) {
        try {
            List<CompanyNode> nodeList = companyService.getTree( userId );
            return CommonOuterResponse.success(nodeList);
        } catch (Exception e) {
            if (e instanceof AppException) {
                return CommonOuterResponse.fail(((AppException) e).getErrorCode(), ((AppException) e).getErrorMsg());
            } else {
            	return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
            }
        }
    }
    
    
    /**
     * 分页查询分公司列表
     */
    @GetMapping("/page")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "分页查询分公司列表", notes = "分页查询分公司列表（管理员）", httpMethod = "GET")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "name", value = "用户名", required = false, dataType = "String",  paramType = "query"),
        @ApiImplicitParam(name = "realName", value = "分公司名称", required = false, dataType = "String",  paramType = "query"),
        @ApiImplicitParam(name = "parentCompanyId", value = "上级公司", required = false, dataType = "Long", paramType = "query"),
        @ApiImplicitParam(name = "status", value = "状态：Y:启用，N：禁用", required = false, dataType = "String", paramType = "query", valueRange = "{Y,N}"),
        @ApiImplicitParam(name = "companyStatus", value = "分公司状态", required = false, dataType = "Long", paramType = "query", digit = true),
        @ApiImplicitParam(name = "display", value = "是否显示明文：0:否； 1：是", required = false, dataType = "String", length = 1, paramType = "query"),
        @ApiImplicitParam(name = "pageNum", value = "查询开始页", required = true, dataType = "int", paramType = "query"),
        @ApiImplicitParam(name = "pageSize", value = "查询的页面大小", required = true, dataType = "int", paramType = "query")})
    public PageResult<UserVo> page(@RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "realName", required = false) String realName,
            @RequestParam(value = "parentCompanyId", required = false) Long parentCompanyId,
            @RequestParam(value = "status", required = false) String status,
            @RequestParam(value = "companyStatus", required = false) Short companyStatus,
            @RequestParam(value = "display", required = false) String display,
            @RequestParam(value = "pageNum", required = true) Integer pageNum,
            @RequestParam(value = "pageSize", required = true) Integer pageSize,
            @RequestHeader(value = "x-userid", required = true) Long userId) {
        
        try {
            // 校验明文显示权限
            if ("1".equals(display) && !userService.checkDecryptPermission(userId)) {
                PageResult<UserVo> userPage = new PageResult<UserVo>();
                userPage.setReturnCode(PasCode.NO_RIGHT_TO_QUERY.code);
                userPage.setReturnMsg(PasCode.NO_RIGHT_TO_QUERY.message);
                return userPage;
            }
            
            int endRowNo = pageNum * pageSize;
            int beginRowNo = (pageNum - 1) * pageSize + 1;
            
            Map map = new HashMap();
            map.put("name", name);
            map.put("realName", realName);
            map.put("parentCompanyId", parentCompanyId);
            map.put("status", status);
            map.put("companyStatus", companyStatus);
            map.put("beginRowNo", beginRowNo);
            map.put("endRowNo", endRowNo);
            map.put("userType", PasConstant.UserType.COMPANY.code);  // 分公司管理员
            map.put("operId", userId);
            map.put("display", display);
            
            com.epaylinks.efps.pas.common.PageResult<UserVo> page = userService.pageQuery(map);
            return PageResult.success(page.getRows(), page.getTotal());
        } catch (Exception e) {
            if (e instanceof AppException) {
            	return PageResult.fail(((AppException) e).getErrorCode(), ((AppException) e).getErrorMsg());
            } else {
            	return PageResult.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
            }
        }
    }


    /**
     * 查询公司（管理员）
     */
    @GetMapping("/query")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "查询分公司(管理员)", notes = "查询分公司(管理员)", httpMethod = "GET")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "uid", value = "业务员ID", required = true, dataType = "Long", paramType = "query"),
        @ApiImplicitParam(name = "display", value = "是否显示明文：0:否； 1：是", required = false, dataType = "String", length = 1, paramType = "query"),
    })
    public CommonOuterResponse query(
        @RequestParam(value = "uid", required = true)Long uid,
        @RequestParam(value = "display", required = false)String display    
    ){
        try {
            Map map = new HashMap();
            map.put("uid", uid);
            map.put("beginRowNo", 1);
            map.put("endRowNo", 1);
            map.put("userType", PasConstant.UserType.COMPANY.code);  // 分公司管理员
            map.put("display", display);

            com.epaylinks.efps.pas.common.PageResult<UserVo> userPage = userService.pageQuery(map);
            if (userPage != null && userPage.getRows() != null && userPage.getRows().size() > 0) {
            	return CommonOuterResponse.success(userPage.getRows().get(0));
            }
            return CommonOuterResponse.fail(PasCode.USER_NOT_EXIST.code, PasCode.USER_NOT_EXIST.message);
        } catch (Exception e) {
			if (e instanceof AppException) {
				return CommonOuterResponse.fail(((AppException) e).getErrorCode(), ((AppException) e).getErrorMsg());
			} else {
			 	return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
			}
        }
    }

    /**
     * 启用
     */
    @PostMapping("/active")
    @Exceptionable
    @Validatable
    @Logable(businessTag = "CompanyController.active")
    @ApiOperation(value = "启用", notes = "启用", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uid", value = "分公司ID", required = true, dataType = "Long", paramType = "query")
    })
    public CommonOuterResponse active(@RequestParam(value="uid" , required = true) Long uid,
    		@RequestHeader(value = "x-userid", required = true) Long userId) {
        try {
        	userService.changeStatus(uid, "Y", userId,null);
        	return CommonOuterResponse.success();
        } catch (Exception e) {
    		if (e instanceof AppException) {
				return CommonOuterResponse.fail(((AppException) e).getErrorCode(), ((AppException) e).getErrorMsg());
			} else {
			 	return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
			}
        }
    }

    /**
     * 禁用
     */
    @PostMapping("/frozen")
    @Exceptionable
    @Validatable
    @Logable(businessTag = "CompanyController.frozen")
    @ApiOperation(value = "禁用", notes = "禁用", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uid", value = "分公司ID", required = true, dataType = "Long", paramType = "query")
    })
    public CommonOuterResponse frozen(@RequestParam(value="uid" , required = true) Long uid,
    		@RequestHeader(value = "x-userid", required = true) Long userId) {
        try {
        	userService.changeStatus(uid, "N", userId,null);
        	return CommonOuterResponse.success();
        } catch (Exception e) {
    		if (e instanceof AppException) {
				return CommonOuterResponse.fail(((AppException) e).getErrorCode(), ((AppException) e).getErrorMsg());
			} else {
			 	return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
			}
        }
    }
    
    /**
     * 搜索分公司
     */
    @GetMapping("/search")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "搜索分公司", notes = "搜索分公司", httpMethod = "GET")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "key", value = "分公司名称", required = false, dataType = "String",  paramType = "query"),
        @ApiImplicitParam(name = "pageNum", value = "查询开始页", required = true, dataType = "int", paramType = "query"),
        @ApiImplicitParam(name = "pageSize", value = "查询的页面大小", required = true, dataType = "int", paramType = "query")})
    public CommonOuterResponse<List<Company>> search(
    	@RequestParam(value = "key", required = false) String key,
        @RequestParam(value = "pageNum", required = true) Integer pageNum,
        @RequestParam(value = "pageSize", required = true) Integer pageSize,
        @RequestHeader(value = "x-userid", required = true) Long userId) {
    	
        try {
            int endRowNo = pageNum * pageSize;
            int beginRowNo = (pageNum - 1) * pageSize + 1;
            Map map = new HashMap();
            map.put("key", key);
            map.put("beginRowNo", beginRowNo);
            map.put("endRowNo", endRowNo);
            map.put("status", "Y");
        	
            return CommonOuterResponse.success(companyService.searchCompany(map, userId));
        } catch (Exception e) {
            if (e instanceof AppException) {
            	return CommonOuterResponse.fail(((AppException) e).getErrorCode(), ((AppException) e).getErrorMsg());
            } else {
            	return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
            }
        }
    }       


    /**
     * 查询公司名
     */
    @GetMapping("/queryCompanyNameById")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "查询公司名称", notes = "查询公司名称", httpMethod = "GET")
    public String queryCompanyNameById(@RequestParam Long companyId) {
    
    	Company company = companyService.queryById(companyId);
        return company != null ? company.getName() : "";
    }
    
    /**
     * 查询所有叶子节点分公司
     */
    @GetMapping("/listLeafCompany")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "查询所有叶子节点分公司", notes = "查询所有叶子节点分公司", httpMethod = "GET")
    public CommonOuterResponse listLeafCompany() {
        try {
            List<Company> nodeList = companyService.queryLeafCompany();
            return CommonOuterResponse.success(nodeList);
        } catch (Exception e) {
            if (e instanceof AppException) {
                return CommonOuterResponse.fail(((AppException) e).getErrorCode(), ((AppException) e).getErrorMsg());
            } else {
                return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
            }
        }
    }

}
