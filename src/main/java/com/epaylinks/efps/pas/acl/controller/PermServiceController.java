package com.epaylinks.efps.pas.acl.controller;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.pas.acl.service.PermServiceService;
import com.epaylinks.efps.pas.common.PasCode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/PermService")
@Api(value = "PermServiceController", description = "权限与接口关联的管理")
public class PermServiceController {

	@Autowired
	private PermServiceService permServiceService;

    /**
     * 查看某功能关联的接口
     */
    @GetMapping("queryPermService")
    @Exceptionable
    @Validatable
    @Logable(businessTag = "queryPermService")
    public CommonOuterResponse queryPermService(@RequestParam Long permId) {
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            List<com.epaylinks.efps.pas.acl.po.PermService> list = permServiceService.queryPermService(permId);
            response.setData(list);
        } catch (Exception e) {
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
            return response;
        }
        return response;
    }

    /**
     * 新增某功能关联的接口
     */
    @PostMapping("/addPermService")
    @Exceptionable
    @Validatable
    @Logable(businessTag = "addPermService")
    @ApiOperation(value = "新增某功能关联的接口", notes = "新增某功能关联的接口", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "permId", value = "功能ID", required = true, dataType = "Long", paramType = "query"),
            @ApiImplicitParam(name = "serviceCode", value = "接口编码", required = true, dataType = "String", paramType = "query", length = 50),
    })
    public CommonOuterResponse addPermService(@RequestParam Long permId, @RequestParam String serviceCode) {
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            permServiceService.addPermService(permId, serviceCode);
        } catch (Exception e) {
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
            return response;
        }
        return response;
    }

    /**
     * 删除某功能关联的接口
     */
    @DeleteMapping("/deletePermService")
    @Exceptionable
    @Validatable
    @Logable(businessTag = "deletePermService")
    @ApiOperation(value = "删除某功能关联的接口", notes = "删除某功能关联的接口", httpMethod = "DELETE")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "permServiceId", value = "PermService ID", required = true, dataType = "Long", paramType = "query")
    })
    public CommonOuterResponse deletePermService(@RequestParam Long permServiceId) {
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            permServiceService.deletePermService(permServiceId);
        } catch (Exception e) {
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
            return response;
        }
        return response;
    }

    /**
     * 修改serviceCode
     * 调用场景：UAA修改ServiceCode的同步通知
     */
    @PutMapping("modifyServiceCode")
    @Exceptionable
    @Validatable
    @Logable(businessTag = "modifyServiceCode")
    public void modifyServiceCode(
            @RequestParam("origServiceCode") String origServiceCode,
            @RequestParam("newServiceCode") String newServiceCode) {
        permServiceService.modifyServiceCode(origServiceCode, newServiceCode);
    }

    /**
     * 删除serviceCode
     * 调用场景：UAA删除ServiceCode的同步通知
     */
    @DeleteMapping("deleteByServiceCode")
    @Exceptionable
    @Validatable
    @Logable(businessTag = "deleteByServiceCode")
    void deleteByServiceCode(@RequestParam("serviceCode") String serviceCode) {
        permServiceService.deleteByServiceCode(serviceCode);
    }
}
