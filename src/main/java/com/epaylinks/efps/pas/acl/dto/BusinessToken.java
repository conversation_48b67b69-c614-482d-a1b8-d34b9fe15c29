package com.epaylinks.efps.pas.acl.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class BusinessToken implements Serializable {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column UAA_BUSINESS_TOKEN.TOKEN_ID
     *
     * @mbggenerated Wed Aug 30 12:46:39 CST 2017
     */
    private Long tokenId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column UAA_BUSINESS_TOKEN.TOKEN
     *
     * @mbggenerated Wed Aug 30 12:46:39 CST 2017
     */
    private String token;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column UAA_BUSINESS_TOKEN.BUSINESS_TYPE
     *
     * @mbggenerated Wed Aug 30 12:46:39 CST 2017
     */
    private String businessType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column UAA_BUSINESS_TOKEN.USER_ID
     *
     * @mbggenerated Wed Aug 30 12:46:39 CST 2017
     */
    private Long userId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column UAA_BUSINESS_TOKEN.APPLY_DATE
     *
     * @mbggenerated Wed Aug 30 12:46:39 CST 2017
     */
    private Date applyDate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column UAA_BUSINESS_TOKEN.EFFECTIVE_DATE
     *
     * @mbggenerated Wed Aug 30 12:46:39 CST 2017
     */
    private Date effectiveDate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column UAA_BUSINESS_TOKEN.USE_TIMES
     *
     * @mbggenerated Wed Aug 30 12:46:39 CST 2017
     */
    private Long useTimes;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column UAA_BUSINESS_TOKEN.UPDATE_DATE
     *
     * @mbggenerated Wed Aug 30 12:46:39 CST 2017
     */
    private Date updateDate;

    /**
     * 用户类型，不保存到数据库
     */
    private Long userType;

    /**
     * 扩展字段，不保存到数据库
     */
    private String extra;

    /**
     * customerCode, 不保存到数据库
     */
    private String customerCode;

    /**
     * 用户角色编码，逗号隔开，不保存到数据库
     */
    private String roles;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column UAA_BUSINESS_TOKEN.TOKEN_ID
     *
     * @return the value of UAA_BUSINESS_TOKEN.TOKEN_ID
     * @mbggenerated Wed Aug 30 12:46:39 CST 2017
     */
    public Long getTokenId() {
        return tokenId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column UAA_BUSINESS_TOKEN.TOKEN_ID
     *
     * @param tokenId the value for UAA_BUSINESS_TOKEN.TOKEN_ID
     * @mbggenerated Wed Aug 30 12:46:39 CST 2017
     */
    public void setTokenId(Long tokenId) {
        this.tokenId = tokenId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column UAA_BUSINESS_TOKEN.TOKEN
     *
     * @return the value of UAA_BUSINESS_TOKEN.TOKEN
     * @mbggenerated Wed Aug 30 12:46:39 CST 2017
     */
    public String getToken() {
        return token;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column UAA_BUSINESS_TOKEN.TOKEN
     *
     * @param token the value for UAA_BUSINESS_TOKEN.TOKEN
     * @mbggenerated Wed Aug 30 12:46:39 CST 2017
     */
    public void setToken(String token) {
        this.token = token;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column UAA_BUSINESS_TOKEN.BUSINESS_TYPE
     *
     * @return the value of UAA_BUSINESS_TOKEN.BUSINESS_TYPE
     * @mbggenerated Wed Aug 30 12:46:39 CST 2017
     */
    public String getBusinessType() {
        return businessType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column UAA_BUSINESS_TOKEN.BUSINESS_TYPE
     *
     * @param businessType the value for UAA_BUSINESS_TOKEN.BUSINESS_TYPE
     * @mbggenerated Wed Aug 30 12:46:39 CST 2017
     */
    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column UAA_BUSINESS_TOKEN.USER_ID
     *
     * @return the value of UAA_BUSINESS_TOKEN.USER_ID
     * @mbggenerated Wed Aug 30 12:46:39 CST 2017
     */
    public Long getUserId() {
        return userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column UAA_BUSINESS_TOKEN.USER_ID
     *
     * @param userId the value for UAA_BUSINESS_TOKEN.USER_ID
     * @mbggenerated Wed Aug 30 12:46:39 CST 2017
     */
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column UAA_BUSINESS_TOKEN.APPLY_DATE
     *
     * @return the value of UAA_BUSINESS_TOKEN.APPLY_DATE
     * @mbggenerated Wed Aug 30 12:46:39 CST 2017
     */
    public Date getApplyDate() {
        return applyDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column UAA_BUSINESS_TOKEN.APPLY_DATE
     *
     * @param applyDate the value for UAA_BUSINESS_TOKEN.APPLY_DATE
     * @mbggenerated Wed Aug 30 12:46:39 CST 2017
     */
    public void setApplyDate(Date applyDate) {
        this.applyDate = applyDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column UAA_BUSINESS_TOKEN.EFFECTIVE_DATE
     *
     * @return the value of UAA_BUSINESS_TOKEN.EFFECTIVE_DATE
     * @mbggenerated Wed Aug 30 12:46:39 CST 2017
     */
    public Date getEffectiveDate() {
        return effectiveDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column UAA_BUSINESS_TOKEN.EFFECTIVE_DATE
     *
     * @param effectiveDate the value for UAA_BUSINESS_TOKEN.EFFECTIVE_DATE
     * @mbggenerated Wed Aug 30 12:46:39 CST 2017
     */
    public void setEffectiveDate(Date effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column UAA_BUSINESS_TOKEN.USE_TIMES
     *
     * @return the value of UAA_BUSINESS_TOKEN.USE_TIMES
     * @mbggenerated Wed Aug 30 12:46:39 CST 2017
     */
    public Long getUseTimes() {
        return useTimes;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column UAA_BUSINESS_TOKEN.USE_TIMES
     *
     * @param useTimes the value for UAA_BUSINESS_TOKEN.USE_TIMES
     * @mbggenerated Wed Aug 30 12:46:39 CST 2017
     */
    public void setUseTimes(Long useTimes) {
        this.useTimes = useTimes;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column UAA_BUSINESS_TOKEN.UPDATE_DATE
     *
     * @return the value of UAA_BUSINESS_TOKEN.UPDATE_DATE
     * @mbggenerated Wed Aug 30 12:46:39 CST 2017
     */
    public Date getUpdateDate() {
        return updateDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column UAA_BUSINESS_TOKEN.UPDATE_DATE
     *
     * @param updateDate the value for UAA_BUSINESS_TOKEN.UPDATE_DATE
     * @mbggenerated Wed Aug 30 12:46:39 CST 2017
     */
    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getExtra() {
        return extra;
    }

    public void setExtra(String extra) {
        this.extra = extra;
    }

    public Long getUserType() {
        return userType;
    }

    public void setUserType(Long userType) {
        this.userType = userType;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getRoles() {
        return roles;
    }

    public void setRoles(String roles) {
        this.roles = roles;
    }
}