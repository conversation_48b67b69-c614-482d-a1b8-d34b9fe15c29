package com.epaylinks.efps.pas.acl.controller;

import java.util.*;
import java.util.stream.Collectors;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import com.epaylinks.efps.common.log.CommonLogger;
import com.epaylinks.efps.pas.acl.dto.UserRoleDataAuthDTO;
import com.epaylinks.efps.pas.acl.service.*;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import com.alibaba.fastjson.JSON;
import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.business.cust.util.VerificationUtils;
import com.epaylinks.efps.common.business.pay.request.UserType;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.hessian.EncryptService;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.oplog.OpLog;
import com.epaylinks.efps.common.oplog.OpLogHandle;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.common.util.DateUtils;
import com.epaylinks.efps.common.util.IPAddressUtils;
import com.epaylinks.efps.common.util.MD5Utils;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.pas.acl.controller.response.LoginResponse;
import com.epaylinks.efps.pas.acl.controller.response.UserResponse;
import com.epaylinks.efps.pas.acl.po.User;
import com.epaylinks.efps.pas.acl.vo.UserVo;
import com.epaylinks.efps.pas.common.PageResult;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.common.PasConstants;
import com.epaylinks.efps.pas.common.PasswordCheck;
import com.epaylinks.efps.pas.mch.client.HssClient;
import com.epaylinks.efps.pas.mch.common.PasswordCreateUtil;
import com.epaylinks.efps.pas.pas.service.LoginInfoService;
import com.epaylinks.efps.pas.pas.service.LoginLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import com.epaylinks.efps.pas.mch.service.feign.CustService;

@RestController
@RefreshScope
@RequestMapping("/user")
@Api(value = "UserController", description = "系统用户管理")
public class UserController {
    @Autowired
    private UserService userService;

    @Autowired
    private SessionService sessionService;

    @Autowired
    private HssClient hssClient;

    @Autowired
    private SequenceService sequenceService;

    @Value("${passWord.salt}")
    private String passWordSalt;
    
    @Autowired
    private LoginLogService loginLogService;

    @Autowired
    private EncryptService encryptService;
    
    @Autowired
    private LoginInfoService loginInfoService;

    @Autowired
    private CustService custService;

    @Autowired
    private CommonLogger logger;

    @Autowired
    private GraphicService graphicService;

    @Autowired
    private RoleService roleService;

    @ExceptionHandler(Exception.class)
    public CommonOuterResponse handleException(Exception ex) {
        if (ex instanceof AppException) {
            return CommonOuterResponse.fail(((AppException) ex).getErrorCode(),((AppException) ex).getErrorMsg());
        } else {
            logger.printMessage("UserController error:" + ex.getMessage());
            logger.printLog(ex);
            return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code,PasCode.SYSTEM_EXCEPTION.message);
        }
    }

    @Validatable
    @Exceptionable
    @PostMapping("/createUser")
    @Logable(businessTag = "UserController.createUser")
    @OpLog(opModule = "系统管理-用户管理",opMethod = "新增用户")
    @ApiOperation(value = "新增用户管理", notes = "新增用户管理", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "deptId", value = "部门ID", required = true, dataType = "Long", length = 32, paramType = "query"),
            @ApiImplicitParam(name = "name", value = "用户名", required = true, dataType = "String", length = 20, paramType = "query"),
            @ApiImplicitParam(name = "realName", value = "真实名称", required = true, dataType = "String", length = 20, paramType = "query"),
            @ApiImplicitParam(name = "remark", value = "备注", required = false, dataType = "String", length = 100, paramType = "query"),
            @ApiImplicitParam(name = "password", value = "密码（RSA加密）", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "email", value = "邮箱", required = false, dataType = "String", length = 50, paramType = "query"),
            @ApiImplicitParam(name = "mobile", value = "手机", required = false, dataType = "String", length = 20, paramType = "query"),
            @ApiImplicitParam(name = "dataAuth", value = "数据查看权限（0：全量；1：自定义；2：本部门；3：本人）", required = false, dataType = "String", length = 20, paramType = "query"),
            @ApiImplicitParam(name = "roleIdList", value = "角色ID,逗号分隔", required = true, dataType = "String", paramType = "query")})
    public CommonOuterResponse createUser(@RequestParam Long deptId,
                                          @RequestParam String name,
                                          @RequestParam String realName,
                                          @RequestParam(required = false) String remark,
                                          @RequestParam String password,
                                          @RequestParam(required = false) String email,
                                          @RequestParam String mobile,
                                          @RequestParam(defaultValue = "0") String dataAuth,
                                          @RequestParam String roleIdList,
                                          @RequestHeader(value = "x-userid") Long userId,
                                          @RequestHeader(value = "x-user-type") String userType) throws Exception {
        password = sessionService.decryptPwd(password);
        if (!PasswordCheck.loginPasswordCheck(password)) {
            throw new AppException(PasCode.NEW_PASSWORD_TOO_SIMPLE.code, PasCode.NEW_PASSWORD_TOO_SIMPLE.message);
        }
        if (!userType.equalsIgnoreCase(UserType.PAS_USER.code)) {
            throw new AppException(Constants.ReturnCode.FAIL.code, Constants.ReturnCode.FAIL.comment + "：非运营管理系统用户");
        }
        User createrUser = userService.selectUserById(userId);
        if (createrUser == null) {
            throw new AppException(PasCode.USER_NOT_EXIST.code, PasCode.USER_NOT_EXIST.message);
        }
        if (userService.selectByName(name) != null) {
            throw new AppException(PasCode.USER_NAME_EXIST.code, PasCode.USER_NAME_EXIST.message);
        }
        List<Long> roleIds = Arrays.asList(roleIdList.split(",")).stream().map(Long::parseLong).collect(Collectors.toList());
        if (roleService.judgeRoleDataByRoleIds(roleIds, dataAuth)) {
            throw new AppException(PasCode.NOT_ACROSS_DATA_PERMISSION.code,PasCode.NOT_ACROSS_DATA_PERMISSION.message);
        }
        User user = new User();
        user.setUid(sequenceService.nextValue("pas_user"));
        user.setSalesCode(sequenceService.nextValue("SALES_CODE"));
        user.setDeptId(deptId);
        user.setCompanyId(deptId);
        user.setName(name);
        user.setRealName(realName);
        user.setRemark(remark);
        String sm3Pwd = encryptService.encryptData(password, "A02");
        user.setPassword(encryptService.symmetricEncryptData(sm3Pwd));
        user.setCreator(createrUser.getName());
        user.setCreateTime(new Date());
        user.setEmail(email);
        user.setMobile(mobile);
        user.setStatus(PasConstants.UserStatus.YES.code);

        userService.create(user, roleIdList, userId);
        return CommonOuterResponse.success();
    }

    @Validatable
    @Exceptionable
    @RequestMapping(value = "/selectUserById", method = RequestMethod.GET)
    @Logable(businessTag = "UserController.selectUserById")
    @ApiOperation(value = "查询用户信息", notes = "查询用户信息", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uid", value = "用户ID", required = true, dataType = "Long", length = 20, paramType = "query"),
            @ApiImplicitParam(name = "display", value = "是否显示明文：0:否； 1：是", required = false, dataType = "String", length = 1, paramType = "query")
    })
    public UserResponse selectUserById(@RequestParam(value = "uid") Long uid,
                                       @RequestParam(value = "display", required = false) String display) {
        UserResponse response = new UserResponse();
        Map map = new HashMap();
        map.put("uid", uid);
        map.put("beginRowNo", 1);
        map.put("endRowNo", 1);
        map.put("display", display); // 脱敏字段是否解展示明文
        
        PageResult<UserVo> userPage = userService.pageQuery(map);
        if (!userPage.getRows().isEmpty()) {
            UserVo userVo = userPage.getRows().get(0);
            BeanUtils.copyProperties(userVo, response);
            List<UserRoleDataAuthDTO> userRoleDataAuthDTOS = roleService.selectRoleDataAuthByUserId(uid,null);
            List<String> dataAuths = userRoleDataAuthDTOS.stream().map(data -> data.getDataAuth()).distinct().collect(Collectors.toList());
            response.setDataAuth(dataAuths.isEmpty() ? null : dataAuths.get(0));
        }
        return response;
    }

    @Validatable
    @Exceptionable
    @PostMapping("/updateUser")
    @Logable(businessTag = "UserController.updateUser")
    @ApiOperation(value = "更新用户管理", notes = "更新用户管理", httpMethod = "POST")
    @OpLog(opModule = "系统管理-用户管理",opMethod = "修改用户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uid", value = "用户ID", required = true, dataType = "Long", length = 20, paramType = "query"),
            @ApiImplicitParam(name = "deptId", value = "部门ID", required = true, dataType = "Long", length = 32, paramType = "query"),
            @ApiImplicitParam(name = "realName", value = "真实名称", required = false, dataType = "String", length = 20, paramType = "query"),
            @ApiImplicitParam(name = "remark", value = "备注", required = false, dataType = "String", length = 100, paramType = "query"),
            @ApiImplicitParam(name = "email", value = "邮箱", required = false, dataType = "String", length = 50, paramType = "query"),
            @ApiImplicitParam(name = "mobile", value = "手机", required = true, dataType = "String", length = 20, paramType = "query"),
            @ApiImplicitParam(name = "dataAuth", value = "数据查看权限（0：全量；1：自定义；2：本部门；3：本人）", required = false, dataType = "String", length = 20, paramType = "query"),
            @ApiImplicitParam(name = "roleIdList", value = "角色ID,逗号分隔", required = true, dataType = "String", paramType = "query")})
    public CommonOuterResponse updateUser(@RequestParam Long uid,
                                          @RequestParam Long deptId,
                                          @RequestParam(required = false) String realName,
                                          @RequestParam(required = false) String remark,
                                          @RequestParam(required = false) String email,
                                          @RequestParam String mobile,
                                          @RequestParam(defaultValue = "0") String dataAuth,
                                          @RequestParam String roleIdList,
                                          @RequestHeader(value = "x-userid") Long userId) {
        User createrUser = userService.selectUserById(userId);
        if (createrUser == null) {
            throw new AppException(PasCode.USER_NOT_EXIST.code, PasCode.USER_NOT_EXIST.message);
        }
        List<Long> roleIds = Arrays.asList(roleIdList.split(",")).stream().map(Long::parseLong).collect(Collectors.toList());
        if (roleService.judgeRoleDataByRoleIds(roleIds, dataAuth)) {
            throw new AppException(PasCode.NOT_ACROSS_DATA_PERMISSION.code,PasCode.NOT_ACROSS_DATA_PERMISSION.message);
        }
        User user = new User();
        user.setUid(uid);
        user.setDeptId(deptId);
        user.setCompanyId(deptId);
        user.setRealName(realName);
        user.setRemark(remark);
        user.setUpdator(createrUser.getName());
        user.setUpdateTime(new Date());
        user.setEmail(email);
        user.setMobile(mobile);


        userService.update(user, roleIdList, String.valueOf(userId));
        return CommonOuterResponse.success();
    }

    @PostMapping("/modifyUser")
    @Logable(businessTag = "UserController.modifyUser")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "启用/禁用/锁定用户", notes = "启用/禁用/锁定用户", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uid", value = "用户ID", required = true, dataType = "Long", length = 20, paramType = "query"),
            @ApiImplicitParam(name = "status", value = "状态:Y:启用；N：禁用；L:锁定", valueRange = "{Y,N,L}", required = true, dataType = "String", length = 1, paramType = "query")
    })
    @OpLog(opModule = "系统管理-用户管理",opMethod = "更新用户状态")
    public UserResponse modifyUser(
            Long uid, String status,
            @RequestHeader(value = "x-userid", required = true) String userId) {
        UserResponse response = new UserResponse();
        try {
            User createrUser = userService.selectUserById(Long.parseLong(userId));
            if (createrUser == null) {
                response.setReturnCode(PasCode.USER_NOT_EXIST.code);
                response.setReturnMsg(PasCode.USER_NOT_EXIST.message);
                return response;
            }
            User oldUser = userService.selectUserById(uid);
            if (oldUser == null) {
                response.setReturnCode(PasCode.USER_NOT_EXIST.code);
                response.setReturnMsg(PasCode.USER_NOT_EXIST.message);
                return response;
            }
            // 限制禁用的用户不能锁定
            if (PasConstants.UserStatus.NO.code.equals(oldUser.getStatus()) && PasConstants.UserStatus.LOCK.code.equals(oldUser.getStatus())) {
                response.setReturnCode(PasCode.USER_NOT_EXIST.code);
                response.setReturnMsg(PasCode.USER_NOT_EXIST.message);
                throw new AppException(PasCode.USER_STATUS_DISABLE_CAN_NOT_LOCK.code, PasCode.USER_STATUS_DISABLE_CAN_NOT_LOCK.message);
            }

            String oldStatus = oldUser.getStatus();
            User u = new User();
            u.setUid(uid);
            u.setUpdator(createrUser.getName());
            u.setUpdateTime(new Date());
            u.setStatus(status);
            userService.modifyUser(u);
            
            if (PasConstants.UserStatus.LOCK.code.equals(oldStatus) && PasConstants.UserStatus.YES.code.equals(u.getStatus())) {
                // 重新启用用户，锁定状态解锁, 登录错误次数清空 2020.11.13
                loginInfoService.recoveryErrorCountByUsernameAndType(oldUser.getName(), UserType.PAS_USER.code);
            }
            OpLogHandle.setOpContent("更新用户"+ oldUser.getName() +"状态为："+ PasConstants.UserStatus.getCommentByCode(u.getStatus()));   
        } catch (Exception e) {
            response = new UserResponse();
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
            return response;
        }
        return response;
    }

    @RequestMapping(value = "/selectList", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @Logable(businessTag = "UserController.selectByPage")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "用户分页列表", notes = "用户分页列表", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startTime", value = "开始时间（yyyyMMdd）", required = false, dataType = "String", length = 20, paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "结束时间（yyyyMMdd）", required = false, dataType = "String", length = 20, paramType = "query"),
            @ApiImplicitParam(name = "name", value = "用户名", required = false, dataType = "String", length = 20, paramType = "query"),
            @ApiImplicitParam(name = "realName", value = "真实名称", required = false, dataType = "String", length = 20, paramType = "query"),
            @ApiImplicitParam(name = "status", value = "状态：Y:启用；N:禁用；L:锁定", valueRange = "{Y,N,L}", required = false, dataType = "String", length = 1, paramType = "query"),
            @ApiImplicitParam(name = "roleName", value = "角色名称", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "deptName", value = "部门名称", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "salesCode", value = "业务员代码", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "canDecrypt", value = "解密权限：0:否； 1：是", required = false, dataType = "Short", length = 1, paramType = "query"),
            @ApiImplicitParam(name = "dataAuth", value = "角色类型（0：全量；1：自定义；2：本部门；3：本人）", required = false, dataType = "String", length = 2, paramType = "query"),
            @ApiImplicitParam(name = "display", value = "是否显示明文：0:否； 1：是", required = false, dataType = "String", length = 1, paramType = "query"),
            @ApiImplicitParam(name = "pageNum", value = "查询开始页", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "查询的页面大小", required = true, dataType = "int", paramType = "query")
    })
    public PageResult<UserVo> selectByPage(
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime,
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "realName", required = false) String realName,
            @RequestParam(value = "status", required = false) String status,
            @RequestParam(value = "roleName", required = false) String roleName,
            @RequestParam(value = "deptName", required = false) String deptName,
            @RequestParam(value = "salesCode", required = false) String salesCode,
            @RequestParam(value = "canDecrypt", required = false) Short canDecrypt,
            @RequestParam(required = false) String dataAuth,
            @RequestParam(value = "display", required = false) String display,
            @RequestParam(value = "pageNum", required = true) Integer pageNum,
            @RequestParam(value = "pageSize", required = true) Integer pageSize,
            @RequestHeader(value = "x-userid", required = true) Long userId) {
        
        int endRowNo = pageNum * pageSize;
        int beginRowNo = (pageNum - 1) * pageSize + 1;

        Map map = new HashMap();
        map.put("startTime",startTime);
        map.put("endTime",endTime);
        map.put("name", name);
        map.put("realName", realName);
        map.put("status", status);
        map.put("roleName", roleName);
        map.put("deptName", deptName);
        map.put("salesCode",salesCode);
        map.put("dataAuth",dataAuth);
        map.put("beginRowNo", beginRowNo);
        map.put("endRowNo", endRowNo);
        map.put("canDecrypt", canDecrypt); // 查询条件
        map.put("display", display); // 脱敏字段是否解展示明文
        
        // 校验明文显示权限
        if ("1".equals(display) && !userService.checkDecryptPermission(userId)) {
            PageResult<UserVo> userPage = new PageResult<UserVo>();
            userPage.setReturnCode(PasCode.NO_RIGHT_TO_QUERY.code);
            userPage.setReturnMsg(PasCode.NO_RIGHT_TO_QUERY.message);
            return userPage;
        }
        
        PageResult<UserVo> userPage = new PageResult();
        try {
            userPage = userService.pageQuery(map);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return userPage;
    }  
  

    /**
     * 删除用户管理
     *
     * @param uid
     * @return
     */
    @RequestMapping(value = "/deleteUser", method = RequestMethod.DELETE)
    @Logable(businessTag = "UserController.deleteUser")
    @Exceptionable
    @Validatable
    public UserResponse deleteUser(@RequestParam("uid") Long uid,
                                   @RequestHeader(value = "x-userid", required = true) String userId) {
        UserResponse response = new UserResponse();
        try {
        	 response.setReturnCode(PasCode.USER_CAN_NOT_DELETE.code);
             response.setReturnMsg(PasCode.USER_CAN_NOT_DELETE.message);
        } catch (Exception e) {
            response = new UserResponse();
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
            return response;
        }
        return response;
    }

    /**
     * 修改密码
     *
     * @param uid
     * @return
     */
    @Logable(businessTag = "UserController.updateUserPwd")
    @Exceptionable
    @Validatable
    @RequestMapping(value = "/PasswordModify", method = RequestMethod.POST)   //FROM PUT TO POST
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uid", value = "用户ID", required = true, dataType = "Long", length = 20, paramType = "query"),
            @ApiImplicitParam(name = "newPwd", value = "新密码(RSA加密)", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "mobile", value = "手机号", required = true, dataType = "String", length = 20, paramType = "query"),
            @ApiImplicitParam(name = "captcha", value = "验证码", required = true, dataType = "String", paramType = "query")})
    @OpLog(opModule = "系统管理-用户管理",opMethod = "修改用户密码")
    public UserResponse updateUserPwd(@RequestParam("uid") Long uid,
                                      @RequestParam("newPwd") String newPwd,
                                      @RequestParam("mobile") String mobile,
                                      @RequestParam("captcha") String captcha,
                                      @RequestHeader(value = "x-userid", required = true) String userId) {
        
        UserResponse response = new UserResponse();
        User operatorUser = userService.selectUserById(Long.parseLong(userId));
        if (operatorUser == null) {
            response.setReturnCode(PasCode.USER_NOT_EXIST.code);
            response.setReturnMsg(PasCode.USER_NOT_EXIST.message);
            return response;
        }
        if (operatorUser.getUid().longValue() != uid.longValue()) {
            response.setReturnCode(PasCode.USER_NOT_EXIST.code);
            response.setReturnMsg(PasCode.USER_NOT_EXIST.message);
            return response;
        }
        String oldMobile = "";
        try {
            oldMobile = encryptService.newDecryptData(operatorUser.getEnMobile());
        } catch (Exception e) {
            e.printStackTrace();
            response.setReturnCode(PasCode.MOBILE_PHONE_INVALID.code);
            response.setReturnMsg(PasCode.MOBILE_PHONE_INVALID.message);
            return response;
        }
        if (!oldMobile.equals(mobile)) {
            response.setReturnCode(PasCode.USER_NOT_EXIST.code);
            response.setReturnMsg(PasCode.USER_NOT_EXIST.message);
            return response;
        }
        try {
            newPwd = sessionService.decryptPwd(newPwd);
            int count = this.userService.updateUserPwd(uid, newPwd, captcha);   //oldPwd
            if (count == 1) {
                response.setUid(uid);
                User oldUser = userService.selectUserById(uid);
                OpLogHandle.setOpContent("修改用户" + oldUser.getName() + "密码");   
            }
        } catch (Exception e) {
            response = new UserResponse();
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
            return response;
        }
        return response;
    }

    /**
     * 运营用户登录入口
     */
    @PostMapping("Session/login")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "运营用户登录", notes = "运营用户登录", httpMethod = "POST")
//    @Logable(businessTag = "UserController.login")
    @Deprecated
    public LoginResponse login(@RequestParam("name") String name,
                               @RequestParam("passWord") String passWord,
                               HttpServletRequest request, HttpServletResponse response) {
        LoginResponse loginResponse;
        try {
            loginResponse = sessionService.login(name, passWord);
            
            // 登录成功日志
            loginLogService.savePasLoginLog(name, "1", IPAddressUtils.getRealIp(request), "登录成功", false);
        } catch (Exception e) {
            loginResponse = new LoginResponse();
            boolean pwdErrow = false;
            if (e instanceof AppException) {
                if (PasCode.LOGIN_PWD_ERORR.code.equals(((AppException) e).getErrorCode())) {
                    pwdErrow = true;
                }
                loginResponse.setReturnCode(((AppException) e).getErrorCode());
                loginResponse.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                loginResponse.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                loginResponse.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
            // 登录失败日志
            loginLogService.savePasLoginLog(name, "0", IPAddressUtils.getRealIp(request), loginResponse.getReturnMsg(), pwdErrow);
            return loginResponse;
        }

        // token和userName写入Cookie【TODO 前端特殊要求，待确定是否可去掉
        Cookie cookie = new Cookie("token", loginResponse.getToken());
        cookie.setPath("/");
        cookie.setSecure(true);
//        cookie.setHttpOnly(true);
        response.addCookie(cookie);
        Cookie cookie2 = new Cookie("userName", loginResponse.getUserName());
        cookie2.setSecure(true);
//        cookie2.setHttpOnly(true);
        response.addCookie(cookie2);

        return loginResponse;
    }

    /**
     * 检查用户名是否存在
     *
     * @return
     */
    @RequestMapping(value = "/userNameValid", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @Logable(businessTag = "UserController.userNameValid")
    @Exceptionable
    @Validatable
    @ApiImplicitParams({
            @ApiImplicitParam(name = "name", value = "用户名", required = true, dataType = "String", length = 20, paramType = "query")})
    public String userNameValid(@RequestParam(value = "name", required = true) String name) {

        HashMap<String, String> map = new HashMap<>();
        map.put("valid", userService.selectByName(name) == null ? "true" : "false");
        return JSON.toJSONString(map);
    }

    @RequestMapping(value = "/realNameCheck", method = RequestMethod.GET)
    @Logable(businessTag = "UserController.realNameCheck")
    @Exceptionable
    @ApiOperation(value = "检查真实姓名是否存在", notes = "检查真实姓名是否存在", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uid", value = "用户ID", required = false, dataType = "int", length = 20, paramType = "query"),
            @ApiImplicitParam(name = "realName", value = "真实名称", required = false, dataType = "String", length = 20, paramType = "query"),
    })
    public CommonOuterResponse realNameCheck(Long uid, String realName,
            @RequestHeader(value = "x-userid", required = true) String userId) {
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            List<User> list = userService.queryUserListByRealName(realName);
            if(list!=null && !list.isEmpty()){
                if(Objects.isNull(uid)){
                    response.setReturnCode(PasCode.ROLE_NAME_EXIST.code);
                    response.setReturnMsg(PasCode.ROLE_NAME_EXIST.message);
                    return response;
                }
                for(User user : list){
                    if(!String.valueOf(user.getUid()).equals(String.valueOf(uid))){
                        response.setReturnCode(PasCode.ROLE_NAME_EXIST.code);
                        response.setReturnMsg(PasCode.ROLE_NAME_EXIST.message);
                        return response;
                    }
                }
            }
        } catch (Exception e) {
            logger.printMessage("检查真实姓名是否存在，异常："+e.getLocalizedMessage());
            logger.printLog(e);
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
        }
        return response;
    }

    @RequestMapping(value = "/restUserPwd", method = RequestMethod.POST)
//    @Logable(businessTag = "UserController.restUserPwd")
    @Exceptionable
    @Validatable
    @OpLog(opModule = "系统管理-用户管理",opMethod = "重置用户密码")
    @ApiOperation(value = "重置用户密码", notes = "重置用户密码", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uid", value = "用户ID", required = true, dataType = "Long", length = 20, paramType = "query")

    })
    public UserResponse restUserPwd(
            Long uid,
            @RequestHeader(value = "x-userid", required = true) String userId) {
        UserResponse response = new UserResponse();
        try {
            User operatorUser = userService.selectUserById(Long.parseLong(userId));
            if (operatorUser == null) {
                response.setReturnCode(PasCode.USER_NOT_EXIST.code);
                response.setReturnMsg(PasCode.USER_NOT_EXIST.message);
                return response;
            }
            User oldUser = userService.selectUserById(uid);
            if (oldUser == null) {
                response.setReturnCode(PasCode.USER_NOT_EXIST.code);
                response.setReturnMsg(PasCode.USER_NOT_EXIST.message);
                return response;
            }
            User u = new User();
            u.setUid(uid);
            String pwd = PasswordCreateUtil.createPassWord(8);
            // sm3转为sm4  20220815 sm4(sm3(password))
            String sm3Pwd = encryptService.encryptData(pwd, "A02");
            u.setPassword(encryptService.symmetricEncryptData(sm3Pwd));
//            u.setPassword(sm3Pwd);
            u.setUpdator(operatorUser.getName());
            u.setStatus("Y");
            // 重置密码更新过期时间
            int pwdCanUseDate = Integer.parseInt(custService.queryStaticParam("RC_PARAM", "PWD_CAN_USE_DATE"));
            u.setPasswordUpdateTime(new Date());
            u.setPasswordExpiredTime(DateUtils.add(u.getPasswordUpdateTime(), DateUtils.Field.DATE, pwdCanUseDate));

            int count = this.userService.modifyUser(u);
            if (count == 1) {
                // 错误次数清零
                loginInfoService.recoveryErrorCountByUsernameAndType(oldUser.getName(), UserType.PAS_USER.code);

                response.setUid(uid);
                //调用发短信功能
                String oldMobile = "";
                try {
                    oldMobile = encryptService.newDecryptData(oldUser.getEnMobile());
                } catch (Exception e) {
                    e.printStackTrace();
                    response.setReturnCode(PasCode.MOBILE_PHONE_INVALID.code);
                    response.setReturnMsg(PasCode.MOBILE_PHONE_INVALID.message);
                    return response;
                }
                hssClient.sendSms(oldMobile,pwd);
                OpLogHandle.setOpContent("重置用户" + oldUser.getName() + "密码");   
            }
        } catch (Exception e) {
            response = new UserResponse();
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
            return response;
        }
        return response;
    }

    @Logable(businessTag = "sendCaptcha", outputArgs = true)
    @Exceptionable
    @ApiOperation(value = "发送短信验证码", notes = "发送短信验证码", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "mobile", value = "手机号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "serviceCode", value = "业务代码", required = true, dataType = "String", paramType = "query")})
    @RequestMapping(value = "/sendCaptcha", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public UserResponse sendCaptcha(
            @RequestParam String mobile,
            @RequestParam String serviceCode,
            @RequestParam(value = "uid",required = false) String uid,
            @RequestParam(value = "graphicCode",required = false) String graphicCode,
            @RequestHeader(value = "x-userId",required = false) Long userId) {
        UserResponse response = new UserResponse();
        try {
            // 图形验证码校验
            if (StringUtils.isNotBlank(uid)) {
                if (!graphicService.validate(uid, graphicCode)) {
                    throw new AppException(PasCode.GRAPHIC_CODE_INVALID.code,PasCode.GRAPHIC_CODE_INVALID.message);
                }
            }
            String userName = mobile;
            if (userId != null) {
                userName = userService.queryUserByIdNoDecrypt(userId).getName();
            }
            // 使用新SMS
            sessionService.sendSMS(mobile,serviceCode,userName,"运营门户修改密码");
//            callUAA.sendVerifyCode(mobile, serviceCode);
        } catch (Exception e) {
            response = new UserResponse();
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
            return response;
        }
        return response;
    }

//    @Logable(businessTag = "findPassword", outputArgs = true)
    @Exceptionable
    @ApiOperation(value = "找回用户登录密码", notes = "忘记密码", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "mobile", value = "手机号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "newPwd", value = "新密码（RSA加密）", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "captcha", value = "验证码", required = true, dataType = "String", paramType = "query")})
    @RequestMapping(value = "/findMyPassword", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public UserResponse findMyPassword(
            @RequestParam String mobile,
            @RequestParam String newPwd,
            @RequestParam String captcha
    ) {
        UserResponse response = new UserResponse();
        try {
            newPwd = sessionService.decryptPwd(newPwd);
            
            userService.findMyPassword(mobile, newPwd, captcha);
        } catch (Exception e) {
            response = new UserResponse();
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
            return response;
        }
        return response;
    }

    @GetMapping("getUserNameByUserId")
    @Logable(businessTag = "getUserNameByUserId")
    @Exceptionable
    public String getUserNameByUserId(@RequestParam Long userId) {
        User user = userService.queryUserById(userId);
        if (user != null) {
            return user.getName();
        }
        return "";    
    }

    @GetMapping("getUserRealNameByUserId")
    @Logable(businessTag = "getUserRealNameByUserId")
    @Exceptionable
    public String getUserRealNameByUserId(@RequestParam Long userId) {
        User user = userService.queryUserByIdNoDecrypt(userId);
        if (user != null) {
            return user.getRealName();
        }
        return "";
    }
    
    @GetMapping("queryUserByRealName")
    @Logable(businessTag = "queryUserByRealName")
    @Exceptionable
    public User queryUserByRealName(@RequestParam String realName) {
        User user = userService.queryUserByRealName(realName);
        return user;
    }

    @GetMapping("queryUserBySalesCode")
    @Logable(businessTag = "queryUserBySalesCode")
    @Exceptionable
    public User queryUserBySalesCode(@RequestParam Long salesCode) {
        User user = userService.queryUserBySalesCode(salesCode);
        return user;
    }
    
    

    @GetMapping("getUserEmailByUserId")
    @Logable(businessTag = "getUserEmailByUserId")
    @Exceptionable
    public String getUserEmailByUserId(@RequestParam Long userId) {
        User user = userService.queryUserById(userId);
        if (user != null) {
            String email = "";
            try {
                email = encryptService.newDecryptData(user.getEnEmail());
            } catch (Exception e) {
                e.printStackTrace();
            }
            return email;
        }
        return "";
    }
    
    @GetMapping("getUserByName")
    @Logable(businessTag = "getUserByName")
    @Exceptionable
    public User getUserByName(@RequestParam String name) {
        return userService.selectByName(name);
    }
    
    @GetMapping("getUserByUserId")
    @Logable(businessTag = "getUserByUserId")
    @Exceptionable
    public User getUserByUserId(@RequestParam Long userId) {
    	
        return userService.queryUserById(userId);
    }
    
    /**
     * 根据userId获取name（批量）
     *
     * @return
     */
    @GetMapping("queryMapOfUserIdAndName")
    @Logable(businessTag = "queryMapOfUserIdAndName")
    public Map<Long, String> queryMapOfUserIdAndName(@RequestParam("userIdList") List<Long> userIdList) {
        return userService.queryMapOfUserIdAndName(userIdList);
    }

    @GetMapping(value = "/selectListByName")
    @Logable(businessTag = "UserController.selectListByName")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "根据名字模糊查询用户", notes = "根据名字模糊查询用户", httpMethod = "GET")
    @ApiImplicitParams({@ApiImplicitParam(name = "name", value = "用户名", dataType = "String", required = true, length = 20, paramType = "query")})
    public PageResult<User> selectListByName(@RequestParam(value = "name") String name) {

        try {
            return userService.selectListByName(name);
        } catch (Exception e) {
            PageResult response = new PageResult<User>();
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
            return response;
        }
    }
    
    /**
     * 启用/禁用用户
     *
     * @param uid
     * @return
     */
    @PostMapping("/changeDecryptPermit")
    @Logable(businessTag = "UserController.changeDecryptPermit")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "修改解密权限", notes = "修改解密权限", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uid", value = "用户ID", required = true, dataType = "Long", length = 20, paramType = "query"),
            @ApiImplicitParam(name = "canDecrypt", value = "是否允许解密：0：不允许，1：允许", required = true, dataType = "Long", length = 1, paramType = "query", valueRange = "{0,1}")
    })
    @OpLog(opModule = "系统管理-用户管理",opMethod = "修改解密权限")
    public CommonOuterResponse changeDecryptPermit(
            Long uid, Long canDecrypt,
            @RequestHeader(value = "x-userid", required = true) String userId) {
        try {
            User createrUser = userService.selectUserById(Long.parseLong(userId));
            if (createrUser == null) {
                return CommonOuterResponse.fail(PasCode.USER_NOT_EXIST.code, PasCode.USER_NOT_EXIST.message);
            }
            
            User oldUser = userService.selectUserById(uid);
            if (oldUser == null) {
                return CommonOuterResponse.fail(PasCode.USER_NOT_EXIST.code, PasCode.USER_NOT_EXIST.message);
            }

            User u = new User();
            u.setUid(uid);
            u.setUpdator(createrUser.getName());
            u.setUpdateTime(new Date());
            u.setCanDecrypt(canDecrypt.shortValue());
            userService.modifyUser(u);
            
            OpLogHandle.setOpContent("修改用户"+oldUser.getName() +"解密权限为："+ (canDecrypt == 1L ? "允许" :"不允许"));   

            return CommonOuterResponse.success();
        } catch (AppException e) {
            return CommonOuterResponse.fail(e.getErrorCode(), e.getErrorMsg());

        } catch (Exception e) {
            return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
        }
    }
    
    private String getCookieValue(Cookie[] cookies, String cookieName) {
        if (cookies == null)
            return null;
        else {
            for (Cookie cookie : cookies) {
                if (cookieName.equals(cookie.getName())) {
                    return cookie.getValue();
                }
            }
        }
        return null;
    }

    @PostMapping("/checkUserPwd")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "校验用户登录密码", notes = "校验用户登录密码", httpMethod = "POST")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "password", value = "密码(RAS加密)", required = true, dataType = "String", paramType = "query"),
    })
    public CommonOuterResponse<Boolean> checkUserPwd(
       @RequestParam("password") String password,
       HttpServletRequest request) {
        
        try {
            password = sessionService.decryptPwd(password);
            // 不需鉴权接口，通过cookie获取用户ID
            String userIdStr = getCookieValue(request.getCookies(), "userId");
            if (userIdStr == null || !VerificationUtils.isNumber(userIdStr)) {
                return CommonOuterResponse.fail(PasCode.LOGIN_USER_NOT_EXIST.code, PasCode.LOGIN_USER_NOT_EXIST.message);
            }
            
            Long userId = Long.parseLong(userIdStr);
            User user = userService.setUserById(userId);
            if (user == null) {
                return CommonOuterResponse.fail(PasCode.LOGIN_USER_NOT_EXIST.code, PasCode.LOGIN_USER_NOT_EXIST.message);
            }

            String pwd = user.getPassword();
            String sm3Pwd = encryptService.encryptData(password, "A02");
            boolean checkSuccess = false;
            if (pwd.indexOf("$b") == 0){
                //sm4加密
                String sm4Pwd = encryptService.symmetricEncryptData(sm3Pwd);
                if (sm4Pwd.equals(pwd)) {
                    checkSuccess = true;
                }
            } else if (pwd.indexOf("$A02") == 0) {
                // 新版国密加密
                if (sm3Pwd.equals(pwd)) {
                    checkSuccess = true;
                }
            } else {
                // 旧版MD5加密
                String md5Pwd = MD5Utils.getMD5(passWordSalt + password);
                if (md5Pwd.equals(pwd)) {
                    checkSuccess = true;
                }
            }
            
            // 用户状态为锁定（第二天自动解除锁定）
            if (user.getStatus().equals(PasConstants.UserStatus.LOCK.code)) {
                throw new AppException(PasCode.LOGIN_USER_STATE_LOCK_ERROR.code, PasCode.LOGIN_USER_STATE_LOCK_ERROR.message);
            }
            
            // 用户状态为禁用
            if (user.getStatus().equals(PasConstants.UserStatus.NO.code)) {
                throw new AppException(PasCode.LOGIN_USER_STATE_DISABLE_ERROR.code, PasCode.LOGIN_USER_STATE_DISABLE_ERROR.message);
            }
            
            // 密码错误
            if (!checkSuccess) {
                return CommonOuterResponse.fail(PasCode.LOGIN_PWD_ERORR.code, PasCode.LOGIN_PWD_ERORR.message);
            }
            
            // 检查登录密码是否过期(暂无)
            Date now = new Date();
            if (user.getPasswordExpiredTime() != null && DateUtils.compareTo(now, user.getPasswordExpiredTime()) > 0) {
                return CommonOuterResponse.fail(PasCode.PASSWORD_EXPIRED.code, PasCode.PASSWORD_EXPIRED.message);
            }
            
            return CommonOuterResponse.success(true);
        
        } catch (AppException e) { 
            return CommonOuterResponse.fail(e.getErrorCode(), e.getErrorMsg());
        } catch (Exception e) {
            return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
        }

    }

    

    @RequestMapping(value = "/savePwdHistory", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @Exceptionable
    @Validatable
    @ApiOperation(value = "保存用户密码历史", notes = "保存用户密码历史", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userName", value = "用户名", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "userType", value = "用户类型(1:运营门户；2：商户门户)", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "password", value = "密码（Md5加密）", required = true, dataType = "String", paramType = "query")
    })
    public CommonOuterResponse savePwdHistory(
            @RequestParam(value = "userName", required = true) String userName,
            @RequestParam(value = "userType", required = true) String userType,
            @RequestParam(value = "password", required = true) String password) {

        try {
            userService.saveHistoryPassword(userName, userType, password);
            return CommonOuterResponse.success();
        } catch (AppException e) {
            return CommonOuterResponse.fail(e.getErrorCode(), e.getErrorMsg());
        } catch (Exception e) {
            return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
        }
    }
    

    @RequestMapping(value = "/checkPwdHistory", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @Exceptionable
    @Validatable
    @ApiOperation(value = "校验用户密码历史", notes = "校验用户密码历史（校验不通过返回false）", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userName", value = "用户名", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "userType", value = "用户类型(1:运营门户；2：商户门户)", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "password", value = "密码（Md5加密）", required = true, dataType = "String", paramType = "query")
    })
    public CommonOuterResponse<Boolean> checkPwdHistory (
            @RequestParam(value = "userName", required = true) String userName,
            @RequestParam(value = "userType", required = true) String userType,
            @RequestParam(value = "password", required = true) String password) {

        try {
            boolean flag = userService.checkHistoryPassword(userName, userType, password);
            return CommonOuterResponse.success(flag);
        } catch (AppException e) {
            return CommonOuterResponse.fail(e.getErrorCode(), e.getErrorMsg());
        } catch (Exception e) {
            return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
        }
    }
    
    
    @RequestMapping(value = "/queryUserIdsByPermId", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @Exceptionable
    @Validatable
    @ApiOperation(value = "查询具有指定菜单权限的用户ID", notes = "查询具有指定菜单权限的用户ID", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "permId", value = "菜单ID", required = true, dataType = "Long", paramType = "query"),
            @ApiImplicitParam(name = "userId", value = "用户ID（结果排除相同角色用户id）", required = false, dataType = "Long", paramType = "query"),

    })
    public CommonOuterResponse<List<Long>> queryUserIdsByPermId(
            @RequestParam(value = "permId", required = true) Long permId,
            @RequestParam(value = "userId", required = false) Long userId) {

        try {
            return CommonOuterResponse.success(userService.queryUserIdsByPermId(permId, userId));
        } catch (AppException e) {
            return CommonOuterResponse.fail(e.getErrorCode(), e.getErrorMsg());
        } catch (Exception e) {
            return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
        }
    }

    @PostMapping("/importantInfoEncrypt")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "用户手机号邮箱重新加密", notes = "用户手机号邮箱重新加密", httpMethod = "POST")
    @ApiImplicitParams({
    })
    public CommonOuterResponse<Map> importantInfoEncrypt(@RequestParam("startPage") Integer startPage,
                                                         @RequestParam("endPage") Integer endPage){
        CommonOuterResponse<Map> response = null;
        try {
            //CommonOuterResponse<Map> response = userService.importantInfoEncrypt(startPage, endPage);
           response = userService.custInfoSetHidden(startPage, endPage);
        }catch (Exception e){

        }
        return response;
    }

    @GetMapping("/queryUserByName")
    public CommonOuterResponse queryUserByName(@RequestParam("name") String name) {
        CommonOuterResponse response = new CommonOuterResponse();
        try {
            response = CommonOuterResponse.success(userService.queryUserByName(name));
        } catch (AppException e) {
            return CommonOuterResponse.fail(e.getErrorCode(), e.getErrorMsg());
        } catch (Exception e) {
            return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
        }
        return response;
    }

    @GetMapping("/testPwd")
    public void test() throws Exception {
        String pwd = PasswordCreateUtil.createPassWord(8);
        // sm3转为sm4  20220815 sm4(sm3(password))
        String sm3Pwd = encryptService.encryptData(pwd, "A02");
        System.out.println(sm3Pwd);
        System.out.println(encryptService.symmetricEncryptData(sm3Pwd));
    }
}
