package com.epaylinks.efps.pas.acl.dao;


import com.epaylinks.efps.pas.acl.po.PasUserAudit;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface PasUserAuditMapper {
    int deleteByPrimaryKey(Long id);

    int insert(PasUserAudit record);

    PasUserAudit selectByPrimaryKey(Long id);

    int updateByPrimaryKey(PasUserAudit record);

    int countByPage(Map map);

    List<PasUserAudit> selectByPage(Map map);
}