package com.epaylinks.efps.pas.acl.service;

import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.pas.acl.controller.response.BasicPermTreeResponse;
import com.epaylinks.efps.pas.acl.dao.*;
import com.epaylinks.efps.pas.acl.dto.PermNode;
import com.epaylinks.efps.pas.acl.po.Perm;
import com.epaylinks.efps.pas.acl.po.User;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class PermService {

	@Autowired
	private PermMapper permMapper;

	@Autowired
	private RoleUserMapper roleUserMapper;

	@Autowired
	private RolePermMapper rolePermMapper;

	@Autowired
	private PermServiceMapper permServiceMapper;

	@Autowired
	private PermService self;

	@Autowired
	private UserMapper userMapper;

	@Value("${busiFinBoardUrl:}")
	private String busiFinBoardUrl;

	@Value("${dataAuthPermIds:}")
	private String dataAuthPermIds;

	/**
	 * 完整的基础权限树
	 * @return
	 */
	@Logable(businessTag = "PermService.getBasicTree")
	public BasicPermTreeResponse getBasicTree(String type) {
		BasicPermTreeResponse basicPermTreeResponse = new BasicPermTreeResponse();
		// 查询所有权限结点
		List<Perm> allPermList = new ArrayList<>();
		if ("1".equals(type) && StringUtils.isNotBlank(dataAuthPermIds)) { // 已做了业务员/分公司管理数据权限的菜单
			List<Long> dataList = Arrays.asList(dataAuthPermIds.split(",")).stream().map(Long::parseLong).collect(Collectors.toList());
			allPermList = permMapper.selectDataPermList(dataList);
		} else {
			allPermList = permMapper.selectAll();
		}
		// 构造根结点
		PermNode root = new PermNode();
		root.setPermId(0L);
		root.setName("root");
		root.setAlias("根目录");
		root.setChildren(this.createChildren(0L, allPermList));
		// 返回结果
		basicPermTreeResponse.setRoot(root);
		return basicPermTreeResponse;
	}

    /**
     * 当前用户的权限（树状）
	 * @param curUserId 当前用户ID
     * @return
     */
    @Logable(businessTag = "PermService.getPermOfCurUser")
    public PermNode getPermOfCurUser(Long curUserId) {
        // 查询当前用户的权限ID集合，以及这些叶子的所有父节点
		List<Long> permIdsOfCurUser = self.selectPermIdsByUserId(curUserId);
		// 判断是否为过检角色3012000，是只返回过检菜单
		Integer i = roleUserMapper.selectCheckRole(curUserId,Long.valueOf(3012000));
		if (i > 0) {
			permIdsOfCurUser = permMapper.selectPermIdsByUserIdAndRoleId(curUserId,Long.valueOf(3012000));
		}

		// 固定返回文件管理菜单 需求12655
		List<Long> filePerm = permMapper.selectSubPermIds(12L);
		permIdsOfCurUser.addAll(filePerm);
		permIdsOfCurUser = permIdsOfCurUser.stream().distinct().collect(Collectors.toList());

        // 查询所有权限结点
        List<Perm> allPermList = permMapper.selectAll();
        // 构造根结点
        PermNode root = new PermNode();
        root.setPermId(0L);
        root.setName("root");
        root.setAlias("根目录");
		root.setChecked(true);
		root.setChildren(this.createChildren(0L, allPermList, permIdsOfCurUser, false,curUserId));
        // 返回根结点
        return root;
    }

	/**
	 * 查看某角色的权限（树状）
	 * 注：完整树
	 */
	@Logable(businessTag = "PermService.getPermTreeByRoleId")
	public PermNode getPermTreeByRoleId(Long roleId) {
		// 查看某角色的权限ID集合，以及这些叶子的所有父节点
		List<Long> permIds = permMapper.selectPermIdsByRoleId(roleId);
		// 查询所有权限结点
		List<Perm> allPermList = permMapper.selectAll();
		// 构造根结点
		PermNode root = new PermNode();
		root.setPermId(0L);
		root.setName("root");
		root.setAlias("根目录");
		root.setChildren(this.createChildren(0L, allPermList, permIds, true,null));
		return root;
	}

    @Logable(businessTag = "PermService.selectById")
	public Perm selectById(Long id) {
		return permMapper.selectByPrimaryKey(id);
	}

    @Logable(businessTag = "PermService.existByName")
	public boolean existByName(String name) {
		if (permMapper.existByName(name) > 0) {
			return true;
		}
		return false;
	}

	/**
	 * 构建权限树
	 */
    @Logable(businessTag = "PermService.getAllTree")
	public List<PermNode> getAllTree() {
		List<PermNode> resultTree = new ArrayList<>();
		List<Perm> list = permMapper.selectAll();
		if (list != null && list.size() > 0) {
			// 查找父节点
			for (Perm perm : list) {
				if (perm.getParentId() == 0) {
					PermNode node = new PermNode();
					node.setPermId(perm.getPermId());
					node.setName(perm.getName());
					node.setAlias(perm.getAlias());
					resultTree.add(node);
				}
			}
		}
		// 为一级菜单设置子菜单，getChild是递归调用的
		for (PermNode node : resultTree) {
			node.setChildren(createChildren(node.getPermId(), list));
		}
		return resultTree;
	}

	/**
	 * 递归查找子结点
	 * @param permId 当前权限id
	 * @param list 要查找的列表
	 * @return
	 */
	private List<PermNode> createChildren(Long permId, List<Perm> list) {
		// 子菜单
		List<PermNode> childList = new ArrayList<>();
		for (Perm perm : list) {
			// 遍历所有节点，将父菜单id与传过来的id比较
			if (permId.equals(perm.getParentId())) {
				PermNode node = new PermNode();
				node.setPermId(perm.getPermId());
				node.setName(perm.getName());
				node.setAlias(perm.getAlias());
				node.setUrl(perm.getUrl());
				childList.add(node);
			}
		}
		// 把子菜单的子菜单再循环一遍
		for (PermNode node : childList) {
			// 递归
			node.setChildren(createChildren(node.getPermId(), list));
		}
		// 递归退出条件
		if (childList.size() == 0) {
			return null;
		}
		return childList;
	}

	/**
	 * 递归查找子结点
	 *
	 * @param permId 当前权限id，即父节点
	 * @param list 要查找的列表
	 * @param permIdsOfCurUser 当前用户的权限ID集合，以及这些叶子的所有父节点
	 * @param isFullTree true:生成完整树, false:仅生成有权限的树
	 * @return
	 */
	private List<PermNode> createChildren(Long permId, List<Perm> list, List<Long> permIdsOfCurUser, boolean isFullTree,Long currentUser) {
		// 子菜单
		List<PermNode> childList = new ArrayList<>();
		for (Perm perm : list) {
			// 遍历所有节点，将父菜单id与传过来的id比较
			if (permId.equals(perm.getParentId())) {
				boolean hasPerm = permIdsOfCurUser.contains(perm.getPermId());
				if (!hasPerm) {
					if (!isFullTree) {
						continue;
					}
				}
				PermNode node = new PermNode();
				node.setPermId(perm.getPermId());
				node.setName(perm.getName());
				node.setAlias(perm.getAlias());
				node.setUrl(perm.getUrl());
				//  核算管理 / 经营分析表 / 业财数据看板修改返回的URL
				if (90507 == perm.getPermId()) {
					User user = userMapper.selectByPrimaryKey(currentUser);
					if (user.getCompanyId() == null) {
						node.setUrl(busiFinBoardUrl);
					} else {
						String url = userMapper.queryCompanyUrlByUserId(currentUser);
						node.setUrl(url);
					}
				}
				node.setChecked(hasPerm);
				childList.add(node);
			}
		}
		// 把子菜单的子菜单再循环一遍
		for (PermNode node : childList) {
			// 递归
			node.setChildren(createChildren(node.getPermId(), list, permIdsOfCurUser, isFullTree,currentUser));
		}
		// 递归退出条件
		if (childList.size() == 0) {
			return null;
		}
		return childList;
	}

	/**
	 * 查询当前用户的权限ID集合，以及这些叶子的所有父节点
	 * @param userId
	 * @return
	 */
	@Logable(businessTag = "PermService.selectPermIdsByUserId")
	public List<Long> selectPermIdsByUserId(Long userId) {
		List<Long> permIds;
		boolean isSuperAdmin = roleUserMapper.checkIsSuperAdmin(userId);
		if (isSuperAdmin) {
			permIds = permMapper.selectAllPermIds();
		} else {
			permIds = permMapper.selectPermIdsByUserId(userId);
		}
		return permIds;
	}

	/**
	 * 判断是否超管用户
	 * @param userId
	 * @return
	 */
	public boolean checkIsSuperAdmin(Long userId){
		return roleUserMapper.checkIsSuperAdmin(userId);
	}

	/**
	 * 查询用户权限ID
	 * @param userId
	 * @return
	 */
	public List<Long> queryPermIdByUser(Long userId){
		List<Long> permIds = permMapper.selectPermIdsByUserId(userId);
		return permIds;
	}

	@Logable(businessTag = "viewPerm")
	public Perm viewPerm(Long permId) {
		return permMapper.selectByPrimaryKey(permId);
	}

	@Logable(businessTag = "modifyPerm")
	public void modifyPerm(Perm perm) {
		permMapper.updateByPrimaryKeySelective(perm);
	}

	@Logable(businessTag = "deletePerm")
	@Transactional
	public void deletePerm(Long permId) {
		// 删除指定结点及其所有子节点
		List<Long> permIds = permMapper.selectSubPermIds(permId);// 查询结果包含自身
		permMapper.deleteByPermIds(permIds);
		// 删除关联表pas_perm_service
		permServiceMapper.deleteByPermIds(permIds);
        // 删除关联表pas_role_perm
        rolePermMapper.deleteByPermIds(permIds);
	}

	@Logable(businessTag = "addSubPerm")
	public void addSubPerm(Perm subPerm) {
		Long maxSubPermId = permMapper.selectMaxSubPermId(subPerm.getParentId());
		if (maxSubPermId == null) {
			maxSubPermId = subPerm.getParentId() * 100;
		}
		Long permId = maxSubPermId + 1L;
		subPerm.setPermId(permId);
		permMapper.insert(subPerm);
	}
	


    public String queryPermNamesById(String permIds) {
        if (permIds == null || permIds.split(",").length < 1) {
            return null;
        }
        try {
            List<String> idList = Arrays.asList(permIds.split(","));
            StringBuffer sb = new StringBuffer();
            idList.forEach( idStr -> {
                Long id = Long.parseLong(idStr);
                String name = permMapper.selectFullPermNamesById(id);
                if (name != null) {
                    sb.append("," + name.replace(",", "-"));  
                }
            });
            
            return sb.length() > 0 ? sb.substring(1) : sb.toString();
        } catch(Exception e) {
            return null;
        }
    }
    
/*
	public void insert(Perm perm){
		if(perm.getPermId() !=null) {
			Perm olePerm = permMapper.selectByPrimaryKey(perm.getPermId());
			if (olePerm != null) {
				throw new AppException(Constants.ReturnCode.INVALID_PARAM.code);
			}
		}else
		{
			Long permId = permMapper.getMaxPermId() ;
			perm.setPermId(permId);
		}
		permMapper.insert(perm);
	}


	public void insertPermService(com.epaylinks.efps.pas.acl.po.PermService permS){
		if(permS.getId() !=null) {
			com.epaylinks.efps.pas.acl.po.PermService olePerm = permServiceMapper.selectByPrimaryKey(permS.getId());
			if (olePerm != null) {
				throw new AppException(Constants.ReturnCode.INVALID_PARAM.code);
			}
		}else
		{
			Long id = permServiceMapper.getMaxPermId() ;
			permS.setId(id);
		}
		permServiceMapper.insert(permS);
	}*/
}
