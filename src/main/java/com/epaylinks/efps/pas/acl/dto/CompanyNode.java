package com.epaylinks.efps.pas.acl.dto;

import java.util.List;

/**
 * 分公司树结点
 */
public class CompanyNode {

    private Long id;
    private String name;
    private Long num = 0L; // 业务员人数
    private List<CompanyNode> children;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getNum() {
        return num;
    }

    public void setNum(Long num) {
        this.num = num;
    }

    public List<CompanyNode> getChildren() {
        return children;
    }

    public void setChildren(List<CompanyNode> children) {
        this.children = children;
    }
    
}
