package com.epaylinks.efps.pas.acl.controller.response;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by adm on 2018/7/23.
 */
public class RoleResponse extends CommonOuterResponse {
    private Long uid;

    private String code;
    private String name;

    private String remark;

    private String permIds;

    private Integer userCount;

    @ApiModelProperty(value = "数据权限范围（空或0：全量，1：非全量）", dataType = "String")
    private String dataAuth;

    @ApiModelProperty(value = "分公司ids,逗号分隔", dataType = "String")
    private String companyIds;// 分公司ids,逗号分隔


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getPermIds() { return permIds; }

    public void setPermIds(String permIds) { this.permIds = permIds; }

    public Integer getUserCount() {
        return userCount;
    }

    public void setUserCount(Integer userCount) {
        this.userCount = userCount;
    }

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public String getDataAuth() {
        return dataAuth;
    }

    public void setDataAuth(String dataAuth) {
        this.dataAuth = dataAuth;
    }

    public String getCompanyIds() {
        return companyIds;
    }

    public void setCompanyIds(String companyIds) {
        this.companyIds = companyIds;
    }
}
