package com.epaylinks.efps.pas.acl.controller;

import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.pas.acl.controller.response.UserResponse;
import com.epaylinks.efps.pas.acl.service.*;
import com.epaylinks.efps.pas.acl.vo.PasUserAuditVo;
import com.epaylinks.efps.pas.common.PageResult;
import com.epaylinks.efps.pas.common.PasCode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import java.util.HashMap;
import java.util.Map;

@RestController
@RefreshScope
@RequestMapping("/userAudit")
@Api(value = "UserAuditController", description = "用户审核管理")
public class UserAuditController {

    @Autowired
    private UserAuditService userAuditService;


    /**
     * 查询用户权限修改记录
     *
     * @return
     */
    @RequestMapping(value = "/selectByPage", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @Logable(businessTag = "UserAuditController.selectByPage")
    @Exceptionable
    @Validatable
    @ApiImplicitParams({
            @ApiImplicitParam(name = "name", value = "用户名", required = false, dataType = "String", length = 20, paramType = "query"),
            @ApiImplicitParam(name = "status", value = "审核状态(0待审核，1审核通过，2审核不通过)", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageNum", value = "查询开始页", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "查询的页面大小", required = true, dataType = "int", paramType = "query")})
    public PageResult<PasUserAuditVo> selectByPage(
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "status", required = false) String status,
            @RequestParam(value = "pageNum", required = true) Integer pageNum,
            @RequestParam(value = "pageSize", required = true) Integer pageSize,
            @RequestHeader(value = "x-userid", required = true) Long userId) {

        int endRowNo = pageNum * pageSize;
        int beginRowNo = (pageNum - 1) * pageSize + 1;

        Map map = new HashMap();
        map.put("name", name);
        map.put("status", status);
        map.put("beginRowNo", beginRowNo);
        map.put("endRowNo", endRowNo);
        PageResult<PasUserAuditVo> userPage = new PageResult();
        try {
            userPage = userAuditService.pageQuery(map);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return userPage;
    }

    @PostMapping("/auditUser")
    @Logable(businessTag = "UserAuditController.auditUser")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "审核用户角色修改审核", notes = "审核用户角色修改审核", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "审核记录id", required = true, dataType = "Long", length = 20, paramType = "query"),
            @ApiImplicitParam(name = "status", value = "状态:1审核通过,2审核不通过", required = true, dataType = "String", length = 1, paramType = "query"),
            @ApiImplicitParam(name = "remark", value = "审核意见",  dataType = "String", paramType = "query")
    })
    public UserResponse auditUser(
            Long id, String status,String remark,
            @RequestHeader(value = "x-userid", required = true) String userId) {
        UserResponse response = new UserResponse();
        try {
            userAuditService.auditUser(id,status,remark,userId);
        } catch (Exception e) {
            response = new UserResponse();
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
            return response;
        }
        return response;
    }
    
}
