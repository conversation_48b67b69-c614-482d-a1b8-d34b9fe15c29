package com.epaylinks.efps.pas.acl.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.pas.acl.service.AuthService;
import com.epaylinks.efps.pas.acl.service.PermService;
import com.epaylinks.efps.pas.common.PasConstants;

/**
 * 鉴权控制器
 * <AUTHOR>
 *
 */
@RestController
public class AuthController {
	@Autowired
	private PermService permService;
	@Autowired
	private AuthService authService;
	
	/**
	 * 根据入参的手机号以及链接来判断该手机号对应的用户是否有链接的访问权限
	 * @param uri
	 * @return
	 */
	@Logable(businessTag = "auth")
	@PostMapping("uriAuth")
	public String auth(
			@RequestParam(value = "name" , required = true)String name , 
			@RequestParam(value = "uri" , required = true)String uri) {
		boolean uriExist = permService.existByName(uri);
		if (!uriExist) {
			//如果没有配置，那么直接返回鉴权成功
			return PasConstants.SUCCESS;
		}
		//如果有进行配置，那么进行权限校验
		boolean authResult = authService.authUri(uri, name);
		if (authResult) {
			//如果鉴权成功
			return PasConstants.SUCCESS;
		}
		return PasConstants.FAIL;
	}
}
