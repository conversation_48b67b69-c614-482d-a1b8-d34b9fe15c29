package com.epaylinks.efps.pas.acl.service;

import com.epaylinks.efps.common.business.cust.service.RedisDataTransService;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.pas.acl.po.User;
import com.epaylinks.efps.pas.common.PasCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Map;

@Service
public class DataAuthService {
    @Autowired
    private UserService userService;

    @Autowired
    private RedisDataTransService redisDataTransService;

    public void setMapParam(Map<String,Object> param, Long userId) {
        if (userId != null) {
            User user = userService.queryUserById(userId);
            if (user == null) {
                throw new AppException(PasCode.USER_NOT_EXIST.code, PasCode.USER_NOT_EXIST.message);
            }
            if (Constants.PasUserType.COMPANY.code.equals(user.getUserType())) {
                param.put("userCompanyId", user.getCompanyId());
            } else if (Constants.PasUserType.SALES.code.equals(user.getUserType())) {
                param.put("businessManId", user.getUid());
            } else {
                List<Long> companyIds = redisDataTransService.getCompanyIdList(userId);
                if (!companyIds.isEmpty()) {
                    param.put("companyIds", companyIds);
                }
            }
        }
    }
}
