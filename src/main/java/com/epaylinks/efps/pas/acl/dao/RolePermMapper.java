package com.epaylinks.efps.pas.acl.dao;

import org.apache.ibatis.annotations.Mapper;

import com.epaylinks.efps.pas.acl.po.RolePerm;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface RolePermMapper {
	
    int insert(RolePerm record);

    int insertSelective(RolePerm record);

    int existByPermId(long uid);

    int deleteByRoleId(long uid);

    List<Long> selectByRoleId(long uid);

    void deleteByPermIds(@Param("permIds") List<Long> permIds);
}