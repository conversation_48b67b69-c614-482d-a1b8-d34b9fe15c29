package com.epaylinks.efps.pas.acl.service.impl;

import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import com.epaylinks.efps.pas.acl.service.GraphicService;
@Service
public class GraphicServiceImpl implements GraphicService {
	
	@Autowired
	private RedisTemplate redisTemplate;

	private final static String REDIS_KEY_FREFIX = "pas_graphic_code:" ;
	
	private final static String FLAG_KEY_FREFIX = "pas_graphic_code_valid_flag:" ;
	
	private final static Long EFECTIVE_TIME = 300L ; // 有效时间，秒
	
	private String getRedisKey(String uid) {
		return REDIS_KEY_FREFIX  +  uid;
	}

	
	@Override
	public void saveToRedis(String uid, String code) {
		
		String key = getRedisKey(uid);
		redisTemplate.opsForValue().set(key, code, EFECTIVE_TIME, TimeUnit.SECONDS);
	}
	
	@Override
	public String getCodeFromRedis(String uid) {
		String key  = getRedisKey(uid);
		if(redisTemplate.opsForValue().get(key) == null) {
			return null;
		}
		return (String) redisTemplate.opsForValue().get(key) ;

	}


	@Override
	public void removeFromRedis(String uid) {
		
		redisTemplate.delete(getRedisKey(uid));
	}


	@Override
	public void setFlag(String uid, boolean flag) {

		redisTemplate.opsForValue().set(FLAG_KEY_FREFIX + uid, flag, EFECTIVE_TIME, TimeUnit.SECONDS);
	}


	@Override
	public boolean getFlag(String uid) {

		Object obj = redisTemplate.opsForValue().get(FLAG_KEY_FREFIX + uid);
		if(obj != null) {
			return (boolean ) obj;
		}
		return false;
	}


	@Override
	public boolean validate(String uid, String graphicCode) {

		String code = getCodeFromRedis(uid);
	    if(graphicCode != null && graphicCode.equalsIgnoreCase(code)) {
	    	removeFromRedis(uid);
	    	setFlag(uid, true);
	    	return true;
	    }
	    return false;
	    
	}

}
