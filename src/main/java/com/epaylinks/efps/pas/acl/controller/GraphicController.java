package com.epaylinks.efps.pas.acl.controller;


import java.io.ByteArrayOutputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.pas.acl.service.GraphicService;
import com.epaylinks.efps.pas.acl.vo.GraphicVo;
import com.epaylinks.efps.pas.common.GraphicCodeUtil;
import com.epaylinks.efps.pas.common.PasCode;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping(value = "/graphic")
@Api(value = "GraphicController", description = "图形验证码服务") //@Api : 用在类上，说明该类的作用
public class GraphicController {

    private static final int GRAPHIC_WIDTH = 120;   // 图片的宽度。
    private static final int GRAPHIC_HEIGHT = 40;    // 图片的高度。
    private static final int GRAPHIC_CODE_COUNT = 5;  // 验证码字符个数
    private static final int GRAPHIC_LINE_COUNT = 20; // 验证码干扰线数
    
    @Autowired
    private GraphicService graphicService;
    
    @Autowired
    SequenceService sequenceService;
    

    @ApiOperation(value = "获取图形验证码（不需鉴权）", notes = "获取图形验证码（不需鉴权）", httpMethod = "GET")
    @Logable(businessTag = "GraphicController.v2GetCode",outputResult = false)
    @RequestMapping(value="/v2/getCode",method = RequestMethod.GET)
    @ApiImplicitParams({
        @ApiImplicitParam(name = "len", value = "验证码长度", required = false, dataType = "String", paramType = "query")
    })
    public CommonOuterResponse<GraphicVo> v2GetCode(
            @RequestParam(value = "len", required = false) Integer len) throws Exception{  
        
        try {
            // 生成图形验证码
            len = len != null ? len : GRAPHIC_CODE_COUNT;
            GraphicCodeUtil vCode = new GraphicCodeUtil(GRAPHIC_WIDTH, GRAPHIC_HEIGHT, len, GRAPHIC_LINE_COUNT);
            
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            vCode.write(os);
            
            //转换成base64串
            String imgBase64 = Base64.encodeBase64String(os.toByteArray()).trim(); 
            String result = "data:image/png;base64," + imgBase64;

            String uid = UUID.randomUUID().toString().replaceAll("-", "").toUpperCase();

            // 写入redis缓存
            graphicService.saveToRedis(uid, vCode.getCode());

            GraphicVo vo = new GraphicVo();
            vo.setImg(result);
            vo.setUid(uid);
            return CommonOuterResponse.success(vo);
        } catch (Exception e) {
            if (e instanceof AppException) {
                return CommonOuterResponse.fail(((AppException) e).getErrorCode(), ((AppException) e).getErrorMsg());
            } else {
                return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
            }
        }
    }

    @ApiOperation(value = "校验图形验证码接口（不需鉴权）", notes = "校验图形验证码接口（返回“0”成功，“1”失败）", httpMethod = "POST")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "uid", value = "获取验证码时返回的uid", required = true, dataType = "String", paramType = "query"),
        @ApiImplicitParam(name = "graphicCode", value = "验证码", required = true, dataType = "String", paramType = "query")
    })
    @Logable(businessTag = "GraphicController.v2Validate")
    @RequestMapping(value = "/v2/validate",method = RequestMethod.POST)
    public Map<String, Object> v2Validate(
            @RequestParam("uid") String uid, 
            @RequestParam("graphicCode") String graphicCode) {
        
        // 前端要求返回格式为 bootstrap的remote验证器需要的返回结果一定是json格式的数据 :
        // {"valid":false} //表示不合法，验证不通过
        // {"valid":true} //表示合法，验证通过
        Map<String, Object> resultMap = new HashMap<String,Object>();
        try{
            String code = graphicService.getCodeFromRedis(uid);
            if(graphicCode != null && graphicCode.equalsIgnoreCase(code)) {
                graphicService.removeFromRedis(uid);
                resultMap.put("valid", true); 
            }else {
                resultMap.put("valid", false);
            }
        }catch (Exception e){
            resultMap.put("valid", false);
        }
        return resultMap;
    }
    
    // end --------------------- 不需uaa鉴权接口 ------------------------- end
    


}
