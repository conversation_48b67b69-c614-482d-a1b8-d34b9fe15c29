package com.epaylinks.efps.pas.acl.dao;

import com.epaylinks.efps.pas.acl.po.PermService;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PermServiceMapper {

    int insert(PermService record);

    int deleteByPrimaryKey(Long id);

    List<String> selectPermServiceByParam(List list);

    void deleteByPermIds(@Param("permIds") List<Long> permIds);

    List<PermService> queryPermService(@Param("permId") Long permId);

    Long selectMaxId(@Param("permId") Long permId);

    void updateServiceCode(@Param("origServiceCode") String origServiceCode, @Param("newServiceCode") String newServiceCode);

    void deleteByServiceCode(@Param("serviceCode") String serviceCode);


       Long getMaxPermId();
       PermService selectByPrimaryKey(Long id);

}
