package com.epaylinks.efps.pas.acl.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.epaylinks.efps.pas.acl.dao.PermMapper;
import com.epaylinks.efps.pas.acl.dao.RolePermMapper;
import com.epaylinks.efps.pas.acl.dao.RoleUserMapper;
import com.epaylinks.efps.pas.acl.dao.UserMapper;
import com.epaylinks.efps.pas.acl.po.Perm;
import com.epaylinks.efps.pas.acl.po.User;
import com.epaylinks.efps.pas.acl.service.AuthService;
@Service
public class AuthServiceImpl implements AuthService{
	@Autowired
	private UserMapper userMapper;
	@Autowired
	private RoleUserMapper roleUserMapper;
	@Autowired
	private RolePermMapper rolePermMapper;
	@Autowired
	private PermMapper permMapper;
	
	/**
	 * 对uri进行鉴权
	 * @param uri 需要鉴权的uri
	 * @param phone 需要鉴权的当前用户(使用电话号码)
	 * @return
	 */
	@Override
	public boolean authUri(String uri, String name) {
		// TODO Auto-generated method stub
		//首先查询用户表
		User user = userMapper.selectByName(name);
		if (user == null) {
			//鉴权失败
			return false;
		}
		//用户不为空，那么查找用户所属的角色id
		List<Long> roleIds = roleUserMapper.selectRoleIdByUserId(user.getUid());
		if (roleIds.isEmpty()) {
			return false;
		}
		//找出该用户角色对应的权限
		for (Long roleId : roleIds) {
			List<Long> permIds = rolePermMapper.selectByRoleId(roleId);
			for (Long permId : permIds) {
				//迭代所有的权限id，查看是否包含对应的入参链接
				Perm perm = permMapper.selectByPrimaryKey(permId);
				if (perm.getName().equals(uri)) {
					//如果找到了对应的链接授权信息，那么通过
					return true;
				}
			}
		}
		//否则当前用户并没有权限去访问此链接
		return false;
	}

}
