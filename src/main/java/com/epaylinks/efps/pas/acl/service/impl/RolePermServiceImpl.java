package com.epaylinks.efps.pas.acl.service.impl;

import com.epaylinks.efps.pas.acl.dao.RolePermMapper;
import com.epaylinks.efps.pas.acl.po.RolePerm;
import com.epaylinks.efps.pas.acl.service.RolePermService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class RolePermServiceImpl implements RolePermService {

    @Autowired
    private RolePermMapper rolePermMapper;

	@Override
    public int insert(RolePerm record){
		return rolePermMapper.insert(record);
	}

	@Override
    public int insertSelective(RolePerm record){
		return rolePermMapper.insertSelective(record);
	}

	@Override
	public boolean existByPermId(long uid){
    	if(rolePermMapper.existByPermId(uid)>0){
			return true;
		}else {
			return false;
		}
	}

	@Override
	public int deleteByRoleId(long uid){
		return rolePermMapper.deleteByRoleId(uid);
	}
}
