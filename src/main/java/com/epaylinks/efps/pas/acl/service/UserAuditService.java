package com.epaylinks.efps.pas.acl.service;

import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.oplog.OpLogHandle;
import com.epaylinks.efps.pas.acl.dao.PasUserAuditMapper;
import com.epaylinks.efps.pas.acl.dao.RoleUserMapper;
import com.epaylinks.efps.pas.acl.dao.UserMapper;
import com.epaylinks.efps.pas.acl.po.PasUserAudit;
import com.epaylinks.efps.pas.acl.po.RoleUser;
import com.epaylinks.efps.pas.acl.po.User;
import com.epaylinks.efps.pas.acl.vo.PasUserAuditVo;
import com.epaylinks.efps.pas.common.PageResult;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.pas.common.PasConstant;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class UserAuditService {

    @Autowired
    private PasUserAuditMapper pasUserAuditMapper;
    @Autowired
    private UserMapper userDAO;
    @Autowired
    private RoleUserMapper roleUserMapper;
    @Autowired
    private RoleService roleService;

    @Logable(businessTag = "UserAuditService.pageQuery")
    public PageResult<PasUserAuditVo> pageQuery(Map map) {
        PageResult<PasUserAuditVo> pagingResult = new PageResult<>();
        int total = pasUserAuditMapper.countByPage(map);
        List<PasUserAudit> list = pasUserAuditMapper.selectByPage(map);
        List<PasUserAuditVo> resultList = new ArrayList<>();
        if(list != null && list.size()>0){
            PasUserAuditVo vo = null;
            for(PasUserAudit userAudit : list){
                vo = new PasUserAuditVo();
                BeanUtils.copyProperties(userAudit, vo);
                User tmp = userDAO.selectByPrimaryKey(userAudit.getUserId());
                if(tmp != null){
                    vo.setLoginName(tmp.getName());
                }
                resultList.add(vo);
            }
        }
        pagingResult.setRows(resultList);
        pagingResult.setTotal(total);
        return pagingResult;
    }

    @Transactional
    public void auditUser(Long id, String status, String remark, String userId) {
        PasUserAudit userAudit = pasUserAuditMapper.selectByPrimaryKey(id);
        if(userAudit != null){
            if(userId.equals(userAudit.getCreatePerson())){
                throw new AppException(PasCode.SYSTEM_EXCEPTION.code, "当前用户不能审核自己申请的记录");
            }
        }
        User operUser = userDAO.selectByPrimaryKey(Long.valueOf(userId));
        userAudit.setStatus(status);
        userAudit.setRemark(remark);
        userAudit.setAuditPerson(operUser.getRealName());
        userAudit.setAuditTime(new Date());
        pasUserAuditMapper.updateByPrimaryKey(userAudit);

        if (!"1".equals(status)) {
            return;
        }

        String newRoleIdList = userAudit.getRoleIdAfter();
        //删除原来角色
        roleUserMapper.deleteByUserId(userAudit.getUserId());
        //重新插入
        if (StringUtils.isNotBlank(newRoleIdList)) {
            List<String> roleIds = Arrays.asList(newRoleIdList.split(","));
            for (int i = 0; i < roleIds.size(); i++) {
                if (org.apache.commons.lang.StringUtils.isNotBlank(roleIds.get(i))) {
                    RoleUser roleUser = new RoleUser();
                    roleUser.setRoleId(Long.valueOf(roleIds.get(i)));
                    roleUser.setUserId(userAudit.getUserId());
                    roleUserMapper.insert(roleUser);
                }
            }
            // 更新用户类型
            User user = new User();
            user.setUid(userAudit.getUserId());
            user.setUserType(roleIds.stream().map(Long::parseLong).collect(Collectors.toList()).contains(11L) ? PasConstant.UserType.SALES.code : PasConstant.UserType.NORMAL.code);
            userDAO.updateByPrimaryKeySelective(user);
        }


        //添加日志
        if(StringUtils.isNotEmpty(userAudit.getRoleIdBefore()) ){
            OpLogHandle.setOpContent("修改用户：" + userAudit.getUserName() + "，" +
                    "角色:" + userAudit.getRoleNameAfter());
        }else{
            OpLogHandle.setOpContent("新增用户：" + userAudit.getUserName() + "，" +
                    "角色:" + userAudit.getRoleNameAfter());
        }
    }
}
