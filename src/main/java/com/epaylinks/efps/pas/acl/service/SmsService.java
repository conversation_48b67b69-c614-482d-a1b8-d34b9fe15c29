package com.epaylinks.efps.pas.acl.service;


import java.util.Map;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.epaylinks.efps.common.config.DefaultFeignConfiguration;
import com.epaylinks.efps.pas.acl.vo.SmsResponse;

@FeignClient(value = "SMS", configuration = DefaultFeignConfiguration.class)
public interface SmsService {

    @RequestMapping(value = "/sms/send", method = RequestMethod.POST)
    public SmsResponse send(@RequestBody Map<String, Object> request);
}
