package com.epaylinks.efps.pas.acl.vo;

import io.swagger.annotations.ApiModelProperty;

/**
 * 业务员信息对象
 * <AUTHOR>
 * @date 2021-05-10
 *
 */
public class SalesmanVo {
    

    @ApiModelProperty(value="用户ID", dataType = "String")
    private Long uid;
    
    @ApiModelProperty(value="用户账号", dataType = "String")
    private String name;
    
    @ApiModelProperty(value="真实用户名称", dataType = "String")
    private String realName;
    
    @ApiModelProperty(value="分公司ID", dataType = "Long")
    private Long companyId;

    @ApiModelProperty(value="状态：Y：启用；N:禁用；L：锁定", dataType = "String")
    private String status;

    @ApiModelProperty(value="自助进件H5链接", dataType = "String")
    private String pieceLink;

    @ApiModelProperty(value="链接状态：0：无效；1：有效", dataType = "Short")
    private Short picecLinkStatus;

    @ApiModelProperty(value="所属分公司名称", dataType = "String")
    private String companyName;

    @ApiModelProperty(value="业务员代码", dataType = "String")
    private Long salesCode;

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getPieceLink() {
        return pieceLink;
    }

    public void setPieceLink(String pieceLink) {
        this.pieceLink = pieceLink;
    }

    public Short getPicecLinkStatus() {
        return picecLinkStatus;
    }

    public void setPicecLinkStatus(Short picecLinkStatus) {
        this.picecLinkStatus = picecLinkStatus;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public Long getSalesCode() {
        return salesCode;
    }

    public void setSalesCode(Long salesCode) {
        this.salesCode = salesCode;
    }
}
