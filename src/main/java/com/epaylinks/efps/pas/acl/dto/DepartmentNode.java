package com.epaylinks.efps.pas.acl.dto;

import java.util.List;

/**
 * 部门树结点
 */
public class DepartmentNode {

    private Long id;
    private String name;
    private Long num = 0L;// 人数
    private List<DepartmentNode> children;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getNum() {
        return num;
    }

    public void setNum(Long num) {
        this.num = num;
    }

    public List<DepartmentNode> getChildren() {
        return children;
    }

    public void setChildren(List<DepartmentNode> children) {
        this.children = children;
    }
}
