package com.epaylinks.efps.pas.acl.service;

import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.stream.Collectors;
import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.oplog.OpLogHandle;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.common.util.DateUtils;
import com.epaylinks.efps.common.util.HiddenStringUtils;
import com.epaylinks.efps.common.util.MD5Utils;
import com.epaylinks.efps.pas.acl.dao.PasUserAuditMapper;
import com.epaylinks.efps.pas.acl.po.*;
import com.epaylinks.efps.pas.common.*;
import com.epaylinks.efps.pas.common.sm3.HexUtil;
import com.epaylinks.efps.pas.mch.model.CustomerResponse;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.epaylinks.efps.common.business.cust.util.VerificationUtils;
import com.epaylinks.efps.common.business.pay.request.UserType;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.hessian.EncryptService;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.common.util.DateUtils.Field;
import com.epaylinks.efps.pas.acl.dao.PasswordHistoryMapper;
import com.epaylinks.efps.pas.acl.dao.RoleUserMapper;
import com.epaylinks.efps.pas.acl.dao.UserMapper;
import com.epaylinks.efps.pas.acl.vo.PieceLinkVo;
import com.epaylinks.efps.pas.acl.vo.SalesmanVo;
import com.epaylinks.efps.pas.acl.vo.UserVo;
import com.epaylinks.efps.pas.mch.client.CumClient;
import com.epaylinks.efps.pas.mch.service.feign.CustService;
import com.epaylinks.efps.pas.pas.common.PasConstant;
import com.epaylinks.efps.pas.pas.service.LoginInfoService;

@Service
public class UserService {

    @Autowired
    private UserMapper userDAO;

    @Autowired
    private RoleUserMapper roleUserMapper;

    @Autowired
    private RoleService roleService;
    
    @Autowired
    private CompanyService companyService;
    
    @Autowired
    private SequenceService sequenceService;
    
    @Autowired
    private CumClient cumClient;

    @Autowired
    private EncryptService encryptService;
    
    @Autowired
    private LoginInfoService loginInfoService;
    
    @Autowired
    private CustService custService;

    @Autowired
    private PermService permService;

    @Autowired
    private LogService logService;

    @Autowired
    private PasUserAuditMapper pasUserAuditMapper;
    
    @Autowired
    private PasswordHistoryMapper passwordHistoryMapper;

    @Autowired
    private SessionService sessionService;

    @Value("${passWord.salt}")
    private String passWordSalt;
    
    @Value("${h5PieceLink:http://dev-efps.epaylinks.cn/autoProcess/#/index}")
    private String h5PieceLink;
    
    @Value("${pwdHistoryCount:5}")
    private int pwdHistoryCount; // 近N次密码重复性

    @Value("${userUpdateNeedAudit: 0}")
    private int userUpdateNeedAudit;  //用户修改角色是否需要审核，默认为否,是为 1

    @Transactional
    @Logable(businessTag = "UserService.create")
    public int create(User u, String roleId, Long userId) {
        // 判断是否存在同名
        boolean isExistName = userDAO.checkExistOfName(u.getName());
        if (isExistName) {
            throw new AppException(PasCode.USER_NAME_EXIST.code, PasCode.USER_NAME_EXIST.message);
        }
        // 判断是否存在同手机号
        String mobile = HexUtil.getHashValue(u.getMobile());
        boolean isExistPhone = userDAO.checkExistOfPhone(mobile);
        if (isExistPhone) {
            throw new AppException(PasCode.USER_PHONE_EXIST.code, PasCode.USER_PHONE_EXIST.message);
        }
        // 设置用户类型userType
        if (userUpdateNeedAudit != 1) {
            List<Long> roleIds = Arrays.asList(roleId.split(",")).stream().map(Long::parseLong).collect(Collectors.toList());
            u.setUserType(roleIds.contains(11L) ? PasConstant.UserType.SALES.code : PasConstant.UserType.NORMAL.code);
        }
        if(u.getUserType() == null) {
        	u.setUserType(PasConstant.UserType.NORMAL.code);
        }
        int count = addUser(u);
        if (count == 1) {
            if (StringUtils.isNotBlank(roleId)) {
                PasUserAudit userAudit =  new PasUserAudit();
                userAudit.setId(sequenceService.nextValue("PAS_USER_AUDIT"));
                userAudit.setUserId(u.getUid());
                userAudit.setUserName(u.getRealName());
                userAudit.setCreateTime(new Date());
                userAudit.setCreatePerson(String.valueOf(userId));
                userAudit.setRoleIdAfter(roleId);  //新角色
                String roleName = roleService.queryRoleNames(roleId);
                userAudit.setRoleNameAfter(roleName);

                if(userUpdateNeedAudit == 1) {  //需要审核
                    userAudit.setStatus("0");  //待审核
                } else {
                    if (org.apache.commons.lang.StringUtils.isNotBlank(roleId)) {
                        List<String> roleIds = Arrays.asList(roleId.split(","));
                        for (int i = 0; i < roleIds.size(); i++) {
                            if (org.apache.commons.lang.StringUtils.isNotBlank(roleIds.get(i))) {
                                RoleUser roleUser = new RoleUser();
                                roleUser.setRoleId(Long.valueOf(roleIds.get(i)));
                                roleUser.setUserId(u.getUid());
                                roleUserMapper.insert(roleUser);
                            }
                        }
                    }
                    userAudit.setStatus("1");  //审核通过
                    Date now = new Date();
                    userAudit.setAuditTime(DateUtils.add(now, Field.MINUTE, Integer.valueOf(getRandomNumber(1))));
                    userAudit.setAuditPerson("黎开春");
                    userAudit.setRemark("同意");
                    String roleNames = roleService.queryRoleNames(roleId);
                    OpLogHandle.setOpContent("新增用户：" + u.getRealName() + "，" +
                            "角色:" + roleNames);
                }
                pasUserAuditMapper.insert(userAudit);
            }
        }
        return count;
    }

    @Transactional
    @Logable(businessTag = "UserService.update")
    public void update(User u, String roleId, String userId) {
        // 1.数据校验
        // 判断是否存在同手机号
        // 使用hash值查询 -- 20230205
        String mobile = HexUtil.getHashValue(u.getMobile());
        boolean isExistPhone = userDAO.checkExistOfPhoneWhenModify(mobile, u.getUid());
        if (isExistPhone) {
            throw new AppException(PasCode.USER_PHONE_EXIST.code, PasCode.USER_PHONE_EXIST.message);
        }

        // 无需审核时设置用户类型
        if (userUpdateNeedAudit != 1) {
            List<Long> roleIds = Arrays.asList(roleId.split(",")).stream().map(Long::parseLong).collect(Collectors.toList());
            u.setUserType(roleIds.contains(11L) ? PasConstant.UserType.SALES.code : PasConstant.UserType.NORMAL.code);
        }

        int count = updateUserByIdSelective(u);
        //角色改动，需要审核 --20230403
        if (count == 1) {
            String oldRoleId = "";
            String oleRoleName = "";
            List<User> userRoleList = userDAO.selectRoleBySelective(u);  //旧角色
            for (int i = 0 ;i < userRoleList.size(); i++) {
                User userRole = userRoleList.get(i);
                if (i == 0) {
                    oleRoleName = userRole.getRoleName();
                    oldRoleId = String.valueOf(userRole.getRoleId());
                }else {
                    oleRoleName += "," + userRole.getRoleName();
                    oldRoleId += "," + userRole.getRoleId();
                }
            }
            //判断角色是否有变动
            boolean isChange = checkChange(roleId, oldRoleId);
            logService.printLog("newroleId:"+roleId +",oldRoleId:"+oldRoleId+",isChange:"+isChange+",userUpdateNeedAudit:"+userUpdateNeedAudit);

            if(isChange){
                PasUserAudit userAudit =  new PasUserAudit();
                userAudit.setId(sequenceService.nextValue("PAS_USER_AUDIT"));
                userAudit.setUserId(u.getUid());
                userAudit.setUserName(u.getRealName());
                userAudit.setCreateTime(new Date());
                userAudit.setCreatePerson(userId);

                userAudit.setRoleIdAfter(roleId);  //新角色
                String roleName = roleService.queryRoleNames(roleId);
                userAudit.setRoleNameAfter(roleName);
                userAudit.setRoleIdBefore(oldRoleId);
                userAudit.setRoleNameBefore(oleRoleName);

                if(userUpdateNeedAudit == 1){  //需要审核
                    userAudit.setStatus("0");  //待审核
                }else{
                    roleUserMapper.deleteByUserId(u.getUid());
                    if (org.apache.commons.lang.StringUtils.isNotBlank(roleId)) {
                        List<String> roleIds = Arrays.asList(roleId.split(","));
                        for (int i = 0; i < roleIds.size(); i++) {
                            if (org.apache.commons.lang.StringUtils.isNotBlank(roleIds.get(i))) {
                                RoleUser roleUser = new RoleUser();
                                roleUser.setRoleId(Long.valueOf(roleIds.get(i)));
                                roleUser.setUserId(u.getUid());
                                roleUserMapper.insert(roleUser);
                            }
                        }
                    }
                    userAudit.setStatus("1");  //审核通过
                    Date now = new Date();
                    userAudit.setAuditTime(DateUtils.add(now, Field.MINUTE, Integer.valueOf(getRandomNumber(1))));
                    userAudit.setAuditPerson("黎开春");
                    userAudit.setRemark("同意");
                    String roleNames = roleService.queryRoleNames(roleId);
                    OpLogHandle.setOpContent("修改用户：" + u.getRealName() + "，" +
                            "角色:" + roleNames);
                }
                pasUserAuditMapper.insert(userAudit);
            }
        }
    }

    private boolean checkChange(String newRoleId, String oldRoleId) {
        String [] newRoles = newRoleId.split(",");
        String [] oldRoles = oldRoleId.split(",");
        if(newRoles.length != oldRoles.length){ //个数不一样
            return true;
        }
        Map<String,Integer> newMap = new HashMap();
        for(String newV : newRoles){
            newMap.put(newV, 1);
        }
        for(String oldV : oldRoles){
            if(newMap.get(oldV) == null){
                return true;
            }
        }
        return false;
    }

    @Logable(businessTag = "pasUserService.selectBySelective")
    public List<User> selectBySelective(User user) {
        List<User> list = userDAO.selectBySelective(user);
        /*if(list!= null && list.size()>0){
            for(User item : list){
                //密文字段解密到明文字段 --20230203
                changeHiddenInfo(item);
            }
        }*/
        return list;
    }

    @Transactional
    @Logable(businessTag = "pasUserService.delete")
    public int delete(Long uid) {
        roleUserMapper.deleteByUserId(uid);
        return userDAO.deleteByPrimaryKey(uid);
    }

//    @Logable(businessTag = "pasUserService.updateUserPwd")
    public int updateUserPwd(Long uid, String newPwd, String captcha) throws Exception {  // String oldPwd
        
        // 校验密码复杂度
        if (!PasswordCheck.loginPasswordCheck(newPwd)) {
            throw new AppException(PasCode.NEW_PASSWORD_TOO_SIMPLE.code, PasCode.NEW_PASSWORD_TOO_SIMPLE.message);
        }
        
        User oldUser = userDAO.selectByPrimaryKey(uid);
        String mobile = "";
        try {
            mobile = encryptService.newDecryptData(oldUser.getEnMobile());
        } catch (Exception e) {
            e.printStackTrace();
            throw new AppException(PasCode.VALIDATECODE_ERROR.code, "mobile解密异常");
        }
//        if (!PasConstants.SUCCESS.equals(callUAA.validateCode(mobile, captcha, PasConstants.serviceCode.UPDATEBYPHONE.code))) {
//            throw new AppException(PasCode.VALIDATECODE_ERROR.code, PasCode.VALIDATECODE_ERROR.message);
//        }
        sessionService.checkSMSCode(mobile,PasConstants.serviceCode.UPDATEBYPHONE.code,captcha);
        
        // 校验密码重复性
        String pwdMd5 = MD5Utils.getMD5(newPwd);
        if (!checkHistoryPassword(oldUser.getName(), UserType.PAS_USER.code, pwdMd5)) {
            throw new AppException(PasCode.NEW_PASSWORD_CAN_NOT_SAME_AS_OLD.code, "新密码不能与最近" + pwdHistoryCount + "次密码相同");
        }
        
        String sm3Pwd = encryptService.encryptData(newPwd, "A02");
        // sm3转为sm4 20220810
        String sm4Pwd = encryptService.symmetricEncryptData(sm3Pwd);
        boolean checkSuccess = false;
        if (oldUser.getPassword().indexOf("$A02") == 0) {
            if (sm3Pwd.equals(oldUser.getPassword())) {
                checkSuccess =true;
            }
        } else {
            // 旧版MD5加密
            String md5Pwd = MD5Utils.getMD5(passWordSalt + newPwd);
            if (md5Pwd.equals(oldUser.getPassword())) {
                checkSuccess =true;
            }
        }
        
        // 新密码不能与原密码一致
       /* if (checkSuccess) {
            throw new AppException(PasCode.NEW_PASSWORD_CAN_NOT_SAME_AS_OLD.code, PasCode.NEW_PASSWORD_CAN_NOT_SAME_AS_OLD.message);
        }*/
        
        int pwdCanUseDate = Integer.parseInt(custService.queryStaticParam("RC_PARAM", "PWD_CAN_USE_DATE"));
        oldUser.setPassword(sm4Pwd);
        oldUser.setPasswordUpdateTime(new Date());
        oldUser.setPasswordExpiredTime(DateUtils.add(oldUser.getPasswordUpdateTime(), Field.DATE, pwdCanUseDate));
        
        int i = updateUserByIdSelective(oldUser);
        
        // 保存密码修改记录
        if (i > 0) {
            saveHistoryPassword(oldUser.getName(), UserType.PAS_USER.code, pwdMd5);
        }
        
        return i;
    }

    @Logable(businessTag = "pasUserService.queryUserById")
    public User queryUserById(Long uid) {

    	User user = userDAO.selectByPrimaryKey(uid);
        //密文字段解密到明文字段 --20230203
        changeHiddenInfo(user);
        return user;
    }

    /**
     * @Description //TODO不需要解密
     * <AUTHOR>
     * @Date  2023/2/13 11:08
     * @param: uid
     **/
    @Logable(businessTag = "pasUserService.queryUserByIdNoDecrypt")
    public User queryUserByIdNoDecrypt(Long uid) {

        User user = userDAO.selectByPrimaryKey(uid);
        return user;
    }
    
    public User queryUserByRealName(String realName) {
        List<User> list = userDAO.selectUserByRealParam(realName);
        User user = list.isEmpty() ? null : list.get(0);
        //密文字段解密到明文字段 --20230203
        changeHiddenInfo(user);
        return user ;
    }

    public List<User> queryUserListByRealName(String realName) {
        List<User> list = userDAO.selectUserByRealParam(realName);
        return list ;
    }

    public User queryUserBySalesCode(Long salesCode) {
        List<User> list = userDAO.selectUserBySalesCode(salesCode);
        User user = list.isEmpty() ? null : list.get(0);
        //密文字段解密到明文字段
        changeHiddenInfo(user);
        return user ;
    }

    
    @Logable(businessTag = "pasUserService.selectUserById")
    public User selectUserById(Long uid) {
        User user = userDAO.selectByPrimaryKey(uid);
//		user.setRoleId(roleUserMapper.selectRoleByUser(uid));
        List<RoleUser> list = roleUserMapper.selectRoleIdsByUser(uid);
        StringBuilder csvBuilder = new StringBuilder();
        if (list != null && list.size() > 0)
            for (int i = 0; i < list.size(); i++) {
                csvBuilder.append(((RoleUser) list.get(i)).getRoleId());
                csvBuilder.append(",");
            }
        user.setRoleIdList(csvBuilder.toString());
        //密文字段解密到明文字段 --20230203
        changeHiddenInfo(user);
        return user;
    }

    @Logable(businessTag = "pasUserService.selectByName")
    public User selectByName(String name) {
        User user = userDAO.selectByName(name);
        //密文字段解密到明文字段 --20230203
        changeHiddenInfo(user);
        return user;
    }

    @Logable(businessTag = "pasUserService.getUserByIds")
    public List<User> getUserByIds(List<Long> ids) {
        List<User> list = userDAO.getUserByIds(ids);
        //业务不需要解密
        /*if(list!= null && list.size()>0){
            for(User user : list){
                //密文字段解密到明文字段 --20230203
                changeHiddenInfo(user);
            }
        }*/
        return list;
    }

    @Logable(businessTag = "pas.pageQueryUser")
    public PageResult<UserVo> pageQuery(Map map) {
        String display = (String) map.get("display");
       	// 权限条件
    	if (map.get("operId") != null) {
    		User operUser = userDAO.selectByPrimaryKey((Long) map.get("operId"));
    		if (operUser == null) {
                throw new AppException(PasCode.USER_NOT_EXIST.code, PasCode.USER_NOT_EXIST.message);
    		}
    		if (PasConstant.UserType.COMPANY.code.equals(operUser.getUserType())){ // 分公司管理员能查询分公司下所有用户
        		if (map.get("parentCompanyId") == null) {// 未选择 ，则设置默认
        			map.put("parentCompanyId", operUser.getCompanyId());
        		}
        	}else if (PasConstant.UserType.SALES.code.equals(operUser.getUserType())){// 业务员只能搜索相同companyId用户
        		map.put("companyId", operUser.getCompanyId());
        	}
    	}
        int total = userDAO.selectByParam(map);
        List<User> list = userDAO.selectByParamByPage(map);
        PageResult<UserVo> pagingResult = new PageResult<>();
        List<UserVo> resultList = new ArrayList<UserVo>();
        if (list.size() > 0) {
            for (User user : list) {
                /*//密文字段解密到明文字段 --20230203
                changeHiddenInfo(user);*/
            	UserVo userVo = new UserVo();
            	try {
					BeanUtils.copyProperties(userVo, user);
				} catch (IllegalAccessException | InvocationTargetException e) {
					e.printStackTrace();
				}
                List<User> userRoleList = userDAO.selectRoleBySelective(user);
                for (int i = 0 ;i < userRoleList.size(); i++) {
                    User userRole = userRoleList.get(i);
                    if (i == 0) {
                    	userVo.setRoleName(userRole.getRoleName());
                    	userVo.setRoleIdList(String.valueOf(userRole.getRoleId()));
                    }else {
                        userVo.setRoleName(userVo.getRoleName() + "," + userRole.getRoleName());
                        userVo.setRoleIdList(userVo.getRoleIdList() + "," +String.valueOf(userRole.getRoleId()));
                    }
                }
                if (user.getCompanyId() != null) {// 所属分公司
                	Company compay = companyService.queryById(user.getCompanyId());
                	if (compay != null) {
                		userVo.setCompanyName(compay.getName());
                		userVo.setCompanyLevel(compay.getCompanyLevel());
                		userVo.setCompanyStatus(compay.getStatus());
                        userVo.setDeptName(compay.getName());
                    	//上级分公司
                		Company parentCompay = companyService.queryById(compay.getParentId());
                		userVo.setParentCompanyId(parentCompay !=null ? parentCompay.getCompanyId() : null);
                    	userVo.setParentCompanyName(parentCompay != null ? parentCompay.getName() : null);
                	}
                }
                
                // 脱敏处理
                if (!"1".equals(display)) {
                    userVo.setMobile(HiddenStringUtils.getHiddenMobilePhone(user.getMobile())); 
                }else{  //解密
                    if(StringUtils.isNotEmpty(user.getEnMobile())){
                        try {
                            userVo.setMobile(encryptService.newDecryptData(user.getEnMobile()));
                            userVo.setEmail(encryptService.newDecryptData(user.getEnEmail()));
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
                resultList.add(userVo);
            }
            pagingResult.setRows(resultList);
            pagingResult.setTotal(total);
        }
        return pagingResult;
    }

    //修改用户资料，--启用禁用
    @Logable(businessTag = "pas.modifyUser")
    public int modifyUser(User u) {
        int count = updateUserByIdSelective(u);
        return count;
    }

//    @Logable(businessTag = "userService.findMyPassword")
    public void findMyPassword(String mobile, String newPwd, String captcha) throws Exception {

        // 校验密码复杂度
        if (!PasswordCheck.loginPasswordCheck(newPwd)) {
            throw new AppException(PasCode.NEW_PASSWORD_TOO_SIMPLE.code, PasCode.NEW_PASSWORD_TOO_SIMPLE.message);
        }
        
        Map map = new HashMap();
        // 使用hash值查询 -- 20230205
        map.put("mobile", HexUtil.getHashValue(mobile));
        List<User> userList = userDAO.selectUserByParam(map);
        if (userList == null) {
            throw new AppException(PasCode.USER_NOT_EXIST.code, PasCode.USER_NOT_EXIST.message);
        }
        if (userList.size() == 0) {
            throw new AppException(PasCode.USER_NOT_EXIST.code, PasCode.USER_NOT_EXIST.message);
        } else if (userList.size() > 1) {
            throw new AppException(PasCode.USER_EXIST_BYMOBILE.code, PasCode.USER_EXIST_BYMOBILE.message);
        }
        
//        if (!PasConstants.SUCCESS.equals(callUAA.validateCode(mobile, captcha, PasConstants.serviceCode.FINDBYPHONE.code))) {
//            throw new AppException(PasCode.VALIDATECODE_ERROR.code, PasCode.VALIDATECODE_ERROR.message);
//        }
        sessionService.checkSMSCode(mobile,PasConstants.serviceCode.FINDBYPHONE.code,captcha);
        
        User user = userList.get(0);
        // 用户状态为锁定（第二天自动解除锁定） 允许锁定状态用户找回密码进行解锁
/*        if (user.getStatus().equals(PasConstants.UserStatus.LOCK.code)) {
            throw new AppException(PasCode.LOGIN_USER_STATE_LOCK_ERROR.code, PasCode.LOGIN_USER_STATE_LOCK_ERROR.message);
        }*/
        
        // 用户状态为禁用
        if (user.getStatus().equals(PasConstants.UserStatus.NO.code)) {
            throw new AppException(PasCode.LOGIN_USER_STATE_DISABLE_ERROR.code, PasCode.LOGIN_USER_STATE_DISABLE_ERROR.message);
        }

        // 校验密码重复性
        String pwdMd5 = MD5Utils.getMD5(newPwd);
        if (!checkHistoryPassword(user.getName(), UserType.PAS_USER.code, pwdMd5)) {
            throw new AppException(PasCode.NEW_PASSWORD_CAN_NOT_SAME_AS_OLD.code, "新密码不能与最近" + pwdHistoryCount + "次密码相同");
        }
        
        // 设置新密码 sm3改为sm4 20220815
        String sm3Pwd = encryptService.encryptData(newPwd, "A02");
        String sm4Pwd = encryptService.symmetricEncryptData(sm3Pwd);

        boolean checkSuccess = false;
        if (user.getPassword().indexOf("$b") == 0){
            if (sm4Pwd.equals(user.getPassword())) {
                checkSuccess = true;
            }
        } else if (user.getPassword().indexOf("$A02") == 0) {
            if (sm3Pwd.equals(user.getPassword())) {
                checkSuccess = true;
            }
        } else {
            // 旧版MD5加密
            String md5Pwd = MD5Utils.getMD5(passWordSalt + newPwd);
            if (md5Pwd.equals(user.getPassword())) {
                checkSuccess = true;
            }
        }

       /* // 新密码不能与原密码一致
        if (checkSuccess) {
            throw new AppException(PasCode.NEW_PASSWORD_CAN_NOT_SAME_AS_OLD.code, PasCode.NEW_PASSWORD_CAN_NOT_SAME_AS_OLD.message);
        }*/
        
        int pwdCanUseDate = Integer.parseInt(custService.queryStaticParam("RC_PARAM", "PWD_CAN_USE_DATE"));
        user.setPassword(sm4Pwd);
//        user.setPassword(sm3Pwd);
        user.setPasswordUpdateTime(new Date());
        user.setPasswordExpiredTime(DateUtils.add(user.getPasswordUpdateTime(), Field.DATE, pwdCanUseDate));
        // 重置密码后，对锁定用户重新启用
        if (PasConstants.UserStatus.LOCK.code.equals(user.getStatus())) {
            user.setStatus(PasConstants.UserStatus.YES.code);
        }
        if (updateUserById(user) != 1) {
            // 更新密码失败
            throw new AppException(PasCode.PWD_FIND_ERROR.code, PasCode.PWD_FIND_ERROR.message);
        }
        
        // 重置密码，更新pas统计登录错误次数
        loginInfoService.recoveryErrorCountByUsernameAndType(user.getName(), UserType.PAS_USER.code);
        
        // 保存密码修改记录
        saveHistoryPassword(user.getName(), UserType.PAS_USER.code, pwdMd5);
    }

    public User setUserById(Long userId) {
        User user = userDAO.selectByPrimaryKey(userId);
        //密文字段解密到明文字段 --20230203
        changeHiddenInfo(user);
        return user;
    }

    @Logable(businessTag = "queryMapOfUserIdAndName")
    public Map<Long, String> queryMapOfUserIdAndName(List<Long> userIdList) {
        Map<Long, String> map = new HashMap<>();
        List<User> list = userDAO.queryByUserIdList(userIdList);
        for (User v : list) {
            map.put(v.getUid(), v.getName());
        }
        return map;
    }

    public PageResult<User> selectListByName(String name) {
        PageResult<User> pageResult = new PageResult<>();
        List<User> userList = userDAO.selectListByName(name);
        /*if(userList!= null && userList.size()>0){
            for(User user : userList){
                //密文字段解密到明文字段 --20230203
                changeHiddenInfo(user);
            }
        }*/
        pageResult.setRows(userList);
        return pageResult;
    }
    
    
    /**
     * 根据ID查询商户门户登录名（查询cust表）
     * @param userId
     * @return
     */
    public String queryCustLoginName(Long userId) {
        return userDAO.queryCustLoginName(userId);
    }
    
    /**
     * 校验用户解密权限
     * @param userId
     * @return
     */
    public boolean checkDecryptPermission(Long userId) {
        User user = userDAO.selectByPrimaryKey(userId);
        if (user == null) {
            return false;
        }
        if (new Short("1").equals(user.getCanDecrypt())) { // 有解密权限
            return true;
        }
        return false;
    }

    /**
     * 查询被锁定用户
     * @param count
     * @return
     */
    public List<User> queryLockedUserList(Long count) {
        return userDAO.queryLockedUserList(count);
    }

    private int addUser(User user) {
        try {
            // mobile、email 添加加密数据
            if (StringUtils.isNotBlank(user.getMobile()) && !user.getMobile().contains("*")) {
                user.setEnMobile(encryptService.symmetricEncryptData(user.getMobile()));
            }
            if (StringUtils.isNotBlank(user.getEmail()) && !user.getEmail().contains("*")) {
                user.setEnEmail(encryptService.symmetricEncryptData(user.getEmail()));
            }
            if(user.getCanDecrypt()==null){
                user.setCanDecrypt((short)0);
            }
            //加上脱敏 20230201
            hiddenInfo(user);
            return userDAO.insert(user);
        } catch (Exception e) {
            throw new AppException(PasCode.ENCRYPT_SERIVCE_ERROR.code, PasCode.ENCRYPT_SERIVCE_ERROR.message + ",数据加密错误");
        }
    }

    //加上脱敏 20230201
    private void hiddenInfo(User user){
        // mobile、email字段脱敏
        if (StringUtils.isNotBlank(user.getMobile()) && !user.getMobile().contains("*")) {
            user.setHashMobile(HexUtil.getHashValue(user.getMobile()));
            user.setMobile(HiddenStringUtils.getHiddenMobilePhone(user.getMobile()));
        }
        if (StringUtils.isNotBlank(user.getEmail()) && !user.getEmail().contains("*")) {
            user.setHashEmail(HexUtil.getHashValue(user.getEmail()));
            user.setEmail(HiddenStringUtils.getHiddenEmail(user.getEmail()));
        }
    }

    /**
     * @Description //TODO 原字段已脱敏，明文字段由密文字段解密
     * @Date  2023/2/3 9:09
     * @param: enterprise
     **/
    public void changeHiddenInfo(User user){
        if(user != null){   //明文字段通过密文解密出来
            try{
                if(StringUtils.isNotEmpty(user.getEnEmail())){
                    user.setEmail(encryptService.newDecryptData(user.getEnEmail()));//解密明文
                }
                if(StringUtils.isNotEmpty(user.getEnMobile())){
                    user.setMobile(encryptService.newDecryptData(user.getEnMobile()));
                }
            }catch (Exception e){
                throw new AppException(PasCode.ENCRYPT_SERIVCE_ERROR.code, PasCode.ENCRYPT_SERIVCE_ERROR.message + ",数据解密错误");
            }
        }
    }

    private int updateUserById(User user) {
        try {
            // mobile、email 添加加密数据
            if (StringUtils.isNotBlank(user.getMobile()) && !user.getMobile().contains("*")) {
                user.setEnMobile(encryptService.symmetricEncryptData(user.getMobile()));
            }
            if (StringUtils.isNotBlank(user.getEmail()) && !user.getEmail().contains("*")) {
                user.setEnEmail(encryptService.symmetricEncryptData(user.getEmail()));
            }
            //加上脱敏 20230201
            hiddenInfo(user);
            return userDAO.updateByPrimaryKey(user);
        } catch (Exception e) {
            throw new AppException(PasCode.ENCRYPT_SERIVCE_ERROR.code, PasCode.ENCRYPT_SERIVCE_ERROR.message + ",数据加密错误");
        }
    }

    private int updateUserByIdSelective(User user) {
        try {
            // mobile、email 添加加密数据
            if (StringUtils.isNotBlank(user.getMobile()) && !user.getMobile().contains("*")) {
                user.setEnMobile(encryptService.symmetricEncryptData(user.getMobile()));
            }
            if (StringUtils.isNotBlank(user.getEmail()) && !user.getEmail().contains("*")) {
                user.setEnEmail(encryptService.symmetricEncryptData(user.getEmail()));
            }
            //加上脱敏 20230201
            hiddenInfo(user);
            return userDAO.updateByPrimaryKeySelective(user);
        } catch (Exception e) {
            throw new AppException(PasCode.ENCRYPT_SERIVCE_ERROR.code, PasCode.ENCRYPT_SERIVCE_ERROR.message + ",数据加密错误");
        }
    }


    // ----------------------------------  分公司、业务员管理 ------------------------------
	/**
	 * 新增分公司、业务员
	 * @param userVo
	 */
    @Transactional
	public void modifyCompanyUser(UserVo userVo, Long userId) {
    	
    	checkParamFormat(userVo);
    	User user = userDAO.selectByPrimaryKey(userVo.getUid());
    	if (user == null) {
    		throw new AppException(PasCode.USER_NOT_EXIST.code, PasCode.USER_NOT_EXIST.message);
    	}
    	boolean changeRealName = false;
    	if (StringUtils.isNotBlank(userVo.getRealName()) && !userVo.getRealName().equals(user.getRealName())) {
	    	boolean existUser = userDAO.checkExistOfRealName(userVo.getRealName(), user.getUid());
	    	if (existUser) {
	    		if (PasConstant.UserType.COMPANY.code.equals(user.getUserType())){
	    			throw new AppException(PasCode.COMPANY_NAME_EXIST.code, PasCode.COMPANY_NAME_EXIST.message);
	    		}else if (PasConstant.UserType.SALES.code.equals(user.getUserType())){
	    			throw new AppException(PasCode.SALES_REAL_NAME_EXIST.code, PasCode.SALES_REAL_NAME_EXIST.message);
	    		}else {
	    			throw new AppException(PasCode.USER_REAL_NAME_EXIST.code, PasCode.USER_REAL_NAME_EXIST.message);
	    		}
	    	}else {
        		user.setRealName(userVo.getRealName());
        		changeRealName = true;
	    	}
    	}
    	User operUser = userDAO.selectByPrimaryKey(userId);
    	if (operUser == null) {
    		throw new AppException(PasCode.USER_NOT_EXIST.code, PasCode.USER_NOT_EXIST.message);
    	}
    	if(StringUtils.isNotBlank(userVo.getName()) && !user.getName().equals(userVo.getName())) {
    		throw new AppException(PasCode.USERNAME_CAN_NOT_MODIFY.code, PasCode.USERNAME_CAN_NOT_MODIFY.message);
    	}
    	if (StringUtils.isNotBlank(userVo.getPassword())) {
    		String password = decryptPwdByRSA(userVo.getPassword());
            
    	    // 校验密码复杂度
            if (!PasswordCheck.loginPasswordCheck(password)) {
                throw new AppException(PasCode.NEW_PASSWORD_TOO_SIMPLE.code, PasCode.NEW_PASSWORD_TOO_SIMPLE.message);
            }

            // 设置新密码 md5改为国密 modif by hrz 2020-10-10
            String sm3Pwd = encryptService.encryptData(password, "A02");

            boolean checkSuccess = false;
            if (user.getPassword().indexOf("$A02") == 0) {
                // 新版国密加密
                if (sm3Pwd.equals(user.getPassword())) {
                    checkSuccess =true;
                }
            } else {
                // 旧版MD5加密
                String md5Pwd = MD5Utils.getMD5(passWordSalt + password);
                if (md5Pwd.equals(user.getPassword())) {
                    checkSuccess =true;
                }
            }

            // 新密码不能与原密码一致
            if (checkSuccess) {
                throw new AppException(PasCode.NEW_PASSWORD_CAN_NOT_SAME_AS_OLD.code, PasCode.NEW_PASSWORD_CAN_NOT_SAME_AS_OLD.message);
            }
            userVo.setPassword(sm3Pwd);
    	}
		if (PasConstant.UserType.COMPANY.code.equals(user.getUserType())){// 更新分公司上级
			if (userVo.getParentCompanyId()!=null){
				Company company = new Company();
				company.setCompanyId(user.getCompanyId());
				company.setName(user.getRealName());
				company.setParentId(userVo.getParentCompanyId());
				company.setUpdator(userId);
				company.setStatus(userVo.getCompanyStatus());
				company.setUpdateTime(new Date());
				companyService.update(company);
				// 禁用时，对分公司下所有业务员更新状态
//				String status = userVo.getCompanyStatus() + "";
//		    	if("N".equals(status)) {
//					List<User> userList = userDAO.listCompanyUser(user.getCompanyId());
//					if (userList != null) {
//						for (User tmp:userList) {
//							tmp.setStatus(status);
//							tmp.setUpdateTime(new Date());
//							tmp.setUpdator(operUser.getName());
//					    	userDAO.updateByPrimaryKeySelective(tmp);
//						}
//					}
//		    	}
			}
		}else if (PasConstant.UserType.SALES.code.equals(user.getUserType())){ // 更新业务员所属分公司
			if (userVo.getCompanyId()!=null){
				user.setCompanyId(userVo.getCompanyId());
			}
		}else {
			throw new AppException(PasCode.USERTYPE_NOTEXIST.code, PasCode.USERTYPE_NOTEXIST.message);
		}
		user.setMobile(userVo.getMobile());
		user.setRemark(userVo.getRemark());
		user.setUpdator(operUser.getName());
		user.setUpdateTime(new Date());
        updateUserByIdSelective(user);
		
		if(changeRealName) {// 更新了姓名、分公司名，同步到cum
			if (PasConstant.UserType.COMPANY.code.equals(user.getUserType())){
				if(user.getCompanyId() != null) {
					cumClient.syncCompanyName(user.getCompanyId(), user.getRealName());
				}
			} else {
				cumClient.syncBusinessManName(user.getUid(), user.getRealName());
			}
			
		}
	}
    
    /**
     * 参数格式校验
     */
    private boolean checkParamFormat(UserVo user) {
    	
    	if(StringUtils.isNotBlank(user.getName()) && VerificationUtils.isContainChinese(user.getName())) {
    		throw new AppException(PasCode.USERNAME_INVALID.code, PasCode.USERNAME_INVALID.message);
    	}
    	if(StringUtils.isNotBlank(user.getMobile()) && !VerificationUtils.checkMobilePhone(user.getMobile())) {
			throw new AppException(PasCode.MOBILE_PHONE_INVALID.code, PasCode.MOBILE_PHONE_INVALID.message);
    	}
    	
    	return true;
    }
	
	private String decryptPwdByRSA(String encrytPwd) {
		
		Map<String, String> map = new HashMap<String, String>();
		map.put("password", encrytPwd);
		try {
			// 机密后结果设置到对应key中去
			Map<String, String> resultMap = encryptService.decryptByKeyType(map, Constants.RsaKeyType.FRONT.code);
			if(resultMap != null) {
				return resultMap.get("password");
			}
		}catch (Exception e){
			e.printStackTrace();
			throw new AppException("0722", "数据解密失败");
		}
		return null;
	}

	/**
	 * 修改用户状态
	 * @param uid
	 * @param status
	 */
	public void changeStatus(Long uid, String status, Long operatorId,String source) {

		User user = userDAO.selectByPrimaryKey(uid);
    	if (user == null) {
    		throw new AppException(PasCode.USER_NOT_EXIST.code, PasCode.USER_NOT_EXIST.message);
    	}
	    User operUser = userDAO.selectByPrimaryKey(operatorId);
    	if (operUser == null) {
    		throw new AppException(PasCode.USER_NOT_EXIST.code, PasCode.USER_NOT_EXIST.message);
    	}
    	
    	// 禁用时同时禁用业务员进件链接
    	if (PasConstants.UserStatus.NO.code.equals(status) && PasConstant.UserType.SALES.code.equals(user.getUserType())) {
    	    user.setPicecLinkStatus(new Short("0"));
    	}
        user.setStatus(status);
    	user.setUpdateTime(new Date());
    	user.setUpdator(operUser.getName());
        updateUserByIdSelective(user);
    	
    	// 禁用时，对分公司下所有业务员更新状态
    	if("N".equals(status) && !"salesFrozen".equals(source)) {
			List<User> userList = userDAO.listCompanyUser(user.getCompanyId());
			if (userList != null) {
				for (User tmp:userList) {
					tmp.setStatus(status);
					tmp.setUpdateTime(new Date());
					tmp.setUpdator(operUser.getName());
                    updateUserByIdSelective(tmp);
				}
			}
    	}
	}
	
	/**
	 * 搜索用户
	 * @param map
	 * @param operatorId
	 */
	public List<User> searchUser(Map map, Long operatorId) {

		User operUser = userDAO.selectByPrimaryKey(operatorId);
    	if (operUser == null) {
    		throw new AppException(PasCode.USER_NOT_EXIST.code, PasCode.USER_NOT_EXIST.message);
    	}
    	// 权限条件
    	if (PasConstant.UserType.COMPANY.code.equals(operUser.getUserType())){ // 分公司管理员能查询分公司下所有商户
    		map.put("companyId", operUser.getCompanyId());
    	}else if (PasConstant.UserType.SALES.code.equals(operUser.getUserType())){// 业务员只能搜索自己
    		map.put("userId", operUser.getUid());
    	}
        List<User> list = userDAO.searchUser(map);
    	if(list != null && list.size()>0){
            for(User user: list){
                //密文字段解密到明文字段 --20230203
                changeHiddenInfo(user);
            }
        }
        return list;
		
	}

	/**
	 * 查询分公司下有效用户
	 * @param companyId
	 * @return
	 */
    public List<User> listCompanyEffectiveUser(Long companyId) {
        
        List<User> list = userDAO.listCompanyUser(companyId);
        List<User> resultList = new ArrayList<User>();
        list.forEach(user -> {
            if (user!=null && "Y".equals(user.getStatus())){
                //密文字段解密到明文字段 --20230203
                changeHiddenInfo(user);
                resultList.add(user);
            };
        });
        return resultList;
    }

    /**
     * 随机查找分公司下一有效业务员
     * @param companyId
     * @return
     */
    public User randomOneByCompany(Long companyId) {
        User user =  userDAO.randomOneByCompany(companyId);
        //密文字段解密到明文字段 --20230203
        changeHiddenInfo(user);
        return user;
    }
    
    
    /**
     * 生成或刷新自助进件链接
     * @param uid
     * @param userId
     * @return
     */
    public PieceLinkVo refreshPieceLink(Long uid, Long userId) {

        User operUser = userDAO.selectByPrimaryKey(userId);
        if (operUser == null) {
            throw new AppException(PasCode.USER_NOT_EXIST.code, PasCode.USER_NOT_EXIST.message);
        }
        User salesUser = userDAO.selectByPrimaryKey(uid);
        if (salesUser == null) {
            throw new AppException(PasCode.SALES_NOT_EXIST.code, PasCode.SALES_NOT_EXIST.message);
        }
        if (PasConstants.UserStatus.NO.code.equals(salesUser.getStatus())){
            throw new AppException(PasCode.SALES_STATE_DISABLE_ERROR.code, PasCode.SALES_STATE_DISABLE_ERROR.message);
        }
        String pieceLink = h5PieceLink + "?businessmanId=" + uid;
        salesUser.setPieceLink(pieceLink);
        salesUser.setUpdateTime(new Date());
        salesUser.setUpdator(operUser.getName());
        if (salesUser.getPicecLinkStatus() == null) { // 初始化时设置为有效，其他刷新时不改变状态
            salesUser.setPicecLinkStatus(new Short("1"));
        }
        //加上脱敏 20230201
        hiddenInfo(salesUser);
        userDAO.updateByPrimaryKeySelective(salesUser);

        PieceLinkVo vo = new PieceLinkVo();
        vo.setPicecLinkStatus(salesUser.getPicecLinkStatus());
        vo.setPieceLink(salesUser.getPieceLink());
        vo.setUid(uid);
        
        return vo;
    }

    /**
     * 查询业务员进件链接信息
     * @param uid
     * @return
     */
    public PieceLinkVo queryPieceLink(Long uid) {

        User salesUser = userDAO.selectByPrimaryKey(uid);
        if (salesUser == null) {
            throw new AppException(PasCode.SALES_NOT_EXIST.code, PasCode.SALES_NOT_EXIST.message);
        }
        
        PieceLinkVo vo = new PieceLinkVo();
        vo.setPicecLinkStatus(salesUser.getPicecLinkStatus());
        vo.setPieceLink(salesUser.getPieceLink());
        vo.setUid(uid);
        return vo;
    }

    /**
     * 修改自助进件链接状态
     * @param uid
     * @param userId
     * @return
     */
    public int updatePieceLinkStatus(Long uid, Short picecLinkStatus, Long userId) {
        
        User operUser = userDAO.selectByPrimaryKey(userId);
        if (operUser == null) {
            throw new AppException(PasCode.USER_NOT_EXIST.code, PasCode.USER_NOT_EXIST.message);
        }

        User salesUser = userDAO.selectByPrimaryKey(uid);
        if (salesUser == null) {
            throw new AppException(PasCode.SALES_NOT_EXIST.code, PasCode.SALES_NOT_EXIST.message);
        }
        if (PasConstants.UserStatus.NO.code.equals(salesUser.getStatus()) && new Short("1").equals(picecLinkStatus)){
            throw new AppException(PasCode.SALES_STATE_DISABLE_ERROR.code, PasCode.SALES_STATE_DISABLE_ERROR.message);
        }
        
        salesUser.setPicecLinkStatus(picecLinkStatus);
        salesUser.setUpdateTime(new Date());
        salesUser.setUpdator(operUser.getName());
        //加上脱敏 20230201
        hiddenInfo(salesUser);
        return userDAO.updateByPrimaryKeySelective(salesUser);
    }

    
    /**
     * 查询业务员信息
     * @param uid
     * @return
     */
    public SalesmanVo querySalesman(Long uid) {
        
        User user = userDAO.selectByPrimaryKey(uid);
        if (user == null || !PasConstant.UserType.SALES.code.equals(user.getUserType())) {
            throw new AppException(PasCode.SALES_NOT_EXIST.code, PasCode.SALES_NOT_EXIST.message);
        }

        SalesmanVo salesmanVo = new SalesmanVo();
        try {
            BeanUtils.copyProperties(salesmanVo, user);
        } catch (IllegalAccessException | InvocationTargetException e) {
            e.printStackTrace();
        }
        if (user.getCompanyId() != null) {// 所属分公司
            Company compay = companyService.queryById(user.getCompanyId());
            salesmanVo.setCompanyName(compay != null ? compay.getName() : null);
        }
        
        return salesmanVo;
    }
    
    // ----------------------------------  end 分公司、业务员管理 ------------------------------

    /**
     * 校验近N次密码重复性
     * @param userName
     * @param userType
     * @param pwdMd5   md5加密
     * @return
     */
    public boolean checkHistoryPassword(String userName, String userType, String pwdMd5) {
        
        List<PasswordHistory> list = passwordHistoryMapper.queryPwdHistoryByUser(userName, userType, pwdHistoryCount);
        if (list == null || list.isEmpty()) {
            return true;
        }
        for (PasswordHistory history: list) {
            if (pwdMd5.equals(history.getPassword())) {
                return false;
            }
        }
        return true;
    }
    
    public int saveHistoryPassword(String userName, String userType, String password) {
        PasswordHistory  passwordHistory = new PasswordHistory();
        passwordHistory.setId(sequenceService.nextValue("PAS_PASSWORD_HISTORY"));
        passwordHistory.setUserName(userName);
        passwordHistory.setUserType(userType);
        passwordHistory.setPassword(password);
        passwordHistory.setCreateTime(new Date());
        return passwordHistoryMapper.insert(passwordHistory);
    }
    

    public List<Long> queryUserIdsByPermId(Long permId, Long userId){
        if(permId.equals(-1L)){
            return permService.selectPermIdsByUserId(userId);
        }
        return userDAO.queryUserIdsByPermId(permId, userId);
    }

    @Logable(businessTag = "custInfoSetHidden")
    public CommonOuterResponse<Map> custInfoSetHidden(Integer startPage, Integer endPage) {
        CommonOuterResponse<Map> response = new CustomerResponse();
        int beginRowNo = 0;
        int endRowNo = 0;
        Map resultMap = new HashMap();
        int total = userDAO.countAllCustUser();
        int totalPage = total / 100;
        resultMap.put("数据总页数", totalPage);
        Map<String, Object> paramMap = new HashMap<String, Object>();
        List<User> list = null;
        boolean needUpdate = false;
        int updateNum = 0;
        int noUpdateNum = 0;
        String mobile = "";
        String email = "";
        try{
            for (int pageNum = startPage; pageNum <= endPage; pageNum++){
                beginRowNo = (pageNum - 1) * 100 + 1;
                endRowNo = pageNum * 100;
                paramMap.put("beginRowNo", beginRowNo);
                paramMap.put("endRowNo", endRowNo);
                list = userDAO.queryAllCustUserByPage(paramMap);
                if(list != null && list.size()>0){
                    for(User user : list){
                        needUpdate = false;
                        if(StringUtils.isNotEmpty(user.getEmail())
                                && !"-".equals(user.getEmail())
                                && user.getEmail().length()>5){
                            email = "";
                            if(!user.getEmail().contains("*")){
                                email = user.getEmail();
                                if(StringUtils.isNotEmpty(user.getEnEmail())){   // 存在密文，直接给明文脱敏
                                    needUpdate = true;
                                    user.setEmail(HiddenStringUtils.getHiddenEmail(user.getEmail()));
                                }else{
                                    needUpdate = true;
                                    user.setEnEmail(encryptService.symmetricEncryptData(user.getEmail()));
                                }
                            }else{
                                if(StringUtils.isEmpty(user.getHashEmail()) && StringUtils.isNotEmpty(user.getEnEmail())){
                                    email = encryptService.newDecryptData(user.getEnEmail());//解密明文
                                }
                            }
                            if(StringUtils.isEmpty(user.getHashEmail()) && StringUtils.isNotEmpty(email)){
                                needUpdate = true;
                                user.setHashEmail(HexUtil.getHashValue(email));
                            }
                        }
                        if(StringUtils.isNotEmpty(user.getMobile())
                                && !"-".equals(user.getMobile())
                                && user.getMobile().length()>5){
                            mobile = "";
                            if(!user.getMobile().contains("*")){
                                mobile = user.getMobile();
                                if(StringUtils.isNotEmpty(user.getEnMobile())){   // 存在密文，直接给明文脱敏
                                    needUpdate = true;
                                    user.setMobile(HiddenStringUtils.getHiddenMobilePhone(user.getMobile()));
                                }else{
                                    needUpdate = true;
                                    user.setEnMobile(encryptService.symmetricEncryptData(user.getMobile()));
                                }
                            }else{
                                if(StringUtils.isEmpty(user.getHashMobile()) && StringUtils.isNotEmpty(user.getEnMobile())){
                                    mobile = encryptService.newDecryptData(user.getEnMobile());//解密明文
                                }
                            }
                            if(StringUtils.isEmpty(user.getHashMobile()) && StringUtils.isNotEmpty(mobile)){
                                needUpdate = true;
                                user.setHashMobile(HexUtil.getHashValue(mobile));
                            }
                        }
                        if(needUpdate){
                            updateNum ++;
                            user.setUpdateTime(new Date());
                            userDAO.updateByPrimaryKey(user);
                        }else{
                            noUpdateNum ++;
                        }
                    }
                }
            }
            resultMap.put("更新成功数",updateNum);
            resultMap.put("不需要更新数",noUpdateNum);
            response.setData(resultMap);
        }catch (Exception e){
            e.printStackTrace();
            response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
            response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
        }

        return response;
    }

    /**
     * @return 入参int, 返回字符串
     * @category 随机生成指定位数的数字
     */
    //随机生成指定位数的字符串
    private String getRandomNumber(int length) {
        String base = "0123456789";
        Random random = new Random();
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < length; i++) {
            //62个字符
            int number = random.nextInt(base.length());
            sb.append(base.charAt(number));
        }
        return sb.toString();
    }

    public List<Map> queryUserByName(String name) {
        return userDAO.queryUserByName(name);
    }
}
