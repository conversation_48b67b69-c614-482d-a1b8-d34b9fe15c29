package com.epaylinks.efps.pas.acl.controller.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class RoleRequest {
    private Long uid;// 必填

    private String name;// 必填

    private String remark;

    private String ids;// 权限Ids,逗号分隔

    @ApiModelProperty(value = "数据权限范围（空或0：全量，1：自定义，2：本部门，3：本人）", dataType = "String")
    private String dataAuth;

    @ApiModelProperty(value = "分公司ids,逗号分隔", dataType = "String")
    private String companyIds;// 分公司ids,逗号分隔
}