package com.epaylinks.efps.pas.acl.po;

import java.util.List;

/**
 * <AUTHOR> 5m
 * @Date : 2017/10/31 11:47
 * @Description :
 */
public class Tree implements java.io.Serializable {

    private static final long serialVersionUID = -1413346686307539707L;
    private String id;
    private String title;
    private String url;
    private String state = "open";// open,closed
    private boolean checked = true;
    private List<Tree> children;
    private String iconCls;
    private String pid;
    private String expand = "true";
    private String code ;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public boolean isChecked() {
        return checked;
    }

    public void setChecked(boolean checked) {
        this.checked = checked;
    }

    public List<Tree> getChildren() {
        return children;
    }

    public void setChildren(List<Tree> children) {
        this.children = children;
    }

    public String getIconCls() {
        return iconCls;
    }

    public void setIconCls(String iconCls) {
        this.iconCls = iconCls;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }


    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getExpand() {
        return expand;
    }

    public void setExpand(String expand) {
        this.expand = expand;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
}

