package com.epaylinks.efps.pas.acl.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.epaylinks.efps.pas.acl.po.RoleUser;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface RoleUserMapper {

    int insert(RoleUser record);

    int insertSelective(RoleUser record);

    int update(RoleUser record);

    int deleteByUserId(Long userId);

    List<Long> selectRoleIdByUserId(Long userId);

    String selectRoleCodeByUserId(Long userId);

    String selectRoleNameByUserId(Long userId);
    
    List<RoleUser> selectRoleIdsByUser(Long userId);

    List<Long> selectUerIdsByRoleId(Long roleId);

    boolean checkIsSuperAdmin(@Param("userId") Long userId);

    void deleteByRoleId(@Param("roleId") Long roleId);

    Integer selectCheckRole(@Param("userId")Long userId,@Param("roleId")Long roleId);
}