package com.epaylinks.efps.pas.acl.po;

public class Perm {
    /**
     * ID
     */
    private Long permId;

    private Long permSort;   //排序值

    public Long getPermSort() {
        return permSort;
    }

    public void setPermSort(Long permSort) {
        this.permSort = permSort;
    }

    /**
     * 权限名称(英文)
     */
    private String name;

    /**
     * 父节点ID
     */
    private Long parentId;

    /**
     * 权限别称(中文)
     */
    private String alias;

    /**
     * 菜单URL
     */
    private String url;

    public Long getPermId() {
        return permId;
    }

    public void setPermId(Long permId) {
        this.permId = permId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }
}