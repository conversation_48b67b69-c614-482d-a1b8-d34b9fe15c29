package com.epaylinks.efps.pas.acl.dto;

import java.util.List;

/**
 * 权限树结点
 */
public class PermNode {

    private Long permId;
    private String name;
    private String alias;
    private String url;
    private boolean checked = false;
    private List<PermNode> children;

    public Long getPermId() {
        return permId;
    }

    public void setPermId(Long permId) {
        this.permId = permId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public boolean isChecked() {
        return checked;
    }

    public void setChecked(boolean checked) {
        this.checked = checked;
    }

    public List<PermNode> getChildren() {
        return children;
    }

    public void setChildren(List<PermNode> children) {
        this.children = children;
    }

    public void addChild(PermNode child) {
        this.children.add(child);
    }
}

