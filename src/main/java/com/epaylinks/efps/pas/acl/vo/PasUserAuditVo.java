package com.epaylinks.efps.pas.acl.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class PasUserAuditVo {
    /**
     * ID
     */
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 登录名
     */
    private String loginName;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 修改前角色ID(英文逗号分隔)
     */
    private String roleIdBefore;

    /**
     * 修改前角色名字
     */
    private String roleNameBefore;

    /**
     * 修改后角色ID(英文逗号分隔)
     */
    private String roleIdAfter;

    /**
     * 修改后角色名字
     */
    private String roleNameAfter;

    /**
     * 审核状态(0待审核，1审核通过，2审核不通过)
     */
    private String status;

    /**
     * 审核意见
     */
    private String remark;

    /**
     * 审核人
     */
    private String auditPerson;

    /**
     * 审核时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date auditTime;

    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;


}