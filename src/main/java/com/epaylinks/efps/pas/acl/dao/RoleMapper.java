package com.epaylinks.efps.pas.acl.dao;

import java.util.List;
import java.util.Map;
import com.epaylinks.efps.pas.acl.dto.UserRoleDataAuthDTO;
import org.apache.ibatis.annotations.Mapper;
import com.epaylinks.efps.pas.acl.po.Role;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface RoleMapper {

	int deleteByPrimaryKey(Long uid);

	int insert(Role record);

	int insertSelective(Role record);

	Role selectByPrimaryKey(Long uid);

	int updateByPrimaryKeySelective(Role record);

	int updateByPrimaryKey(Role record);

	List<Role> queryList();

	int addPermissons(Map<String, Object> paramMap);

	List<Role> selectByPage(Map map);

	List<Role> selectBySelective(Role role);

	List<Role> selectAllRoles(@Param("dataAuth") String dataAuth);

	List<Role> selectRolesByPage(Map map);

	List<Role> selectRolesBySelective(Map  map);

    boolean checkExistOfRoleName(@Param("name") String name);

    boolean checkExistOfRoleNameWhenModify(@Param("name") String name, @Param("roleId") Long roleId);

	Role selectByRoleCode(@Param("roleCode") String code);

	List<UserRoleDataAuthDTO> selectRoleDataAuthByUserId(@Param("userId") Long userId,@Param("dataAuth") String dataAuth);

	Boolean judgeRoleDataByRoleIds(@Param("list") List<Long> list,@Param("dataAuth") String dataAuth);
}