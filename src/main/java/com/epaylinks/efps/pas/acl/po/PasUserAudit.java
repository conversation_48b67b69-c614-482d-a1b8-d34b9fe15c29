package com.epaylinks.efps.pas.acl.po;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class PasUserAudit {
    /**
     * ID
     */
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 修改前角色ID(英文逗号分隔)
     */
    private String roleIdBefore;

    /**
     * 修改前角色名字
     */
    private String roleNameBefore;

    /**
     * 修改后角色ID(英文逗号分隔)
     */
    private String roleIdAfter;

    /**
     * 修改后角色名字
     */
    private String roleNameAfter;

    /**
     * 审核状态(0待审核，1审核通过，2审核不通过)
     */
    private String status;

    /**
     * 审核意见
     */
    private String remark;

    /**
     * 审核人
     */
    private String auditPerson;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createPerson;


}