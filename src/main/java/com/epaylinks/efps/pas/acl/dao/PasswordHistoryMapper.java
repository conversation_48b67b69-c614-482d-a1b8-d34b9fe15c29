package com.epaylinks.efps.pas.acl.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.epaylinks.efps.pas.acl.po.PasswordHistory;

@Mapper
public interface PasswordHistoryMapper {
    int deleteByPrimaryKey(Long id);

    int insert(PasswordHistory record);

    int insertSelective(PasswordHistory record);

    PasswordHistory selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PasswordHistory record);

    int updateByPrimaryKey(PasswordHistory record);

    int countHistoryPassword(@Param("userName") String userName, @Param("userType") String userType, @Param("password")  String password);

    List<PasswordHistory> queryPwdHistoryByUser(@Param("userName")String userName, @Param("userType")String userType,  @Param("pwdHistoryCount")int pwdHistoryCount);
}