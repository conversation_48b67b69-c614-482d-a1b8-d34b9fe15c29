package com.epaylinks.efps.pas.acl.dao;

import com.epaylinks.efps.pas.acl.dto.DepartmentUserAmount;
import com.epaylinks.efps.pas.acl.po.Department;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DepartmentMapper {

    int deleteByPrimaryKey(Long id);

    int insert(Department record);

    int insertSelective(Department record);

    Department selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(Department record);

    int updateByPrimaryKey(Department record);

    List<DepartmentUserAmount> selectUserAmountByDeptIds(@Param("deptIdList") List<Long> deptIdList);

    List<DepartmentUserAmount> queryUserAmountByDeptIds(@Param("deptIdList") List<Long> deptIdList);

    boolean checkExistOfName(@Param("name") String name);

    boolean checkExistOfNameWhenModify(@Param("name") String name, @Param("id") Long id);

    void deleteDeptFromUser(@Param("deptId") Long deptId);
}