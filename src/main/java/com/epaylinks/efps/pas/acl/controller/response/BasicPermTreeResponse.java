package com.epaylinks.efps.pas.acl.controller.response;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.pas.acl.dto.PermNode;

/**
 * 基础权限树 - 响应类
 */
public class BasicPermTreeResponse extends CommonOuterResponse {

    private PermNode root;

    public PermNode getRoot() {
        return root;
    }

    public void setRoot(PermNode root) {
        this.root = root;
    }
}
