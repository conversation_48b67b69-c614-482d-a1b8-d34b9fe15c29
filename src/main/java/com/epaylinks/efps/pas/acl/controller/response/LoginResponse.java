package com.epaylinks.efps.pas.acl.controller.response;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.pas.acl.dto.PermNode;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 登录响应
 */
@ApiModel
public class LoginResponse extends CommonOuterResponse {

    private Long userId;
    private String userName;
    private String userRealName;
    private Short userType;
    private String token;
    
    @ApiModelProperty(value="是否允许解密：0：不允许；1：允许", dataType = "Short")
    private Short canDecrypt;
    
    @ApiModelProperty(value = "登录密码修改建议：0:不需修改；1:建议修改;2:必须修改", dataType = "Integer")
    private Integer loginPwdChangeSuggest;
    
    private Integer payPwdChangeSuggest;
    
    private PermNode permTreeRoot;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Integer getLoginPwdChangeSuggest() {
        return loginPwdChangeSuggest;
    }

    public void setLoginPwdChangeSuggest(Integer loginPwdChangeSuggest) {
        this.loginPwdChangeSuggest = loginPwdChangeSuggest;
    }

    public Integer getPayPwdChangeSuggest() {
        return payPwdChangeSuggest;
    }

    public void setPayPwdChangeSuggest(Integer payPwdChangeSuggest) {
        this.payPwdChangeSuggest = payPwdChangeSuggest;
    }

    public PermNode getPermTreeRoot() {
        return permTreeRoot;
    }

    public void setPermTreeRoot(PermNode permTreeRoot) {
        this.permTreeRoot = permTreeRoot;
    }

	public String getUserRealName() {
		return userRealName;
	}

	public void setUserRealName(String userRealName) {
		this.userRealName = userRealName;
	}

	public Short getUserType() {
		return userType;
	}

	public void setUserType(Short userType) {
		this.userType = userType;
	}

    public Short getCanDecrypt() {
        return canDecrypt;
    }

    public void setCanDecrypt(Short canDecrypt) {
        this.canDecrypt = canDecrypt;
    }

}
