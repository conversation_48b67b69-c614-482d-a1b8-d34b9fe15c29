package com.epaylinks.efps.pas.acl.vo;

import com.epaylinks.efps.pas.acl.po.User;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel
public class UserVo extends User {
	
	@ApiModelProperty(value="所属分公司名称", dataType = "String")
	private String companyName;
	
	@ApiModelProperty(value="上级分公司ID", dataType = "Long")
	private Long parentCompanyId;

	@ApiModelProperty(value="上级分公司名称", dataType = "String")
	private String parentCompanyName;

	@ApiModelProperty(value="分公司级别", dataType = "Long")
	private Short companyLevel;
	
	@ApiModelProperty(value="分公司状态", dataType = "Long")
	private Short companyStatus;


	public Short getCompanyStatus() {
		return companyStatus;
	}

	public void setCompanyStatus(Short companyStatus) {
		this.companyStatus = companyStatus;
	}

	public Long getParentCompanyId() {
		return parentCompanyId;
	}

	public void setParentCompanyId(Long parentCompanyId) {
		this.parentCompanyId = parentCompanyId;
	}

	public String getCompanyName() {
		return companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	public String getParentCompanyName() {
		return parentCompanyName;
	}

	public void setParentCompanyName(String parentCompanyName) {
		this.parentCompanyName = parentCompanyName;
	}

	public Short getCompanyLevel() {
		return companyLevel;
	}

	public void setCompanyLevel(Short companyLevel) {
		this.companyLevel = companyLevel;
	}

	

}
