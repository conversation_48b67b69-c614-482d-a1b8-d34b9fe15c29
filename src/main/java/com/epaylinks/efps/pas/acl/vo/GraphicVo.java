package com.epaylinks.efps.pas.acl.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel
public class GraphicVo {

	@ApiModelProperty(value="序列", dataType = "String")
	private String uid;
	@ApiModelProperty(value="图形base64编码", dataType = "String")
	private String img;

	public String getUid() {
		return uid;
	}

	public void setUid(String uid) {
		this.uid = uid;
	}

	public String getImg() {
		return img;
	}

	public void setImg(String img) {
		this.img = img;
	}
	
	

}
