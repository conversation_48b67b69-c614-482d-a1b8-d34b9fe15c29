package com.epaylinks.efps.pas.acl.dao;

import org.apache.ibatis.annotations.Mapper;

import com.epaylinks.efps.pas.acl.po.Perm;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface PermMapper {
	
    int deleteByPrimaryKey(Long permId);

    int insert(Perm record);

    int insertSelective(Perm record);

    Perm selectByPrimaryKey(Long permId);

    int updateByPrimaryKeySelective(Perm record);

    int updateByPrimaryKey(Perm record);

    List<Perm> selectAll();

    List<Perm> selectDataPermList(@Param("list") List<Long> list);
    
    int existByName(String name);

    List<Long> selectPermIdsByUserId(Long userId);

    List<Long> selectPermIdsByUserIdAndRoleId(@Param("userId")Long userId,@Param("roleId")Long roleId);

    List<Long> selectPermIdsByRoleId(Long roleId);

    List<Long> selectAllPermIds();

    List<Long> selectSubPermIds(@Param("permId") Long permId);

    void deleteByPermIds(@Param("permIds") List<Long> permIds);

    Long selectMaxSubPermId(@Param("parentId") Long parentId);

    Long getMaxPermId();

    int selectCountPasPerm(Map map);

     List<Perm> selectByPage(Map map);
     
     /**
      * 根据id查询菜单全路径（英文逗点分割）
      * @param permId
      * @return
      */
     String selectFullPermNamesById(@Param("permId") Long permId);
}