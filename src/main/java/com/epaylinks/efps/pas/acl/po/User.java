package com.epaylinks.efps.pas.acl.po;

import com.epaylinks.efps.common.datadownload.annotation.FieldAnnotation;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

@Data
@ApiModel
public class User {

	@ApiModelProperty(value="用户ID", dataType = "String")
	private Long uid;
	
	private Long deptId;
	
	@ApiModelProperty(value="用户账号", dataType = "String")
	private String name;
	
	@ApiModelProperty(value="真实用户名称", dataType = "String")
	private String realName;
	
	private String remark;

	private String password;
	
	private String creator;

	private String roleIdList;
	private String roleName;
	private String deptName;
	
	@ApiModelProperty(value="分公司ID", dataType = "Long")
    private Long companyId;

	@ApiModelProperty(value="营销代码", dataType = "Long")
    private Long salesCode;

	@ApiModelProperty(value="分用户类型：1：分公司管理员；2：业务员；其他：默认", dataType = "Long")
    private Short userType;

	@ApiModelProperty(value="密码过期时间[为空即不过期]", dataType = "Date")
    private Date passwordExpiredTime;

    @ApiModelProperty(value="最近密码修改时间[为空则未修改过]", dataType = "Date")
    private Date passwordUpdateTime;
	
    @ApiModelProperty(value="是否允许解密：0：不允许；1：允许", dataType = "Short")
    private Short canDecrypt;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @FieldAnnotation(fieldName = "创建时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
	private Date createTime;
	
	private String updator;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @FieldAnnotation(fieldName = "修改时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
	private Date updateTime;
	
	private String email;
	
	private String mobile;
	
    @ApiModelProperty(value="状态：Y：启用；N:禁用；L：锁定", dataType = "String")
	private String status;

	private Long roleId;

	/**
	 * 用户手机号码（密文）
	 */
	private String enMobile;

	/**
	 * 用户邮箱（密文）
	 */
	private String enEmail;
	
    @ApiModelProperty(value="自助进件H5链接", dataType = "String")
    private String pieceLink;

    @ApiModelProperty(value="链接状态：0：无效；1：有效", dataType = "Short")
    private Short picecLinkStatus;

	/**
	 * 用户手机号码（HASH）
	 */
	private String hashMobile;
	/**
	 * 用户邮箱（HASH）
	 */
	private String hashEmail;

	private String dataAuth;

	public Long getUid() {
		return uid;
	}

	public void setUid(Long uid) {
		this.uid = uid;
	}

	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getUpdator() {
		return updator;
	}

	public void setUpdator(String updator) {
		this.updator = updator;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public Long getRoleId() {
		return roleId;
	}

	public void setRoleId(Long roleId) {
		this.roleId = roleId;
	}

	public Long getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Long companyId) {
		this.companyId = companyId;
	}

	public Long getSalesCode() {
		return salesCode;
	}

	public void setSalesCode(Long salesCode) {
		this.salesCode = salesCode;
	}

	public Short getUserType() {
		return userType;
	}

	public void setUserType(Short userType) {
		this.userType = userType;
	}

    public Date getPasswordExpiredTime() {
        return passwordExpiredTime;
    }

    public void setPasswordExpiredTime(Date passwordExpiredTime) {
        this.passwordExpiredTime = passwordExpiredTime;
    }

    public Date getPasswordUpdateTime() {
        return passwordUpdateTime;
    }

    public void setPasswordUpdateTime(Date passwordUpdateTime) {
        this.passwordUpdateTime = passwordUpdateTime;
    }

    public Short getCanDecrypt() {
        return canDecrypt;
    }

    public void setCanDecrypt(Short canDecrypt) {
        this.canDecrypt = canDecrypt;
    }
    
    public String getRoleIdList() {
        return roleIdList;
    }

    public void setRoleIdList(String roleIdList) {
        this.roleIdList = roleIdList;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

	public String getEnMobile() {
		return enMobile;
	}

	public void setEnMobile(String enMobile) {
		this.enMobile = enMobile;
	}

	public String getEnEmail() {
		return enEmail;
	}

	public void setEnEmail(String enEmail) {
		this.enEmail = enEmail;
	}

    public String getPieceLink() {
        return pieceLink;
    }

    public void setPieceLink(String pieceLink) {
        this.pieceLink = pieceLink;
    }

    public Short getPicecLinkStatus() {
        return picecLinkStatus;
    }

    public void setPicecLinkStatus(Short picecLinkStatus) {
        this.picecLinkStatus = picecLinkStatus;
    }

	public String getHashMobile() {
		return hashMobile;
	}

	public void setHashMobile(String hashMobile) {
		this.hashMobile = hashMobile;
	}

	public String getHashEmail() {
		return hashEmail;
	}

	public void setHashEmail(String hashEmail) {
		this.hashEmail = hashEmail;
	}
}