package com.epaylinks.efps.pas.provisions.epcc.util;

import com.epaylinks.efps.common.business.CommonResponse;

/**
 * @Author: Liuq
 * @Date: 2018/9/27 10:37
 * @Description: 额度查询返回前端response
 */
public class QuotaQueryReponse extends CommonResponse {

    /**
     * 账户余额
     */
    private String acctBlcAmt;
    /**
     * 可用额度
     */
    private String avlBlnAmt;
    /**
     * 映射额度
     */
    private String ldRsrvAmt;


    //虚拟记账额度
    private String virtualAmt ;

    public String getVirtualAmt() {
        return virtualAmt;
    }

    public void setVirtualAmt(String virtualAmt) {
        this.virtualAmt = virtualAmt;
    }

    public String getAcctBlcAmt() {
        return acctBlcAmt;
    }

    public void setAcctBlcAmt(String acctBlcAmt) {
        this.acctBlcAmt = acctBlcAmt;
    }

    public String getAvlBlnAmt() {
        return avlBlnAmt;
    }

    public void setAvlBlnAmt(String avlBlnAmt) {
        this.avlBlnAmt = avlBlnAmt;
    }

    public String getLdRsrvAmt() {
        return ldRsrvAmt;
    }

    public void setLdRsrvAmt(String ldRsrvAmt) {
        this.ldRsrvAmt = ldRsrvAmt;
    }
}
