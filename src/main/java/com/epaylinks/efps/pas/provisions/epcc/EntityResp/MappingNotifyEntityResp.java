package com.epaylinks.efps.pas.provisions.epcc.EntityResp;

import java.math.BigDecimal;
import java.util.Date;

public class MappingNotifyEntityResp {
    /**
     */
    private Long provisionsNotifyId;

    /**
     * 借贷标识 1:借 2:贷
     */
    private String dcFlag;

    /**
     * 通知码，1001:资金清算导致映射额度变动，2001:可用额度低于阈值
     */
    private String ntcRsn;

    /**
     * 通知原因说明
     */
    private String ntcRsnDesc;

    /**
     * 账户余额
     */
    private BigDecimal acctBlnAmt;

    /**
     * 映射额度
     */
    private BigDecimal ldRsrvAmt;

    /**
     * 可用额度
     */
    private BigDecimal avlBlnAmt;

    /**
     * 映射额度变动金额
     */
    private BigDecimal ldRsrvChgAmt;

    /**
     * 通知时间
     */
    private Date notifyTime;

    /**
     * 映射额度更新时间
     */
    private Date updateTime;

    /**
     * 可用额度更新时间
     */
    private Date mappedUpdateTime;

    public Long getProvisionsNotifyId() {
        return provisionsNotifyId;
    }

    public void setProvisionsNotifyId(Long provisionsNotifyId) {
        this.provisionsNotifyId = provisionsNotifyId;
    }

    public String getDcFlag() {
        return dcFlag;
    }

    public void setDcFlag(String dcFlag) {
        this.dcFlag = dcFlag;
    }

    public String getNtcRsn() {
        return ntcRsn;
    }

    public void setNtcRsn(String ntcRsn) {
        this.ntcRsn = ntcRsn;
    }

    public String getNtcRsnDesc() {
        return ntcRsnDesc;
    }

    public void setNtcRsnDesc(String ntcRsnDesc) {
        this.ntcRsnDesc = ntcRsnDesc;
    }

    public BigDecimal getAcctBlnAmt() {
        return acctBlnAmt;
    }

    public void setAcctBlnAmt(BigDecimal acctBlnAmt) {
        this.acctBlnAmt = acctBlnAmt;
    }

    public BigDecimal getLdRsrvAmt() {
        return ldRsrvAmt;
    }

    public void setLdRsrvAmt(BigDecimal ldRsrvAmt) {
        this.ldRsrvAmt = ldRsrvAmt;
    }

    public BigDecimal getAvlBlnAmt() {
        return avlBlnAmt;
    }

    public void setAvlBlnAmt(BigDecimal avlBlnAmt) {
        this.avlBlnAmt = avlBlnAmt;
    }

    public BigDecimal getLdRsrvChgAmt() {
        return ldRsrvChgAmt;
    }

    public void setLdRsrvChgAmt(BigDecimal ldRsrvChgAmt) {
        this.ldRsrvChgAmt = ldRsrvChgAmt;
    }

    public Date getNotifyTime() {
        return notifyTime;
    }

    public void setNotifyTime(Date notifyTime) {
        this.notifyTime = notifyTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getMappedUpdateTime() {
        return mappedUpdateTime;
    }

    public void setMappedUpdateTime(Date mappedUpdateTime) {
        this.mappedUpdateTime = mappedUpdateTime;
    }
}