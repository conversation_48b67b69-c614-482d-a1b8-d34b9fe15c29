package com.epaylinks.efps.pas.provisions.epcc.util;

/**
 * Created by Liu<PERSON> on 2018/9/26.
 * @Description: 额度映射response
 */
public class QuotaRecordResponse {

      private String outorderno;

      private String trxctgy;

      private String trxid;

      private String trxdttm;

      private String trxamt;

      private String instgid;

      private String sysrtncd;

      private String sysrtndesc;

      private String sysrtntm;

      private String bizstscd;

      private String bizstsdesc;

      private String oritrxid;

      private String oritrxctgy;

      //原调整金额
      private String oritrxamt;

      private String trxstatus;

      //映射额度
      private String ldrsrvamt;

      private String ldrsrvupdt;

      //可用额度
      private String avlblnamt;

      private String avlblnupdt;

    public QuotaRecordResponse() {
   		super();
   	}
   	public QuotaRecordResponse(String sysRtnCd, String sysRtnDesc, String sysRtnTm, String bizStsCd, String bizStsDesc,
   			String oriTrxid, String oriTrxctgy, String oriTrxamt, String trxStatus, String ldrsrvAmt, String ldrsrvUpdt,
   			String avlblnAmt, String avlblnUpdt) {
   		super();
        sysrtncd = sysRtnCd;
        sysrtndesc = sysRtnDesc;
        sysrtntm = sysRtnTm;
        bizstscd = bizStsCd;
        bizstsdesc = bizStsDesc;
        oritrxid = oriTrxid;
        oritrxctgy = oriTrxctgy;
        oritrxamt = oriTrxamt;
        trxstatus = trxStatus;
        ldrsrvamt = ldrsrvAmt;
        ldrsrvupdt = ldrsrvUpdt;
        avlblnamt = avlblnAmt;
        avlblnupdt = avlblnUpdt;
   	}

      public String getOutorderno() {
          return outorderno;
      }

      public void setOutorderno(String outorderno) {
          this.outorderno = outorderno == null ? null : outorderno.trim();
      }

      public String getTrxctgy() {
          return trxctgy;
      }

      public void setTrxctgy(String trxctgy) {
          this.trxctgy = trxctgy == null ? null : trxctgy.trim();
      }

      public String getTrxid() {
          return trxid;
      }

      public void setTrxid(String trxid) {
          this.trxid = trxid == null ? null : trxid.trim();
      }

      public String getTrxdttm() {
          return trxdttm;
      }

      public void setTrxdttm(String trxdttm) {
          this.trxdttm = trxdttm == null ? null : trxdttm.trim();
      }

      public String getTrxamt() {
          return trxamt;
      }

      public void setTrxamt(String trxamt) {
          this.trxamt = trxamt == null ? null : trxamt.trim();
      }

      public String getInstgid() {
          return instgid;
      }

      public void setInstgid(String instgid) {
          this.instgid = instgid == null ? null : instgid.trim();
      }

      public String getSysrtncd() {
          return sysrtncd;
      }

      public void setSysrtncd(String sysrtncd) {
          this.sysrtncd = sysrtncd == null ? null : sysrtncd.trim();
      }

      public String getSysrtndesc() {
          return sysrtndesc;
      }

      public void setSysrtndesc(String sysrtndesc) {
          this.sysrtndesc = sysrtndesc == null ? null : sysrtndesc.trim();
      }

      public String getSysrtntm() {
          return sysrtntm;
      }

      public void setSysrtntm(String sysrtntm) {
          this.sysrtntm = sysrtntm == null ? null : sysrtntm.trim();
      }

      public String getBizstscd() {
          return bizstscd;
      }

      public void setBizstscd(String bizstscd) {
          this.bizstscd = bizstscd == null ? null : bizstscd.trim();
      }

      public String getBizstsdesc() {
          return bizstsdesc;
      }

      public void setBizstsdesc(String bizstsdesc) {
          this.bizstsdesc = bizstsdesc == null ? null : bizstsdesc.trim();
      }

      public String getOritrxid() {
          return oritrxid;
      }

      public void setOritrxid(String oritrxid) {
          this.oritrxid = oritrxid == null ? null : oritrxid.trim();
      }

      public String getOritrxctgy() {
          return oritrxctgy;
      }

      public void setOritrxctgy(String oritrxctgy) {
          this.oritrxctgy = oritrxctgy == null ? null : oritrxctgy.trim();
      }

      public String getOritrxamt() {
          return oritrxamt;
      }

      public void setOritrxamt(String oritrxamt) {
          this.oritrxamt = oritrxamt == null ? null : oritrxamt.trim();
      }

      public String getTrxstatus() {
          return trxstatus;
      }

      public void setTrxstatus(String trxstatus) {
          this.trxstatus = trxstatus == null ? null : trxstatus.trim();
      }

      public String getLdrsrvamt() {
          return ldrsrvamt;
      }

      public void setLdrsrvamt(String ldrsrvamt) {
          this.ldrsrvamt = ldrsrvamt == null ? null : ldrsrvamt.trim();
      }

      public String getLdrsrvupdt() {
          return ldrsrvupdt;
      }

      public void setLdrsrvupdt(String ldrsrvupdt) {
          this.ldrsrvupdt = ldrsrvupdt == null ? null : ldrsrvupdt.trim();
      }

      public String getAvlblnamt() {
          return avlblnamt;
      }

      public void setAvlblnamt(String avlblnamt) {
          this.avlblnamt = avlblnamt == null ? null : avlblnamt.trim();
      }

      public String getAvlblnupdt() {
          return avlblnupdt;
      }

      public void setAvlblnupdt(String avlblnupdt) {
          this.avlblnupdt = avlblnupdt == null ? null : avlblnupdt.trim();
      }

    }
