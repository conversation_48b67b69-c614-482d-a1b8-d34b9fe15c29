package com.epaylinks.efps.pas.provisions.epcc.domain;

import java.sql.Clob;
import java.util.Date;

public class AccountInfoEntiy {
    /**
     * 自增id
     */
    private Long accountId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 报文内容xml格式存储
     */
    private String contents;

    /**
     * 地址url
     */
    private String url;

    /**
     * 本对账文件的对账日期
     */
    private Date accountDate;

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getContents() {
        return contents;
    }

    public void setContents(String contents) {
        this.contents = contents;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Date getAccountDate() {
        return accountDate;
    }

    public void setAccountDate(Date accountDate) {
        this.accountDate = accountDate;
    }
}