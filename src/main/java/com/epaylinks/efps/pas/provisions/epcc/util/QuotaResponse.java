package com.epaylinks.efps.pas.provisions.epcc.util;

/**
 * Created by <PERSON><PERSON> on 2018/9/26.
 * @Description: 额度映射查询response
 */
public class QuotaResponse {
    private String SysRtnCd;
    private String SysRtnDesc;
    private String SysRtnTm;
    private String BizStsCd;
    private String BizStsDesc;
    private String InstgId;
    private String AcctBlcChkDt;
    private String AcctBlcAmt;
    private String LdRsrvUpDt;
    private String AvlBlnAmt;
    private String AvlBlnUpDt;
    private String LdRsrvAmt;

    //虚拟记账额度
    private String virtualAmt ;

    public String getVirtualAmt() {
        return virtualAmt;
    }

    public void setVirtualAmt(String virtualAmt) {
        this.virtualAmt = virtualAmt;
    }

    public QuotaResponse() {
        super();
    }

    public QuotaResponse(String sysRtnCd, String sysRtnDesc, String sysRtnTm, String bizStsCd, String bizStsDesc,
                         String instgId, String acctBlcChkDt, String acctBlcAmt, String ldRsrvUpDt, String avlBlnAmt, String avlBlnUpDt,String ldRsrvAmt) {
        super();
        SysRtnCd = sysRtnCd;
        SysRtnDesc = sysRtnDesc;
        SysRtnTm = sysRtnTm;
        BizStsCd = bizStsCd;
        BizStsDesc = bizStsDesc;
        InstgId = instgId;
        AcctBlcChkDt = acctBlcChkDt;
        AcctBlcAmt = acctBlcAmt;
        LdRsrvUpDt = ldRsrvUpDt;
        AvlBlnAmt = avlBlnAmt;
        AvlBlnUpDt = avlBlnUpDt;
        LdRsrvAmt = ldRsrvAmt;
    }

    public String getSysRtnCd() {
        return SysRtnCd;
    }

    public void setSysRtnCd(String sysRtnCd) {
        SysRtnCd = sysRtnCd;
    }

    public String getSysRtnDesc() {
        return SysRtnDesc;
    }

    public void setSysRtnDesc(String sysRtnDesc) {
        SysRtnDesc = sysRtnDesc;
    }

    public String getSysRtnTm() {
        return SysRtnTm;
    }

    public void setSysRtnTm(String sysRtnTm) {
        SysRtnTm = sysRtnTm;
    }

    public String getBizStsCd() {
        return BizStsCd;
    }

    public void setBizStsCd(String bizStsCd) {
        BizStsCd = bizStsCd;
    }

    public String getBizStsDesc() {
        return BizStsDesc;
    }

    public void setBizStsDesc(String bizStsDesc) {
        BizStsDesc = bizStsDesc;
    }

    public String getInstgId() {
        return InstgId;
    }

    public void setInstgId(String instgId) {
        InstgId = instgId;
    }

    public String getAcctBlcChkDt() {
        return AcctBlcChkDt;
    }

    public void setAcctBlcChkDt(String acctBlcChkDt) {
        AcctBlcChkDt = acctBlcChkDt;
    }

    public String getAcctBlcAmt() {
        return AcctBlcAmt;
    }

    public void setAcctBlcAmt(String acctBlcAmt) {
        AcctBlcAmt = acctBlcAmt;
    }

    public String getLdRsrvUpDt() {
        return LdRsrvUpDt;
    }

    public void setLdRsrvUpDt(String ldRsrvUpDt) {
        LdRsrvUpDt = ldRsrvUpDt;
    }

    public String getAvlBlnAmt() {
        return AvlBlnAmt;
    }

    public void setAvlBlnAmt(String avlBlnAmt) {
        AvlBlnAmt = avlBlnAmt;
    }

    public String getAvlBlnUpDt() {
        return AvlBlnUpDt;
    }

    public void setAvlBlnUpDt(String avlBlnUpDt) {
        AvlBlnUpDt = avlBlnUpDt;
    }

    public String getLdRsrvAmt() {
        return LdRsrvAmt;
    }

    public void setLdRsrvAmt(String ldRsrvAmt) {
        LdRsrvAmt = ldRsrvAmt;
    }
}
