package com.epaylinks.efps.pas.provisions.epcc.util;

import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.pas.provisions.epcc.Enum.ProvisionsErrorCode;
import com.epaylinks.efps.pas.provisions.epcc.dao.ProvionsChangedMapper;
import net.sf.json.JSONObject;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.text.Format;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @Author: Liuq
 * @Date: 2018/9/27 11:44
 */
public class HttpUtils {

    /**
     * 接收异步通知参数，封装成Map
     *
     * @param request
     * @param
     * @return
     */
    public static JSONObject getReqData(HttpServletRequest request) throws IOException {
        String body = "";
        ServletInputStream inputStream = null;
        BufferedReader br = null;
        try {
            inputStream = request.getInputStream();
            br = new BufferedReader(new InputStreamReader(inputStream,"UTF-8"));
            while (true) {
                String info = br.readLine();
                if (info == null) {
                    break;
                }
                if (body == null || "".equals(body)) {
                    body = info;
                } else {
                    body += info;
                }
            }
        } catch (UnsupportedEncodingException e) {
            //logger.error("接收epcc异步通知UnsupportedEncodingException异常：", e);
            throw new UnsupportedEncodingException();
        } catch (IOException e) {
            //logger.error("接收epcc异步通知IOException异常：", e);
            e.printStackTrace();
        }finally{
            if(inputStream!=null){
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if(br!=null){
                try {
                    br.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        JSONObject fromObject = JSONObject.fromObject(body);
        return fromObject;
    }
    //把jsonOBject对象转为MappedNotifyResponse对象
    @Logable(businessTag = "couvertToResponse")
    public static MappedNotifyResponse couvertToResponse(JSONObject jsonObject)  {
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        MappedNotifyResponse mappedNotifyResponse = new MappedNotifyResponse();
        if(jsonObject != null){
            String LdRsrvChgAmt = "";
            if(jsonObject.get("LdRsrvChgAmt") != null && !jsonObject.get("LdRsrvChgAmt").equals("")){
                LdRsrvChgAmt = ((String) jsonObject.get("LdRsrvChgAmt")).substring(3);
            }
            String AcctBlcAmt = "";
            if(jsonObject.get("AcctBlcAmt") != null && !jsonObject.get("AcctBlcAmt").equals("")){
                AcctBlcAmt = ((String) jsonObject.get("AcctBlcAmt")).substring(3);
            }
            String LdRsrvAmt = "";
            if(jsonObject.get("LdRsrvAmt") != null && !jsonObject.get("LdRsrvAmt").equals("")){
                LdRsrvAmt = ((String) jsonObject.get("LdRsrvAmt")).substring(3);
            }
            String AvlBlnAmt = "";
            if(jsonObject.get("AvlBlnAmt") != null && !jsonObject.get("AvlBlnAmt").equals("")){
                AvlBlnAmt = ((String) jsonObject.get("AvlBlnAmt")).substring(3);
            }
            mappedNotifyResponse.setAcctBlnAmt(AcctBlcAmt);
            mappedNotifyResponse.setLdRsrvChgAmt(LdRsrvChgAmt);
            mappedNotifyResponse.setLdRsrvAmt(LdRsrvAmt);
            mappedNotifyResponse.setAvlBlnAmt(AvlBlnAmt);
            Date AvlBlnUpDt = new Date();
            Date NtcDtTm = new Date();
            Date LdRsrvUpDt = new Date();
            if(jsonObject.get("AvlBlnUpDt") != null && !jsonObject.get("AvlBlnUpDt").equals("")){
                String tmpDate = (String)jsonObject.get("AvlBlnUpDt");
                tmpDate = tmpDate.replace("T"," ");
                try{
                    ParsePosition pos = new ParsePosition(0);
                    AvlBlnUpDt = sf.parse(tmpDate, pos);
                }catch (Exception e){
                    //抛出异常
                    e.printStackTrace();
                }
            }
            if(jsonObject.get("NtcDtTm") != null && !jsonObject.get("AvlBlnUpDt").equals("")){
                String tmpDate = (String)jsonObject.get("NtcDtTm");
                tmpDate = tmpDate.replace("T"," ");
                try{
                    ParsePosition pos = new ParsePosition(0);
                    NtcDtTm = sf.parse(tmpDate, pos);
                }catch (Exception e){
                    //抛出异常
                    e.printStackTrace();
                }
            }
            if(jsonObject.get("LdRsrvUpDt") != null && !jsonObject.get("LdRsrvUpDt").equals("")){
                String tmpDate = (String)jsonObject.get("LdRsrvUpDt");
                tmpDate = tmpDate.replace("T"," ");
                try{
                    ParsePosition pos = new ParsePosition(0);
                    LdRsrvUpDt = sf.parse(tmpDate, pos);
                }catch (Exception e){
                    //抛出异常
                    e.printStackTrace();
                }
            }
            mappedNotifyResponse.setAvlBlnUpDt(AvlBlnUpDt);
            Integer dcFlag = null;
            if(jsonObject.get("DCFlg") !=null && !jsonObject.get("DCFlg").equals("")){
                dcFlag = Integer.valueOf((String)( jsonObject.get("DCFlg")));
            }
            mappedNotifyResponse.setDcFlag(dcFlag);
            mappedNotifyResponse.setInstgId((String) (jsonObject.get("InstgId")==null?"":jsonObject.get("InstgId")));
            mappedNotifyResponse.setNtcRsn((String) (jsonObject.get("NtcRsn")==null?"":jsonObject.get("NtcRsn")));
            mappedNotifyResponse.setNtcRsnDesc((String) (jsonObject.get("NtcRsnDesc")==null?"":jsonObject.get("NtcRsnDesc")));
            mappedNotifyResponse.setNtcDtTm(NtcDtTm);
            mappedNotifyResponse.setLdRsrvUpDt(LdRsrvUpDt);
        }else{
            //抛出异常
            throw new AppException(ProvisionsErrorCode.EPCC_BLANK_ERROR.code);
        }
        return mappedNotifyResponse;
    }
}
