package com.epaylinks.efps.pas.provisions.epcc.controller;

import com.epaylinks.efps.common.datadownload.annotation.DownloadAble;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.pas.provisions.epcc.EntityResp.AccountInfoEntityResp;
import com.epaylinks.efps.pas.provisions.epcc.EntityResp.BatchInfExportDto;
import com.epaylinks.efps.pas.provisions.epcc.EntityResp.SettInfoExportDto;
import com.epaylinks.efps.pas.provisions.epcc.EntityResp.SubItemInfoExportDto;
import com.epaylinks.efps.pas.provisions.epcc.dao.AccountInfoEntiyMapper;
import com.epaylinks.efps.pas.provisions.epcc.domain.AccountInfoEntiy;
import com.epaylinks.efps.pas.provisions.epcc.service.AccountInfoService;
import com.epaylinks.efps.pas.provisions.epcc.service.impl.AccountInfoServiceImpl;
import com.epaylinks.efps.pas.provisions.epcc.util.BackendUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.io.IOUtils;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * @Author: Liuq
 * @Date: 2018/9/27 15:54
 * @para: 网联对账单文件控制器api
 */
@RestController
@RequestMapping("/ProvisionsAccountInfo")
@Api(value = "ProviAccountInfoController", description = "网联对账文件接口")
public class ProviAccountInfoController {

    @Autowired
    private AccountInfoService accountInfoService;
    @Autowired
    private AccountInfoEntiyMapper accountInfoEntiyMapper;
    @Autowired
    private AccountInfoServiceImpl accountInfoServiceImpl;

    @RequestMapping(value = "/query", method = RequestMethod.GET)
    @Logable(businessTag = "query")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "网联对账文件查询", notes = "网联对账文件查询", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "accountDate", value = "对账日期（yyyy-MM-dd）", required = true, dataType = "String", length = 100, paramType = "query")})
    public AccountInfoEntityResp queryAccountInfo(@RequestParam String accountDate) {
        AccountInfoEntityResp accountInfoEntityResp = null;
        BackendUtils backendUtils = new BackendUtils();
        //本地数据库若有，则从数据库获取；否则从ftp上获取
        accountInfoEntityResp = accountInfoService.getLocalAccountInf(accountDate);
        if(accountInfoEntityResp == null){
            try{
                //连接ftp获取对账单压缩文件,保存至本地数据库
                accountInfoService.saveAccountInfo(accountDate);
                //查询本地数据库获取对账文件
                accountInfoEntityResp = accountInfoService.getLocalAccountInf(accountDate);
            }catch (Exception e){
                //抛出异常
            }
        }
        return accountInfoEntityResp;
    }

    @RequestMapping(value = "/export")
    @Logable(businessTag = "accountInfoExport")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "网联对账文件导出", notes = "网联对账文件导出", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "accountDate", value = "对账日期（yyyy-MM-dd）", required = true, dataType = "String", length = 100, paramType = "query"),
            @ApiImplicitParam(name = "download", value = "是否下载:true/false", required = true, dataType = "boolean", length = 100, paramType = "query"),
            @ApiImplicitParam(name = "fileName", value = "例:AccountInfo", required = true, dataType = "String", length = 100, paramType = "query"),
            @ApiImplicitParam(name = "type", value = "格式:csv", required = true, dataType = "String", length = 100, paramType = "query")})
    public PageResult<SettInfoExportDto> exportAccountInfo(@RequestParam String accountDate,
                                                           @RequestParam(value = "download", required = true) boolean download,
                                                           @RequestParam(value = "fileName", required = true) String fileName,
                                                           @RequestParam(value = "type", required = true) String type,
                                                           HttpServletRequest request, HttpServletResponse response) {

        PageResult<SettInfoExportDto> pageResult = new PageResult<SettInfoExportDto>();
        AccountInfoEntityResp accountInfoEntityResp = accountInfoService.getLocalAccountInf(accountDate);
        //1.账务日期
        //2.结算总笔数....
        //3.结算场次列表
        List<String> settListTitles = new ArrayList<>();
        settListTitles.add("报文标识号");
        settListTitles.add("场次借贷标识");
        settListTitles.add("场次金额");
        //4.批次列表
        List<String> batchInfListTitles = new ArrayList<>();
        batchInfListTitles.add("报文标识号");
        batchInfListTitles.add("批次号");
        batchInfListTitles.add("批次借贷标识");
        batchInfListTitles.add("批次金额");
        //5.分项列表
        List<String> sttlInfListTitles = new ArrayList<>();
        sttlInfListTitles.add("批次号");
        sttlInfListTitles.add("业务类型");
        sttlInfListTitles.add("银行金额机构标识");
        sttlInfListTitles.add("账户类型");
        sttlInfListTitles.add("分项借方发生额");
        sttlInfListTitles.add("分项借方发生笔数");
        sttlInfListTitles.add("分项贷方发生额");
        sttlInfListTitles.add("分项贷方发生笔数");

        //账务日期...
        Map<String, String> statisticsData = new HashMap<>();
        String settCount = accountInfoEntityResp.getSttlCntNb().toString();
        String debitAmount = accountInfoEntityResp.getDebitCntAmt().toString();
        String creditAmount = accountInfoEntityResp.getCreditCntAmt().toString();
        statisticsData.put("财务日期:", accountDate.replace("//-","///"));
        statisticsData.put("结算总笔数", settCount);
        statisticsData.put("借方金额", debitAmount);
        statisticsData.put("贷方金额", creditAmount);
        //结算场次列表数据list
        List<SettInfoExportDto> settInfoExportDtoList = new ArrayList<SettInfoExportDto>();
        //批次列表数据list
        List<BatchInfExportDto> batchInfExportDtoList = new ArrayList<BatchInfExportDto>();
        //分项列表数据list
        List<SubItemInfoExportDto> subItemInfoExportDtoList = new ArrayList<SubItemInfoExportDto>();
        if(accountInfoEntityResp.getSttlList() != null){
            //结算场次列表数据
            for(int i=0; i<accountInfoEntityResp.getSttlList().size(); i++){
                SettInfoExportDto settInfoExportDto = new SettInfoExportDto();
                settInfoExportDto.setSttlAmt(accountInfoEntityResp.getSttlList().get(i).getSttlAmt());
                settInfoExportDto.setSttlDCFlg(accountInfoServiceImpl.DCFlag(accountInfoEntityResp.getSttlList().get(i).getSttlDCFlg()));
                settInfoExportDto.setSttlReptFlg(accountInfoEntityResp.getSttlList().get(i).getSttlReptFlg());
                settInfoExportDtoList.add(settInfoExportDto);
                //批次列表数据
                if(accountInfoEntityResp.getSttlList().get(i).getBatchList() != null){
                    for(int j= 0; j<accountInfoEntityResp.getSttlList().get(i).getBatchList().size(); j++) {
                        BatchInfExportDto batchInfExportDto = new BatchInfExportDto();
                        batchInfExportDto.setSttlReptFlg(accountInfoEntityResp.getSttlList().get(i).getSttlReptFlg());
                        batchInfExportDto.setBatchDCFlg(accountInfoEntityResp.getSttlList().get(i).getBatchList().get(j).getBatchDCFlg());
                        batchInfExportDto.setBatchId(accountInfoEntityResp.getSttlList().get(i).getBatchList().get(j).getBatchId());
                        batchInfExportDto.setBatchNetAmt(accountInfoEntityResp.getSttlList().get(i).getBatchList().get(j).getBatchNetAmt());
                        batchInfExportDtoList.add(batchInfExportDto);
                        //分项列表数据
                        if(accountInfoEntityResp.getSttlList().get(i).getBatchList().get(j).getSubItemList() != null){
                            for(int f=0; f< accountInfoEntityResp.getSttlList().get(i).getBatchList().get(j).getSubItemList().size(); f++){
                                SubItemInfoExportDto subItemInfoExportDto = new SubItemInfoExportDto();
                                subItemInfoExportDto.setBatchId(accountInfoEntityResp.getSttlList().get(i).getBatchList().get(j).getBatchId());
                                String subStr = accountInfoEntityResp.getSttlList().get(i).getBatchList().get(j).getSubItemList().get(f).getSubItemInf();
                                subStr = subStr.replace("CNY","");
                                String[] subArray = {};
                                subArray = subStr.split("\\|");
                                subItemInfoExportDto.setBusinessType(accountInfoServiceImpl.busiType(subArray[0]));
                                subItemInfoExportDto.setBankOrgFlag(subArray[1]);
                                subItemInfoExportDto.setAccountType(accountInfoServiceImpl.accountType(subArray[2]));
                                subItemInfoExportDto.setDebitSplitAmount(subArray[3]);
                                subItemInfoExportDto.setDebitSplitCount(Integer.valueOf(subArray[4]));
                                subItemInfoExportDto.setCreditSplitAmount(subArray[5]);
                                subItemInfoExportDto.setCreditSplitCount(Integer.valueOf(subArray[6]));
                                subItemInfoExportDtoList.add(subItemInfoExportDto);
                            }
                        }
                    }
                }
            }
        }
        settListTitles.addAll(batchInfListTitles);
        settListTitles.addAll(sttlInfListTitles);
        CSVPrinter csvPrinter = accountInfoService.downloadCsv(request, response, settListTitles);
        pageResult.setTitles(settListTitles);
        pageResult.setResult(Constants.detailReturnCode.RETURN_SUCCESS.code);
        pageResult.setRows(settInfoExportDtoList);
        accountInfoService.printlnCsv(csvPrinter,statisticsData,settInfoExportDtoList,batchInfExportDtoList,
                subItemInfoExportDtoList,settListTitles,batchInfListTitles,sttlInfListTitles);
        IOUtils.closeQuietly(csvPrinter);
        return null;
    }
}