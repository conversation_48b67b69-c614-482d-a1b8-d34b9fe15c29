package com.epaylinks.efps.pas.provisions.epcc.Enum;

import com.epaylinks.efps.common.systemcode.SystemCode;

@SystemCode
public enum ProvisionsErrorCode {
	//网联备付金错误码
	EPCC_CONNECT_ERROR("440001" , "连接epcc系统异常，请检查."),
	EPCC_BLANK_ERROR("440002" , "epcc系统接口返回空，请检查."),
	DB_CONNECT_ERROR("440003" , "连接数据库异常，请检查.");
    public final String code;
    public final String message;
    ProvisionsErrorCode(String code, String message) {
        this.code = code;
        this.message = message;
    }
	public String getCode() {
		return code;
	}

	public String getMessage() {
		return message;
	}

	public static String getMessageByCode(String code) {
		for(ProvisionsErrorCode v: ProvisionsErrorCode.values())
		{
			if(v.code.equalsIgnoreCase(code))
				return v.message;
		}
		return null;
	}
}
