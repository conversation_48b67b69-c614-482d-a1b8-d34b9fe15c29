package com.epaylinks.efps.pas.provisions.epcc.util;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.util.Map;
import java.util.Properties;
import java.util.Vector;

import org.apache.log4j.Logger;

import com.jcraft.jsch.Channel;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import com.jcraft.jsch.SftpException;

/**
 * @Comments : 通过SFTP，读取数据 
 * @Version : 1.0.0 
 */
public class SFTPChannel {

    Session session = null;
    Channel channel = null;

    private static final Logger LOG = Logger.getLogger(SFTPChannel.class);

    /**
     * @MethodName : getChannel 
     * @Description : 得到Channel 
     * @param sftpDetails
     * @param timeout 超时时间
     * @return  https://blog.csdn.net/sanluo11/article/details/80619021
     * @throws JSchException
     */
    public ChannelSftp getChannel(Map<String, String> sftpDetails, int timeout)
            throws JSchException {

        String ftpHost = sftpDetails.get(SFTPConstants.SFTP_REQ_HOST);
        String port = sftpDetails.get(SFTPConstants.SFTP_REQ_PORT);
        String ftpUserName = sftpDetails.get(SFTPConstants.SFTP_REQ_USERNAME);
        String ftpPassword = sftpDetails.get(SFTPConstants.SFTP_REQ_PASSWORD);
        String sftpPassiveMode = sftpDetails.get(SFTPConstants.SFTP_PASSIVE_MODE);
        String ftpFilePath = sftpDetails.get(SFTPConstants.SFTP_FILE_PATH);

        JSch jsch = new JSch(); // 创建JSch对象  
        session = jsch.getSession(ftpUserName, ftpHost, Integer.parseInt(port)); // 根据用户名，主机ip，端口获取一个Session对象
        LOG.debug("Session created.");
        if (ftpPassword != null) {
            session.setPassword(ftpPassword); // 设置密码
        }
        Properties config = new Properties();
        config.put("StrictHostKeyChecking", "no");
        session.setConfig(config); // 为Session对象设置properties
        session.setTimeout(timeout); // 设置timeout时间
        session.connect(); // 通过Session建立链接
        LOG.debug("Session connected.");

        LOG.debug("Opening Channel.");
        channel = (Channel) session.openChannel("sftp"); // 打开SFTP通道
        channel.connect(); // 建立SFTP通道的连接
        LOG.debug("Connected successfully to ftpHost = " + ftpHost + ",as ftpUserName = "
                + ftpUserName + ", returning: " + channel);
        return (ChannelSftp) channel;
    }

    /**
     * @MethodName : closeChannel 
     * @Description : 关闭channel 
     * @throws Exception
     */
    public void closeChannel() throws Exception {
        if (channel != null) {
            channel.disconnect();
        }
        if (session != null) {
            session.disconnect();
        }
    }

    /**
     * @MethodName : getSFTPChannel 
     * @Description : 得到SFTPChannel 类实例对象 
     * @return
     */
    public SFTPChannel getSFTPChannel() {
        return new SFTPChannel();
    }

    /**
     * @MethodName  : download 
     * @Description : 下载文件 
     * @param directory  下载目录 
     * @param downloadFile 下载的文件 
     * @param saveFile 存在本地的路径 
     * @param sftp
     */
    public void download(String directory, String downloadFile, String saveFile, ChannelSftp sftp) {
        try {
            sftp.cd(directory);
            File file = new File(saveFile);
            sftp.get(downloadFile, new FileOutputStream(file));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * @MethodName  : upload 
     * @Description : 上传文件 
     * @param directory  上传的目录 
     * @param uploadFile  要上传的文件 
     * @param sftp
     */
    public void upload(String directory, String uploadFile, ChannelSftp sftp) {
        try {
            sftp.cd(directory);
            File file = new File(uploadFile);
            sftp.put(new FileInputStream(file), file.getName());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * @MethodName  : delete 
     * @Description :  删除文件 
     * @param directory 要删除文件所在目录 
     * @param deleteFile 要删除的文件 
     * @param sftp
     */
    public void delete(String directory, String deleteFile, ChannelSftp sftp) {
        try {
            sftp.cd(directory);
            sftp.rm(deleteFile);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * @MethodName  : listFiles 
     * @Description : 列出目录下的文件 
     * @param directory  要列出的目录 
     * @param sftp sftp 
     * @return
     * @throws SftpException
     */
    public Vector listFiles(String directory, ChannelSftp sftp) throws SftpException {
        return sftp.ls(directory);
    }

}  
