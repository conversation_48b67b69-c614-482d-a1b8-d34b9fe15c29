package com.epaylinks.efps.pas.provisions.epcc.client;

import com.epaylinks.efps.pas.provisions.epcc.util.QuotaRecordResponse;
import com.epaylinks.efps.pas.provisions.epcc.util.QuotaResponse;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @Author: Liuq
 * @Date: 2018/9/25 10:07
 * @Description: 网联前置接口
 */
@FeignClient("EPCC")
public interface EpccService {

    //额度映射（解映射）接口
    @RequestMapping(value = "/rcmp/quotaAdjust", method = RequestMethod.POST)
    QuotaRecordResponse quotaAdjust(@RequestParam("TrxCtgy") String TrxCtgy,
                               @RequestParam("TrxAmt") String TrxAmt);

    //额度查询接口
    @RequestMapping(value = "/rcmp/queryQuota", method = RequestMethod.GET)
    QuotaResponse queryQuota(@RequestParam("InqDtTm") String InqDtTm);

}
