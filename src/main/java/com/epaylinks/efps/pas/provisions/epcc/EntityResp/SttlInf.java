package com.epaylinks.efps.pas.provisions.epcc.EntityResp;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: Liuq
 * @Date: 2018/9/25 11:11
 * @Description: 结算信息实体类
 */
public class SttlInf {

    /**
     * 报文标识号
     */
    private String SttlReptFlg;
    /**
     * 场次借贷标识
     */
    private Integer SttlDCFlg;
    /**
     * 场次金额
     */
    private BigDecimal SttlAmt;
    /**
     * 批次列表
     */
    private List<BatchInf> BatchList;

    public String getSttlReptFlg() {
        return SttlReptFlg;
    }

    public void setSttlReptFlg(String sttlReptFlg) {
        SttlReptFlg = sttlReptFlg;
    }

    public Integer getSttlDCFlg() {
        return SttlDCFlg;
    }

    public void setSttlDCFlg(Integer sttlDCFlg) {
        SttlDCFlg = sttlDCFlg;
    }

    public BigDecimal getSttlAmt() {
        return SttlAmt;
    }

    public void setSttlAmt(BigDecimal sttlAmt) {
        SttlAmt = sttlAmt;
    }

    public List<BatchInf> getBatchList() {
        return BatchList;
    }

    public void setBatchList(List<BatchInf> batchList) {
        BatchList = batchList;
    }
}
