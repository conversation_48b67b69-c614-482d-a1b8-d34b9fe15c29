package com.epaylinks.efps.pas.provisions.epcc.EntityResp;

import com.epaylinks.efps.common.datadownload.annotation.FieldAnnotation;

import java.math.BigDecimal;

/**
 * @Author: Liuq
 * @Date: 2018/10/15 15:51
 */
public class SettInfoExportDto {
    /**
     * @params: 结算场次列表
     * 报文标识号
     */
    @FieldAnnotation(fieldName="报文标识号")
    private String sttlReptFlg;
    /**
     * 场次借贷标识
     */
    @FieldAnnotation(fieldName="场次借贷标识")
    private String sttlDCFlg;
    /**
     * 场次金额
     */
    @FieldAnnotation(fieldName="场次金额")
    private BigDecimal sttlAmt;

    public String getSttlReptFlg() {
        return sttlReptFlg;
    }

    public void setSttlReptFlg(String sttlReptFlg) {
        this.sttlReptFlg = sttlReptFlg;
    }

    public String getSttlDCFlg() {
        return sttlDCFlg;
    }

    public void setSttlDCFlg(String sttlDCFlg) {
        this.sttlDCFlg = sttlDCFlg;
    }

    public BigDecimal getSttlAmt() {
        return sttlAmt;
    }

    public void setSttlAmt(BigDecimal sttlAmt) {
        this.sttlAmt = sttlAmt;
    }
}
