package com.epaylinks.efps.pas.provisions.epcc.service;

import com.epaylinks.efps.pas.provisions.epcc.EntityResp.AccountInfoEntityResp;
import com.epaylinks.efps.pas.provisions.epcc.EntityResp.BatchInfExportDto;
import com.epaylinks.efps.pas.provisions.epcc.EntityResp.SettInfoExportDto;
import com.epaylinks.efps.pas.provisions.epcc.EntityResp.SubItemInfoExportDto;
import com.jcraft.jsch.JSchException;
import org.apache.commons.csv.CSVPrinter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * @Author: Liuq
 * @Date: 2018/9/27 17:04
 * @Description: 对账文件service
 */
public interface AccountInfoService {
    /**
     * 根据对账日期获取对账文件
     */
    public void saveAccountInfo(String accountDate) throws JSchException;
    /**
     * 判断是否数据库存在，如存在则从数据库中获取
     */
    public AccountInfoEntityResp getLocalAccountInf(String accountDate);

    /**
     * 导出对账文件
     */
    public void exportAccountInfo(String accountDate,HttpServletRequest request,HttpServletResponse response);

    /**
     * 导出方法
     */
    public CSVPrinter downloadCsv(HttpServletRequest request, HttpServletResponse response, List<String> titles);

    /**
     * csv导出
     */
    public void printlnCsv(CSVPrinter printer, Map<String, String> statisticsData,List<SettInfoExportDto> settInfoExportDtoList,
                           List<BatchInfExportDto> batchInfExportDtoList,List<SubItemInfoExportDto> subItemInfoExportDtoList,
                           List<String> settListTitles,List<String> batchInfListTitles,List<String> sttlInfListTitles);
}
