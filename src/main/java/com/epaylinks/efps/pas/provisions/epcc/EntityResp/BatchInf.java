package com.epaylinks.efps.pas.provisions.epcc.EntityResp;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: Liuq
 * @Date: 2018/9/25 11:14
 * @descrition: 批次信息实体类
 */
public class BatchInf {
    /**
     * 批次号
     */
    private String BatchId;
    /**
     * 批次借贷标识
     */
    private Integer BatchDCFlg;
    /**
     * 轧差净额
     */
    private BigDecimal BatchNetAmt;
    /**
     * 分项列表
     */
    private List<SubItemInf> SubItemList;

    public String getBatchId() {
        return BatchId;
    }

    public void setBatchId(String batchId) {
        BatchId = batchId;
    }

    public Integer getBatchDCFlg() {
        return BatchDCFlg;
    }

    public void setBatchDCFlg(Integer batchDCFlg) {
        BatchDCFlg = batchDCFlg;
    }

    public BigDecimal getBatchNetAmt() {
        return BatchNetAmt;
    }

    public void setBatchNetAmt(BigDecimal batchNetAmt) {
        BatchNetAmt = batchNetAmt;
    }

    public List<SubItemInf> getSubItemList() {
        return SubItemList;
    }

    public void setSubItemList(List<SubItemInf> subItemList) {
        SubItemList = subItemList;
    }
}
