package com.epaylinks.efps.pas.provisions.epcc.util;

import java.util.Date;

/**
 * @Author: Liuq
 * @Date: 2018/9/21 15:20
 * @para: 额度变动通知Response
 */
public class MappedNotifyResponse {
    /**
     * 金融机构编码
     */
    private String instgId;
    /**
     *借贷标识 1:借 2:贷
     */
    private Integer dcFlag;
    /**
     * 通知码
     * 1001:资金清算导致映射额度变动
     * 2001:可用额度低于阈值
     */
    private String ntcRsn;
    /**
     * 通知原因说明
     */
    private String ntcRsnDesc;
    /**
     * 账户余额
     */
    private String acctBlnAmt;
    /**
     * 映射额度
     */
    private String ldRsrvAmt;
    /**
     * 可用额度
     */
    private String avlBlnAmt;
    /**
     * 映射额度变动金额
     */
    private String ldRsrvChgAmt;
    /**
     * 通知时间
     */
    private Date ntcDtTm;
    /**
     * 映射额度更新时间
     */
    private Date ldRsrvUpDt;
    /**
     * 可用额度更新时间
     */
    private Date avlBlnUpDt;

    public String getId() {
        return instgId;
    }

    public void setId(String id) {
        this.instgId = instgId;
    }

    public Integer getDcFlag() {
        return dcFlag;
    }

    public void setDcFlag(Integer dcFlag) {
        this.dcFlag = dcFlag;
    }

    public String getNtcRsn() {
        return ntcRsn;
    }

    public void setNtcRsn(String ntcRsn) {
        this.ntcRsn = ntcRsn;
    }

    public String getNtcRsnDesc() {
        return ntcRsnDesc;
    }

    public void setNtcRsnDesc(String ntcRsnDesc) {
        this.ntcRsnDesc = ntcRsnDesc;
    }

    public String getAcctBlnAmt() {
        return acctBlnAmt;
    }

    public void setAcctBlnAmt(String acctBlnAmt) {
        this.acctBlnAmt = acctBlnAmt;
    }

    public String getLdRsrvAmt() {
        return ldRsrvAmt;
    }

    public void setLdRsrvAmt(String ldRsrvAmt) {
        this.ldRsrvAmt = ldRsrvAmt;
    }

    public String getAvlBlnAmt() {
        return avlBlnAmt;
    }

    public void setAvlBlnAmt(String avlBlnAmt) {
        this.avlBlnAmt = avlBlnAmt;
    }

    public String getLdRsrvChgAmt() {
        return ldRsrvChgAmt;
    }

    public void setLdRsrvChgAmt(String ldRsrvChgAmt) {
        this.ldRsrvChgAmt = ldRsrvChgAmt;
    }

    public String getInstgId() {
        return instgId;
    }

    public void setInstgId(String instgId) {
        this.instgId = instgId;
    }

    public Date getNtcDtTm() {
        return ntcDtTm;
    }

    public void setNtcDtTm(Date ntcDtTm) {
        this.ntcDtTm = ntcDtTm;
    }

    public Date getLdRsrvUpDt() {
        return ldRsrvUpDt;
    }

    public void setLdRsrvUpDt(Date ldRsrvUpDt) {
        this.ldRsrvUpDt = ldRsrvUpDt;
    }

    public Date getAvlBlnUpDt() {
        return avlBlnUpDt;
    }

    public void setAvlBlnUpDt(Date avlBlnUpDt) {
        this.avlBlnUpDt = avlBlnUpDt;
    }
}
