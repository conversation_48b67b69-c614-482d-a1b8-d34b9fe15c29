package com.epaylinks.efps.pas.provisions.epcc.service;

import com.epaylinks.efps.common.business.CommonResponse;
import com.epaylinks.efps.pas.common.PageResult;
import com.epaylinks.efps.pas.provisions.epcc.EntityResp.MappingEntityResp;
import com.epaylinks.efps.pas.provisions.epcc.EntityResp.MappingNotifyEntityResp;
import com.epaylinks.efps.pas.provisions.epcc.domain.MappingNotifyEntity;
import com.epaylinks.efps.pas.provisions.epcc.util.MappedNotifyResponse;
import com.epaylinks.efps.pas.provisions.epcc.domain.MappingEntity;
import com.epaylinks.efps.pas.provisions.epcc.util.QuotaQueryReponse;
import org.springframework.stereotype.Service;

/**
 * @Author: Liuq
 * @Date: 2018/9/21 16:06
 * @para: 备付金额度service
 */

public interface ProvisoinsService {

    /**
     * 额度映射/解映射新增
     */
    public CommonResponse proviMappedAdded(String trxCtgy,String instgId,String ldRsrvAmt, String username);
    /**
     * 额度映射/解映射记录查询
     */
    public PageResult<MappingEntityResp> proviMappedQuery(String trxCtgy, String instgId, String startTime,
                                   String endTime,Integer trxStatus,Integer pageNo,Integer pageNum);
    /**
     * 额度查询
     */
    public QuotaQueryReponse proviMappedSelect(String InqDtTm);
    /**
     * 额度变动通知
     */
    public String proviChangeNotify(MappedNotifyResponse mappedNotifyResponse);
    /**
     * 额度变动记录查询
     */
    public PageResult<MappingNotifyEntityResp> proviChangeQuery(String ntcRsn, Integer dcFlag,String startTime,
                                   String endTime, Integer pageNo,Integer pageNum);
}
