package com.epaylinks.efps.pas.provisions.epcc.service.impl;

import com.epaylinks.efps.common.datadownload.annotation.FieldAnnotation;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.pas.provisions.epcc.EntityResp.*;
import com.epaylinks.efps.pas.provisions.epcc.Enum.ProvisionEnum;
import com.epaylinks.efps.pas.provisions.epcc.Enum.ProvisionsErrorCode;
import com.epaylinks.efps.pas.provisions.epcc.dao.AccountInfoEntiyMapper;
import com.epaylinks.efps.pas.provisions.epcc.domain.AccountInfoEntiy;
import com.epaylinks.efps.pas.provisions.epcc.service.AccountInfoService;
import com.epaylinks.efps.pas.provisions.epcc.util.BackendUtils;
import com.epaylinks.efps.pas.provisions.epcc.util.SFTPChannel;
import com.epaylinks.efps.pas.provisions.epcc.util.SFTPConstants;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSchException;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.ServletContext;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.sql.rowset.serial.SerialClob;
import java.io.*;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.sql.Clob;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Author: Liuq
 * @Date: 2018/9/27 17:06
 */
@Service
public class AccountInfoServiceImpl implements AccountInfoService{

    @Autowired
    private AccountInfoEntiyMapper accountInfoEntiyMapper;
    @Autowired
    private SequenceService sequenceService;
    SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    @Logable(businessTag = "sftpConnect")
    public void saveAccountInfo(String accountDate) throws JSchException {
        // 设置主机ip，端口，用户名，密码
        Map<String, String> sftpDetails = BackendUtils.GetConfigProperty(
                "account.properties", SFTPConstants.SFTP_REQ_HOST,
                SFTPConstants.SFTP_REQ_USERNAME, SFTPConstants.SFTP_REQ_PASSWORD,
                SFTPConstants.SFTP_REQ_PORT, SFTPConstants.SFTP_PASSIVE_MODE,
                SFTPConstants.SFTP_FILE_PATH);

        SFTPChannel channel = new SFTPChannel();
        ChannelSftp chSftp = channel.getChannel(sftpDetails, 60000);
        BackendUtils backendUtils = new BackendUtils();
        //连接文件目录
        String directory = sftpDetails.get(SFTPConstants.SFTP_FILE_PATH+"********");
        //连接交易记录文件
        String downloadFile = directory+"S20180525.xml";
        //保存本地的文件全路径
        String saveFile = "accountInfo/";
        //下载文件
        channel.download(directory, downloadFile, saveFile, chSftp);
        //保存至本地数据库
        //dom4j获取xmlStr字符串
        String xmlContents  = backendUtils.Dom4jGetXML(saveFile+accountDate+".xml");
        AccountInfoEntiy accountInfoEntiy = new AccountInfoEntiy();
        accountInfoEntiy.setAccountId(sequenceService.nextValue("pas_provisions_accountInfo"));
        accountInfoEntiy.setCreateTime(new Date());
        accountInfoEntiy.setUrl("/resource/accountInfo/"+accountDate+".xml");
        Clob clobContent = null;
        try{
            clobContent = new SerialClob(xmlContents.toCharArray());
        }catch (Exception e){
            //抛出异常
            e.printStackTrace();
        }
        accountInfoEntiy.setContents(xmlContents);
        accountInfoEntiy.setAccountDate(new Date());
        int result = 0;
        try{
            result = accountInfoEntiyMapper.insert(accountInfoEntiy);
            if(result != 1){
                //抛出异常
                throw new AppException(ProvisionsErrorCode.DB_CONNECT_ERROR.code);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            chSftp.quit();
            try {
                channel.closeChannel();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * @param accountDate
     * @return AccountInfoEntityResp对象
     */
    @Override
    @Logable(businessTag = "getLocalAccountInfo")
    public AccountInfoEntityResp getLocalAccountInf(String accountDate) {
        AccountInfoEntityResp accountInfoEntityResp = null;
        BackendUtils backendUtils = new BackendUtils();
        //本地数据库若有，则从数据库获取；否则从ftp上获取
        List<AccountInfoEntiy> accountInfoEntiy = accountInfoEntiyMapper.accountInfoQuery(accountDate);
        if(accountInfoEntiy != null && accountInfoEntiy.size() > 0){
            String xmlStr = accountInfoEntiy.get(0).getContents();
            Document document = null;
            try{
                document = DocumentHelper.parseText(xmlStr);
            }catch(Exception e){
                //抛出异常
                e.printStackTrace();
            }
            Element rootElement = document.getRootElement();
            accountInfoEntityResp = backendUtils.getNodes(rootElement);
        }
        return accountInfoEntityResp;
    }

    @Logable(businessTag = "downloadCsv")
    public CSVPrinter downloadCsv(HttpServletRequest request, HttpServletResponse response, List<String> titles) {
        try {
            // 获取数据
            String fileName = request.getParameter("fileName");
            // 设置文件名, 用于下载
            response.setContentType("application/csv;charset=UTF-8");
            response.addHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(fileName, "utf-8"));
            // 添加UTF-8的BOM头，避免Excel打开文件时使用系统默认编码产生乱码
            byte[] byteBOM = new byte[] { (byte) 0xEF, (byte) 0xBB, (byte) 0xBF };
            OutputStream outputStream = response.getOutputStream();
            outputStream.write(byteBOM);
            OutputStreamWriter outputStreamWriter =  new OutputStreamWriter(outputStream, "utf-8");
            CSVPrinter printer = CSVFormat.DEFAULT.withHeader(titles.toArray(new String[titles.size()])).print(outputStreamWriter);
            return printer;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * csv导出
     */
    @Logable(businessTag = "accountInfoPrintlnCsv")
    public void printlnCsv(CSVPrinter printer, Map<String, String> statisticsData,List<SettInfoExportDto> settInfoExportDtoList,
                           List<BatchInfExportDto> batchInfExportDtoList,List<SubItemInfoExportDto> subItemInfoExportDtoList,
                           List<String> settListTitles,List<String> batchInfListTitles,List<String> sttlInfListTitles){
        if(subItemInfoExportDtoList.size() > 0){
            try{
                //报文标识号
                String sttlReptFlg = null;
                //结算列表数据
                for(SettInfoExportDto row:settInfoExportDtoList){
                    Field[] declaredFields = row.getClass().getDeclaredFields();
                    for (Field declaredField : declaredFields) {
                        declaredField.setAccessible(true);
                        FieldAnnotation annotation = declaredField.getAnnotation(FieldAnnotation.class);
                        if (annotation == null) {
                            // 只有注解了 FieldAnnotation 的字段，才会输出
                            continue;
                        }
                        Object value = declaredField.get(row);
                        if (StringUtils.equals(annotation.fieldName(), "报文标识号")) {
                            sttlReptFlg = (String) value;
                        }
                        printer.print(value + "\t");
                    }
                    // 换行
                    printer.println();
                    //打印批次列表数据
                    printBacthRecord(printer,sttlReptFlg, batchInfExportDtoList, subItemInfoExportDtoList, settListTitles
                            ,batchInfListTitles, sttlInfListTitles);
                }
                // 换行
                printer.println();
                //手续费行
                if (statisticsData != null && !statisticsData.isEmpty()) {
                    Iterator<String> iterator = statisticsData.keySet().iterator();
                    while (iterator.hasNext()) {
                        String key = (String) iterator.next();
                        printer.print(key);
                        printer.print(statisticsData.get(key));
                        // 换行
                        printer.println();
                    }
                    byte[] nulls = new byte[] { (byte) 00, (byte) 00, (byte) 00 };
                    printer.print(new String(nulls));
                }
                printer.flush();
            }catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
    /**
     * 打印批次列表数据
     */
    public void printBacthRecord(CSVPrinter printer,String sttlReptFlg,List<BatchInfExportDto> batchInfExportDtoList,
                                 List<SubItemInfoExportDto> subItemInfoExportDtoList,List<String> settListTitles ,
                                 List<String> batchInfListTitles,List<String> sttlInfListTitles) throws IOException {
        int i = settListTitles.size() - batchInfListTitles.size() - sttlInfListTitles.size();
        //取出该报文标识号的批次列表
        List<BatchInfExportDto> printBatchInfoList = new ArrayList<BatchInfExportDto>();
        if(batchInfExportDtoList != null && batchInfExportDtoList.size()>0){
            for(BatchInfExportDto record:batchInfExportDtoList){
                if(record.getSttlReptFlg().equals(sttlReptFlg)){
                    printBatchInfoList.add(record);
                }
            }
        }
        if (printBatchInfoList != null && !printBatchInfoList.isEmpty()) {
            String batchId = null;
            for (BatchInfExportDto bacthInfo : printBatchInfoList) {
                byte[] nulls = new byte[i];
                for (byte b : nulls) {
                    printer.print(new String(nulls));
                }
                printer.print(sttlReptFlg);
                printer.print(bacthInfo.getBatchId());
                printer.print(bacthInfo.getBatchDCFlg());
                printer.print(bacthInfo.getBatchNetAmt());
                batchId = bacthInfo.getBatchId();
                // 换行
                printer.println();
                //打印分项列表数据
                printSubItemRecord(printer,batchId,subItemInfoExportDtoList,settListTitles,sttlInfListTitles);
            }
        }
    }
    /**
     * 打印分项列表数据
     */
    public void printSubItemRecord(CSVPrinter printer,String batchId,List<SubItemInfoExportDto> subItemInfoExportDtoList,
                                   List<String> settListTitles ,List<String> sttlInfListTitles ) throws IOException {
        int i = settListTitles.size() - sttlInfListTitles.size();
        //取出该批次号的分项列表
        List<SubItemInfoExportDto> subItemPrintRecordList = new ArrayList<SubItemInfoExportDto>();
        if(subItemInfoExportDtoList != null && subItemInfoExportDtoList.size()>0){
            for(SubItemInfoExportDto record : subItemInfoExportDtoList){
                if(record.getBatchId().equals(batchId)){
                    subItemPrintRecordList.add(record);
                }
            }
        }
        if (subItemPrintRecordList != null && !subItemPrintRecordList.isEmpty()) {
            for (SubItemInfoExportDto subItemInfo : subItemPrintRecordList) {
                byte[] nulls = new byte[i];
                for (byte b : nulls) {
                    printer.print(new String(nulls));
                }
                printer.print(batchId);
                printer.print(subItemInfo.getBusinessType());
                printer.print(subItemInfo.getBankOrgFlag());
                printer.print(subItemInfo.getAccountType());
                printer.print(subItemInfo.getDebitSplitAmount());
                printer.print(subItemInfo.getDebitSplitCount());
                printer.print(subItemInfo.getCreditSplitAmount());
                printer.print(subItemInfo.getDebitSplitCount());
                // 换行
                printer.println();
            }
        }
    }

    /**
     * @param accountDate
     * @param response
     * @Description: 导出某天的对账文件
     */
    @Override
    public void exportAccountInfo(String accountDate, HttpServletRequest request, HttpServletResponse response) {
        BackendUtils backendUtils = new BackendUtils();
        AccountInfoEntityResp accountInfoEntityResp = null;
        String filePath = "accountInfo/"+accountDate+".xml";
        accountInfoEntityResp = getLocalAccountInf(accountDate);
        //先判断本地是否存在，存在则直接从本地导出，不存在需要去ftp上获取
        if(accountInfoEntityResp == null){
            try{
                saveAccountInfo(accountDate);
            }catch (Exception e){
                //抛出异常
            }
        }
        //导出
        try{
            exportExcel(accountDate,accountInfoEntityResp,request, response);
        }catch (Exception e){
            //抛出异常
        }
    }
    /**
     * 导出exc方法
     */
    public void exportExcel(String accountDate, AccountInfoEntityResp accountInfoEntityResp,HttpServletRequest request,HttpServletResponse response) throws IOException {

        // 声明一个工作簿
        HSSFWorkbook wb = new HSSFWorkbook();
        // 声明一个工作单并命名
        HSSFSheet sheet = wb.createSheet("accountInfo");
        // 创建标题样式
        HSSFCellStyle headerStyle = (HSSFCellStyle) wb.createCellStyle();
        headerStyle.setAlignment(HorizontalAlignment.CENTER);//水平居中
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);// 垂直对齐居中
        headerStyle.setBorderBottom(BorderStyle.THIN);//下边框
        headerStyle.setBorderLeft(BorderStyle.THIN);//左边框
        headerStyle.setBorderTop(BorderStyle.THIN);//上边框
        headerStyle.setBorderRight(BorderStyle.THIN);//右边框
        //创建字体
        HSSFFont header_Font = (HSSFFont) wb.createFont();
        header_Font.setFontName("微软雅黑");
        header_Font.setFontHeightInPoints((short) 15);
        headerStyle.setFont(header_Font);

        // 创建单元格样式
        HSSFCellStyle cell_Style = (HSSFCellStyle) wb.createCellStyle();// 设置字体样式
        cell_Style.setAlignment(HorizontalAlignment.CENTER);
        cell_Style.setVerticalAlignment(VerticalAlignment.CENTER);// 垂直对齐居中
        cell_Style.setBorderBottom(BorderStyle.THIN);//下边框
        cell_Style.setBorderLeft(BorderStyle.THIN);//左边框
        cell_Style.setBorderTop(BorderStyle.THIN);//上边框
        cell_Style.setBorderRight(BorderStyle.THIN);//右边框
        cell_Style.setWrapText(true); // 设置为自动换行
        HSSFFont cell_Font = (HSSFFont) wb.createFont();
        cell_Font.setFontName("微软雅黑");
        cell_Font.setFontHeightInPoints((short) 12);
        cell_Style.setFont(cell_Font);

        //创建自由样式
        HSSFCellStyle free_Style = (HSSFCellStyle) wb.createCellStyle();// 设置字体样式
        free_Style.setVerticalAlignment(VerticalAlignment.CENTER);
        free_Style.setAlignment(HorizontalAlignment.CENTER);
        HSSFFont free_Font = (HSSFFont) wb.createFont();
        free_Font.setFontName("微软雅黑");
        free_Font.setFontHeightInPoints((short) 12);
        free_Style.setFont(free_Font);

        //【结算场次列表】单元格行数:SttlList->SttlInf集合size大小+1(标题行)
        int sttlInfCount = accountInfoEntityResp.getSttlList().size()+1;
        //【批次列表】单元格行数:SttlList->SttlInf->BatchList->BatchInf的集合大小+1(标题行)
        int batchInfCount = 1;
        //【分项列表】单元格行数:SttlList->SttlInf->BatchList->BatchInf->SubItemList->SubItemInf的集合大小+1(标题行)
        int subItemInfCount = 0;
        if(accountInfoEntityResp.getSttlList() != null){
            for(int i = 0; i< accountInfoEntityResp.getSttlList().size() ;i++){
                //批次列表大小
                batchInfCount = batchInfCount + accountInfoEntityResp.getSttlList().get(i).getBatchList().size();
            }
        }
        //结算场次列表+批次列表+分项列表 =明细单元格行数
        int detailCount = sttlInfCount + batchInfCount + subItemInfCount ;

        //1.1创建合并单元格对象
        //头部"账务日期"
        CellRangeAddress callRangeAddress = new CellRangeAddress(0,0,0,7);//起始行,结束行,起始列,结束列
        //第二行
        CellRangeAddress callRangeAddress1 = new CellRangeAddress(1,1,0,1);//起始行,结束行,起始列,结束列
        CellRangeAddress callRangeAddress1_2 = new CellRangeAddress(1,1,2,3);//起始行,结束行,起始列,结束列
        CellRangeAddress callRangeAddress1_3 = new CellRangeAddress(1,1,4,5);//起始行,结束行,起始列,结束列
        CellRangeAddress callRangeAddress1_4 = new CellRangeAddress(1,1,6,7);//起始行,结束行,起始列,结束列
        //第三行
        CellRangeAddress callRangeAddress2 = new CellRangeAddress(2,2,0,1);//起始行,结束行,起始列,结束列
        CellRangeAddress callRangeAddress2_3 = new CellRangeAddress(2,2,2,3);//起始行,结束行,起始列,结束列
        CellRangeAddress callRangeAddress2_4 = new CellRangeAddress(2,2,4,5);//起始行,结束行,起始列,结束列
        CellRangeAddress callRangeAddress2_5 = new CellRangeAddress(2,2,6,7);//起始行,结束行,起始列,结束列
        //明细
        CellRangeAddress callRangeAddress3 = new CellRangeAddress(3,3,0,7);//起始行,结束行,起始列,结束列
        //结算场次列表
        CellRangeAddress callRangeAddress4 = new CellRangeAddress(4,4,0,7);//起始行,结束行,起始列,结束列
        //批次列表
        CellRangeAddress callRangeAddress5 = new CellRangeAddress(sttlInfCount+4+1,sttlInfCount+4+1,0,7);//起始行,结束行,起始列,结束列
        //分项列表
        CellRangeAddress callRangeAddress6 = new CellRangeAddress(sttlInfCount+4+1+batchInfCount+1,sttlInfCount+4+1+batchInfCount+1,0,7);//起始行,结束行,起始列,结束列

        //2.1加载合并单元格对象
        sheet.addMergedRegion(callRangeAddress);
        sheet.addMergedRegion(callRangeAddress1);
        sheet.addMergedRegion(callRangeAddress1_2);
        sheet.addMergedRegion(callRangeAddress1_3);
        sheet.addMergedRegion(callRangeAddress1_4);
        sheet.addMergedRegion(callRangeAddress2);
        sheet.addMergedRegion(callRangeAddress2_3);
        sheet.addMergedRegion(callRangeAddress2_4);
        sheet.addMergedRegion(callRangeAddress2_5);
        sheet.addMergedRegion(callRangeAddress3);
        sheet.addMergedRegion(callRangeAddress4);
        sheet.addMergedRegion(callRangeAddress5);
        sheet.addMergedRegion(callRangeAddress6);

        //3.1创建头标题行;并且设置头标题
        HSSFRow row = sheet.createRow(0);
        HSSFCell cell = row.createCell(0);
        //加载单元格样式
        cell.setCellStyle(headerStyle);
        cell.setCellValue("财务日期:"+accountDate);
        //4.1创建第二行
        HSSFRow rower = sheet.createRow(1);
        //结算总笔数
        HSSFCell celler_1 = rower.createCell(0);
        //借方金额
        HSSFCell celler_2 = rower.createCell(2);
        //贷方金额
        HSSFCell celler_3 = rower.createCell(4);
        //结算场次明细
        HSSFCell celler_4 = rower.createCell(6);
        //加载单元格样式
        celler_1.setCellStyle(cell_Style);
        celler_1.setCellValue("结算总笔数");
        celler_2.setCellStyle(cell_Style);
        celler_2.setCellValue("借方金额");
        celler_3.setCellStyle(cell_Style);
        celler_3.setCellValue("贷方金额");
        celler_4.setCellStyle(cell_Style);
        celler_4.setCellValue("结算场次明细");
        //创建第三行
        HSSFRow rowsan = sheet.createRow(2);
        //结算总笔数
        HSSFCell cellsan_1 = rowsan.createCell(0);
        //借方金额
        HSSFCell cellsan_2 = rowsan.createCell(2);
        //贷方金额
        HSSFCell cellsan_3 = rowsan.createCell(4);
        //结算场次明细
        HSSFCell cellsan_4 = rowsan.createCell(6);
        //加载单元格样式
        cellsan_1.setCellStyle(cell_Style);
        cellsan_1.setCellValue(accountInfoEntityResp.getSttlCntNb());
        cellsan_2.setCellStyle(cell_Style);
        String tmpVal = String.valueOf(accountInfoEntityResp.getDebitCntAmt());
        cellsan_2.setCellValue(tmpVal);
        cellsan_3.setCellStyle(cell_Style);
        String tmpVal2 = String.valueOf(accountInfoEntityResp.getCreditCntAmt());
        cellsan_3.setCellValue(tmpVal2);
        cellsan_4.setCellStyle(cell_Style);
        cellsan_4.setCellValue("");

        //创建明细标题、结算场次列表标题、批次列表标题、分项列表标题
        //第四行：明细
        HSSFRow rowsi = sheet.createRow(3);
        HSSFCell cellsi = rowsi.createCell(0);
        //加载单元格样式
        cellsi.setCellStyle(cell_Style);
        cellsi.setCellValue("明细");
        //第五行：结算场次列表
        HSSFRow rowwu = sheet.createRow(4);
        HSSFCell cellwu = rowwu.createCell(0);
        //加载单元格样式
        cellwu.setCellStyle(cell_Style);
        cellwu.setCellValue("结算场次列表");
        //第六行：结算场次列表-标题栏
        HSSFRow rowsix = sheet.createRow(5);
        HSSFCell cellsix_1 = rowsix.createCell(0);
        HSSFCell cellsix_2 = rowsix.createCell(1);
        HSSFCell cellsix_3 = rowsix.createCell(2);
        //加载单元格样式
        cellsix_1.setCellStyle(cell_Style);
        cellsix_1.setCellValue("报文标识号");
        cellsix_2.setCellStyle(cell_Style);
        cellsix_2.setCellValue("场次借贷标识");
        cellsix_3.setCellStyle(cell_Style);
        cellsix_3.setCellValue("场次金额");
        //第7行：批次列表
        HSSFRow rowseven = sheet.createRow(sttlInfCount+4+1);
        HSSFCell cellseven = rowseven.createCell(0);
        //加载单元格样式
        cellseven.setCellStyle(cell_Style);
        cellseven.setCellValue("批次列表");
        //第8行：批次列表-->标题栏
        HSSFRow rowsegiht = sheet.createRow(sttlInfCount+4+2);
        HSSFCell cellegiht_1 = rowsegiht.createCell(0);
        HSSFCell cellegiht_2 = rowsegiht.createCell(1);
        HSSFCell cellegiht_3 = rowsegiht.createCell(2);
        HSSFCell cellegiht_4 = rowsegiht.createCell(3);
        //加载单元格样式
        cellegiht_1.setCellStyle(cell_Style);
        cellegiht_1.setCellValue("报文标识号");
        cellegiht_2.setCellStyle(cell_Style);
        cellegiht_2.setCellValue("批次号");
        cellegiht_3.setCellStyle(cell_Style);
        cellegiht_3.setCellValue("批次借贷标识");
        cellegiht_4.setCellStyle(cell_Style);
        cellegiht_4.setCellValue("批次金额");
        //第9行：分项列表
        HSSFRow rowba = sheet.createRow(sttlInfCount+4+1+batchInfCount+1);
        HSSFCell cellba = rowba.createCell(0);
        //加载单元格样式
        cellba.setCellStyle(cell_Style);
        cellba.setCellValue("分项列表");
        //第10行：分项列表—>标题栏
        HSSFRow rowsten = sheet.createRow(sttlInfCount+4+1+batchInfCount+2);
        HSSFCell cellten_1 = rowsten.createCell(0);
        HSSFCell cellten_2 = rowsten.createCell(1);
        HSSFCell cellten_3 = rowsten.createCell(2);
        HSSFCell cellten_4 = rowsten.createCell(3);
        HSSFCell cellten_5 = rowsten.createCell(4);
        HSSFCell cellten_6 = rowsten.createCell(5);
        HSSFCell cellten_7 = rowsten.createCell(6);
        HSSFCell cellten_8 = rowsten.createCell(7);
        //加载单元格样式
        cellten_1.setCellStyle(cell_Style);
        cellten_1.setCellValue("批次号");
        cellten_2.setCellStyle(cell_Style);
        cellten_2.setCellValue("业务类型");
        cellten_3.setCellStyle(cell_Style);
        cellten_3.setCellValue("银行金额机构标识");
        cellten_4.setCellStyle(cell_Style);
        cellten_4.setCellValue("账户类型");
        cellten_5.setCellStyle(cell_Style);
        cellten_5.setCellValue("分项借方发生额");
        cellten_6.setCellStyle(cell_Style);
        cellten_6.setCellValue("分项借方发生笔数");
        cellten_7.setCellStyle(cell_Style);
        cellten_7.setCellValue("分项贷方发生额");
        cellten_8.setCellStyle(cell_Style);
        cellten_8.setCellValue("分项贷方发生笔数");

        //结算场次列表、批次列表数据、分项列表数据
        List<SttlInf> sttlList = new ArrayList<SttlInf>();
        if(accountInfoEntityResp.getSttlList() != null ){
            sttlList = accountInfoEntityResp.getSttlList();
        }
        //5.操作单元格;将列表数据写入excel
        if(sttlList != null){
            //结算场次列表数据
            //批次列表循环计数器
            int batInfCount = 0;
            //分项列表循环计数器
            int subItInfCount = 0;
            for(int j=0; j< sttlList.size(); j++)
            {
                HSSFRow row3 = sheet.createRow(j+6);
                HSSFCell cell0 = row3.createCell(0);
                cell0.setCellStyle(cell_Style);
                cell0.setCellValue(sttlList.get(j).getSttlReptFlg());
                HSSFCell cell1 = row3.createCell(1);
                cell1.setCellStyle(cell_Style);
                cell1.setCellValue(DCFlag(sttlList.get(j).getSttlDCFlg()));
                HSSFCell cell2 = row3.createCell(2);
                cell2.setCellStyle(cell_Style);
                cell2.setCellValue(String.valueOf(sttlList.get(j).getSttlAmt()));

                List<BatchInf> BatchList = new ArrayList<BatchInf>();
                if(sttlList.get(j).getBatchList() != null){
                    BatchList = sttlList.get(j).getBatchList();
                }
                if(BatchList != null){
                    //批次列表数据
                    for(int i = 0; i< BatchList.size(); i++){
                        batInfCount++;
                        HSSFRow row4 = sheet.createRow(sttlList.size()+6+1+batInfCount);
                        HSSFCell cell3 = row4.createCell(0);
                        cell3.setCellStyle(cell_Style);
                        cell3.setCellValue(sttlList.get(j).getSttlReptFlg());
                        HSSFCell cell4 = row4.createCell(1);
                        cell4.setCellStyle(cell_Style);
                        cell4.setCellValue(BatchList.get(i).getBatchId());
                        HSSFCell cell5 = row4.createCell(2);
                        cell5.setCellStyle(cell_Style);
                        cell5.setCellValue(DCFlag(BatchList.get(i).getBatchDCFlg()));
                        HSSFCell cell6 = row4.createCell(3);
                        cell6.setCellStyle(cell_Style);
                        cell6.setCellValue(String.valueOf(BatchList.get(i).getBatchNetAmt()));

                        List<SubItemInf> SubItemList = new ArrayList<SubItemInf>();
                        if(BatchList.get(i).getSubItemList() != null){
                            SubItemList = BatchList.get(i).getSubItemList();
                        }
                        //分项列表数据
                        if(SubItemList != null) {
                            for (int f = 0; f < SubItemList.size(); f++) {
                                subItInfCount++;
                                HSSFRow row5 = sheet.createRow((sttlInfCount+4+1+batchInfCount+2)+subItInfCount);
                                HSSFCell cell7 = row5.createCell(0);
                                cell7.setCellStyle(cell_Style);
                                cell7.setCellValue(BatchList.get(i).getBatchId());
                                String SubItemInf = SubItemList.get(f).getSubItemInf().replace("CNY","");
                                String[] subArray = {};
                                subArray = SubItemInf.split("\\|");
                                HSSFCell cell8 = row5.createCell(1);
                                cell8.setCellStyle(cell_Style);
                                cell8.setCellValue(busiType(subArray[0]));
                                HSSFCell cell9 = row5.createCell(2);
                                cell9.setCellStyle(cell_Style);
                                cell9.setCellValue(subArray[1]);
                                HSSFCell cell10 = row5.createCell(3);
                                cell10.setCellStyle(cell_Style);
                                cell10.setCellValue(accountType(subArray[2]));
                                HSSFCell cell11 = row5.createCell(4);
                                cell11.setCellStyle(cell_Style);
                                cell11.setCellValue(subArray[3]);
                                HSSFCell cell12 = row5.createCell(5);
                                cell12.setCellStyle(cell_Style);
                                cell12.setCellValue(subArray[4]);
                                HSSFCell cell13 = row5.createCell(6);
                                cell13.setCellStyle(cell_Style);
                                cell13.setCellValue(subArray[5]);
                                HSSFCell cell14 = row5.createCell(7);
                                cell14.setCellStyle(cell_Style);
                                cell14.setCellValue(subArray[6]);
                            }
                        }
                    }
                }
            }
        }
        String downFilename = "accountInfo.xls";
        // 获取文件的MIME类型：
       ServletContext servletContext = request.getServletContext();
        String contentType = servletContext.getMimeType(downFilename);
        // 将MIME类型放入响应
        response.setContentType(contentType);
        // 浏览器类型
        String agent = request.getHeader("user-agent");
        // 获取附件的名字和下载方式
        String contentDisposition = "attachment;filename=" + downFilename;
        // 将附件名字和下载方式放入响应头信息中
        response.setHeader("Content-Disposition", contentDisposition);
        // 告诉浏览器用什么软件可以打开此文件
        /*response.setHeader("content-Type", "application/vnd.ms-excel");
        // 下载文件的默认名称
        response.setHeader("Content-Disposition", "attachment;filename="+ URLEncoder.encode(downFilename, "utf-8"));*/
        ServletOutputStream out = response.getOutputStream();
        wb.write(out);
        out.close();
     }
    /**
     * @params: 英文代码
     * 业务类型转换
     * @return 中文含义
     */
    public String busiType(String busiType){
        //非空判断
        if(busiType.isEmpty() || busiType.equals("")){
            return "";
        }
        switch(busiType) {
            //协议支付
            case "0110": return ProvisionEnum.BusiType.AGREE_PAYMENT.comment;
            //验证支付
            case "0111": return ProvisionEnum.BusiType.VERIFY_PAYMENT.comment;
            //网关支付
            case "0112": return ProvisionEnum.BusiType.GATEWAY_PAYMENT.comment;
            //认证支付
            case "0113": return ProvisionEnum.BusiType.AUTHEN_PAYMENT.comment;
            //委托支付
            case "0114": return ProvisionEnum.BusiType.ENTRUST_PAYMENT.comment;
            //签约支付
            case "0115": return ProvisionEnum.BusiType.CONTRACT_PAYMENT.comment;
            //付款
            case "0120": return ProvisionEnum.BusiType.PAYMENT.comment;
            //退款-协议支付
            case "0121": return ProvisionEnum.BusiType.REFUND_AGREE_PAYMENT.comment;
            //退款-网关支付
            case "0122": return ProvisionEnum.BusiType.REFUND_GATEWAY_PAYMENT.comment;
            //退款-验证支付
            case "0123": return ProvisionEnum.BusiType.REFUND_VERIFY_PAYMENT.comment;
            //退款-认证支付
            case "0124": return ProvisionEnum.BusiType.REFUND_AUTHEN_PAYMENT.comment;
            //退款-委托支付
            case "0125": return ProvisionEnum.BusiType.REFUND_ENTRUST_PAYMENT.comment;
            //退款-签约支付
            case "0126": return ProvisionEnum.BusiType.REFUND_CONTRACT_PAYMENT.comment;
        }
        return "";
    }
    /**
     * @params: 英文代码
     * 账户类型转换
     * @return 中文含义
     */
    public String accountType(String accountType){
        //非空判断
        if(accountType.isEmpty() || accountType.equals("")){
            return "";
        }
        switch(accountType) {
            //个人银行借记账户
            case "00": return ProvisionEnum.AccountType.PERSONAL_DEBIT.comment;
            //个人银行贷记账户
            case "01": return ProvisionEnum.AccountType.PERSONAL_CREDIT.comment;
            //个人银行准贷记账户
            case "02": return ProvisionEnum.AccountType.QUASI_PERSONAL_CREDIT.comment;
            //对公银行账户
            case "05": return ProvisionEnum.AccountType.PUBLIC_ACCOUNT.comment;
            //存折
            case "07": return ProvisionEnum.AccountType.BANKBOOK.comment;
        }
        return "";
    }
    /**
     * @params: 英文代码
     * 借贷标识转换
     * @return 中文含义
     */
    public String DCFlag(Integer DCFlag){
        //非空判断
        if(DCFlag == null){
            return "";
        }
        switch(DCFlag) {
            //借
            case 1: return "借";
            //贷
            case 2: return "贷";
        }
        return "";
    }
}