package com.epaylinks.efps.pas.provisions.epcc.dao;

import java.math.BigDecimal;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.epaylinks.efps.pas.provisions.epcc.util.MappedNotifyDto;

/**
 * @Author: Liuq
 * @Date: 2018/9/25 11:23
 * @Description: 额度变动映射Mapper
 */
@Mapper
public interface ProvionsChangedMapper {

    /**
     * 额度变动通知新增
     */
    int changedMappingAdd(@Param("accountNo") String accountNo, @Param("ldRsrvAmt") BigDecimal ldRsrvAmt);

    /**
     * 额度变动记录查询
     * @para: 通知类型（ntcRsn 1001:资金清算导致映射额度变动，2001:可用额度低于阈值）
     * 时间(开始时间，结束时间),分页参数（开始页码，结束页码）
     * dcFlag  借贷标识 1:借 2:贷
     * @return: 分页List
     */
    List<MappedNotifyDto> changedMappingQuery(@Param("ntcRsn") String ntcRsn, @Param("dcFlag") String dcFlag,
                                            @Param("startTime") String startTime,@Param("endTime") String endTime,
                                            @Param("beginRowNo") Integer beginRowNo,@Param("endRowNo") Integer endRowNo);
    /**
     * 额度变动记录总记录数
     * @para: 通知类型（ntcRsn 1001:资金清算导致映射额度变动，2001:可用额度低于阈值）
     * 时间(开始时间，结束时间),分页参数（开始页码，结束页码）
     * dcFlag  借贷标识 1:借 2:贷
     * @return: int记录数
     */
    Integer countchangedMappQuery(@Param("ntcRsn") String ntcRsn, @Param("dcFlag") String dcFlag,
                                  @Param("startTime") String startTime,  @Param("endTime") String endTime,
                                  @Param("beginRowNo") Integer beginRowNo, @Param("endRowNo") Integer endRowNo);
}
