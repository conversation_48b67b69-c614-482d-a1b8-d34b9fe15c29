package com.epaylinks.efps.pas.provisions.epcc.EntityResp;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: Liuq
 * @Date: 2018/9/25 11:06
 * @Description: 对账文件实体类,用于生成excel报表等
 */
public class AccountInfoEntityResp {
    /**
     * 结算总笔数
     */
    private Integer SttlCntNb;
    /**
     * 借方金额
     */
    private BigDecimal DebitCntAmt;
    /**
     * 贷方金额
     */
    private BigDecimal CreditCntAmt;
    /**
     * 结算场次列表
     */
    private List<SttlInf> SttlList;

    public Integer getSttlCntNb() {
        return SttlCntNb;
    }

    public void setSttlCntNb(Integer sttlCntNb) {
        SttlCntNb = sttlCntNb;
    }

    public BigDecimal getDebitCntAmt() {
        return DebitCntAmt;
    }

    public void setDebitCntAmt(BigDecimal debitCntAmt) {
        DebitCntAmt = debitCntAmt;
    }

    public BigDecimal getCreditCntAmt() {
        return CreditCntAmt;
    }

    public void setCreditCntAmt(BigDecimal creditCntAmt) {
        CreditCntAmt = creditCntAmt;
    }

    public List<SttlInf> getSttlList() {
        return SttlList;
    }

    public void setSttlList(List<SttlInf> sttlList) {
        SttlList = sttlList;
    }
}
