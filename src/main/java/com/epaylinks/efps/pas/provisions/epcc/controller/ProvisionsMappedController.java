package com.epaylinks.efps.pas.provisions.epcc.controller;

import com.epaylinks.efps.common.business.CommonResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.pas.common.PageResult;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.provisions.epcc.EntityResp.MappingEntityResp;
import com.epaylinks.efps.pas.provisions.epcc.EntityResp.MappingNotifyEntityResp;
import com.epaylinks.efps.pas.provisions.epcc.domain.MappingEntity;
import com.epaylinks.efps.pas.provisions.epcc.domain.MappingNotifyEntity;
import com.epaylinks.efps.pas.provisions.epcc.util.HttpUtils;
import com.epaylinks.efps.pas.provisions.epcc.util.MappedNotifyResponse;
import com.epaylinks.efps.pas.provisions.epcc.service.ProvisoinsService;
import com.epaylinks.efps.pas.provisions.epcc.util.QuotaQueryReponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * @Author: Liuq
 * @Date: 2018/9/21 15:54
 * @para: 网联额度控制器
 */
@RestController
@RequestMapping("/ProvisionsMapped")
@Api(value = "ProvisionsMappedController", description = "网联备付金控制器")
public class ProvisionsMappedController {

    @Autowired
    private SequenceService sequenceService;
    @Autowired
    private ProvisoinsService provisoinsService;

    @RequestMapping(value ="/create", method = RequestMethod.POST)
    @Logable(businessTag = "create")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "网联额度映射", notes = "网联额度映射", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "trxCtgy", value = "交易类别(0001:额度映射 0002:额度解映射)", required = true, dataType = "String", length =20, paramType = "query"),
            @ApiImplicitParam(name = "instgId", value = "网联备付金账户", required = false, dataType = "String", length =50, paramType = "query"),
            @ApiImplicitParam(name = "ldRsrvAmt", value = "映射金额(例:10)", required = true, dataType = "int", length =100, paramType = "query"),
            @ApiImplicitParam(name = "username", value = "创建者", required = true, dataType = "String", length =100, paramType = "query")})
    public CommonResponse create(@RequestParam String trxCtgy,
                         @RequestParam(required = false) String instgId,
                         @RequestParam String ldRsrvAmt,
                         @RequestParam String username) {
        CommonResponse commonResponse = new CommonResponse();
        try{
            commonResponse = provisoinsService.proviMappedAdded(trxCtgy, instgId, ldRsrvAmt, username);
        }catch (Exception e){
            //抛出异常
            commonResponse.setCode(PasCode.SYSTEM_EXCEPTION.code);
            commonResponse.setMessage(PasCode.SYSTEM_EXCEPTION.message);
        }
        return commonResponse;
    }

    @RequestMapping(value ="/recordQuery", method = RequestMethod.GET)
    @Logable(businessTag = "recordQuery")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "额度映射记录查询", notes = "额度映射记录查询", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "trxCtgy", value = "交易类别(0001:额度映射,0002:额度解映射)", required = true, dataType = "String", length =20, paramType = "query"),
            @ApiImplicitParam(name = "instgId", value = "网联备付金账户", required = false, dataType = "String", length =50, paramType = "query"),
            @ApiImplicitParam(name = "startTime", value = "开始时间(yyyy-MM-dd)", required = false, dataType = "String", length =100, paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "结束时间(yyyy-MM-dd)", required = false, dataType = "String", length =100, paramType = "query"),
            @ApiImplicitParam(name = "trxStatus", value = "交易状态(0:成功,1:交易失败,2:处理中)", required = false, dataType = "Integer", length =100, paramType = "query"),
            @ApiImplicitParam(name = "pageNo", value = "分页码", required = true, dataType = "Integer", length =100, paramType = "query"),
            @ApiImplicitParam(name = "pageNum", value = "分页大小", required = true, dataType = "Integer", length =100, paramType = "query")})
    public PageResult<MappingEntityResp> recordQuery(@RequestParam String trxCtgy,
                         @RequestParam(required = false) String instgId,
                         @RequestParam(required = false) String startTime,
                         @RequestParam(required = false) String endTime,
                         @RequestParam(required = false) Integer trxStatus,
                         @RequestParam Integer pageNo,
                         @RequestParam Integer pageNum) {
        PageResult<MappingEntityResp> pageResult = null;
        try{
            pageResult = provisoinsService.proviMappedQuery(trxCtgy, instgId, startTime,
                    endTime, trxStatus, pageNo, pageNum);
        }catch (Exception e){
            pageResult = new PageResult<MappingEntityResp>();
            pageResult.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            pageResult.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
        }
        return pageResult;
    }

    @RequestMapping(value ="/select", method = RequestMethod.GET)
    @Logable(businessTag = "select")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "网联额度查询", notes = "网联额度查询", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "InqDtTm", value = "查询日期(yyyy-MM-dd)", required = true, dataType = "String", length =100, paramType = "query")})
    public QuotaQueryReponse mappingQuery(@RequestParam String InqDtTm) {
        QuotaQueryReponse quotaQueryReponse = null;
        try{
            quotaQueryReponse = provisoinsService.proviMappedSelect(InqDtTm);
        }catch (Exception e){
            throw new AppException(PasCode.SYSTEM_EXCEPTION.code);
        }
        return quotaQueryReponse;
    }

    @RequestMapping(value ="/notify", method = RequestMethod.POST)
    @Logable(businessTag = "notify")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "网联额度变动通知", notes = "网联额度变动通知", httpMethod = "POST")
    public String provisionsNotify(@ApiIgnore HttpServletRequest request) {
        MappedNotifyResponse mappedNotifyResponse = new MappedNotifyResponse();
        JSONObject reqData = new JSONObject();
        try{
            reqData = HttpUtils.getReqData(request);
        }catch (IOException e){
            //抛出异常
            e.printStackTrace();
        }
        mappedNotifyResponse = HttpUtils.couvertToResponse(reqData);
        return provisoinsService.proviChangeNotify(mappedNotifyResponse);
    }

    @RequestMapping(value ="/query", method = RequestMethod.GET)
    @Logable(businessTag = "query")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "额度变动记录查询", notes = "额度变动记录查询", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ntcRsn", value = "通知类型(1001:资金清算导致映射额度变动,2001:可用额度低于阈值)", required = false, dataType = "String", length =50, paramType = "query"),
            @ApiImplicitParam(name = "dcFlag", value = "借贷标识(1:借 2:贷)", required = false, dataType = "Integer", length =20, paramType = "query"),
            @ApiImplicitParam(name = "startTime", value = "通知开始时间(yyyy-MM-dd)", required = false, dataType = "String", length =100, paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "通知结束时间(yyyy-MM-dd)", required = false, dataType = "String", length =100, paramType = "query"),
            @ApiImplicitParam(name = "pageNo", value = "分页码", required = true, dataType = "Integer", length =50, paramType = "query"),
            @ApiImplicitParam(name = "pageNum", value = "分页大小", required = true, dataType = "Integer", length =50, paramType = "query")})
    public PageResult<MappingNotifyEntityResp> provisionsQuery(@RequestParam(required = false) String ntcRsn,
                                  @RequestParam(required = false) Integer dcFlag,
                                  @RequestParam(required = false) String startTime,
                                  @RequestParam(required = false) String endTime,
                                  @RequestParam Integer pageNo,
                                  @RequestParam Integer pageNum) {
        PageResult<MappingNotifyEntityResp> pageResult = null;
        try{
            pageResult = provisoinsService.proviChangeQuery(ntcRsn, dcFlag, startTime, endTime, pageNo, pageNum);
        }catch (Exception e){
            pageResult = new PageResult<MappingNotifyEntityResp>();
            pageResult.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
            pageResult.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
        }
        return pageResult;
    }
}
