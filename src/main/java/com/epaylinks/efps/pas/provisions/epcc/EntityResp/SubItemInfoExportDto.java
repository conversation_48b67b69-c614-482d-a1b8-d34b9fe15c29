package com.epaylinks.efps.pas.provisions.epcc.EntityResp;

import com.epaylinks.efps.common.datadownload.annotation.FieldAnnotation;

/**
 * @Author: Liuq
 * @Date: 2018/10/15 16:51
 */
public class SubItemInfoExportDto {
    /**
     * @params: 分项列表
     * 批次号
     */
    @FieldAnnotation(fieldName="批次号2")
    private String batchId;
    /**
     * 业务类型
     */
    @FieldAnnotation(fieldName="业务类型")
    private String businessType;
    /**
     * 银行金额机构标识
     */
    @FieldAnnotation(fieldName="银行金额机构标识")
    private String bankOrgFlag;
    /**
     * 账户类型
     */
    @FieldAnnotation(fieldName="账户类型")
    private String accountType;
    /**
     * 分项借方发生额
     */
    @FieldAnnotation(fieldName="分项借方发生额(元)")
    private String debitSplitAmount;
    /**
     * 分项借方发生笔数
     */
    @FieldAnnotation(fieldName="分项借方发生笔数")
    private Integer debitSplitCount;
    /**
     * 分项贷方发生额
     */
    @FieldAnnotation(fieldName="分项贷方发生额(元)")
    private String creditSplitAmount;
    /**
     * 分项贷方发生笔数
     */
    @FieldAnnotation(fieldName="分项贷方发生笔数")
    private Integer creditSplitCount;

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getBankOrgFlag() {
        return bankOrgFlag;
    }

    public void setBankOrgFlag(String bankOrgFlag) {
        this.bankOrgFlag = bankOrgFlag;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public String getDebitSplitAmount() {
        return debitSplitAmount;
    }

    public void setDebitSplitAmount(String debitSplitAmount) {
        this.debitSplitAmount = debitSplitAmount;
    }

    public Integer getDebitSplitCount() {
        return debitSplitCount;
    }

    public void setDebitSplitCount(Integer debitSplitCount) {
        this.debitSplitCount = debitSplitCount;
    }

    public String getCreditSplitAmount() {
        return creditSplitAmount;
    }

    public void setCreditSplitAmount(String creditSplitAmount) {
        this.creditSplitAmount = creditSplitAmount;
    }

    public Integer getCreditSplitCount() {
        return creditSplitCount;
    }

    public void setCreditSplitCount(Integer creditSplitCount) {
        this.creditSplitCount = creditSplitCount;
    }
}
