package com.epaylinks.efps.pas.provisions.epcc.util;

import java.util.Date;

/**
 * @Author: Liuq
 * @Date: 2018/9/21 15:20
 * @para: 额度变动通知实体类
 */
public class MappedNotifyDto {
    /**
     * 主键
     */
    private Long id;
    /**
     *借贷标识 1:借 2:贷
     */
    private Integer dcFlag;
    /**
     * 通知码
     * 1001:资金清算导致映射额度变动
     * 2001:可用额度低于阈值
     */
    private String ntcRsn;
    /**
     * 通知原因说明
     */
    private String ntcRsnDesc;
    /**
     * 账户余额
     */
    private Integer acctBlnAmt;
    /**
     * 映射额度
     */
    private Integer ldRsrvAmt;
    /**
     * 可用额度
     */
    private Integer avlBlnAmt;
    /**
     * 映射额度变动金额
     */
    private Integer ldRsrvChgAmt;
    /**
     * 通知时间
     */
    private Date notifyTime;
    /**
     * 映射额度更新时间
     */
    private Date updateTime;
    /**
     * 可用额度更新时间
     */
    private Date mappedUpdateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getDcFlag() {
        return dcFlag;
    }

    public void setDcFlag(Integer dcFlag) {
        this.dcFlag = dcFlag;
    }

    public String getNtcRsn() {
        return ntcRsn;
    }

    public void setNtcRsn(String ntcRsn) {
        this.ntcRsn = ntcRsn;
    }

    public String getNtcRsnDesc() {
        return ntcRsnDesc;
    }

    public void setNtcRsnDesc(String ntcRsnDesc) {
        this.ntcRsnDesc = ntcRsnDesc;
    }

    public Integer getAcctBlnAmt() {
        return acctBlnAmt;
    }

    public void setAcctBlnAmt(Integer acctBlnAmt) {
        this.acctBlnAmt = acctBlnAmt;
    }

    public Integer getLdRsrvAmt() {
        return ldRsrvAmt;
    }

    public void setLdRsrvAmt(Integer ldRsrvAmt) {
        this.ldRsrvAmt = ldRsrvAmt;
    }

    public Integer getAvlBlnAmt() {
        return avlBlnAmt;
    }

    public void setAvlBlnAmt(Integer avlBlnAmt) {
        this.avlBlnAmt = avlBlnAmt;
    }

    public Integer getLdRsrvChgAmt() {
        return ldRsrvChgAmt;
    }

    public void setLdRsrvChgAmt(Integer ldRsrvChgAmt) {
        this.ldRsrvChgAmt = ldRsrvChgAmt;
    }

    public Date getNotifyTime() {
        return notifyTime;
    }

    public void setNotifyTime(Date notifyTime) {
        this.notifyTime = notifyTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getMappedUpdateTime() {
        return mappedUpdateTime;
    }

    public void setMappedUpdateTime(Date mappedUpdateTime) {
        this.mappedUpdateTime = mappedUpdateTime;
    }
}
