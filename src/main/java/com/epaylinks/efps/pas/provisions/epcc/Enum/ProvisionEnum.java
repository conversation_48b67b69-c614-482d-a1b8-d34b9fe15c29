package com.epaylinks.efps.pas.provisions.epcc.Enum;

import com.epaylinks.efps.common.util.Constants;

/**
 * @Author: Liuq
 * @Date: 2018/9/28 16:22
 */
public interface ProvisionEnum extends Constants {

    /**
     * 交易状态
     */
    enum Trxstatus{
        SUCCESS("0", "成功"),
        FAIL("1", "失败"),
        DOING("2", "处理中");
        public final String code;
        public final String comment;
        Trxstatus(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }
    }
    //借贷标识
    enum DCflag {
        BORROW("1", "借"),
        LOAN("2", "贷");
        public final String code;
        public final String comment;
        DCflag(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }
    }

    //业务类型
    enum BusiType {
        AGREE_PAYMENT("0110", "协议支付"),
        VERIFY_PAYMENT("0111", "验证支付"),
        GATEWAY_PAYMENT("0112", "网关支付"),
        AUTHEN_PAYMENT("0113", "认证支付"),
        ENTRUST_PAYMENT("0114", "委托支付"),
        CONTRACT_PAYMENT("0115", "签约支付"),
        PAYMENT("0120", "付款"),
        REFUND_AGREE_PAYMENT("0121", "退款-协议支付"),
        REFUND_GATEWAY_PAYMENT("0122", "退款-网关支付"),
        REFUND_VERIFY_PAYMENT("0123", "退款-验证支付"),
        REFUND_AUTHEN_PAYMENT("0124", "退款-认证支付"),
        REFUND_ENTRUST_PAYMENT("0125", "退款-委托支付"),
        REFUND_CONTRACT_PAYMENT("0126", "退款-签约支付");
        public final String code;
        public final String comment;
        BusiType(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }
    }
    //账户类型
    enum AccountType {
        PERSONAL_DEBIT("00", "个人银行借记账户"),
        PERSONAL_CREDIT("01", "个人银行贷记账户"),
        QUASI_PERSONAL_CREDIT ("02", "个人银行准贷记账户"),
        PUBLIC_ACCOUNT("05", "对公银行账户"),
        BANKBOOK("07", "存折");
        public final String code;
        public final String comment;
        AccountType(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }
    }
}
