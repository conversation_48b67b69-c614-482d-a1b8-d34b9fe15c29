package com.epaylinks.efps.pas.provisions.epcc.EntityResp;

import com.epaylinks.efps.common.datadownload.annotation.FieldAnnotation;

import java.math.BigDecimal;

/**
 * @Author: Liuq
 * @Date: 2018/10/15 16:50
 */
public class BatchInfExportDto {
    /**
     * @params: 批次列表
     * 报文标识号
     */
    @FieldAnnotation(fieldName="报文标识号2")
    private String sttlReptFlg;
    /**
     * 批次号
     */
    @FieldAnnotation(fieldName="批次号")
    private String batchId;
    /**
     * 批次借贷标识
     */
    @FieldAnnotation(fieldName="批次借贷标识")
    private Integer batchDCFlg;
    /**
     * 轧差净额
     */
    @FieldAnnotation(fieldName="轧差净额(元)" , yuanHandler = true)
    private BigDecimal BatchNetAmt;

    public String getSttlReptFlg() {
        return sttlReptFlg;
    }

    public void setSttlReptFlg(String sttlReptFlg) {
        this.sttlReptFlg = sttlReptFlg;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public Integer getBatchDCFlg() {
        return batchDCFlg;
    }

    public void setBatchDCFlg(Integer batchDCFlg) {
        this.batchDCFlg = batchDCFlg;
    }

    public BigDecimal getBatchNetAmt() {
        return BatchNetAmt;
    }

    public void setBatchNetAmt(BigDecimal batchNetAmt) {
        BatchNetAmt = batchNetAmt;
    }
}
