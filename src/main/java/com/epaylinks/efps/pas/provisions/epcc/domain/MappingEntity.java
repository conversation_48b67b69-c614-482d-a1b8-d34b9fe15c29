package com.epaylinks.efps.pas.provisions.epcc.domain;

import java.math.BigDecimal;
import java.util.Date;

public class MappingEntity {
    /**
     * 自增id
     */
    private Long provisionsId;

    /**
     * 交易流水号
     */
    private String trxId;

    /**
     * 交易类别 0001:额度映射 0002:额度解映射
     */
    private String trxCtgy;

    /**
     * 映射金额
     */
    private BigDecimal ldRsrvAmt;

    /**
     * 原调整金额
     */
    private BigDecimal oriTrxAmt;

    /**
     * 可用额度
     */
    private BigDecimal avlBlnAmt;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 交易状态 0:成功,1:交易失败,2:交易处理中
     */
    private Long trxStatus;

    /**
     * 创建人
     */
    private String author;

    public Long getProvisionsId() {
        return provisionsId;
    }

    public void setProvisionsId(Long provisionsId) {
        this.provisionsId = provisionsId;
    }

    public String getTrxId() {
        return trxId;
    }

    public void setTrxId(String trxId) {
        this.trxId = trxId;
    }

    public String getTrxCtgy() {
        return trxCtgy;
    }

    public void setTrxCtgy(String trxCtgy) {
        this.trxCtgy = trxCtgy;
    }

    public BigDecimal getLdRsrvAmt() {
        return ldRsrvAmt;
    }

    public void setLdRsrvAmt(BigDecimal ldRsrvAmt) {
        this.ldRsrvAmt = ldRsrvAmt;
    }

    public BigDecimal getOriTrxAmt() {
        return oriTrxAmt;
    }

    public void setOriTrxAmt(BigDecimal oriTrxAmt) {
        this.oriTrxAmt = oriTrxAmt;
    }

    public BigDecimal getAvlBlnAmt() {
        return avlBlnAmt;
    }

    public void setAvlBlnAmt(BigDecimal avlBlnAmt) {
        this.avlBlnAmt = avlBlnAmt;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getTrxStatus() {
        return trxStatus;
    }

    public void setTrxStatus(Long trxStatus) {
        this.trxStatus = trxStatus;
    }

    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author;
    }
}