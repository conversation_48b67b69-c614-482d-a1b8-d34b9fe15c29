package com.epaylinks.efps.pas.provisions.epcc.dao;

import com.epaylinks.efps.pas.provisions.epcc.domain.AccountInfoEntiy;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * @Author: Liuq
 * @Date: 2018/9/25 11:30
 * @Description: 对账文件mapper
 */
@Mapper
public interface AccountInfoEntiyMapper {

    int deleteByPrimaryKey(Long accountId);

    int insert(AccountInfoEntiy record);

    int insertSelective(AccountInfoEntiy record);

    AccountInfoEntiy selectByPrimaryKey(Integer accountId);

    int updateByPrimaryKeySelective(AccountInfoEntiy record);

    int updateByPrimaryKeyWithBLOBs(AccountInfoEntiy record);

    int updateByPrimaryKey(AccountInfoEntiy record);

    /**
     * 对账记录新增（保存到本地数据库）
     */
    int accountInfoAdd(AccountInfoEntiy accountInfo);

    /**
     * 对账记录查询
     * @Descriptions: 查询某一天的对账文件
     * @params: accoutTime 对账日期时间
     */
    List<AccountInfoEntiy> accountInfoQuery(String accoutTime);
}