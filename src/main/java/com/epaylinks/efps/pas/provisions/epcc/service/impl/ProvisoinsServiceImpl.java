package com.epaylinks.efps.pas.provisions.epcc.service.impl;

import com.epaylinks.efps.common.business.CommonResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.pas.common.PageResult;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.mch.client.AcsClient;
import com.epaylinks.efps.pas.mch.client.model.ResetWarnNoticeKey;
import com.epaylinks.efps.pas.provisions.epcc.EntityResp.MappingEntityResp;
import com.epaylinks.efps.pas.provisions.epcc.EntityResp.MappingNotifyEntityResp;
import com.epaylinks.efps.pas.provisions.epcc.Enum.ProvisionEnum;
import com.epaylinks.efps.pas.provisions.epcc.Enum.ProvisionsErrorCode;
import com.epaylinks.efps.pas.provisions.epcc.client.EpccService;
import com.epaylinks.efps.pas.provisions.epcc.dao.MappingEntityMapper;
import com.epaylinks.efps.pas.provisions.epcc.dao.MappingNotifyEntityMapper;
import com.epaylinks.efps.pas.provisions.epcc.domain.MappingEntity;
import com.epaylinks.efps.pas.provisions.epcc.domain.MappingNotifyEntity;
import com.epaylinks.efps.pas.provisions.epcc.util.MappedNotifyResponse;
import com.epaylinks.efps.pas.provisions.epcc.util.QuotaQueryReponse;
import com.epaylinks.efps.pas.provisions.epcc.util.QuotaRecordResponse;
import com.epaylinks.efps.pas.provisions.epcc.util.QuotaResponse;
import com.epaylinks.efps.pas.provisions.epcc.service.ProvisoinsService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Author: Liuq
 * @Date: 2018/9/21 16:07
 */
@Service
public class ProvisoinsServiceImpl implements ProvisoinsService{
    @Autowired
    AcsClient acsClient;
    @Autowired
    private EpccService epccService;
    @Autowired
    private SequenceService sequenceService;
    @Autowired
    private MappingEntityMapper provionsMappedMapper;
    @Autowired
    private MappingNotifyEntityMapper provionsChangedMapper;

    SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    //额度映射&解映射新增
    @Override
    @Logable(businessTag = "proviMappedAdded")
    public CommonResponse proviMappedAdded(String trxCtgy,String instgId,String ldRsrvAmt,String username) {
        CommonResponse commonResponse = new CommonResponse();
        //返回码
        String strReult = "";
        String msgReult = "";
        //1.参数校验
        //2.调用epcc接口
        QuotaRecordResponse quotaResponse = new QuotaRecordResponse();
        try{
            BigDecimal bd = new BigDecimal(ldRsrvAmt);
            bd = bd.setScale(2, BigDecimal.ROUND_DOWN);
            String amt = "CNY" + bd.toString();
            if ("0001".equals(trxCtgy)) {
                acsClient.resetWarnNotice(ResetWarnNoticeKey.NET_AVLB_BAL);
            }
            quotaResponse = epccService.quotaAdjust(trxCtgy, amt);
        }catch (Exception e){
            //抛出异常
            throw new AppException(ProvisionsErrorCode.EPCC_CONNECT_ERROR.code);
        }
        //系统返回码
        String sysrtncd = null;
        //系统返回说明
        String sysrtndesc = null;
        //业务返回码
        String bizstscd = null;
        //业务返回说明
        String bizstsdesc = null;
        //原映射额度
        String oritrxamt = null;
        //映射额度
        String ldrsrvamt = "";
        //可用额度
        String avlblnamt = null;
        //交易状态 00:成功 01:失败 02:处理中
        String trxstatus = "";
        //系统返回时间
        String sysrtntm = null;
        //映射额度更新时间
        String ldrsrvupdt = null;
        //可用额度更新时间
        String avlblnupdt = null;
        //交易流水号
        String trxId = null;
        int result = 0;
        if(quotaResponse != null){
            sysrtncd = quotaResponse.getSysrtncd();
            sysrtndesc = quotaResponse.getSysrtndesc();
            bizstscd = quotaResponse.getBizstscd();
            bizstsdesc = quotaResponse.getBizstsdesc();
            oritrxamt = quotaResponse.getOritrxamt();
            ldrsrvamt = quotaResponse.getLdrsrvamt();
            avlblnamt = quotaResponse.getAvlblnamt();
            trxstatus = quotaResponse.getTrxstatus();
            sysrtntm = quotaResponse.getSysrtntm();
            ldrsrvupdt = quotaResponse.getLdrsrvupdt();
            avlblnupdt = quotaResponse.getAvlblnupdt();
            trxId = quotaResponse.getOritrxid();
        }else{
            //调用epcc返回空，抛出异常
            throw new AppException(ProvisionsErrorCode.EPCC_BLANK_ERROR.code);
        }
        //3.处理epcc接口返回
        MappingEntity mappingEntity = new MappingEntity();
        if(sysrtncd.equals("00000000") ){//系统返回码:00000000,业务返回码:00000000
            mappingEntity.setProvisionsId(sequenceService.nextValue("pas_provisions_add"));
            mappingEntity.setTrxCtgy(trxCtgy);
            if(trxstatus != null && !trxstatus.equals("")){
                if(trxstatus.equals("00")){//成功
                    mappingEntity.setTrxStatus(Long.valueOf(ProvisionEnum.Trxstatus.SUCCESS.code));
                }else if(trxstatus.equals("01")){//失败
                    mappingEntity.setTrxStatus(Long.valueOf(ProvisionEnum.Trxstatus.FAIL.code));
                }else{//处理中
                    mappingEntity.setTrxStatus(Long.valueOf(ProvisionEnum.Trxstatus.DOING.code));
                }
            }
            if(ldrsrvamt != null && !ldrsrvamt.equals("")){
                //截取CNY
                ldrsrvamt = ldrsrvamt.substring(3);
                mappingEntity.setLdRsrvAmt(new BigDecimal(ldrsrvamt));
            }
            if(oritrxamt != null && !oritrxamt.equals("")){
                oritrxamt = oritrxamt.substring(3);
                mappingEntity.setOriTrxAmt(new BigDecimal(oritrxamt));
            }
            if(avlblnamt != null && !avlblnamt.equals("")){
                avlblnamt = avlblnamt.substring(3);
                mappingEntity.setAvlBlnAmt(new BigDecimal(avlblnamt));
            }
            Date createTime = new Date();
            if(sysrtntm != null && !sysrtntm.equals("")){
                String tmpDate = sysrtntm;
                tmpDate = tmpDate.replace("T"," ");
                try{
                    ParsePosition pos = new ParsePosition(0);
                    createTime = sf.parse(tmpDate, pos);
                }catch (Exception e){
                    //抛出异常
                    throw new ClassCastException();
                }
            }
            mappingEntity.setCreateTime(createTime);
            mappingEntity.setTrxId(trxId);
            mappingEntity.setAuthor(username);
        }
        //4.插入本地数据库
        try{
            result = provionsMappedMapper.insert(mappingEntity);
        }catch (Exception e){
            //抛出异常
            throw new AppException(ProvisionsErrorCode.DB_CONNECT_ERROR.code);
        }
        //设置返回状态码
        if(result == 1){
            if(trxstatus.equals("00")){//成功
                strReult = CommonResponse.SUCCEE;
                msgReult = "交易成功";
            }else if(trxstatus.equals("02")){//处理中
                strReult = bizstscd;
                msgReult = "交易处理中"+bizstsdesc;
            }else{//失败
                strReult = bizstscd;
                msgReult = "交易失败"+bizstsdesc;
            }
        }else{
            strReult = bizstscd;
            msgReult = "交易失败"+bizstsdesc;
        }
        commonResponse.setCode(strReult);
        commonResponse.setMessage(msgReult);
        //5.返回运营门户前端
        return commonResponse;
    }

    /**
     * 额度映射/解映射记录查询
     * @param trxCtgy
     * @param instgId
     * @param startTime
     * @param endTime
     * @param trxStatus
     * @param pageNo
     * @param pageNum
     * @return
     */
    @Override
    @Logable(businessTag = "proviMappedQuery")
    public PageResult<MappingEntityResp> proviMappedQuery(String trxCtgy, String instgId, String startTime, String endTime,
                                                          Integer trxStatus, Integer pageNo, Integer pageNum) {

        //1.参数校验
        //2.查询本地记录
        PageResult<MappingEntityResp> pageResult = new PageResult<MappingEntityResp>();
        Integer beginRowNo = (pageNo - 1) * pageNum + 1;
        Integer endRowNo = pageNo * pageNum;
        List<MappingEntity> mappedList = new ArrayList<MappingEntity>();
        List<MappingEntityResp> mappedRespList = new ArrayList<MappingEntityResp>();
        try{
            mappedList = provionsMappedMapper.MappingQuery(trxCtgy, startTime, endTime, trxStatus, beginRowNo, endRowNo);
        }catch (Exception e){
            pageResult.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
            pageResult.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            //抛出异常
            throw new AppException(ProvisionsErrorCode.DB_CONNECT_ERROR.code);
        }
        int total = 0;
        try{
            total = provionsMappedMapper.countMappingQuery(trxCtgy, startTime, endTime, trxStatus, beginRowNo, endRowNo);
        }catch (Exception e){
            pageResult.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
            pageResult.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            //抛出异常
            throw new AppException(ProvisionsErrorCode.DB_CONNECT_ERROR.code);
        }
        //赋值给前端对象
        if(mappedList != null ){
            for(MappingEntity entity:mappedList){
                MappingEntityResp mappingEntityResp = new MappingEntityResp();
                BeanUtils.copyProperties(entity, mappingEntityResp);
                mappingEntityResp.setTrxStatus(getStatus(entity.getTrxStatus()));
                mappedRespList.add(mappingEntityResp);
            }
            pageResult.setReturnCode(com.epaylinks.efps.common.util.page.PageResult.SUCCEE);
            pageResult.setReturnMsg(PageResult.SUCCEE);
        }
        pageResult.setRows(mappedRespList);
        pageResult.setTotal(total);
        //3.返回记录
        return pageResult;
    }

    //交易状态
    public String getStatus(Long trxStatus){
        String status = "";
        if(trxStatus ==null){
            return status;
        }
        if(trxStatus == 1){
            status = "交易失败";
        }else if(trxStatus == 0){
            status = "交易成功";
        }else if(trxStatus == 2){
            status = "交易处理中";
        }else{
            status = "";
        }
        return status;
    }
    //额度映射查询
    @Override
    @Logable(businessTag = "proviMappedSelect")
    public QuotaQueryReponse proviMappedSelect(String InqDtTm) {
        //返回给前端的额度查询对象
        QuotaQueryReponse quotaQueryReponse = new QuotaQueryReponse();
        //1.参数校验
        //2.调用epcc查询接口
        QuotaResponse quotaResponse = new QuotaResponse();
        try{
            quotaResponse = epccService.queryQuota(InqDtTm);
        }catch (Exception e){
            //调用出错，抛出异常
            throw new AppException(ProvisionsErrorCode.EPCC_CONNECT_ERROR.code);
        }
        //系统返回码
        String sysRtnCd = null;
        //系统返回信息
        String sysRtnDesc = null;
        //业务返回码
        String bizStsCd = null;
        //业务返回信息
        String bizStsDesc = null;
        //账户余额
        String acctBlcAmt = null;
        //可用额度
        String avlBlnAmt = null;
        //映射额度
        String ldRsrvAmt = null;
        //虚拟记账额度
        String virtualAmt = null;
        //3.处理epcc返回校验
        if(quotaResponse != null){
            sysRtnCd =  quotaResponse.getSysRtnCd();
            sysRtnDesc = quotaResponse.getSysRtnDesc();
            bizStsCd = quotaResponse.getBizStsCd();
            bizStsDesc = quotaResponse.getBizStsDesc();
            if(sysRtnCd.equals("00000000") && bizStsCd.equals("00000000")){
                acctBlcAmt = quotaResponse.getAcctBlcAmt();
                avlBlnAmt = quotaResponse.getAvlBlnAmt();
                ldRsrvAmt = quotaResponse.getLdRsrvAmt();
                virtualAmt =  quotaResponse.getVirtualAmt();
                //设置金额
                quotaQueryReponse.setAcctBlcAmt(acctBlcAmt);
                quotaQueryReponse.setAvlBlnAmt(avlBlnAmt);
                quotaQueryReponse.setLdRsrvAmt(ldRsrvAmt);
                quotaQueryReponse.setVirtualAmt(virtualAmt);
                //设置返回码
                quotaQueryReponse.setCode(CommonResponse.SUCCEE);
                quotaQueryReponse.setMessage("查询成功!");
            }else{
                //查询失败，设置金额为0
                quotaQueryReponse.setAcctBlcAmt(null);
                quotaQueryReponse.setAvlBlnAmt(null);
                quotaQueryReponse.setLdRsrvAmt(null);
                quotaQueryReponse.setVirtualAmt(null);
                //设置返回码
                quotaQueryReponse.setCode(sysRtnCd);
                if(bizStsDesc != null && !bizStsDesc.equals("")){
                    quotaQueryReponse.setErrorMsg("查询失败!"+sysRtnDesc+bizStsDesc);
                }else{
                    quotaQueryReponse.setErrorMsg("查询失败!"+sysRtnDesc);
                }
            }
        }else{
            //调用epcc返回空，抛出异常
            //查询失败，设置金额为0
            quotaQueryReponse.setAcctBlcAmt(null);
            quotaQueryReponse.setAvlBlnAmt(null);
            quotaQueryReponse.setLdRsrvAmt(null);
            quotaQueryReponse.setVirtualAmt(null);
            //设置返回码
            quotaQueryReponse.setCode("0001");
            quotaQueryReponse.setErrorMsg("系统异常，查询失败!请重试。");
        }
        //4.返回运营门户前端系统
        return quotaQueryReponse;
    }

    /*
    * 额度变动通知
    * @return：返回给epcc的应答码
    * */
    @Override
    @Logable(businessTag = "proviChangeNotify")
    public String proviChangeNotify(MappedNotifyResponse mappedNotifyResponse) {
        //1.验证epcc返回参数
        String strResult = null;
        //返回通知码
        String ntcRsn = "";
        //返回通知原因说明
        String ntcRsnDesc = "";
        //账户余额
        String acctBlnAmt = null;
        //可用余额
        String avlBlnAmt = null;
        //映射额度变动金额
        String ldRsrvChgAmt = null;
        //映射额度
        String ldRsrvAmt = null;
        //插入结果int
        int result = 0;
        MappingNotifyEntity mappedNotifyDto = new MappingNotifyEntity();
        mappedNotifyDto.setProvisionsNotifyId(sequenceService.nextValue("pas_provisions_change"));
        if(mappedNotifyResponse != null){
            try{
                if(mappedNotifyResponse.getLdRsrvAmt() != null && !mappedNotifyResponse.getLdRsrvAmt().equals("")){
                    ldRsrvAmt = mappedNotifyResponse.getLdRsrvAmt();
                    mappedNotifyDto.setLdRsrvAmt(new BigDecimal(ldRsrvAmt));
                }
                if(mappedNotifyResponse.getAcctBlnAmt() != null && !mappedNotifyResponse.getAcctBlnAmt().equals("")){
                    acctBlnAmt = mappedNotifyResponse.getAcctBlnAmt();
                    mappedNotifyDto.setAcctBlnAmt(new BigDecimal(acctBlnAmt));
                }
                if(mappedNotifyResponse.getAvlBlnAmt() != null && !mappedNotifyResponse.getAvlBlnAmt().equals("")){
                    avlBlnAmt = mappedNotifyResponse.getAvlBlnAmt();
                    mappedNotifyDto.setAvlBlnAmt(new BigDecimal(avlBlnAmt));
                }
                if(mappedNotifyResponse.getDcFlag() != null){
                    if(mappedNotifyResponse.getDcFlag() == 1){
                        mappedNotifyDto.setDcFlag(Short.valueOf(ProvisionEnum.DCflag.BORROW.code));
                    }else{
                        mappedNotifyDto.setDcFlag(Short.valueOf(ProvisionEnum.DCflag.LOAN.code));
                    }
                }else{
                    mappedNotifyDto.setDcFlag(null);
                }
                if(mappedNotifyResponse.getLdRsrvChgAmt() != null && !mappedNotifyResponse.getLdRsrvChgAmt().equals("")){
                    ldRsrvChgAmt = mappedNotifyResponse.getLdRsrvChgAmt();
                    mappedNotifyDto.setLdRsrvChgAmt(new BigDecimal(ldRsrvChgAmt));
                }
                if(mappedNotifyResponse.getAvlBlnUpDt() != null ){
                    mappedNotifyDto.setMappedUpdateTime(mappedNotifyResponse.getAvlBlnUpDt());
                }
                if(mappedNotifyResponse.getNtcDtTm() != null ){
                    mappedNotifyDto.setNotifyTime(mappedNotifyResponse.getNtcDtTm());
                }
                if(mappedNotifyResponse.getNtcDtTm() != null ){
                    mappedNotifyDto.setUpdateTime(mappedNotifyResponse.getLdRsrvUpDt());
                }
                if(mappedNotifyResponse.getNtcRsn() != null && !mappedNotifyResponse.getNtcRsn().equals("")){
                    ntcRsn = mappedNotifyResponse.getNtcRsn();
                }
                mappedNotifyDto.setNtcRsn(ntcRsn);
                if(mappedNotifyResponse.getNtcRsnDesc() != null && !mappedNotifyResponse.getNtcRsnDesc().equals("")){
                    ntcRsnDesc = mappedNotifyResponse.getNtcRsnDesc();
                }
                mappedNotifyDto.setNtcRsnDesc(ntcRsnDesc);
            }catch (Exception e){
                //抛出异常
                throw new AppException(PasCode.SYSTEM_EXCEPTION.code);
            }
            strResult ="00";
        }else{
            //epcc返回为空，抛出异常
            strResult ="01";
        }
        //2.本地记录数据库mappedNotifyDto
        try{
            result = provionsChangedMapper.insert(mappedNotifyDto);
        }catch (Exception e){
            //抛出异常
            throw new AppException(PasCode.SYSTEM_EXCEPTION.code);
        }
        if(result != 1){
            //抛出异常
            throw new AppException(PasCode.SYSTEM_EXCEPTION.code);
        }
        //3.返回应答
        return strResult;
    }

    /**
     * 额度变动记录查询
     * @params: 通知类型（ntcRsn 1001:资金清算导致映射额度变动，2001:可用额度低于阈值）
     * dcFlag  借贷标识 1:借 2:贷
     * 通知时间 （开始时间 startTime，结束时间 endTime）
     * 分页参数 （pageNo pageNum）
     */
    @Override
    @Logable(businessTag = "proviChangeQuery")
    public PageResult<MappingNotifyEntityResp> proviChangeQuery(String ntcRsn, Integer dcFlag, String startTime,
                                                        String endTime, Integer pageNo, Integer pageNum) {
        //1.参数校验
        //2.查询本地记录
        PageResult<MappingNotifyEntityResp> pageResult = new PageResult<MappingNotifyEntityResp>();
        List<MappingNotifyEntity> mappedList = new ArrayList<MappingNotifyEntity>();
        List<MappingNotifyEntityResp> mappedRespList = new ArrayList<MappingNotifyEntityResp>();
        int total = 0;
        Integer beginRowNo = (pageNo - 1) * pageNum + 1;;
        Integer endRowNo = pageNo * pageNum;
        try{
            mappedList = provionsChangedMapper.changedMappingQuery(ntcRsn,dcFlag, startTime,
                    endTime,beginRowNo,endRowNo);
            total = provionsChangedMapper.countchangedMappQuery(ntcRsn,dcFlag, startTime,
                    endTime,beginRowNo,endRowNo);
        }catch (Exception e){
            pageResult.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
            pageResult.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            throw new AppException(PasCode.SYSTEM_EXCEPTION.code);
        }
        //赋值给前端
        if(mappedList != null){
            for(MappingNotifyEntity entity:mappedList){
                MappingNotifyEntityResp mappingNotifyEntityResp = new MappingNotifyEntityResp();
                BeanUtils.copyProperties(entity, mappingNotifyEntityResp);
                mappingNotifyEntityResp.setDcFlag(getDcFlag(entity.getDcFlag()));
                mappedRespList.add(mappingNotifyEntityResp);
            }
            pageResult.setReturnCode(com.epaylinks.efps.common.util.page.PageResult.SUCCEE);
            pageResult.setReturnMsg(com.epaylinks.efps.common.util.page.PageResult.SUCCEE);
        }
        pageResult.setTotal(total);
        pageResult.setRows(mappedRespList);
        //3.设置pageResult分页记录
        //4.返回对象记录
        return pageResult;
    }

    //借贷标识
    public String getDcFlag(Short dcFlag){
        String dcFlg = "";
        if(dcFlag == null){
            return dcFlg;
        }
        if(dcFlag ==1){
            dcFlg = "借";
        }else if(dcFlag ==2){
            dcFlg = "贷";
        }else{
            dcFlg = "未知";
        }
        return dcFlg;
    }
}