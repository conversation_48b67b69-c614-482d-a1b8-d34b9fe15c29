package com.epaylinks.efps.pas.provisions.epcc.dao;

import com.epaylinks.efps.pas.provisions.epcc.domain.MappingEntity;
import com.epaylinks.efps.pas.provisions.epcc.domain.MappingNotifyEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: Liuq
 * @Date: 2018/9/25 11:23
 * @Description: 额度变动通知Mapper
 */
@Mapper
public interface MappingNotifyEntityMapper {
    int deleteByPrimaryKey(Long provisionsNotifyId);

    int insert(MappingNotifyEntity record);

    int insertSelective(MappingNotifyEntity record);

    MappingNotifyEntity selectByPrimaryKey(Long provisionsNotifyId);

    int updateByPrimaryKeySelective(MappingNotifyEntity record);

    int updateByPrimaryKey(MappingNotifyEntity record);

    /**
     * 额度变动记录新增
     */
    int proviMappingAdd(@Param("accountNo")String accountNo,@Param("ldRsrvAmt") BigDecimal ldRsrvAmt);

    /**
     * 额度变动记录查询
     * @para: 通知类别，（ntcRsn 1001:资金清算导致映射额度变动，2001:可用额度低于阈值）
     * 时间(开始时间，结束时间),分页参数（开始页码，结束页码）
     * dcFlag  借贷标识 1:借 2:贷
     * @return: 分页PageHelper
     */
    List<MappingNotifyEntity> changedMappingQuery(@Param("ntcRsn") String trxCtgy,@Param("dcFlag") Integer dcFlag,
                                            @Param("startTime") String startTime,@Param("endTime") String endTime,
                                            @Param("beginRowNo") Integer beginRowNo,@Param("endRowNo") Integer endRowNo);
    /**
     * 额度变动总记录数
     * @para: 通知类别，（ntcRsn 1001:资金清算导致映射额度变动，2001:可用额度低于阈值）
     * 时间(开始时间，结束时间),分页参数（开始页码，结束页码）
     * dcFlag  借贷标识 1:借 2:贷
     * @return: int记录数
     */
    Integer countchangedMappQuery(@Param("ntcRsn") String trxCtgy,@Param("dcFlag") Integer dcFlag,
                                  @Param("startTime") String startTime,@Param("endTime") String endTime,
                                  @Param("beginRowNo") Integer beginRowNo,@Param("endRowNo") Integer endRowNo);
}