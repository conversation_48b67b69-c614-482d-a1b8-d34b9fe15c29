package com.epaylinks.efps.pas.provisions.epcc.util;

import java.io.*;
import java.math.BigDecimal;
import java.util.*;
import com.epaylinks.efps.pas.provisions.epcc.EntityResp.AccountInfoEntityResp;
import com.epaylinks.efps.pas.provisions.epcc.EntityResp.BatchInf;
import com.epaylinks.efps.pas.provisions.epcc.EntityResp.SttlInf;
import com.epaylinks.efps.pas.provisions.epcc.EntityResp.SubItemInf;
import org.dom4j.*;
import org.dom4j.io.SAXReader;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

/**
 * @Author: Liuq
 * @Date: 2018/9/27 16:34
 */
public class BackendUtils {

    /**
     * @MethodName  : GetConfigProperty
     * @Description :  去读配置文件中的property属性
     * @param fileName 配置文件名称
     * @param propName 获取的属性
     * @return propMap
     */
    public static Map<String, String> GetConfigProperty(String fileName,String...  propName){
        Properties prop = new Properties();
        Map<String, String> propMap = new HashMap<String, String>();
        try {
            prop.load(BackendUtils.class.getClassLoader().getResourceAsStream(fileName));
            for (String propery : propName) {
                propMap.put(propery, prop.getProperty(propery));
            }
        } catch(IOException e) {
            e.printStackTrace();
        }
        return propMap;
    }

    /**
     * 解析xml文件并
     */
    public String Dom4jGetXML(String saveFile){
        //返回的Entity对象
        String xmlContents = new String();
        // 创建SAXReader的对象reader
        SAXReader reader = new SAXReader();
        try {
//            Resource resource = new ClassPathResource("accountInfo/"+accountDate+".xml");
            Resource resource = new ClassPathResource(saveFile);
            File file = null;
            try{
                file = resource.getFile();
            }catch (Exception e){
                //抛出异常
            }
            // 通过reader对象的read方法加载books.xml文件,获取docuemnt对象。
            Document document = reader.read(file);
            // 通过document对象获取根节点bookstore
            Element rootElement = document.getRootElement();
            //document转换为String字符串
            xmlContents  = document.asXML();
            //解析xml文件并转换为obj
        } catch (DocumentException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return xmlContents;
    }

    /**
     * 解析对账文件xml，转换为obj对象返回前段
     * @Descriptions: 循环遍历所有子节点,保存每个节点的值
     * @Return: AccountInfoEntity
     */
    public AccountInfoEntityResp getNodes(Element node){
        AccountInfoEntityResp accountInfoEntityResp = new AccountInfoEntityResp();
        List<SttlInf> SttlList = new ArrayList<SttlInf>();
        //获得指定节点下面的子节点，首先要知道自己要操作的节点。
        Element SttlCntNbElem = node.element("SttlCntNb");
        //获取SttlCntNb
        String SttlCntNb = SttlCntNbElem.getTextTrim();
        //获取DebitCntAmt
        Element DebitCntAmtElem = node.element("DebitCntAmt");
        String DebitCntAmt = DebitCntAmtElem.getTextTrim();
        //获取CreditCntAmt
        Element CreditCntAmtElem = node.element("CreditCntAmt");
        String CreditCntAmt = CreditCntAmtElem.getTextTrim();
        accountInfoEntityResp.setSttlCntNb(Integer.valueOf(SttlCntNb));
        BigDecimal CreditCntAmtVal = null;
        if(CreditCntAmt != null && !CreditCntAmt.isEmpty()){
            CreditCntAmtVal = new BigDecimal(CreditCntAmt.replace("CNY", ""));
        }
        accountInfoEntityResp.setCreditCntAmt(CreditCntAmtVal);
        BigDecimal DebitCntAmtval = null;
        if(DebitCntAmt != null && !DebitCntAmt.isEmpty()){
            DebitCntAmtval = new BigDecimal(DebitCntAmt.replace("CNY", ""));
        }
        accountInfoEntityResp.setDebitCntAmt(DebitCntAmtval);
        //获取SttlList
        Element SttlListEle = node.element("SttlList");
        //获取SttlInf
        Element SttlInfEle = SttlListEle.element("SttlInf");
        List<Element> SttlInfList = SttlListEle.elements();
        //遍历SttlInfList节点
        String SttlReptFlg  = "";
        Integer SttlDCFlg = null;
        BigDecimal SttlAmt = null;
        for(Element e : SttlInfList){
            List<BatchInf> BatchList = new ArrayList<BatchInf>();
            SttlInf sttlInf = new SttlInf();
            //SttlInf下的子节点
            Element SttlReptFlgEle = e.element("SttlReptFlg");
            SttlReptFlg = SttlReptFlgEle.getTextTrim();
            Element SttlDCFlgEle = e.element("SttlDCFlg");
            SttlDCFlg = Integer.valueOf(SttlDCFlgEle.getTextTrim());
            Element SttlAmtEle = e.element("SttlAmt");
            SttlAmt = new BigDecimal(SttlAmtEle.getTextTrim().replace("CNY", ""));
            //设值
            sttlInf.setSttlAmt(SttlAmt);
            sttlInf.setSttlDCFlg(SttlDCFlg);
            sttlInf.setSttlReptFlg(SttlReptFlg);
            //获取BatchList节点
            Element BatchListEle = e.element("BatchList");
            //获取BatchInf
            List<Element> BatchInfList = BatchListEle.elements();
            for(Element e2 : BatchInfList){
                List<SubItemInf> SubItemList = new ArrayList<SubItemInf>();
                BatchInf batchInf = new BatchInf();
                String BatchId = "";
                Integer BatchDCFlg = null;
                BigDecimal BatchNetAmt = null;
                Element BatchIdEle = e2.element("BatchId");
                BatchId = BatchIdEle.getTextTrim();
                Element BatchDCFlgEle = e2.element("BatchDCFlg");
                BatchDCFlg = Integer.valueOf(BatchDCFlgEle.getTextTrim());
                Element BatchNetAmtEle = e2.element("BatchNetAmt");
                BatchNetAmt = new BigDecimal(BatchNetAmtEle.getTextTrim().replace("CNY", ""));
                batchInf.setBatchDCFlg(BatchDCFlg);
                batchInf.setBatchId(BatchId);
                batchInf.setBatchNetAmt(BatchNetAmt);
                //获取SubItemList节点
                Element SubItemListEle = e2.element("SubItemList");
                //获取BatchInf
                List<Element> SubItemInfList = SubItemListEle.elements();
                for(Element e3 : SubItemInfList){
                    SubItemInf subItemInf = new SubItemInf();
                    String subItem = "";
                    subItem = e3.getTextTrim().replace("CNY", "");
                    subItemInf.setSubItemInf(subItem);
                    SubItemList.add(subItemInf);
                }
                batchInf.setSubItemList(SubItemList);
                BatchList.add(batchInf);
            }
            sttlInf.setBatchList(BatchList);
            SttlList.add(sttlInf);
        }
        accountInfoEntityResp.setSttlList(SttlList);
        return accountInfoEntityResp;
    }

    /**
     * @return document
     *  Document对象（读xml生成的）
     * @throws Throwable
     */
    public Document getLocalDocument(String filePath){
        Document document = null;
        // 创建SAXReader的对象reader
        SAXReader reader = new SAXReader();
        try {
            Resource resource = new ClassPathResource(filePath);
            File file = null;
            try {
                file = resource.getFile();
            } catch (Exception e) {
                //抛出异常
            }
            // 通过reader对象的read方法加载books.xml文件,获取docuemnt对象。
            document = reader.read(file);
        }catch (Exception e){
            //抛出异常
        }
        return document;
    }
}