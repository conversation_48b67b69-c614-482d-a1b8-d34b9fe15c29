package com.epaylinks.efps.pas.config;

import com.epaylinks.efps.pas.interceptor.SpecialCharInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

@Configuration
public class InterceptorConfig extends WebMvcConfigurerAdapter  {
    @Autowired
    private SpecialCharInterceptor specialCharInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(specialCharInterceptor)
                .addPathPatterns("/**")
        ;
    }
}
