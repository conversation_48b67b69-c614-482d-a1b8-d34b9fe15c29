package com.epaylinks.efps.pas.config;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.pas.interceptor.SpecialCharException;
import com.epaylinks.efps.pas.common.PasCode;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice
public class ExceptionAdvice {
    @ExceptionHandler({SpecialCharException.class})
    public Object validExceptionHandler(SpecialCharException validError) {
        return CommonOuterResponse.fail(PasCode.DATA_ERROR.code, validError.getMessage());
    }
}
