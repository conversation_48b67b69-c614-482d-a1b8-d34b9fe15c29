package com.epaylinks.efps.pas.config;

import java.util.Properties;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.github.pagehelper.PageHelper;

/** * <AUTHOR> :Liuq 
* @date 创建时间：2017年9月1日 下午4:39:26 
* @version TODO
* @parameter   
* @return  
*/
@Configuration
public class MyBatisConfig {
    @Bean
    public PageHelper pageHelper() {
        PageHelper pageHelper = new PageHelper();
        Properties p = new Properties();
        p.setProperty("offsetAsPageNum", "true");
        p.setProperty("rowBoundsWithCount", "true");
        p.setProperty("reasonable", "true");
        p.setProperty("dialect", "oracle");
        pageHelper.setProperties(p);
        return pageHelper;
    }
}
