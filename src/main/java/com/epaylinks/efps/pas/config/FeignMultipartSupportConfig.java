//package com.epaylinks.efps.pas.config;
//
//import feign.codec.Encoder;
//import feign.form.spring.SpringFormEncoder;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.Primary;
//import org.springframework.context.annotation.Scope;
//
//@Configuration
//public class FeignMultipartSupportConfig {
//
//	/**
//     * Multipart form encoder encoder.
//     *
//     * @return the encoder
//     */
//    @Bean
//    @Primary
//    @Scope("prototype")
//    public Encoder multipartFormEncoder() {
//        return new SpringFormEncoder();
//    }
//
//
//    /**
//     * Multipart logger level feign . logger . level.
//     *
//     * @return the feign . logger . level
//     */
//    @Bean
//    public feign.Logger.Level multipartLoggerLevel() {
//        return feign.Logger.Level.FULL;
//    }
//
//}
