package com.epaylinks.efps.pas.acc.feign.impl;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.pas.common.LogService;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.mch.client.AcsClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Author: <EMAIL>
 * @Date: 2019/9/4 15:10
 * @Description
 * @Version 1.0
 */
@Service
public class AcsServiceImpl {

    @Autowired
    private LogService logService;

    @Autowired
    private AcsClient acsClient;

    @Logable(businessTag = "addUnionQuota")
    public void addUnionQuota(Long amount, String remark, Long userId) {
        try {
            CommonOuterResponse response = acsClient.addUnionQuota(amount, remark, userId);
            if (!CommonOuterResponse.SUCCEE.equals(response.getReturnCode()))
                throw new AppException(response.getReturnCode(), response.getReturnMsg());
        } catch (Exception e) {
            logService.printLog(e);
            throw new AppException(PasCode.SYSTEM_EXCEPTION.code, "调用ACS子系统异常,原因：" + e.getCause());
        }
    }
}
