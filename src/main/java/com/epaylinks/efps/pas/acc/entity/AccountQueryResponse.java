package com.epaylinks.efps.pas.acc.entity;

import com.alibaba.fastjson.JSON;
import com.epaylinks.efps.common.business.CommonOuterResponse;

public class AccountQueryResponse extends CommonOuterResponse{
	
	private String customerCode;
	
	private Long availableBalance;
	
	private Long floatBalance;

	public String getCustomerCode() {
		return customerCode;
	}

	public void setCustomerCode(String customerCode) {
		this.customerCode = customerCode;
	}

	public Long getAvailableBalance() {
		return availableBalance;
	}

	public void setAvailableBalance(Long availableBalance) {
		this.availableBalance = availableBalance;
	}

	public Long getFloatBalance() {
		return floatBalance;
	}

	public void setFloatBalance(Long floatBalance) {
		this.floatBalance = floatBalance;
	}
	
	@Override
	public String toString() {
		// TODO Auto-generated method stub
		return JSON.toJSONString(this);
	}
}
