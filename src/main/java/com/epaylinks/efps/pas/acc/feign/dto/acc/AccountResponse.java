package com.epaylinks.efps.pas.acc.feign.dto.acc;


import com.epaylinks.efps.common.util.page.PageResult;

public class AccountResponse {

    private String code;
    private String errorMsg;
    private String message;
    private String result;
    private PageResult<AccountVo> accountList;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public PageResult<AccountVo> getAccountList() {
        return accountList;
    }

    public void setAccountList(PageResult<AccountVo> accountList) {
        this.accountList = accountList;
    }

}
