package com.epaylinks.efps.pas.acc.service;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.pas.common.PasConstants;
import com.epaylinks.efps.pas.mch.model.AccountNotifyBean;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.epaylinks.efps.common.mykafka.SendToNts;
import com.epaylinks.efps.pas.acc.dao.AcctQuotaRecordMapper;
import com.epaylinks.efps.pas.acc.dto.AcctQuotaNoticeDTO;
import com.epaylinks.efps.pas.acc.entity.AccountQueryResponse;
import com.epaylinks.efps.pas.acc.entity.AcctQuotaRecord;
import com.epaylinks.efps.pas.acc.feign.impl.AccServiceImpl;
import com.epaylinks.efps.pas.common.LogService;
import com.epaylinks.efps.pas.mch.service.feign.CustService;

/**
 * @Author: <EMAIL>
 * @Date: 2019/12/26 17:00
 * @Description 调账结果通知服务
 * @Version 1.0
 */
@Service
public class ChangeAvailableBalanceNoticeService {

    private static final String ASYN_NOTIFY_ORDER_TYPE = "REFILL_AVAILABLE";

    @Autowired
    private LogService logService;

    @Autowired
    private SendToNts sendToNts;

    @Autowired
    private CustService custService;

    @Autowired
    private AccServiceImpl accClient;

    @Autowired
    private AcctQuotaRecordMapper acctQuotaRecordMapper;

    /**
     * 银行转账调账结果通知服务
     *
     * @param record       调账流水
     * @param modifyAmount 调账金额：调增/调减
     */
    public void noticeResultToCustomer(AcctQuotaRecord record, Long modifyAmount) {
        try {
            //调账类型判断
            if (!record.getTransactionNo().startsWith("ACS") && modifyAmount < 0) return;

            if(PasConstants.AcctQuotaRechargeFundType.TRADE_RECHARGE.code.equals(record.getFundType())
                    && PasConstants.AcctQuotaChangeType.RECHARGE.code.equals(record.getChangeType())) {
                //收单充值统一在txs结算成功发送通知。
                return;
            }

            String notifyUrl = null;
            String parentNotifyUrl = null;
            String parentCustomerCode = null;
            //获取商户接收接收地址
            try {
                CommonOuterResponse<AccountNotifyBean> accountNotifyRsp = custService.queryAdjustAccountNotifyUrl(record.getCustomerCode());
                if("0000".equals(accountNotifyRsp.getReturnCode())){
                    AccountNotifyBean accountNotifyBean = accountNotifyRsp.getData();
                    if(accountNotifyBean != null){
                        notifyUrl = accountNotifyBean.getAjustAccountUrl();
                        parentNotifyUrl = accountNotifyBean.getParentAjustAccountUrl();
                        parentCustomerCode = accountNotifyBean.getParentCustomerCode();
                    }
                }
            } catch (Exception e) {
                logService.printLog("调用客户子系统异常：" + e.getMessage());
            }


            AccountQueryResponse response = accClient.accountQueryInner(record.getCustomerCode());
            AcctQuotaNoticeDTO notice = new AcctQuotaNoticeDTO(record, response);
            //设置银行账户名
//            String accountName = acctQuotaRecordMapper.selectAccountName(record.getCustomerCode(), record.getRemark().substring(6));
//            notice.setBankName(accountName);

            String content = JSON.toJSONString(notice);
            if (!StringUtils.isEmpty(notifyUrl)) {
                String notifyNo = record.getTransactionNo();
                if(PasConstants.AcctQuotaRechargeFundType.TRADE_RECHARGE.code.equals(record.getFundType())){
                    notifyNo = notifyNo + "_CZ";
                }
                //消息发送至客户系统
                String[] notifyUrls = notifyUrl.split(";");
                for (int i = 0; i < notifyUrls.length; i++) {
                    sendToNts.notifyCustomer(notifyUrls[i], notifyNo + (i == 0 ? "" : ("_" + i)), ASYN_NOTIFY_ORDER_TYPE, content,
                            record.getCustomerCode());
                }
            }
            if (!StringUtils.isEmpty(parentNotifyUrl)) {
                String notifyNo = record.getTransactionNo() + "_P";
                //消息发送至客户系统
                String[] notifyUrls = parentNotifyUrl.split(";");
                for (int i = 0; i < notifyUrls.length; i++) {
                    sendToNts.notifyCustomer(notifyUrls[i], notifyNo + (i == 0 ? "" : ("_" + i)), ASYN_NOTIFY_ORDER_TYPE, content,
                            parentCustomerCode);
                }
            }


        } catch (Exception e) {
            logService.printLog("调账结果通知异常：" + e);
        }
    }
}
