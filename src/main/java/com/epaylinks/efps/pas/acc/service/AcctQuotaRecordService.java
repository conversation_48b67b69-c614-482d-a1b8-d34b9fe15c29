package com.epaylinks.efps.pas.acc.service;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.business.cust.model.Customer;
import com.epaylinks.efps.common.business.pay.request.UserType;
import com.epaylinks.efps.common.exception.AppAssert;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.tool.error.prediction.EpAssert;
import com.epaylinks.efps.common.tool.time.Timex;
import com.epaylinks.efps.common.util.DateUtils;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.pas.acc.controller.request.AcctQuotaAuditRequest;
import com.epaylinks.efps.pas.acc.controller.request.AcctQuotaModifyRequest;
import com.epaylinks.efps.pas.acc.dao.AcctQuotaRecordMapper;
import com.epaylinks.efps.pas.acc.dto.AcctQuotaRecordDTO;
import com.epaylinks.efps.pas.acc.entity.AcctQuotaRecord;
import com.epaylinks.efps.pas.acc.feign.AccClient;
import com.epaylinks.efps.pas.acc.feign.CumClient;
import com.epaylinks.efps.pas.acc.feign.PayClient;
import com.epaylinks.efps.pas.acc.feign.dto.acc.DirectVoucherRequest;
import com.epaylinks.efps.pas.acc.feign.dto.acc.DirectVoucherResponse;
import com.epaylinks.efps.pas.acc.feign.impl.AcsServiceImpl;
import com.epaylinks.efps.pas.acl.dao.UserMapper;
import com.epaylinks.efps.pas.acl.po.User;
import com.epaylinks.efps.pas.common.LogService;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.common.PasConstants;
import com.epaylinks.efps.pas.mch.client.CustClient;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class AcctQuotaRecordService {

    @Autowired
    private AcctQuotaRecordMapper acctQuotaRecordMapper;
    @Autowired
    private AccClient accClient;

    @Autowired
    private PayClient payClient;
    @Autowired
    private LogService logService;
    @Autowired
    private CumClient cumClient;
    @Autowired
    private UserMapper userDAO;
    @Autowired
    private AcctQuotaRecordService self;

    @Autowired
    AcsServiceImpl acsService;

    @Autowired
    private CustClient custClient;

    @Autowired
    private RcService rcService;

    @Autowired
    private ChangeAvailableBalanceNoticeService noticeService;

    @Autowired
    private PermissionService permissionService;

    private boolean isRealTime(AcctQuotaRecord record) {
        return record.getSettCycleRuleCode() == null || "RealTime".equals(record.getSettCycleRuleCode());
    }

    @Logable(businessTag = "pageQuery")
    public PageResult<AcctQuotaRecordDTO> pageQuery(boolean download, int pageNum, int pageSize, String timeBegin, String timeEnd,
                                                    String changeType, String customerCode, String transactionNo, String fundType,
                                                    String auditState, String acctState, String debtorAccount, String debtorAccountName,
                                                    String feeCustomerCode) {
        //去掉前端传入的空字符串
        timeEnd = StringUtils.isEmpty(timeEnd) ? null : timeEnd;
        timeBegin = StringUtils.isEmpty(timeBegin) ? null : timeBegin;
        changeType = StringUtils.isEmpty(changeType) ? null : changeType;
        customerCode = StringUtils.isEmpty(customerCode) ? null : customerCode;
        feeCustomerCode = StringUtils.isEmpty(feeCustomerCode) ? null : feeCustomerCode;
        transactionNo = StringUtils.isEmpty(transactionNo) ? null : transactionNo;
        fundType = StringUtils.isEmpty(fundType) ? null : fundType;
        auditState = StringUtils.isEmpty(auditState) ? null : auditState;
        acctState = StringUtils.isEmpty(acctState) ? null : acctState;

        // 转化日期参数
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
        Date beginTime = null;
        Date endTime = null;
        if (StringUtils.isNotBlank(timeBegin)) {
            try {
                beginTime = formatter.parse(timeBegin);
            } catch (ParseException e) {
                logService.printLog(e);
                throw new AppException(PasCode.ERROR_PARSEDATE.code);
            }
        }
        if (StringUtils.isNotBlank(timeEnd)) {
            try {
                endTime = formatter.parse(timeEnd);
            } catch (ParseException e) {
                logService.printLog(e);
                throw new AppException(PasCode.ERROR_PARSEDATE.code);
            }
        }

        PermissionService.UserSearchCondition userSearchCondition = permissionService.getUserSearchCondition();

        // 查询总数
        int total = acctQuotaRecordMapper.count(beginTime, endTime, changeType, customerCode, transactionNo, fundType, auditState, acctState, debtorAccountName, debtorAccount,
                feeCustomerCode, userSearchCondition);
        // 查询列表
        List<AcctQuotaRecord> recordList;
        if (download) {
            recordList = acctQuotaRecordMapper.notPageQuery(beginTime, endTime, changeType, customerCode, transactionNo, fundType, auditState, acctState, debtorAccountName, debtorAccount,
                    feeCustomerCode, userSearchCondition);
        } else {
            int beginRowNo = (pageNum - 1) * pageSize + 1;
            int endRowNo = pageNum * pageSize;
            recordList = acctQuotaRecordMapper.pageQuery(beginRowNo, endRowNo, beginTime, endTime, changeType, customerCode, transactionNo, fundType, auditState, acctState, debtorAccountName, debtorAccount,
                    feeCustomerCode, userSearchCondition);
        }
        // 转化为前端所需
        Map<String, String> mapOfCustomerCodeAndName = self.getMapOfCustomerCodeAndName(recordList);// 关联查询CUM，得到客户编码与客户名称的映射
        List<AcctQuotaRecordDTO> dtoList = new ArrayList<>();
        for (AcctQuotaRecord record : recordList) {
            AcctQuotaRecordDTO dto = AcctQuotaRecordDTO.createDtoByEntity(record);
            dto.setCustomerName(mapOfCustomerCodeAndName.get(dto.getCustomerCode()));
            if (record.getFeeCustomerCode() != null) {
                dto.setFeeCustomerName(mapOfCustomerCodeAndName.get(record.getFeeCustomerCode()));
            }
            User user = userDAO.selectByPrimaryKey(dto.getCreateUser());
            String createUserName = "";
            if (null != user) {
                createUserName = StringUtils.isNotEmpty(user.getRealName()) ? user.getRealName() : user.getName();
            }
            dto.setCreateUserName(createUserName);
            User auditUser = userDAO.selectByPrimaryKey(dto.getAuditUser());
            String auditUserName = "";
            if (null != auditUser) {
                auditUserName = StringUtils.isNotEmpty(auditUser.getRealName()) ? auditUser.getRealName() : auditUser.getName();
            }
            dto.setAuditUserName(auditUserName);
            dtoList.add(dto);
        }
        // 返回
        PageResult<AcctQuotaRecordDTO> pageResult = new PageResult<>();
        pageResult.setTotal(total);
        pageResult.setRows(dtoList);
        return pageResult;
    }

    @Logable(businessTag = "viewRecord")
    public AcctQuotaRecordDTO viewRecord(Long id) {
        AcctQuotaRecord record = acctQuotaRecordMapper.selectByPrimaryKey(id);
        if (record == null) {
            throw new AppException(PasCode.RECORD_NOT_EXIST.code, PasCode.RECORD_NOT_EXIST.message);
        }
        AcctQuotaRecordDTO dto = AcctQuotaRecordDTO.createDtoByEntity(record);
        return dto;
    }

    @Logable(businessTag = "modifyRecord")
    public void modifyRecord(AcctQuotaModifyRequest request, Long curUserId) {
        AcctQuotaRecord record = acctQuotaRecordMapper.selectByPrimaryKey(request.getId());
        if (record == null) {
            throw new AppException(PasCode.RECORD_NOT_EXIST.code, PasCode.RECORD_NOT_EXIST.message);
        }
        if (!PasConstants.AcctQuotaAuditState.FAIL.code.equals(record.getAuditState())) {
            throw new AppException(PasCode.MODIFY_NO_LIMIT.code, PasCode.MODIFY_NO_LIMIT.message + "（仅审批拒绝的才能修改）");
        }

        BeanUtils.copyProperties(request, record);
        record.setUpdateUser(curUserId);
        record.setUpdateTime(new Date());
        record.setAuditState(PasConstants.AcctQuotaAuditState.WAITING.code);
        try {
            acctQuotaRecordMapper.updateByPrimaryKey(record);
        } catch (Exception e) {
            logService.printLog(e);
            throw new AppException(PasCode.ERROR_SAVE_RECORD.code, PasCode.ERROR_SAVE_RECORD.message);
        }
    }

    private boolean isAddUnionQuotaTag(AcctQuotaRecord record) {
        return record.getModifyAmount() > 0 &&
                PasConstants.AcctQuotaAcctState.SUCCESS.code.equals(record.getAcctState()) &&
                record.getTransactionNo().startsWith("ACS");
    }

    @Logable(businessTag = "auditRecord")
    @Transactional
    public void auditRecord(AcctQuotaAuditRequest request, Long curUserId, String userType) throws Exception {

        AcctQuotaRecord record = acctQuotaRecordMapper.selectByPrimaryKey(request.getId());
        if (record == null) {
            throw new AppException(PasCode.RECORD_NOT_EXIST.code, PasCode.RECORD_NOT_EXIST.message);
        }
        BeanUtils.copyProperties(request, record);
        record.setAuditUser(curUserId);
        record.setAuditTime(new Date());

        if (!isRealTime(record)) { //非实时结算，计算结算时间
            String transactionDate = Timex.ofDate(record.getCreateTime()).to(Timex.Format.yyyyMMdd);
            String settDate = acctQuotaRecordMapper.calcSettDate(transactionDate, record.getSettCycleRuleCode());
            record.setSettTime(Timex.of(settDate, Timex.Format.yyyyMMdd).toDate());
        }

        try {
            acctQuotaRecordMapper.updateByPrimaryKey(record);
        } catch (Exception e) {
            logService.printLog(e);
            throw new AppException(PasCode.ERROR_SAVE_RECORD.code, PasCode.ERROR_SAVE_RECORD.message);
        }
        //执行过的数据不执行，防止重复调账
        if (PasConstants.AcctQuotaAcctState.SUCCESS.code.equals(record.getAcctState()))
            throw new AppException(PasCode.ERROR_ACCT_QUOTA_EXECUTED.code, PasCode.ERROR_ACCT_QUOTA_EXECUTED.message);

        //仅当审核通过才执行调账
        if (PasConstants.AcctQuotaAuditState.SUCCESS.code.equals(record.getAuditState())) {
            //调账类型业务逻辑
            Long modifyAmount = record.getModifyAmount();

            // 同步到ACC
            try {
                Customer customer = custClient.queryCustomerByCustomerNo(record.getCustomerCode());
                if (PasConstants.AcctQuotaChangeType.WITHDRAW.code.equals(record.getChangeType())) {
                    //判断商户状态如果为“冻结”或“止付”，系统提款失败
                    if ((customer == null || customer.getStatus() == 2 || customer.getStatus() == 4) && !UserType.PAS_USER.code.equals(userType)) {
                        record.setAcctErrorCode(PasCode.CUSTOMER_STATUS_EXCEPTION.code);
                        record.setAcctErrorMsg(PasCode.CUSTOMER_STATUS_EXCEPTION.message);
                        record.setAcctState(PasConstants.AcctQuotaAcctState.FAIL.code);
                        throw new AppException(PasCode.CUSTOMER_STATUS_EXCEPTION.code, PasCode.CUSTOMER_STATUS_EXCEPTION.message);
                    }
                } else {
                    //判断商户状态如果为“注销”或“禁止入金”或“冻结”，则不给该商户自动加额
                    if (customer == null || customer.getStatus() == 2 || customer.getStatus() == 3 || customer.getStatus() == 5) {
                        record.setAcctErrorCode(PasCode.CUSTOMER_STATUS_EXCEPTION.code);
                        record.setAcctErrorMsg(PasCode.CUSTOMER_STATUS_EXCEPTION.message);
                        record.setAcctState(PasConstants.AcctQuotaAcctState.FAIL.code);
                        throw new AppException(PasCode.CUSTOMER_STATUS_EXCEPTION.code, PasCode.CUSTOMER_STATUS_EXCEPTION.message);
                    }
                }
                if (PasConstants.AcctQuotaRechargeFundType.TRADE_RECHARGE.code.equals(record.getFundType())
                        && PasConstants.AcctQuotaChangeType.RECHARGE.code.equals(record.getChangeType())) {

                    if (record.getCreateTime().compareTo(DateUtils.getDateAfter(-60 * 60 * 24 * 10)) < 0) {
                        throw new AppException(PasCode.OVER_TIME.code, PasCode.OVER_TIME.message);
                    }

                    CommonOuterResponse<String> payResponse = payClient.payReAccount(record.getTransactionNo());
                    if (CommonOuterResponse.SUCCEE.equals(payResponse.getReturnCode())) {
                        String acctVoucherNo = payResponse.getData();
                        record.setAcctVoucherNo(acctVoucherNo);
                        record.setAcctErrorCode(PasConstants.AcctQuotaAcctState.SUCCESS.code);
                        record.setAcctErrorMsg(payResponse.getReturnMsg());
                        record.setAcctState(PasConstants.AcctQuotaAcctState.SUCCESS.code);

                    } else {
                        record.setAcctErrorCode(payResponse.getReturnCode());
                        String returnMsg = PasCode.ERROR_SAVE_RECORD.message;
                        if (null != payResponse.getReturnMsg())
                            returnMsg = payResponse.getReturnMsg();
                        record.setAcctErrorMsg(returnMsg);
                        record.setAcctState(PasConstants.AcctQuotaAcctState.FAIL.code);
                        throw new AppException(payResponse.getReturnCode(), returnMsg);
                    }

                } else {
                    if (isRealTime(record) || record.getSettTime().before(new Date())) {
                        accounting(record);
                    }
                }

            } catch (Exception e) {
                logService.printLog(e);
                if (e instanceof AppException) {
                    record.setAcctErrorCode(((AppException) e).getErrorCode());
                    record.setAcctErrorMsg(((AppException) e).getErrorMsg());
                    record.setAcctState(PasConstants.AcctQuotaAcctState.FAIL.code);
                    throw e;
                } else {
                    record.setAcctErrorCode(PasCode.ERROR_SAVE_RECORD.code);
                    record.setAcctErrorMsg(PasCode.ERROR_SAVE_RECORD.message);
                    record.setAcctState(PasConstants.AcctQuotaAcctState.FAIL.code);
                    throw new AppException(PasCode.ERROR_SAVE_RECORD.code, PasCode.ERROR_SAVE_RECORD.message);
                }
            } finally {
                acctQuotaRecordMapper.updateByPrimaryKey(record);
                try {
                    noticeService.noticeResultToCustomer(record, modifyAmount);
                    if (isAddUnionQuotaTag(record)) {
                        acsService.addUnionQuota(record.getAmount(), record.getTransactionNo() + "；" + record.getRemark(), curUserId);
                    }
                } catch (Exception e) {
                    logService.printLog(e);
                }
                rcService.sendRcResult(record);
            }
        }
    }

    public void settle() {
        List<Long> ids = acctQuotaRecordMapper.selectUnSettRecord();
        for (Long id : ids) {
            try {
                logService.printLog(String.format("[%d]结算记账", id));
                accounting(id);
            } catch (Exception e) {
                logService.printLog(String.format("[%d]结算记账异常", id));
                logService.printLog(e);
            }
        }
    }

    private void accounting(Long recordId) {
        AcctQuotaRecord record = acctQuotaRecordMapper.selectByPrimaryKey(recordId);
        AppAssert.notNull(record, PasCode.RECORD_NOT_EXIST);
        accounting(record);
        acctQuotaRecordMapper.updateByPrimaryKey(record);
    }

    private void accounting(AcctQuotaRecord record) {
        DirectVoucherRequest voucherRequest = new DirectVoucherRequest();
        voucherRequest.setTransactionNo(record.getTransactionNo());
        voucherRequest.setTransactionType(record.getTransactionType());
        voucherRequest.setPayType(DirectVoucherRequest.PayType.RECHARGE);
        voucherRequest.setBusinessCode(record.getBusinessCode());

        voucherRequest.addDetail(record.getCustomerCode(), record.getModifyAmount(), DirectVoucherRequest.AccountType.JY, DirectVoucherRequest.BalanceType.USEFUL_AMOUNT);

        if (record.getFeeCustomerCode() != null && !record.getFeeCustomerCode().equals(record.getCustomerCode())) {
            voucherRequest.addDetail(record.getFeeCustomerCode(), -record.getProcedureFee(), DirectVoucherRequest.AccountType.JY, DirectVoucherRequest.BalanceType.USEFUL_AMOUNT);
        }
        DirectVoucherResponse response = accClient.direct(voucherRequest);
        if (response.isSuccess()) {
            record.setAcctVoucherNo(response.getAccountVoucherNo());
            record.setAcctErrorCode(PasConstants.AcctQuotaAcctState.SUCCESS.code);
            record.setAcctErrorMsg(response.getAccountVoucherNo());
            record.setAcctState(PasConstants.AcctQuotaAcctState.SUCCESS.code);
        } else {
            record.setAcctErrorCode(response.getCode());
            if ("0423".equals(response.getCode()) && record.getFeeCustomerCode() != null) {
                record.setAcctErrorMsg("手续费扣减方账户余额不足");
            } else {
                record.setAcctErrorMsg(response.getMessage() == null ? PasCode.ERROR_ACCT_QUOTA_RESET.message : response.getMessage());
            }
            record.setAcctState(PasConstants.AcctQuotaAcctState.FAIL.code);
            throw new AppException(record.getAcctErrorCode(), record.getAcctErrorMsg());
        }
    }

    /**
     * 关联查询CUM，得到客户编码与客户名称的映射
     *
     * @param recordList
     * @return
     */
    @Logable(businessTag = "getMapOfCustomerCodeAndName")
    public Map<String, String> getMapOfCustomerCodeAndName(List<AcctQuotaRecord> recordList) {
        Map<String, String> mapOfCustomerCodeAndName = new HashMap<>();
        // 汇总所有customerCode并查询
        List<String> customerCodeList = new ArrayList<>();
        for (AcctQuotaRecord r : recordList) {
            String customerCode = r.getCustomerCode();
            if (!customerCodeList.contains(customerCode)) {
                customerCodeList.add(customerCode);
            }
            String feeCustomerCode = r.getFeeCustomerCode();
            if (!customerCodeList.contains(feeCustomerCode)) {
                customerCodeList.add(feeCustomerCode);
            }
        }
        if (customerCodeList != null && !customerCodeList.isEmpty()) {
            mapOfCustomerCodeAndName = cumClient.queryMapOfCustomerCodeAndName(customerCodeList);
        }
        return mapOfCustomerCodeAndName;
    }

    @Logable(businessTag = "notifyCustomer")
    public void notifyCustomer(Long id) {
        AcctQuotaRecord acctQuotaRecord = acctQuotaRecordMapper.selectByPrimaryKey(id);
        EpAssert.notNull(acctQuotaRecord, PasCode.RECORD_NOT_EXIST);
        EpAssert.state(PasConstants.AcctQuotaAuditState.SUCCESS.code.equals(acctQuotaRecord.getAuditState()), PasCode.RECORD_NOT_EXIST.msg("记录未审核通过"));
        noticeService.noticeResultToCustomer(acctQuotaRecord, acctQuotaRecord.getModifyAmount());
    }
}