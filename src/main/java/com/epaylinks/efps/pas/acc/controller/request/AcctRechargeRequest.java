package com.epaylinks.efps.pas.acc.controller.request;

public class AcctRechargeRequest {
    /**
     * ID
     */
    private Long id;

    /**
     * 客户编号
     */
    private String customerCode;

    /**
     * 缴费的资金类型:1银行转账,2商户赔付,3商户退款,4商户补款
     */
    private String fundType;

    /**
     * 金额(分)
     */
    private Long amount;

    /**
     * 缴费时间
     */
    private String operateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 附件在文件系统的uniqueId
     */
    private String attachment;
    
    /**
     * 原业务订单号
     */
    private String oriTransactionNo;
    /**
     * 卡号报备，0未报备，1已报备
     */
    private String backUpStatus;
    /**
     * 自动审核 0-否，1-是
     */
    private String nAutoReview;
    /**
     * 审批状态：00审批通过,01审批拒绝,02待审批
     */
    private String auditState;

    private String acctState;

    private Long procedureFee;

    private Long rechargeAmount;
    private String businessCode;

    /**
     * 银行卡号
     */
    private String debtorAccount;

    /**
     * 银行账户名称
     */
    private String debtorAccountName;

    private String feeCustomerCode;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getFundType() {
        return fundType;
    }

    public void setFundType(String fundType) {
        this.fundType = fundType;
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public String getOperateTime() {
        return operateTime;
    }

    public void setOperateTime(String operateTime) {
        this.operateTime = operateTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getAttachment() {
        return attachment;
    }

    public void setAttachment(String attachment) {
        this.attachment = attachment;
    }

	public String getOriTransactionNo() {
		return oriTransactionNo;
	}

	public void setOriTransactionNo(String oriTransactionNo) {
		this.oriTransactionNo = oriTransactionNo;
	}

    public String getBackUpStatus() {
        return backUpStatus;
    }

    public void setBackUpStatus(String backUpStatus) {
        this.backUpStatus = backUpStatus;
    }

    public String getnAutoReview() {
        return nAutoReview;
    }

    public void setnAutoReview(String nAutoReview) {
        this.nAutoReview = nAutoReview;
    }

    public String getAuditState() {
        return auditState;
    }

    public void setAuditState(String auditState) {
        this.auditState = auditState;
    }

    public String getAcctState() {
        return acctState;
    }

    public void setAcctState(String acctState) {
        this.acctState = acctState;
    }

    public Long getProcedureFee() {
        return procedureFee;
    }

    public void setProcedureFee(Long procedureFee) {
        this.procedureFee = procedureFee;
    }

    public Long getRechargeAmount() {
        return rechargeAmount;
    }

    public void setRechargeAmount(Long rechargeAmount) {
        this.rechargeAmount = rechargeAmount;
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }

    public String getDebtorAccount() {
        return debtorAccount;
    }

    public void setDebtorAccount(String debtorAccount) {
        this.debtorAccount = debtorAccount;
    }

    public String getDebtorAccountName() {
        return debtorAccountName;
    }

    public void setDebtorAccountName(String debtorAccountName) {
        this.debtorAccountName = debtorAccountName;
    }

    public String getFeeCustomerCode() {
        return feeCustomerCode;
    }

    public void setFeeCustomerCode(String feeCustomerCode) {
        this.feeCustomerCode = feeCustomerCode;
    }
}
