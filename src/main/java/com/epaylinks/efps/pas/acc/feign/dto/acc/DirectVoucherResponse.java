package com.epaylinks.efps.pas.acc.feign.dto.acc;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/5/21 15:09
 */
public class DirectVoucherResponse extends BaseResponse {
    /**
     * 记账凭证号，成功或重复请求时必须
     */
    private String accountVoucherNo;

    public String getAccountVoucherNo() {
        return accountVoucherNo;
    }

    public void setAccountVoucherNo(String accountVoucherNo) {
        this.accountVoucherNo = accountVoucherNo;
    }
}
