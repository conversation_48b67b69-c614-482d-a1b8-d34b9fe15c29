package com.epaylinks.efps.pas.acc.service;

import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.pas.acc.controller.request.AcctWithdrawRequest;
import com.epaylinks.efps.pas.acc.dao.AcctQuotaRecordMapper;
import com.epaylinks.efps.pas.acc.entity.AcctQuotaRecord;
import com.epaylinks.efps.pas.common.LogService;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.common.PasConstants;
import com.epaylinks.efps.pas.common.TransactionNoUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class AcctQuotaWithdrawService {

    @Autowired
    private AcctQuotaRecordMapper acctQuotaRecordMapper;
    @Autowired
    private TransactionNoUtil transactionNoUtil;
    @Autowired
    private SequenceService sequenceService;
    @Autowired
    private LogService logService;

    @Logable(businessTag = "withdraw")
    public void withdraw(AcctWithdrawRequest request, Long curUserId) {
        AcctQuotaRecord record = new AcctQuotaRecord();
        BeanUtils.copyProperties(request, record);
        record.setId(sequenceService.nextValue("PAS_AcctQuotaRecord"));
        record.setChangeType(PasConstants.AcctQuotaChangeType.WITHDRAW.code);
        record.setCreateUser(curUserId);
        record.setCreateTime(new Date());
        record.setAuditState(PasConstants.AcctQuotaAuditState.WAITING.code);
        record.setAcctState(PasConstants.AcctQuotaAcctState.INIT.code);
        String transactionNo = transactionNoUtil.generateTransactionNo("PAS_AcctQuotaRecharge", "TKDJ");
        record.setTransactionNo(transactionNo);
        try {
            acctQuotaRecordMapper.insert(record);
        } catch (Exception e) {
            logService.printLog(e);
            throw new AppException(PasCode.ERROR_SAVE_RECORD.code, PasCode.ERROR_SAVE_RECORD.message);
        }
    }
}