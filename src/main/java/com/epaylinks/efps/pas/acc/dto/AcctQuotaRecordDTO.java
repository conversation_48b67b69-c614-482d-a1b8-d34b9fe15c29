package com.epaylinks.efps.pas.acc.dto;

import com.epaylinks.efps.common.datadownload.annotation.FieldAnnotation;
import com.epaylinks.efps.pas.acc.entity.AcctQuotaRecord;
import com.epaylinks.efps.pas.common.PasConstants;
import com.epaylinks.efps.pas.pas.util.BigDecimalUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.beans.BeanUtils;

import java.util.Date;

@ApiModel(value = "AcctQuota", description = "调账对象")
public class AcctQuotaRecordDTO {
    /**
     * ID
     */
    @ApiModelProperty(value = "ID", dataType = "Long")
    private Long id;

    /**
     * 易票联流水号
     */
    @ApiModelProperty(value = "易票联流水号", dataType = "String")
    @FieldAnnotation(fieldName = "易票联流水号")
    private String transactionNo;

    /**
     * 客户编号
     */
    @ApiModelProperty(value = "客户编号", dataType = "String")
    @FieldAnnotation(fieldName = "商户编号")
    private String customerCode;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称", dataType = "String")
    @FieldAnnotation(fieldName = "商户名称")
    private String customerName;

    /**
     * 调整类型:1缴费,2提款
     */
    @ApiModelProperty(value = "调整类型:1缴费,2提款", dataType = "String")
    private String changeType;

    /**
     * 缴费/提款的资金类型:1银行转账,2现金
     */
    @FieldAnnotation(fieldName = "缴费类型")
    @ApiModelProperty(value = "缴费的资金类型:1银行转账,2现金;提款的资金类型:1银行转账,2现金,3跨境结算,4跨境手续费", dataType = "String")
    private String fundType;

    /**
     * 金额(分)
     */
    @ApiModelProperty(value = "金额(分)", dataType = "Long")
    private Long amount;

    private Long rechargeAmount;

    private Long procedureFee;


    @FieldAnnotation(fieldName = "充值金额（元）")
    private String rechargeAmountView;

    @FieldAnnotation(fieldName = "手续费（元）")
    private String procedureFeeView;

    public String getRechargeAmountView() {
        return rechargeAmountView;
    }

    public void setRechargeAmountView(String rechargeAmountView) {
        this.rechargeAmountView = rechargeAmountView;
    }

    public void setProcedureFeeView(String procedureFeeView) {
        this.procedureFeeView = procedureFeeView;
    }

    public void setRechargeAmountView(Long rechargeAmountView) {
        if(rechargeAmountView != null){
            this.rechargeAmountView = BigDecimalUtil.div(String.valueOf(rechargeAmountView), "100", 2);
        }

    }

    public String getProcedureFeeView() {
        return procedureFeeView;
    }

    public void setProcedureFeeView(Long procedureFeeView) {
        if(procedureFeeView != null){
            this.procedureFeeView = BigDecimalUtil.div(String.valueOf(procedureFeeView), "100", 2);
        }
    }

    @FieldAnnotation(fieldName = "缴费金额（元）")
    private String viewAmount;

    public String getViewAmount() {
        return viewAmount;
    }

    public void setViewAmount(String viewAmount) {
        this.viewAmount = viewAmount;
    }

    public void setViewAmount(Long amount) {
        this.viewAmount = BigDecimalUtil.div(String.valueOf(amount), "100", 2);
    }

    /**
     * 缴费/提款时间
     */
    @ApiModelProperty(value = "缴费/提款时间，格式为年月日时分秒，如20190614235959", dataType = "String")
    private String operateTime;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", dataType = "String")
    @FieldAnnotation(fieldName = "备注")
    private String remark;

    /**
     * 附件在文件系统的uniqueId
     */
    @ApiModelProperty(value = "附件在文件系统的uniqueId", dataType = "String")
    private String attachment;

    /**
     * 创建人ID
     */
    @ApiModelProperty(value = "创建人ID", dataType = "Long")
    private Long createUser;

    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名", dataType = "String")
    @FieldAnnotation(fieldName = "创建人姓名")
    private String createUserName;

    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @FieldAnnotation(fieldName = "创建时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间，格式：yyyy-MM-dd HH:mm:ss", dataType = "Date")
    private Date createTime;

    /**
     * 最近更新人ID
     */
    @ApiModelProperty(value = "最近更新人ID", dataType = "Long")
    private Long updateUser;

    /**
     * 最近更新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "最近更新时间，格式：yyyy-MM-dd HH:mm:ss", dataType = "Date")
    private Date updateTime;

    /**
     * 审批状态：00审批通过,01审批拒绝,02待审批
     */
    @ApiModelProperty(value = "审批状态：00审批通过,01审批拒绝,02待审批", dataType = "String")
    private String auditState;

    @FieldAnnotation(fieldName = "状态")
    private String auditStateName;

    /**
     * 审批意见
     */
    @ApiModelProperty(value = "审批意见", dataType = "String")
    private String auditComment;

    /**
     * 审批人ID
     */
    @ApiModelProperty(value = "审批人ID", dataType = "Long")
    private Long auditUser;

    /**
     * 审批时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @FieldAnnotation(fieldName = "审批时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审批时间，格式：yyyy-MM-dd HH:mm:ss", dataType = "Date")
    private Date auditTime;

    /**
     * 记账状态：00成功,01失败,02未记账
     */
    @ApiModelProperty(value = "记账状态：00成功,01失败,02未记账", dataType = "String")
    private String acctState;

    /**
     * 记账凭证(记账成功时ACC返回)
     */
    @ApiModelProperty(value = "记账凭证(记账成功时ACC返回)", dataType = "String")
    private String acctVoucherNo;

    /**
     * 记账错误码
     */
    @ApiModelProperty(value = "记账错误码(账户接口返回)", dataType = "String")
    private String acctErrorCode;

    /**
     * 记账错误信息
     */
    @ApiModelProperty(value = "记账错误信息(账户接口返回)", dataType = "String")
    @FieldAnnotation(fieldName = "结果说明")
    private String acctErrorMsg;


    @ApiModelProperty(value = "审批人", dataType = "String")
    @FieldAnnotation(fieldName = "审批人")
    private String auditUserName;

    /**
     * 业务员
     */
    @ApiModelProperty(value = "业务员", dataType = "String")
    @FieldAnnotation(fieldName = "业务员")
    private String salesMan;

    /**
     * 卡号报备，0未报备，1已报备
     */
    private String backUpStatus;

    private String businessCode;

    /**
     * 付款人银行账号
     */
    @ApiModelProperty(value = "付款人银行账号", dataType = "String")
    @FieldAnnotation(fieldName = "付款人银行账号")
    private String debtorAccount;

    /**
     * 付款人账户名
     */
    @ApiModelProperty(value = "付款人账户名", dataType = "String")
    @FieldAnnotation(fieldName = "付款人账户名")
    private String debtorAccountName;


    /**
     * 付款人账户名
     */
    @ApiModelProperty(value = "手续费扣减方", dataType = "String")
    @FieldAnnotation(fieldName = "手续费扣减方")
    private String feeCustomerCode;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "手续费扣减方名称", dataType = "String")
    @FieldAnnotation(fieldName = "手续费扣减方名称")
    private String feeCustomerName;

    @ApiModelProperty(value = "到账周期", dataType = "String")
    @FieldAnnotation(fieldName = "到账周期")
    private String settCycleRuleCode;

    public static AcctQuotaRecordDTO createDtoByEntity(AcctQuotaRecord record) {
        AcctQuotaRecordDTO dto = new AcctQuotaRecordDTO();
        BeanUtils.copyProperties(record, dto);
        dto.setChangeType(PasConstants.AcctQuotaChangeType.getCommentByCode(record.getChangeType()));

        if (PasConstants.AcctQuotaChangeType.RECHARGE.code.equals(record.getChangeType()))
            dto.setFundType(PasConstants.AcctQuotaRechargeFundType.getCommentByCode(record.getFundType()));
        if (PasConstants.AcctQuotaChangeType.WITHDRAW.code.equals(record.getChangeType()))
            dto.setFundType(PasConstants.AcctQuotaWithdrawFundType.getCommentByCode(record.getFundType()));

        dto.setAcctState(PasConstants.AcctQuotaAcctState.getCommentByCode(record.getAcctState()));
        dto.setAuditStateName(PasConstants.AcctQuotaAuditState.getCommentByCode(record.getAuditState()));
        dto.setViewAmount(record.getAmount());
        dto.setProcedureFeeView(record.getProcedureFee());
        dto.setRechargeAmountView(record.getRechargeAmount());
        dto.setSettCycleRuleCode(record.getSettCycleRuleCode());
        return dto;
    }

    public String getAuditStateName() {
        return auditStateName;
    }

    public void setAuditStateName(String auditStateName) {
        this.auditStateName = auditStateName;
    }

    public void setAuditUserName(String auditUserName) {
        this.auditUserName = auditUserName;
    }


    public String getAuditUserName() {
        return auditUserName;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTransactionNo() {
        return transactionNo;
    }

    public void setTransactionNo(String transactionNo) {
        this.transactionNo = transactionNo;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getChangeType() {
        return changeType;
    }

    public void setChangeType(String changeType) {
        this.changeType = changeType;
    }

    public String getFundType() {
        return fundType;
    }

    public void setFundType(String fundType) {
        this.fundType = fundType;
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public String getOperateTime() {
        return operateTime;
    }

    public void setOperateTime(String operateTime) {
        this.operateTime = operateTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getAttachment() {
        return attachment;
    }

    public void setAttachment(String attachment) {
        this.attachment = attachment;
    }

    public Long getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getAuditState() {
        return auditState;
    }

    public void setAuditState(String auditState) {
        this.auditState = auditState;
    }

    public String getAuditComment() {
        return auditComment;
    }

    public void setAuditComment(String auditComment) {
        this.auditComment = auditComment;
    }

    public Long getAuditUser() {
        return auditUser;
    }

    public void setAuditUser(Long auditUser) {
        this.auditUser = auditUser;
    }

    public Date getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(Date auditTime) {
        this.auditTime = auditTime;
    }

    public String getAcctState() {
        return acctState;
    }

    public void setAcctState(String acctState) {
        this.acctState = acctState;
    }

    public String getAcctVoucherNo() {
        return acctVoucherNo;
    }

    public void setAcctVoucherNo(String acctVoucherNo) {
        this.acctVoucherNo = acctVoucherNo;
    }

    public String getAcctErrorCode() {
        return acctErrorCode;
    }

    public void setAcctErrorCode(String acctErrorCode) {
        this.acctErrorCode = acctErrorCode;
    }

    public String getAcctErrorMsg() {
        return acctErrorMsg;
    }

    public void setAcctErrorMsg(String acctErrorMsg) {
        this.acctErrorMsg = acctErrorMsg;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getSalesMan() {
        return salesMan;
    }

    public void setSalesMan(String salesMan) {
        this.salesMan = salesMan;
    }

    public String getBackUpStatus() {
        return backUpStatus;
    }

    public void setBackUpStatus(String backUpStatus) {
        this.backUpStatus = backUpStatus;
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }

    public Long getProcedureFee() {
        return procedureFee;
    }

    public void setProcedureFee(Long procedureFee) {
        this.procedureFee = procedureFee;
    }

    public Long getRechargeAmount() {
        return rechargeAmount;
    }

    public void setRechargeAmount(Long rechargeAmount) {
        this.rechargeAmount = rechargeAmount;
    }

    public String getDebtorAccount() {
        return debtorAccount;
    }

    public void setDebtorAccount(String debtorAccount) {
        this.debtorAccount = debtorAccount;
    }

    public String getDebtorAccountName() {
        return debtorAccountName;
    }

    public void setDebtorAccountName(String debtorAccountName) {
        this.debtorAccountName = debtorAccountName;
    }

    public String getFeeCustomerCode() {
        return feeCustomerCode;
    }

    public void setFeeCustomerCode(String feeCustomerCode) {
        this.feeCustomerCode = feeCustomerCode;
    }

    public String getFeeCustomerName() {
        return feeCustomerName;
    }

    public void setFeeCustomerName(String feeCustomerName) {
        this.feeCustomerName = feeCustomerName;
    }

    public String getSettCycleRuleCode() {
        return settCycleRuleCode;
    }

    public void setSettCycleRuleCode(String settCycleRuleCode) {
        this.settCycleRuleCode = settCycleRuleCode;
    }
}