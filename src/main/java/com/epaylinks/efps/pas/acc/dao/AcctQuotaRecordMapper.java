package com.epaylinks.efps.pas.acc.dao;

import com.epaylinks.efps.pas.acc.entity.AcctQuotaRecord;
import com.epaylinks.efps.pas.acc.service.PermissionService;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

@Mapper
public interface AcctQuotaRecordMapper {

    int deleteByPrimaryKey(Long id);

    int insert(AcctQuotaRecord record);

    int insertSelective(AcctQuotaRecord record);

    AcctQuotaRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(AcctQuotaRecord record);

    int updateByPrimaryKey(AcctQuotaRecord record);

    String selectAccountName(@Param("customerCode") String customerCode, @Param("accountCode") String accountCode);

    int count(@Param("beginTime") Date beginTime, @Param("endTime") Date endTime, @Param("changeType") String changeType, @Param("customerCode") String customerCode, @Param("transactionNo") String transactionNo, @Param("fundType") String fundType, @Param("auditState") String auditState, @Param("acctState") String acctState,@Param("debtorAccountName") String debtorAccountName, @Param("debtorAccount") String debtorAccount,
              @Param("feeCustomerCode") String feeCustomerCode,
              @Param("userSearchCondition") PermissionService.UserSearchCondition userSearchCondition);

    List<AcctQuotaRecord> notPageQuery(@Param("beginTime") Date beginTime, @Param("endTime") Date endTime, @Param("changeType") String changeType, @Param("customerCode") String customerCode, @Param("transactionNo") String transactionNo, @Param("fundType") String fundType, @Param("auditState") String auditState, @Param("acctState") String acctState,@Param("debtorAccountName") String debtorAccountName, @Param("debtorAccount") String debtorAccount,
                                       @Param("feeCustomerCode") String feeCustomerCode,
                                       @Param("userSearchCondition") PermissionService.UserSearchCondition userSearchCondition);

    List<AcctQuotaRecord> pageQuery(@Param("beginRowNo") int beginRowNo, @Param("endRowNo") int endRowNo, @Param("beginTime") Date beginTime, @Param("endTime") Date endTime, @Param("changeType") String changeType, @Param("customerCode") String customerCode, @Param("transactionNo") String transactionNo, @Param("fundType") String fundType, @Param("auditState") String auditState, @Param("acctState") String acctState,@Param("debtorAccountName") String debtorAccountName, @Param("debtorAccount") String debtorAccount,
                                    @Param("feeCustomerCode") String feeCustomerCode,
                                    @Param("userSearchCondition") PermissionService.UserSearchCondition userSearchCondition);

    List<AcctQuotaRecord> selectAllByFundTypeAndAuditStateAndCreateTimeBetween(@Param("fundType")String fundType,@Param("auditState")String auditState,@Param("minCreateTime")Date minCreateTime,@Param("maxCreateTime")Date maxCreateTime);

    int updateAcctErrorMsgById(@Param("updatedAcctErrorMsg")String updatedAcctErrorMsg,@Param("id")Long id);

    @Select("select CALC_SETT_DATE(#{transactionDate}, #{settCycleRuleCode}) from dual")
    String calcSettDate(@Param("transactionDate") String transactionDate,
                        @Param("settCycleRuleCode") String settCycleRuleCode);

    @Select("select id from PAS_ACCT_QUOTA_RECORD where AUDIT_STATE = '00' and ACCT_STATE = '02' and SETT_TIME < sysdate and FUND_TYPE = '1' and CHANGE_TYPE = '1'")
    List<Long> selectUnSettRecord();
}