package com.epaylinks.efps.pas.acc.controller.request;

public class AcctWithdrawRequest {
    /**
     * ID
     */
    private Long id;

    /**
     * 客户编号
     */
    private String customerCode;

    /**
     * 提款的资金类型:1商户扣款、2银联退款、3商户扣罚
     */
    private String fundType;

    /**
     * 金额(分)
     */
    private Long amount;

    /**
     * 提款时间
     */
    private String operateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 附件在文件系统的uniqueId
     */
    private String attachment;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getFundType() {
        return fundType;
    }

    public void setFundType(String fundType) {
        this.fundType = fundType;
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public String getOperateTime() {
        return operateTime;
    }

    public void setOperateTime(String operateTime) {
        this.operateTime = operateTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getAttachment() {
        return attachment;
    }

    public void setAttachment(String attachment) {
        this.attachment = attachment;
    }
}
