package com.epaylinks.efps.pas.acc.feign;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@FeignClient("PAY")
public interface PayClient {

	@PostMapping("/payReAccount")
	CommonOuterResponse<String> payReAccount(@RequestParam(value = "transactionNo") String transactionNo);
}