package com.epaylinks.efps.pas.acc.service;

import com.alibaba.fastjson.JSON;
import com.epaylinks.efps.common.mykafka.MyKafkaTemplate;
import com.epaylinks.efps.common.mykafka.MyKafkaTemplateService;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.pas.acc.entity.AcctQuotaRecord;
import com.epaylinks.efps.pas.common.LogService;
import com.epaylinks.efps.pas.common.PasConstants;
import com.google.common.collect.ImmutableList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/4/23 17:46
 */
@Service
public class RcService {
    public static class TxsPayResultMsg {
        /**
         * 外部订单号
         */
        private String outTradeNo;
        /**
         * 易票联订单号
         */
        private String transactionNo;
        /**
         * 业务类型
         */
        private String businessType;
        /**
         * 业务编码
         */
        private String businessCode;

        /**
         * 支付方式
         */
        private String payMethod;

        /**
         * 订单状态: 00 :成功；01：失败
         */
        private String payState;
        /**
         * 指标参数，如{"amount" : "100" , "tradeNum" : "1"}
         */
        private Map<String, String> indexs;
        /**
         * 风控的业务对象id，如：{"005" : "562XXXXX001" , "004" : "***********"}
         */
        private Map<String, String> businessTargetIds;

        public String getOutTradeNo() {
            return outTradeNo;
        }

        public void setOutTradeNo(String outTradeNo) {
            this.outTradeNo = outTradeNo;
        }

        public String getTransactionNo() {
            return transactionNo;
        }

        public void setTransactionNo(String transactionNo) {
            this.transactionNo = transactionNo;
        }

        public String getBusinessType() {
            return businessType;
        }

        public void setBusinessType(String businessType) {
            this.businessType = businessType;
        }

        public Map<String, String> getIndexs() {
            return indexs;
        }

        public void setIndexs(Map<String, String> indexs) {
            this.indexs = indexs;
        }

        public Map<String, String> getBusinessTargetIds() {
            return businessTargetIds;
        }

        public void setBusinessTargetIds(Map<String, String> businessTargetIds) {
            this.businessTargetIds = businessTargetIds;
        }

        public String getBusinessCode() {
            return businessCode;
        }

        public void setBusinessCode(String businessCode) {
            this.businessCode = businessCode;
        }

        public String getPayState() {
            return payState;
        }

        public void setPayState(String payState) {
            this.payState = payState;
        }

        public String getPayMethod() {
            return payMethod;
        }

        public void setPayMethod(String payMethod) {
            this.payMethod = payMethod;
        }

        @Override
        public String toString() {
            return JSON.toJSONString(this);
        }

    }

    @Autowired
    private LogService logService;

    @Autowired
    private MyKafkaTemplateService kafkaTemplateService;

    public void sendRcResult(AcctQuotaRecord quotaRecord) {
        try {
            //不是审核通过，不需要发送RC
            if (!PasConstants.AcctQuotaAuditState.SUCCESS.code.equals(quotaRecord.getAuditState())) {
                return;
            }

            if (quotaRecord.getTransactionNo() == null) {
                return;
            }

            TxsPayResultMsg txsPayResultMsg = new TxsPayResultMsg();
            txsPayResultMsg.setOutTradeNo(Optional.ofNullable(quotaRecord.getOutTradeNo()).orElse(quotaRecord.getTransactionNo()));
            txsPayResultMsg.setTransactionNo(quotaRecord.getTransactionNo());
            txsPayResultMsg.setBusinessType(Constants.rcBusinessType.ACCT_QUOTA.code);
            if (quotaRecord.getTransactionNo().startsWith("JFDJ")) { //门户直接登记的
                txsPayResultMsg.setBusinessCode("JFDJ");
            } else if (quotaRecord.getTransactionNo().startsWith("TKDJ")) { //门户直接登记的
                txsPayResultMsg.setBusinessCode("TKDJ");
            } else {
                txsPayResultMsg.setBusinessCode(quotaRecord.getBusinessCode());
            }
            txsPayResultMsg.setPayMethod(null);
            txsPayResultMsg.setPayState("00");
            Map<String, String> indexs = new HashMap<>();
            indexs.put("amount", Optional.ofNullable(quotaRecord.getRechargeAmount()).orElse(quotaRecord.getAmount()) + "");
            indexs.put("tradeNum", "1");
            indexs.put("bankUserName", quotaRecord.getDebtorAccountName());
            txsPayResultMsg.setIndexs(indexs);

            Map<String, String> businessTargetIds = new HashMap<>();
            businessTargetIds.put("005", quotaRecord.getCustomerCode());
            businessTargetIds.put("004", quotaRecord.getDebtorAccount());
            txsPayResultMsg.setBusinessTargetIds(businessTargetIds);

            MyKafkaTemplate myKafkaTemplate = this.kafkaTemplateService.getMyKafkaTemplate();
            myKafkaTemplate.send("TXS_RC_RESULT",
                    quotaRecord.getTransactionNo(),
                    ImmutableList.of(txsPayResultMsg).toString());
        } catch (Exception e) {
            logService.printLog(e);
        }

    }
}
