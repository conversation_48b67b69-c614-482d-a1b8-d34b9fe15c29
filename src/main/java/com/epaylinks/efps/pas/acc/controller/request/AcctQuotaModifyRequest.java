package com.epaylinks.efps.pas.acc.controller.request;

public class AcctQuotaModifyRequest {
    /**
     * ID
     */
    private Long id;

    /**
     * 客户编号
     */
    private String customerCode;

    /**
     * 缴费的资金类型:1银行转账,2商户赔付,3商户退款,4商户补款；提款的资金类型:1商户扣款、2银联退款、3商户扣罚
     */
    private String fundType;

    /**
     * 金额(分)
     */
    private Long amount;

    /**
     * 缴费/提款时间
     */
    private String operateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 附件在文件系统的uniqueId
     */
    private String attachment;

    private Long procedureFee;

    private Long rechargeAmount;

    /**
     * 银行卡号
     */
    private String debtorAccount;

    /**
     * 银行账户名称
     */
    private String debtorAccountName;


    public String getAttachment() {
        return attachment;
    }

    public void setAttachment(String attachment) {
        this.attachment = attachment;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getFundType() {
        return fundType;
    }

    public void setFundType(String fundType) {
        this.fundType = fundType;
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public String getOperateTime() {
        return operateTime;
    }

    public void setOperateTime(String operateTime) {
        this.operateTime = operateTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Long getProcedureFee() {
        return procedureFee;
    }

    public void setProcedureFee(Long procedureFee) {
        this.procedureFee = procedureFee;
    }

    public Long getRechargeAmount() {
        return rechargeAmount;
    }

    public void setRechargeAmount(Long rechargeAmount) {
        this.rechargeAmount = rechargeAmount;
    }

    public String getDebtorAccount() {
        return debtorAccount;
    }

    public void setDebtorAccount(String debtorAccount) {
        this.debtorAccount = debtorAccount;
    }

    public String getDebtorAccountName() {
        return debtorAccountName;
    }

    public void setDebtorAccountName(String debtorAccountName) {
        this.debtorAccountName = debtorAccountName;
    }
}
