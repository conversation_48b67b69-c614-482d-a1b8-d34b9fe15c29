package com.epaylinks.efps.pas.acc.entity;

import com.epaylinks.efps.common.business.TransactionType;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.common.PasConstants;

import java.util.Date;

public class AcctQuotaRecord {
    /**
     * ID
     */
    private Long id;

    /**
     * 易票联流水号
     */
    private String transactionNo;

    /**
     * 客户编号
     */
    private String customerCode;

    /**
     * 调整类型:1缴费,2提款
     */
    private String changeType;

    /**
     * 缴费的资金类型:1银行转账,2商户赔付,3商户退款,4商户补款
     * 提款的资金类型:1商户扣款、2银联退款、3商户扣罚
     */
    private String fundType;

    /**
     * 金额(分)
     */
    private Long amount;

    /**
     * 缴费/提款时间，格式为年月日时分秒，如20190614235959
     */
    private String operateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 附件路径
     */
    private String attachment;

    /**
     * 创建人ID
     */
    private Long createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最近更新人ID
     */
    private Long updateUser;

    /**
     * 最近更新时间
     */
    private Date updateTime;

    /**
     * 审批状态：00审批通过,01审批拒绝,02待审批
     */
    private String auditState;

    /**
     * 审批意见
     */
    private String auditComment;

    /**
     * 审批人
     */
    private Long auditUser;

    /**
     * 审批时间
     */
    private Date auditTime;

    /**
     * 记账状态：00成功,01失败,02未记账
     */
    private String acctState;

    /**
     * 记账凭证(记账成功时ACC返回)
     */
    private String acctVoucherNo;

    /**
     * 记账错误码
     */
    private String acctErrorCode;

    /**
     * 记账错误信息
     */
    private String acctErrorMsg;

    private String remark1;

    private String remark2;
    /**
     * 付款人账户
     */
    private String debtorAccount;
    /**
     * 付款人账户名
     */
    private String debtorAccountName;
    /**
     * 付款人行号，12位数字
     */
    private String debtorMemberId;

    /**
     * 原业务订单号
     */
    private String oriTransactionNo;

    /**
     * 业务员
     */
    private String salesMan;
    /**
     * 卡号报备，0未报备，1已报备
     */
    private String backUpStatus;

    private String businessCode;

    private Long procedureFee;

    private Long rechargeAmount;
    /**
     * 收款人姓名
     */
    private String creditorAccountName;

    /**
     * 商户订单号
     */
    private String outTradeNo;

    private String feeCustomerCode;

    private String settCycleRuleCode;

    private Date settTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTransactionNo() {
        return transactionNo;
    }

    public void setTransactionNo(String transactionNo) {
        this.transactionNo = transactionNo;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getChangeType() {
        return changeType;
    }

    public void setChangeType(String changeType) {
        this.changeType = changeType;
    }

    public String getFundType() {
        return fundType;
    }

    public void setFundType(String fundType) {
        this.fundType = fundType;
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public String getOperateTime() {
        return operateTime;
    }

    public void setOperateTime(String operateTime) {
        this.operateTime = operateTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getAttachment() {
        return attachment;
    }

    public void setAttachment(String attachment) {
        this.attachment = attachment;
    }

    public Long getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getAuditState() {
        return auditState;
    }

    public void setAuditState(String auditState) {
        this.auditState = auditState;
    }

    public String getAuditComment() {
        return auditComment;
    }

    public void setAuditComment(String auditComment) {
        this.auditComment = auditComment;
    }

    public Long getAuditUser() {
        return auditUser;
    }

    public void setAuditUser(Long auditUser) {
        this.auditUser = auditUser;
    }

    public Date getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(Date auditTime) {
        this.auditTime = auditTime;
    }

    public String getAcctState() {
        return acctState;
    }

    public void setAcctState(String acctState) {
        this.acctState = acctState;
    }

    public String getAcctVoucherNo() {
        return acctVoucherNo;
    }

    public void setAcctVoucherNo(String acctVoucherNo) {
        this.acctVoucherNo = acctVoucherNo;
    }

    public String getAcctErrorCode() {
        return acctErrorCode;
    }

    public void setAcctErrorCode(String acctErrorCode) {
        this.acctErrorCode = acctErrorCode;
    }

    public String getAcctErrorMsg() {
        return acctErrorMsg;
    }

    public void setAcctErrorMsg(String acctErrorMsg) {
        this.acctErrorMsg = acctErrorMsg;
    }

    public String getRemark1() {
        return remark1;
    }

    public void setRemark1(String remark1) {
        this.remark1 = remark1;
    }

    public String getRemark2() {
        return remark2;
    }

    public void setRemark2(String remark2) {
        this.remark2 = remark2;
    }

    public String getDebtorAccount() {
        return debtorAccount;
    }

    public void setDebtorAccount(String debtorAccount) {
        this.debtorAccount = debtorAccount;
    }

    public String getDebtorAccountName() {
        return debtorAccountName;
    }

    public void setDebtorAccountName(String debtorAccountName) {
        this.debtorAccountName = debtorAccountName;
    }

    public String getDebtorMemberId() {
        return debtorMemberId;
    }

    public void setDebtorMemberId(String debtorMemberId) {
        this.debtorMemberId = debtorMemberId;
    }

    public String getOriTransactionNo() {
        return oriTransactionNo;
    }

    public void setOriTransactionNo(String oriTransactionNo) {
        this.oriTransactionNo = oriTransactionNo;
    }

    public String getSalesMan() {
        return salesMan;
    }

    public void setSalesMan(String salesMan) {
        this.salesMan = salesMan;
    }

    public String getBackUpStatus() {
        return backUpStatus;
    }

    public void setBackUpStatus(String backUpStatus) {
        this.backUpStatus = backUpStatus;
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }

    public Long getProcedureFee() {
        return procedureFee;
    }

    public void setProcedureFee(Long procedureFee) {
        this.procedureFee = procedureFee;
    }

    public Long getRechargeAmount() {
        return rechargeAmount;
    }

    public void setRechargeAmount(Long rechargeAmount) {
        this.rechargeAmount = rechargeAmount;
    }

    public String getCreditorAccountName() {
        return creditorAccountName;
    }

    public void setCreditorAccountName(String creditorAccountName) {
        this.creditorAccountName = creditorAccountName;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public String getFeeCustomerCode() {
        return feeCustomerCode;
    }

    public void setFeeCustomerCode(String feeCustomerCode) {
        this.feeCustomerCode = feeCustomerCode;
    }

    public String getSettCycleRuleCode() {
        return settCycleRuleCode;
    }

    public void setSettCycleRuleCode(String settCycleRuleCode) {
        this.settCycleRuleCode = settCycleRuleCode;
    }

    public Date getSettTime() {
        return settTime;
    }

    public void setSettTime(Date settTime) {
        this.settTime = settTime;
    }

    public Long getModifyAmount() {
        //提款
        if (PasConstants.AcctQuotaChangeType.WITHDRAW.code.equals(getChangeType())) {
            return -getAmount();
        }

        //缴费
        if (PasConstants.AcctQuotaChangeType.RECHARGE.code.equals(getChangeType())) {
            return getAmount();
        }

        throw new AppException(PasCode.ERROR_ACCT_QUOTA_CHANGE_TYPE.getCode(), PasCode.ERROR_ACCT_QUOTA_CHANGE_TYPE.getMessage());
    }

    public String getTransactionType() {
        return PasConstants.AcctQuotaRechargeFundType.FOREIGN_EXCHANGE.code.equals(getFundType()) ?
                TransactionType.FX.code : TransactionType.TZ.code;
    }
}