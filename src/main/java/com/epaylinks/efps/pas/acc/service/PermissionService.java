package com.epaylinks.efps.pas.acc.service;

import com.epaylinks.efps.common.business.pay.request.UserType;
import com.epaylinks.efps.common.tool.spring.SpringUtils;
import com.epaylinks.efps.common.util.Constants;
import com.epaylinks.efps.pas.acl.po.User;
import com.epaylinks.efps.pas.acl.service.UserService;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import com.epaylinks.efps.common.business.cust.service.RedisDataTransService;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/14 9:43
 */
@Service
@RequiredArgsConstructor
public class PermissionService {
    private final RedisDataTransService redisDataTransService;
    private final UserService userService;

    @Data
    public static class UserSearchCondition {
        private Long businessManId;
        private Long companyId;
        private List<Long> companyIdList;
    }

    public UserSearchCondition getUserSearchCondition() {
        UserSearchCondition userSearchCondition = new UserSearchCondition();

        String userType = SpringUtils.getHeader("x-user-type");
        String userIdString = SpringUtils.getHeader("x-userid");

        if (UserType.PAS_USER.code.equals(userType) && userIdString != null) {
            Long userId = Long.valueOf(userIdString);
            User user = userService.queryUserById(userId);
            if (Constants.PasUserType.COMPANY.code.equals(user.getUserType())) {
                userSearchCondition.setCompanyId(user.getCompanyId());
            } else if (Constants.PasUserType.SALES.code.equals(user.getUserType())) {
                userSearchCondition.setBusinessManId(userId);
            } else {
                List<Long> companyIds = redisDataTransService.getCompanyIdList(userId);
                if (CollectionUtils.isNotEmpty(companyIds)) {
                    userSearchCondition.setCompanyIdList(companyIds);
                }
            }
        }
        return userSearchCondition;
    }
}
