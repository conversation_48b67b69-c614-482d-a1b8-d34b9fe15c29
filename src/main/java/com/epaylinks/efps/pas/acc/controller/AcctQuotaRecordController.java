package com.epaylinks.efps.pas.acc.controller;

import java.util.HashMap;
import java.util.Map;

import com.epaylinks.efps.common.tool.response.EpResponse;
import com.epaylinks.efps.pas.acc.dao.AcctQuotaRecordMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;
import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.business.CommonResponse;
import com.epaylinks.efps.common.datadownload.annotation.DownloadAble;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.exception.Exceptionable;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.util.page.PageResult;
import com.epaylinks.efps.common.validate.Validatable;
import com.epaylinks.efps.pas.acc.controller.request.AcctQuotaAuditRequest;
import com.epaylinks.efps.pas.acc.controller.request.AcctQuotaModifyRequest;
import com.epaylinks.efps.pas.acc.controller.request.AcctRechargeRequest;
import com.epaylinks.efps.pas.acc.controller.request.AcctWithdrawRequest;
import com.epaylinks.efps.pas.acc.dto.AcctQuotaRecordDTO;
import com.epaylinks.efps.pas.acc.entity.AcctQuotaRecord;
import com.epaylinks.efps.pas.acc.entity.FileUploadResponse;
import com.epaylinks.efps.pas.acc.service.AcctQuotaRechargeService;
import com.epaylinks.efps.pas.acc.service.AcctQuotaRecordService;
import com.epaylinks.efps.pas.acc.service.AcctQuotaWithdrawService;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.common.PasConstants;
import com.epaylinks.efps.pas.mch.service.feign.FsService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import springfox.documentation.annotations.ApiIgnore;

@RestController
@RequestMapping("/AcctQuota")
@Api(value = "AcctQuotaRecordController", description = "账户增减额度管理")
public class AcctQuotaRecordController {

    @Autowired
    private AcctQuotaRechargeService acctQuotaRechargeService;
    @Autowired
    private AcctQuotaWithdrawService acctQuotaWithdrawService;
    @Autowired
    private AcctQuotaRecordService acctQuotaRecordService;
    @Autowired
    private FsService fsService;

    @Autowired
    private AcctQuotaRecordMapper acctQuotaRecordMapper;

    static final String FILE_TYPE_SHOW = "show";
    static final Integer EXPIREDTIME = 30;
    static final Integer MAX_ACCESSCOUNT = 100;

//    @Validatable
//    @Exceptionable
//    @Logable(businessTag = "uploadImgFile")
//    @ApiOperation(value = "上传Base64文件")
//    @PostMapping("/uploadImgFile")
//    @ApiImplicitParams({})
//    public CommonOuterResponse uploadImgFile(@RequestPart("file") MultipartFile file) {
//        InputStream is = null;
//        try {
//            is = file.getInputStream();
//            byte[] buffer = new byte[(int) file.getSize()];
//            is.read(buffer);
//            is.close();
//            String fileBase64Content = new BASE64Encoder().encode(buffer);
//            String uploadToken = fsClient.uploadToekn(PasConstants.FileBusinessType.ACCT_QUOTA.type, "pas", "0", PasConstants.FileBusinessType.ACCT_QUOTA.comment);
//            Base64FileUploadRequest base64Req = new Base64FileUploadRequest();
//            base64Req.setBusinessType(PasConstants.FileBusinessType.ACCT_QUOTA.type);
//            base64Req.setFileBaseSixFour(fileBase64Content);
//            base64Req.setFileName("");
//            base64Req.setUploadToken(uploadToken);
//            com.epaylinks.efps.pas.mch.client.model.FileUploadResponse fileUploadResponse = fsClient.baseSixFour(base64Req);
//            return CommonOuterResponse.success(fileUploadResponse);
//        } catch (Exception e) {
//            e.printStackTrace();
//            System.out.println(e.getCause());
//        }
//        return CommonOuterResponse.fail("", "");
//    }

    @Validatable
    @Exceptionable
    @Logable(businessTag = "upload")
    @ApiOperation(value = "上传审核文件")
    @PostMapping("/upload")
    @ApiImplicitParams({})
    public CommonOuterResponse uploadFile(@RequestPart("file") MultipartFile file) {
        try {
            //获取上传token
            String uploadToken = fsService.uploadToken(PasConstants.FileBusinessType.ACCT_QUOTA.type, "pas", "0", PasConstants.FileBusinessType.ACCT_QUOTA.comment);

            //上传文件
            String respStr = fsService.uploadFile(file, uploadToken);

            FileUploadResponse resp = JSON.parseObject(respStr, FileUploadResponse.class);
            //上传结果
            if (!CommonResponse.SUCCEE.equals(resp.getResultCode())) {
                return CommonOuterResponse.fail(resp.getResultCode(), resp.getResultMsg());
            }
            //文件路径
            Map<String, String> map = fsService.filePath(resp.getUniqueId(), EXPIREDTIME, MAX_ACCESSCOUNT, FILE_TYPE_SHOW);
            if (!CommonResponse.SUCCEE.equals(map.get("resultCode"))) {
                return CommonOuterResponse.fail(map.get("resultCode"), map.get("resultMsg"));
            }

            //返回数据
            Map<String, String> dataMap = new HashMap<>();
            dataMap.put("url", map.get("filePath"));
            dataMap.put("uniqueId", resp.getUniqueId());
            return CommonOuterResponse.success("上传成功", dataMap);
        } catch (Exception e) {
            if (e instanceof AppException) {
                return CommonOuterResponse.fail(((AppException) e).getErrorCode(), ((AppException) e).getErrorMsg());
            } else {
                return CommonOuterResponse.fail(PasCode.SYSTEM_EXCEPTION.code, PasCode.SYSTEM_EXCEPTION.message);
            }
        }
    }


    @Logable(businessTag = "pageQuery")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "商户账户额度调整记录查询或导出", httpMethod = "GET")
    @RequestMapping("/pageQuery")
    @DownloadAble
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页码", required = true, dataType = "int", paramType = "query", digit = true),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", required = true, dataType = "int", paramType = "query", digit = true),
            @ApiImplicitParam(name = "timeBegin", value = "创建时间开始，格式20180727", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "timeEnd", value = "创建时间结束，格式20180727", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "changeType", value = "调整类型:1缴费,2提款", required = true, dataType = "String", paramType = "query", valueRange = "{1,2}"),
            @ApiImplicitParam(name = "customerCode", value = "商户编码", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "feeCustomerCode", value = "手续费扣减方", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "transactionNo", value = "易票联流水号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "fundType", value = "缴费的资金类型:1银行转账,2商户赔付,3商户退款,4商户补款,6结汇;提款的资金类型:1商户扣款、2银联退款、3商户扣罚、4跨境结算，5跨境手续费,6结汇", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "auditState", value = "审批状态：00审批通过,01审批拒绝,02待审批", required = false, dataType = "String", paramType = "query", valueRange = "{00,01,02}"),
            @ApiImplicitParam(name = "debtorAccount", value = "付款人银行账号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "debtorAccountName", value = "付款人账户名", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "download", value = "是否导出，true/false", required = false, dataType = "Boolean", paramType = "query"),
            @ApiImplicitParam(name = "fileName", value = "导出文件名称", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "acctState", value = "记账状态：00成功,01失败,02未记账", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "type", value = "导出文件类型，例如.csv", required = false, dataType = "String", paramType = "query")
    })
    public PageResult<AcctQuotaRecordDTO> pageQuery(
            @RequestParam int pageNum,
            @RequestParam int pageSize,
            @RequestParam(required = false) String timeBegin,
            @RequestParam(required = false) String timeEnd,
            @RequestParam String changeType,
            @RequestParam(required = false) String customerCode,
            @RequestParam(required = false) String feeCustomerCode,
            @RequestParam(required = false) String transactionNo,
            @RequestParam(required = false) String fundType,
            @RequestParam(required = false) String auditState,
            @RequestParam(required = false) String debtorAccount,
            @RequestParam(required = false) String debtorAccountName,
            @RequestParam(required = false) boolean download,
            @RequestParam(required = false) String fileName,
            @RequestParam(required = false) String acctState,
            @RequestParam(required = false) String type) {
        PageResult<AcctQuotaRecordDTO> page = new PageResult<>();
        try {
            page = acctQuotaRecordService.pageQuery(download, pageNum, pageSize, timeBegin, timeEnd, changeType, customerCode,
                    transactionNo, fundType, auditState,acctState,debtorAccount,debtorAccountName, feeCustomerCode);
            page.setCode(CommonResponse.SUCCEE);
        } catch (Exception e) {
            if (e instanceof AppException) {
                page.setCode(((AppException) e).getErrorCode());
                page.setMessage(((AppException) e).getErrorMsg());
            } else {
                page.setCode(PasCode.SYSTEM_EXCEPTION.code);
                page.setMessage(PasCode.SYSTEM_EXCEPTION.message);
            }
        }
        return page;
    }

    @PostMapping("/recharge")
    @Logable(businessTag = "recharge")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "商户缴费登记", notes = "商户缴费登记", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "customerCode", value = "客户编号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "fundType", value = "缴费的资金类型:1银行转账,2商户赔付,3商户退款,4商户补款,5跨境手续费,6结汇,7收单充值，A拒付处理费，B拒付金额", required = true, dataType = "String", paramType = "query", valueRange = "{1,2,3,4,5,6,7,A,B}"),
            @ApiImplicitParam(name = "amount", value = "金额(分)", required = true, dataType = "Long", paramType = "query"),
            @ApiImplicitParam(name = "operateTime", value = "缴费时间，格式为20190614235959", required = false, dataType = "String", paramType = "query", length = 14),
            @ApiImplicitParam(name = "remark", value = "备注", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "attachment", value = "附件在文件系统的uniqueId", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "oriTransactionNo", value = "原业务订单号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "backUpStatus", value = "卡号报备，0未报备，1已报备", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "nAutoReview", value = "自动审核 0-否，1-是", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "auditState", value = "审批状态：00审批通过,01审批拒绝,02待审批", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "acctState", value = "记账状态,00成功", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "rechargeAmount", value = "充值金额(分)", required = false, dataType = "Long", paramType = "query"),
            @ApiImplicitParam(name = "procedureFee", value = "手续费(分)", required = false, dataType = "Long", paramType = "query"),
            @ApiImplicitParam(name = "businessCode", value = "业务编号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "debtorAccount", value = "付款人银行账号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "debtorAccountName", value = "付款人账户名", required = false, dataType = "String", paramType = "query")
    })
    public CommonOuterResponse recharge(
            @ApiIgnore AcctRechargeRequest request,
            @RequestHeader(value = "x-userid", required = true) Long curUserId) {
        CommonOuterResponse response = new CommonOuterResponse<>();
        try {
        	AcctQuotaRecord acctQuotaRecord = acctQuotaRechargeService.recharge(request, curUserId);
        	response.setData(acctQuotaRecord.getTransactionNo());
        } catch (Exception e) {
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
        }
        
        return response;
    }

    @PostMapping("/withdraw")
    @Logable(businessTag = "withdraw")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "商户提款登记", notes = "商户提款登记", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "customerCode", value = "客户编号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "fundType", value = "提款的资金类型:1商户扣款、2银联退款、3商户扣罚、4跨境结算，5跨境手续费,6结汇，A拒付处理费，B拒付金额", required = true, dataType = "String", paramType = "query", valueRange = "{1,2,3,4,5,6,A,B}"),
            @ApiImplicitParam(name = "amount", value = "金额(分)", required = true, dataType = "Long", paramType = "query"),
            @ApiImplicitParam(name = "operateTime", value = "提款时间，格式为20190614235959", required = false, dataType = "String", paramType = "query", length = 14),
            @ApiImplicitParam(name = "remark", value = "备注", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "attachment", value = "附件在文件系统的uniqueId", required = false, dataType = "String", paramType = "query")
    })
    public CommonOuterResponse withdraw(
            @ApiIgnore AcctWithdrawRequest request,
            @RequestHeader(value = "x-userid", required = true) Long curUserId) {
        CommonOuterResponse response = new CommonOuterResponse<>();
        try {
            acctQuotaWithdrawService.withdraw(request, curUserId);
        } catch (Exception e) {
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
        }
        return response;
    }

    @GetMapping("/viewRecord")
    @Logable(businessTag = "viewRecord")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "查看", notes = "查看", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true, dataType = "Long", paramType = "query")
    })
    public CommonOuterResponse viewRecord(@RequestParam Long id) {
        CommonOuterResponse response = new CommonOuterResponse<>();
        try {
            response.setData(acctQuotaRecordService.viewRecord(id));
        } catch (Exception e) {
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
        }
        return response;
    }

    @PostMapping("/modifyRecord")
    @Logable(businessTag = "modifyRecord")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "商户缴费/提款登记-修改", notes = "商户缴费/提款登记-修改", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true, dataType = "Long", paramType = "query"),
            @ApiImplicitParam(name = "customerCode", value = "客户编号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "fundType", value = "缴费的资金类型:1银行转账,2商户赔付,3商户退款,4商户补款；提款的资金类型:1商户扣款、2银联退款、3商户扣罚、4跨境结算，5跨境手续费", required = true, dataType = "String", paramType = "query",valueRange = "{1,2,3,4,5,7}"),
            @ApiImplicitParam(name = "amount", value = "金额(分)", required = true, dataType = "Long", paramType = "query"),
            @ApiImplicitParam(name = "operateTime", value = "缴费/提款时间，格式为20190614235959", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "remark", value = "备注", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "attachment", value = "附件在文件系统的uniqueId", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "rechargeAmount", value = "充值金额(分)", required = false, dataType = "Long", paramType = "query"),
            @ApiImplicitParam(name = "procedureFee", value = "手续费(分)", required = false, dataType = "Long", paramType = "query"),
            @ApiImplicitParam(name = "debtorAccount", value = "付款人银行账号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "debtorAccountName", value = "付款人账户名", required = false, dataType = "String", paramType = "query")
    })
    public CommonOuterResponse modifyRecord(
            @ApiIgnore AcctQuotaModifyRequest request,
            @RequestHeader(value = "x-userid", required = true) Long curUserId) {
        CommonOuterResponse response = new CommonOuterResponse<>();
        try {
            acctQuotaRecordService.modifyRecord(request, curUserId);
        } catch (Exception e) {
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
        }
        return response;
    }

    @PostMapping("/auditRecord")
    @Logable(businessTag = "auditRecord")
    @Exceptionable
    @Validatable
    @ApiOperation(value = "商户缴费/提款登记-审批", notes = "商户缴费/提款登记-审批", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true, dataType = "Long", paramType = "query"),
            @ApiImplicitParam(name = "auditState", value = "审批状态：00审批通过,01审批拒绝,02待审批", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "auditComment", value = "审批意见", required = false, dataType = "String", paramType = "query")
    })
    public CommonOuterResponse auditRecord(
            @ApiIgnore AcctQuotaAuditRequest request,
            @RequestHeader(value = "x-userid", required = true) Long curUserId,
            @RequestHeader(value = "x-user-type", required = false) String userType) {
        CommonOuterResponse response = new CommonOuterResponse<>();
        try {
            acctQuotaRecordService.auditRecord(request, curUserId, userType);
        } catch (Exception e) {
            if (e instanceof AppException) {
                response.setReturnCode(((AppException) e).getErrorCode());
                response.setReturnMsg(((AppException) e).getErrorMsg());
            } else {
                response.setReturnCode(PasCode.SYSTEM_EXCEPTION.code);
                response.setReturnMsg(PasCode.SYSTEM_EXCEPTION.message);
            }
            acctQuotaRecordMapper.updateAcctErrorMsgById(response.getReturnMsg(), request.getId());
        }
        return response;
    }

    @PostMapping("/settle")
    public void settle() {
        acctQuotaRecordService.settle();
    }

    @PostMapping("/notifyCustomer")
    @ApiOperation(value = "补发通知商户", notes = "补发通知商户", httpMethod = "POST")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "PAS_ACCT_QUOTA_RECORD表ID字段", required = true, dataType = "Long", paramType = "query"),
    })
    public EpResponse<?> notifyCustomer(Long id) {
        acctQuotaRecordService.notifyCustomer(id);
        return EpResponse.success();
    }
}