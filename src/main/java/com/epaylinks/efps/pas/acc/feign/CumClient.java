package com.epaylinks.efps.pas.acc.feign;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@FeignClient("CUM")
public interface CumClient {

	@GetMapping("/Customer/queryMapOfCustomerCodeAndName")
	Map<String, String> queryMapOfCustomerCodeAndName(@RequestParam(value = "customerCodeList") List<String> customerCodeList);
}