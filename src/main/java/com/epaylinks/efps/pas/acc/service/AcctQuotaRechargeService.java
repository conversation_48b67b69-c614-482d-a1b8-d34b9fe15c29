package com.epaylinks.efps.pas.acc.service;

import com.epaylinks.efps.common.business.pay.request.UserType;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.sequence.SequenceService;
import com.epaylinks.efps.common.util.StringUtils;
import com.epaylinks.efps.pas.acc.controller.request.AcctQuotaAuditRequest;
import com.epaylinks.efps.pas.acc.controller.request.AcctRechargeRequest;
import com.epaylinks.efps.pas.acc.dao.AcctQuotaRecordMapper;
import com.epaylinks.efps.pas.acc.entity.AcctQuotaRecord;
import com.epaylinks.efps.pas.common.LogService;
import com.epaylinks.efps.pas.common.PasCode;
import com.epaylinks.efps.pas.common.PasConstants;
import com.epaylinks.efps.pas.common.TransactionNoUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class AcctQuotaRechargeService {

    @Autowired
    private AcctQuotaRecordMapper acctQuotaRecordMapper;
    @Autowired
    private TransactionNoUtil transactionNoUtil;
    @Autowired
    private AcctQuotaRecordService acctQuotaRecordService;
    @Autowired
    private SequenceService sequenceService;
    @Autowired
    private LogService logService;
    @Autowired
    private ChangeAvailableBalanceNoticeService noticeService;
    @Autowired
    private RcService rcService;

    @Logable(businessTag = "recharge")
    public AcctQuotaRecord recharge(AcctRechargeRequest request, Long curUserId) throws  Exception {
        AcctQuotaRecord record = new AcctQuotaRecord();

        Date now = new Date();
        BeanUtils.copyProperties(request, record);
        record.setId(sequenceService.nextValue("PAS_AcctQuotaRecord"));
        record.setChangeType(PasConstants.AcctQuotaChangeType.RECHARGE.code);
        record.setCreateUser(curUserId);
        record.setCreateTime(now);
        if(StringUtils.isBlank(record.getAuditState())){
            record.setAuditState(PasConstants.AcctQuotaAuditState.WAITING.code);
        }
        if(StringUtils.isBlank(record.getAcctState() )){
            record.setAcctState(PasConstants.AcctQuotaAcctState.INIT.code);
        }

        record.setBackUpStatus(request.getBackUpStatus());
        String transactionNo = null;
        if(PasConstants.AcctQuotaRechargeFundType.TRADE_RECHARGE.code.equals(record.getFundType())
                && PasConstants.AcctQuotaChangeType.RECHARGE.code.equals(record.getChangeType())) {
            transactionNo = request.getOriTransactionNo();
        }
        else {
            transactionNo = transactionNoUtil.generateTransactionNo("PAS_AcctQuotaRecharge", "JFDJ");
        }
        record.setTransactionNo(transactionNo);

        if(PasConstants.AcctQuotaAuditState.SUCCESS.code.equals(record.getAuditState())){
            record.setAuditUser(curUserId);
            record.setAuditTime(now);
            record.setAuditComment("自动审核");


        }
        //类型、状态 为常量
        try {
            acctQuotaRecordMapper.insert(record);
        } catch (Exception e) {
            logService.printLog(e);
            throw new AppException(PasCode.ERROR_SAVE_RECORD.code, PasCode.ERROR_SAVE_RECORD.message);
        }
        long superUserId = 0;

        rcService.sendRcResult(record);

//        if("1".equals(request.getnAutoReview()) && PasConstants.AcctQuotaAuditState.WAITING.code.equals(record.getAuditState())){
//            AcctQuotaAuditRequest auditRequest = new AcctQuotaAuditRequest();
//            auditRequest.setAuditState(PasConstants.AcctQuotaAuditState.SUCCESS.code);
//            auditRequest.setAuditComment("自动审核");
//            auditRequest.setId(record.getId());
//
//
//
//            acctQuotaRecordService.auditRecord(auditRequest, superUserId, UserType.PAS_USER.code);
//
//        }

        return record;
    }
}