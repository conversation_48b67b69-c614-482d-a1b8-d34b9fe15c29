package com.epaylinks.efps.pas.acc.dto;

import com.epaylinks.efps.common.util.UUIDUtils;
import com.epaylinks.efps.pas.acc.entity.AccountQueryResponse;
import com.epaylinks.efps.pas.acc.entity.AcctQuotaRecord;

import java.text.SimpleDateFormat;

/**
 * @Author: <EMAIL>
 * @Date: 2019/12/27 10:01
 * @Description 调账通知消息
 * @Version 1.0
 */
public class AcctQuotaNoticeDTO {

    private String customerCode;
    private String bankAccount;
    private String bankName;
    private String channelOrder;
    private String transactionNo;
    private Long arrivalAmount;
    private Long availableBalance;
    private String transactionTime;
    private String payMethod;
    private String outTradeNo;
    //备注1
    private String memo1;
    //备注2
    private String memo2;
    
    private String nonceStr = UUIDUtils.uuid();

    public AcctQuotaNoticeDTO(AcctQuotaRecord record, AccountQueryResponse response) {
        this.customerCode = record.getCustomerCode();
//        String bankAccount = record.getRemark().substring(6);//数据不准确风险
//        this.bankAccount = bankAccount.substring(bankAccount.length() - 4);
        this.bankAccount = record.getDebtorAccount();
        this.channelOrder = record.getTransactionNo().substring(3);//数据不准确风险
        this.transactionNo = record.getTransactionNo();
        this.arrivalAmount = record.getAmount();
        this.availableBalance = response.getAvailableBalance();
        this.transactionTime = new SimpleDateFormat("yyyyMMddHHmmss").format(record.getAuditTime());
        this.payMethod = "ACS";
        this.memo1 = record.getRemark1();
        this.memo2 = record.getRemark2();
        this.bankName = record.getDebtorAccountName();
        this.outTradeNo=record.getOutTradeNo();
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getBankAccount() {
        return bankAccount;
    }

    public void setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getChannelOrder() {
        return channelOrder;
    }

    public void setChannelOrder(String channelOrder) {
        this.channelOrder = channelOrder;
    }

    public String getTransactionNo() {
        return transactionNo;
    }

    public void setTransactionNo(String transactionNo) {
        this.transactionNo = transactionNo;
    }

    public Long getArrivalAmount() {
        return arrivalAmount;
    }

    public void setArrivalAmount(Long arrivalAmount) {
        this.arrivalAmount = arrivalAmount;
    }

    public Long getAvailableBalance() {
        return availableBalance;
    }

    public void setAvailableBalance(Long availableBalance) {
        this.availableBalance = availableBalance;
    }

    public String getNonceStr() {
        return nonceStr;
    }

    public void setNonceStr(String nonceStr) {
        this.nonceStr = nonceStr;
    }

    public String getTransactionTime() {
        return transactionTime;
    }

    public void setTransactionTime(String transactionTime) {
        this.transactionTime = transactionTime;
    }

    public String getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(String payMethod) {
        this.payMethod = payMethod;
    }

	public String getMemo1() {
		return memo1;
	}

	public void setMemo1(String memo1) {
		this.memo1 = memo1;
	}

	public String getMemo2() {
		return memo2;
	}

	public void setMemo2(String memo2) {
		this.memo2 = memo2;
	}

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }
}
