package com.epaylinks.efps.pas.acc.feign.dto.acc;

public class AccountVo {

    private String customerCode;
    private String businessCode;
    private String accountTypeCode;
    private String accountTypeName;
    private String status;
    private Long balance;
    private Long availableBalance;
    private Long floatBalance;
    private Long frozenBalance;
    private Long fpBalance;
    private Long frBalance;

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }

    public String getAccountTypeCode() {
        return accountTypeCode;
    }

    public void setAccountTypeCode(String accountTypeCode) {
        this.accountTypeCode = accountTypeCode;
    }

    public String getAccountTypeName() {
        return accountTypeName;
    }

    public void setAccountTypeName(String accountTypeName) {
        this.accountTypeName = accountTypeName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Long getBalance() {
        return balance;
    }

    public void setBalance(Long balance) {
        this.balance = balance;
    }

    public Long getAvailableBalance() {
        return availableBalance;
    }

    public void setAvailableBalance(Long availableBalance) {
        this.availableBalance = availableBalance;
    }

    public Long getFloatBalance() {
        return floatBalance;
    }

    public void setFloatBalance(Long floatBalance) {
        this.floatBalance = floatBalance;
    }

    public Long getFrozenBalance() {
        return frozenBalance;
    }

    public void setFrozenBalance(Long frozenBalance) {
        this.frozenBalance = frozenBalance;
    }

    public Long getFpBalance() {
        return fpBalance;
    }

    public void setFpBalance(Long fpBalance) {
        this.fpBalance = fpBalance;
    }

    public Long getFrBalance() {
        return frBalance;
    }

    public void setFrBalance(Long frBalance) {
        this.frBalance = frBalance;
    }
}
