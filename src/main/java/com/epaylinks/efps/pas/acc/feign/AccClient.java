package com.epaylinks.efps.pas.acc.feign;

import com.epaylinks.efps.pas.acc.feign.dto.acc.AccountResponse;
import com.epaylinks.efps.pas.acc.feign.dto.acc.DirectVoucherRequest;
import com.epaylinks.efps.pas.acc.feign.dto.acc.DirectVoucherResponse;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.*;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.pas.acc.entity.AccountQueryResponse;

import javax.validation.Valid;

@FeignClient("ACC")
public interface AccClient {

 @PostMapping("/resetAmount")
 CommonOuterResponse resetAmount(
         @RequestParam("accountCode") String accountCode,
         @RequestParam("balanceType") Integer balanceType,
         @RequestParam("amount") Long amount,
         @RequestParam("summary") String summary,
         @RequestParam("transactionType") String transactionType,
         @RequestParam("transactionNo") String transactionNo);

 @PostMapping("/accountQueryInner")
 AccountQueryResponse accountQueryInner(@RequestHeader(value = "x-customer-code", required = true) String customerCodeHead,
                                        @RequestParam("customerCode")String customerCode ,
                                        @RequestParam("accountType") String accountType);

 @PostMapping("api/v1/voucher/direct")
 DirectVoucherResponse direct(@RequestBody @Valid DirectVoucherRequest request);

 @GetMapping("/account/queryAccount")
 AccountResponse queryAccount(@RequestHeader("x-user-type") String userType,
                              @RequestParam("pageNum") Integer pageNum,
                              @RequestParam("pageSize") Integer pageSize,
                              @RequestParam(value = "accountCode", required = false)String accountCode ,
                              @RequestParam(value = "customerCode", required = false)String customerCode ,
                              @RequestParam(value = "accountTypeCode", required = false)String accountTypeCode ,
                              @RequestParam(value = "accountStatus", required = false) Integer accountStatus);

 @PostMapping("/fr/frte")
 CommonOuterResponse frte( @RequestParam(value = "customerCode", required = false)String customerCode ,
                           @RequestParam(value = "transactionType", required = false)String transactionType ,
                           @RequestParam(value = "amount", required = false) Integer amount,
                           @RequestParam(value = "transactionNo", required = false)String transactionNo ,
                           @RequestParam(value = "summary", required = false) String summary,
                           @RequestParam(value = "kpDate", required = false) String kpDate);
}
