package com.epaylinks.efps.pas.cum.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.epaylinks.efps.pas.cum.domain.CumCustomerInfo;

@Mapper
public interface CumCustomerInfoMapper {
	


    List<CumCustomerInfo> selectBySelective(CumCustomerInfo cumCustomerInfo);


    List<CumCustomerInfo> selectByNameOnLike(String name);
    /*
     * 查询范围内的商户号对应商户
     */
    List<CumCustomerInfo> getCustomerInfoByCodes(List<String> codes);
	
}