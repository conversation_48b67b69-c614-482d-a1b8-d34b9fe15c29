package com.epaylinks.efps.pas.cum.service.impl;

import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.pas.cum.dao.CumCustomerInfoMapper;
import com.epaylinks.efps.pas.cum.domain.CumCustomerInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 业务逻辑层：客户基本信息
 * <AUTHOR>
 */
@Service
public class CumCumCustomerInfoServiceImpl {

	@Autowired
	private CumCustomerInfoMapper cumCustomerInfoMapper;
	


	@Logable(businessTag = "selectBySelective")
	public List<CumCustomerInfo> selectBySelective(CumCustomerInfo cumCustomerInfo) {
		return cumCustomerInfoMapper.selectBySelective(cumCustomerInfo);
	}

	@Logable(businessTag = "selectByNameOnLike")
	public List<CumCustomerInfo> selectByNameOnLike(String name) {
		return cumCustomerInfoMapper.selectByNameOnLike(name);
	}




	/*
	 * 查询范围内的商户号对应商户名称
	 */

	@Logable(businessTag = "getCustomerInfoByCodes")
	public List<CumCustomerInfo> getCustomerInfoByCodes(List<String> codes) {
		return cumCustomerInfoMapper.getCustomerInfoByCodes(codes);
	}


}
