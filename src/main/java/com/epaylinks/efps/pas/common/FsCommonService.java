package com.epaylinks.efps.pas.common;

import com.epaylinks.efps.common.business.CommonResponse;
import com.epaylinks.efps.common.exception.AppException;
import com.epaylinks.efps.common.util.StringUtils;
import com.epaylinks.efps.pas.mch.client.FsClient;
import com.epaylinks.efps.pas.mch.client.model.FileUploadResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

@Service
public class FsCommonService {

    static final String FILE_TYPE_SHOW = "show";
    static final String FILE_TYPE_DOWNLOAD = "download";
    static final Integer EXPIREDTIME = 30;
    static final Integer MAX_ACCESSCOUNT = 100;
    static final String SUCCESS = "0000";

    @Autowired
    private FsClient fsClient;

    /**
     * 根据fs文件ID获取下载url
     * @param uniqueId
     * @param pathForDownload
     * @return
     */
    public Map<String, String> queryFileUrlForDownload(String uniqueId,boolean pathForDownload) {
        String fileType = pathForDownload ? FILE_TYPE_DOWNLOAD : FILE_TYPE_SHOW;
        Map<String, String> resMap = new HashMap<>();
        if (StringUtils.isBlank(uniqueId)) {
            return resMap;
        }
        Map<String, String> map = fsClient.filePath(uniqueId, EXPIREDTIME, MAX_ACCESSCOUNT, fileType);
        if(SUCCESS.equals(map.get("resultCode"))){
            resMap.put("url",map.get("filePath"));
            resMap.put("uniqueId", uniqueId);
            resMap.put("name",map.get("fileName"));
        }
        return resMap;
    }

    /**
     * 附件上传，需指定业务类型，类型枚举见fs_business_type表
     * @param file
     * @return
     */
    public String uploadFile(MultipartFile file,String businessType,String remark){
        //获取上传token
        String uploadToken = fsClient.uploadToekn(businessType, "pas", "0", remark);
        //上传文件
        FileUploadResponse resp = fsClient.uploadFile(file, uploadToken,"pas");// customerCodea非空返回相对路径
        if(!CommonResponse.SUCCEE.equals(resp.getResultCode())) {
            throw new AppException(resp.getResultCode(), resp.getResultMsg());
        }
        return resp.getUniqueId();
    }
}
