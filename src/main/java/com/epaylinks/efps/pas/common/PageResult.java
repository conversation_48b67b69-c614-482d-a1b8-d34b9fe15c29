package com.epaylinks.efps.pas.common;

import com.epaylinks.efps.common.business.CommonOuterResponse;
import com.epaylinks.efps.common.business.CommonResponse;

import java.util.List;
import java.util.Map;

/**
 * 分页查询结果        pas extends CommonOuterResponse有returncode,returnmessage
 *
 * <AUTHOR>
 */
public class PageResult<T> extends CommonOuterResponse {

    /**
     * 总记录数
     */
    private int total;

    /**
     * 数据列表
     */
    private List<T> rows;
    

    /**
     * 需要统计字段， key为字段名，value为显示的中文名。字段必须是数值
     */
    private List<Map<String, String>> statistics;
    
    private List<String> titles;

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public List<T> getRows() {
        return rows;
    }

    public void setRows(List<T> rows) {
        this.rows = rows;
    }

    public List<Map<String, String>> getStatistics() {
        return statistics;
    }

    public void setStatistics(List<Map<String, String>> statistics) {
        this.statistics = statistics;
    }

	public List<String> getTitles() {
		return titles;
	}

	public void setTitles(List<String> titles) {
		this.titles = titles;
	}
    
}