package com.epaylinks.efps.pas.common;

import com.epaylinks.efps.common.systemcode.SystemCode;
import com.epaylinks.efps.common.tool.error.code.EpCode;

@SystemCode
public enum PasCode implements EpCode {
    SYSTEM_EXCEPTION("4401", "pas系统内部错误"),
    SEND_KAFKA_EXCEPTION("4402", "发送kafka数据异常"),
    FEIGN_CUMEXCEPTION("4403", "调用客户管理系统异常"),
    UNSUPPORT_SETTCYCLE("4404", "不支持的结算周期"),
    DIFFERENT_RATE_MODE("4405", "费率模式不一致"),
    DLS_RATE_PARAM_TOO_LARGE("4406", "会员子商户的费率不能小于代理商的费率"),  //代理商的费率比拓展商户的大
    USEUSCC_EXCEPTION("4407", "三证合一参数填写异常，小微商户可选，其他商户必填"),
    BUSINESS_LINENSE_IMAGE_EXCEPTION("4408", "营业执照参数填写异常，小微商户可选，其他商户必填"),
    BUSINESS_LINENSENO_EXCEPTION("4409", "营业执照号参数填写异常，小微商户可选，其他商户必填"),
    BUSINESS_LINENSE_EXPIRED_DATE_EXCEPTION("4410", "营业执照到期时间参数填写异常，小微商户可选，其他商户必填"),
    NOT_OPEN_FZ_EXCEPTION("0004", "商户未开通分账业务"),
    TZSH_OPEN_EXCEPTION("4411", "当前商户不是代理商或者当前代理商户并没开通对应要开通的业务"),
    TWO_SAME_BUSINESS_EXCEPTION("4412", "用户开通了两个相同的业务"),
    NOT_ALLOW_REPEATED_BUSINESS("4413", "不允许开通重复的业务"),
    FILENAME_TYPE_MUSTHAS("4414", "文件名和类型必传"),
    PARENT_NOT_OPEN_FZ("4415", "父商户未开通分账业务"),
    PARENT_CUS_INVALID("4416", "父商户非法"),
    INPUT_BUSINESS_CODE_INVALID("4417", "输入的业务非法"),
    PARENT_NOT_OPEN_DL("4418", "父商户未开通代理业务"),
    REPEATED_INPUT_BUSIINESS_CODE("4419", "输入了重复的业务编码"),
    MUST_HAVE_ONE_CONATCT("4420", "必须输入而且仅能输入一个联系人"),
    CONTACT_PARAM_INVALID("4421", "联系人参数非法"),
    CUSTOMER_TYPE_INVALID("4422", "客户类型非法"),
    LEAL_PERSON_ID_IMAGE_INVALID("4423", "身份证图片非法"),
    LEAL_PERSON_NAME_INVALID("4424", "法人姓名非法"),
    LEAL_PERSON_ID_EXPIRED_DATE_EXCEPTION("4425", "法人证件过期时间非法"),
    LEAL_PERSON_NO_INVALID("4426", "法人证件号码非法"),
    CUSTOMER_CODE_NOT_EXIST("4427", "修改商户资料，但是客户编码不存在"),
    CUSTOMER_ALREADY_EXIST("4428", "商户已经存在"),
    SETT_CYCLE_PARAM_INVALID("4429", "结算周期规则参数非法"),
    UPLOAD_ATTACHEMENT_FAILED("4430", "上传附件失败"),
    USERTYPE_NOTEXIST("4431", "用户类型不存在"),
    BUSINESS_DATE_INVALID("4432", "业务列表的开始时间/结束时间格式非法"),
    UNSUPPORT_UPDATE_METHOD("4433", "尚不支持修改接口，不允许传入customerCode字段"),
    CUSTOMER_NAME_INVALID("4434", "客户名称非法"),
    BUSINESS_NOT_ALLOW_NULL("4435", "开通的业务列表不允许为空"),
    CUSTOMER_MOBILE_MUST("4436", "商户手机号必填"),
    PARENT_CUSTOMER_BUSINESS_NOT_EXIT("4437", "父商户业务不存在"),
    SUBSCRIPTION_RATIO_NOT_EXIT("4438", "商户的兑换参数不存在"),
    LOGIN_USER_NOT_EXIST("4439", "登录用户不存在"),
    LOGIN_PWD_ERORR("4440", "登录密码错误"),
    LOGIN_USER_STATE_LOCK_ERROR("4441", "用户已被锁定"),
    ROLE_CODE_EXIST("4442", "角色编码已经存在"),
    USER_NOT_EXIST("4443", "用户不存在"),
    DATA_NOT_EXIST("4444", "找不到相关数据"),
    SYNC_ROLE_SERVICECODE_ERROR("4445", "远程调用UAA同步角色权限数据异常"),
    USER_NAME_EXIST("4446", "用户名已存在"),
    DEPT_NOT_EXIST("4447", "部门不存在"),
    DEPT_MODIFY_FAIL("4448", "部门修改失败"),
    DEPT_DELETE_FAIL("4449", "部门删除失败"),
    SHORTNAME_NOT_ALLOW_NULL("4450", "商户简称不能为空"),
    AREACODE_NOT_ALLOW_NULL("4451", "省市区编码不能为空"),
    SETTTARGET_NOT_ALLOW_NULL("4452", "结算目标不能为空"),
    SETTMODE_NOT_ALLOW_NULL("4453", "结算模式不能为空"),
    NOTIFYURL_NOT_ALLOW_NULL("4454", "异步通知地址不能为空"),
    ROLE_NAME_EXIST("4455", "角色名称已存在"),
    NOT_ALLOW_CHANGE_LPIN("4456", "不允许修改法人或经营者证件号码"),
    USER_ROLE_NOT_EXIST("4457", "用户没有角色，请联系管理员"),
    DEPT_NAME_EXIST("4458", "部门名称已存在"),
    ROLE_DELETE_FAIL("4459", "角色删除失败"),
    USER_PHONE_EXIST("4460", "用户手机号已存在"),
    D0_BUSINESSLIST_EXCEPTION("4461", "D0相关业务参数列表异常"),
    HOLIDAY_EXCEPTION("4462", "节假日数据不能为空"),
    INVALID_DATE_FORMAT("4463", "无效的节假日期格式"),
    VALIDATECODE_ERROR("4464", "验证码错误"),
    USER_EXIST_BYMOBILE("4465", "当前手机号存在多个用户，不能找回密码"),
    PWD_FIND_ERROR("4466", "密码找回失败，请重新操作"),
    USER_OR_PWD_NOT_EXIST("4467", "用户名或密码不正确"),
    DATE_RANGE_ERROR("4468", "不可跨年设置节假日时间"),
    DATE_RANGE_EXIST_ERROR("4469", "当年节假日已经存在，不可重复添加"),
    DATE_FORMAT_ERROR("4470", "日期格式不正确，格式为:yyyyMMdd"),
    OSS_CONNECT_ERROR("4471", "调用OSS存储过程异常"),
    CLR_CONNECT_ERROR("4472", "调用clr清算异常"),
    CAN_NOT_FIND_SETT_CYCLE_INST("4473", "业务实例找不到对应的结算周期实例"),
    BUSINESS_LINENSENO_LENGTH_ERROR("4474", "营业执照号长度超限，最大为30个字符"),
    NON_UNION_YUN_MERCHANT("4475", "非云闪付平台商户资料，不能进行审核"),
    NOT_ALLOW_REPEATED_SCYLE_BUSINESS("4476", "不允许开通重复结算周期业务"),
    CUSTOMER_CODE_DIFF_CERT("4477", "商户编码和证书不匹配"),
    FARE_RATE_TYPE_ERROR("4478", "费率类型不正确"),
    FARE_RATE_ERROR("4479", "费率参数不正确"),
    CUSTOMER_NOT_PLAT("4480", "非平台商户不能发起平台签约"),
    SIGN_MODEL_ERROR("4481", "签约模式不正确"),
    TASK_STATE_EXCEPTION("4482", "定时任务状态不正确"),
    PARENT_NOT_OPEN_WITHDRAW_EXCEPTION("4483", "父商户未开通代付到储蓄卡业务"),
    ERROR_SAVE_RECORD("4484", "记录保存失败"),
    RECORD_NOT_EXIST("4485", "记录不存在"),
    MODIFY_NO_LIMIT("4486", "无权限修改"),
    ERROR_ACCT_QUOTA_RESET("4487", "账户调账失败"),
    ERROR_PARSEDATE("4488", "日期格式异常"),
    BANK_ACCOUNT_TYPE_INVALID("4489", "结算账户类型非法"),
    SETTLE_ACCOUNT_UNMATCH_LAWYER("4490", "结算账户开户名称与法人姓名不一致"),
    SETTLE_ACCOUNT_UNMATCH_MERCHANT("4491", "结算账户开户名称与商户名称不一致"),
    ERROR_ACCT_QUOTA_CHANGE_TYPE("4492", "账户调账类型错误"),
    ERROR_ACCT_QUOTA_EXECUTED("4493", "调账申请已执行，请勿重复操作"),
    PARENT_CUSTOMER_CODE_NOT_ALLOW_NULL("4494" , "主商户编号不能为空"),
    SUB_CUSTOMER_CODE_NOT_ALLOW_NULL("4495" , "分账子商户编号不能为空"),
    SETTTARGET_ERROR("4496", "结算目标不正确"),
    USE_UECC_NOT_ALLOW_NULL("4497", "是否三证合一不能为空"),
    USE_UECC_ERROR("4498", "是否三证合一不正确"),
	LEAL_PERSON_NAME_NOT_ALLOW_NULL("4499","法人或经营者姓名不能为空"),
	LEAL_PERSON_ID_TYPE_NOT_ALLOW_NULL("44100","法人或经营者证件类型不能为空"),
	LEAL_PERSON_ID_NO_NOT_ALLOW_NULL("44101","法人或经营者证件号码不能为空"),
    SHORT_NAME_LENGTH_ERROR("44102", "商户简称超出长度限制"),
    COMPANY_NAME_EXIST("44103", "分公司名称已存在"),
    COMPANY_CAN_NOT_NUL("44104", "分公司不能为空"),
    COMPANY_PARENT_CAN_NOT_NULL("44105", "上级分公司不能为空"),
    ROLE_CAN_NOT_DELETE("44106", "角色不能删除"),
    USERNAME_INVALID("44107", "用户名不正确"),
    MOBILE_PHONE_INVALID("44108", "手机号码不正确"),
    USERNAME_CAN_NOT_MODIFY("44109", "用户名不能修改"),
    USER_REAL_NAME_EXIST("44110","业务员姓名已存在"),
    COMPANY_REAL_NAME_EXIST("44111","分公司名称已存在"),
	SALES_REAL_NAME_EXIST("44112","业务员姓名已存在"),
	USER_CAN_NOT_DELETE("44113","用户不能删除"),
	LOGINLOG_ERROR("44114","登录日志查询异常"),
	LOGININFO_ERROR("44115","登录信息查询异常"),
    GRAPHIC_CODE_INVALID("44116", "图形验证码不正确"),
    PASSWORD_EXPIRED("44117", "密码已过期，请通过“忘记密码”重置您的登录密码"),
    NEW_PASSWORD_CAN_NOT_SAME_AS_OLD("44118", "新密码不能与原密码相同"),
    NEW_PASSWORD_TOO_SIMPLE("44119", "密码过于简单：长度8-16位，至少包含数字、字母、特殊字符三种"),
    NO_RIGHT_TO_QUERY("44120", "没有权限查询"),
    USER_OR_PASSWORD_UNCORRECT("44121", "账号或密码有误"),
    LOGIN_USER_STATE_DISABLE_ERROR("44122", "用户已被禁用"),
    USER_STATUS_LOCK_EXCEPTION("44123", "用户被锁定，请通过忘记密码或联系系统管理员解锁"),
    USER_STATUS_DISABLE_CAN_NOT_LOCK("44123", "用户已被禁用，不能锁定"),
    ENCRYPT_SERIVCE_ERROR("44124", "调用密码服务异常"),
    CUSTOMER_STATUS_EXCEPTION("44125", "商户状态异常"),
    RECORD_EXIST("44126", "记录已存在"),
    BANK_NAME_ERROR("44127", "行别代码错误，请填写正确行别代码，联行号前3位"),
    SALES_NOT_EXIST("44128","业务员不存在"),
    SALES_STATE_DISABLE_ERROR("44129", "业务员已禁用"),
    OVER_TIME("44130", "订单已失效"),
    DATA_ERROR("44131","数据错误"),
    MISSING_PARAMETER("44132","缺少参数"),
    CURRENT_STATUS_CANNOT_MODIFIED("44133","当前状态不可修改"),
    CURRENT_STATUS_CANNOT_AUDIT("44134","当前状态不可审核"),
    STATUS_ERROR("44135","状态错误"),
    ERROR_PARAM("44136","参数错误"),
    LENGTH_LIMIT("44137","长度超过限制"),
    USER_DISABLE_LOGIN("44138","用户禁止登录"),
    UPDATE_ROLE_FIAL("44139","修改角色失败"),
    NOT_ACROSS_DATA_PERMISSION("44140","不可跨数据权限类型多选角色")
    ;


    
    
    public final String code;
    public final String message;

    PasCode(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }


    public String getMessage() {
        return message;
    }


    public static String getMessageByCode(String code) {
        for (PasCode v : PasCode.values()) {
            if (v.code.equalsIgnoreCase(code))
                return v.message;
        }
        return null;
    }
}
