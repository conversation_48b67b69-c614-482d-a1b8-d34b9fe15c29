package com.epaylinks.efps.pas.common;

import com.epaylinks.efps.common.util.Constants;
import com.sun.org.apache.xpath.internal.operations.Bool;

import java.util.ArrayList;
import java.util.List;

public interface PasConstants extends Constants {

    //用户状态
    enum UserStatus {
        YES("Y", "启用"),
        NO("N", "禁用"),
        LOCK("L", "锁定");
        public final String code;
        public final String comment;

        UserStatus(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }
        public static String getCommentByCode(String code) {
            for (UserStatus enumV : UserStatus.values()) {
                if (enumV.code.equals(code)) {
                    return enumV.comment;
                }
            }
            return "";
        }
    }

    enum pasSettCycleRuleType {
        D0("D0", "D0结算类型"),
        D("D", "D+N结算类型"),
        T("T", "T+N结算类型"),
        REALTIME("RealTime", "RealTime结算类型");
        public final String code;
        public final String comment;

        pasSettCycleRuleType(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }
    }

    enum pasSettCycleRuleStatus {
        VALID("Valid", "结算周期规则有效"),
        INVALID("Invalid", "结算周期规则无效");
        public final String code;
        public final String comment;

        pasSettCycleRuleStatus(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }
    }

    enum RateMode {
        FIX_RATE((short) 1, "固定费率"),
        PERCENT_RATE((short) 2, "百分比费率");
        public final Short code;
        public final String comment;

        RateMode(Short code, String comment) {
            this.code = code;
            this.comment = comment;
        }
    }

    enum serviceCode {
        UPDATEBYPHONE("updateByPhone", "手机验证码修改密码"),
        FINDBYPHONE("findUserByPhone", "手机号找回密码"),
        BUSINESSLOGIN("businessLogin","pc版进件平台业务员登录"),
        MANAGEMENTDATA("managementData","经管数据提取文件")
        ;
        public final String code;
        public final String comment;

        serviceCode(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }
    }

    enum timeTaskConcurrent {
        TRUE("0", true),
        FALSE("1", false);

        public final String code;
        public final boolean value;

        timeTaskConcurrent(String code, boolean value) {
            this.code = code;
            this.value = value;
        }
    }

    enum timeTaskStatus {
        OPEN("0", "开启"),
        STOP("1", "关闭");

        public final String code;
        public final String comment;

        timeTaskStatus(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }
    }

    /**
     * 结转用途
     */
    enum CfdPurpose {
        PURPOSE_01("01", "结转利息收入"),
        PURPOSE_02("02", "结转手续费收入"),
        PURPOSE_03("03", "结转预付费卡现金赎回资金"),
        PURPOSE_04("04", "备付金交存"),
        PURPOSE_OTHER("09", "其他");
        public final String code;
        public final String comment;

        CfdPurpose(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }

        public static String getCommentByCode(String code) {
            for (CfdPurpose enumV : CfdPurpose.values()) {
                if (enumV.code.equals(code)) {
                    return enumV.comment;
                }
            }
            return null;
        }
    }

    enum timeTaskRecordStatus {
        SUCCESS("0", "成功"),
        FAIL("1", "失败"),
        PROCESSING("2", "处理中");

        public final String code;
        public final String comment;

        timeTaskRecordStatus(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }
    }

    /**
     * 账户额度-调整类型
     */
    enum AcctQuotaChangeType {
        RECHARGE("1", "缴费"),
        WITHDRAW("2", "提款");
        public final String code;
        public final String comment;

        AcctQuotaChangeType(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }

        public static String getCommentByCode(String code) {
            for (AcctQuotaChangeType enumV : AcctQuotaChangeType.values()) {
                if (enumV.code.equals(code)) {
                    return enumV.comment;
                }
            }
            return null;
        }
    }

    /**
     * 提款的资金类型:1商户扣款、2银联退款、3商户扣罚、4跨境结算，5跨境手续费
     */
    enum AcctQuotaWithdrawFundType {
        DEDUCT_MONEY("1", "商户扣款"),
        UNIONPAY_REFUND("2", "银联退款"),
        WITHHOLDING_PENALTIES("3", "商户扣罚"),
        CROSS_BORDER_SETTLEMENT("4", "跨境结算"),
        CROSS_BORDER_HANDLING_FEES("5", "跨境手续费"),
        FOREIGN_EXCHANGE("6", "结汇"),
        JFCLF("A", "拒付处理费"),
        JFJE("B", "拒付金额"),
        ;
        public final String code;
        public final String comment;

        AcctQuotaWithdrawFundType(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }

        public static String getCommentByCode(String code) {
            for (AcctQuotaWithdrawFundType enumV : AcctQuotaWithdrawFundType.values()) {
                if (enumV.code.equals(code)) {
                    return enumV.comment;
                }
            }
            return null;
        }
    }

    /**
     * 缴费的资金类型:1银行转账,2商户赔付,3商户退款,4商户补款
     */
    enum AcctQuotaRechargeFundType {
        BANK_TRANSFER("1", "银行转账"),
        PAY_COMPENSATION("2", "商户赔付"),
        BUSINESS_REFUND("3", "商户退款"),
        BUSINESS_SUBSIDIES("4", "商户补款"),
        FOREIGN_EXCHANGE("6", "结汇"),
        TRADE_RECHARGE("7", "收单充值"),
        JFCLF("A", "拒付处理费"),
        JFJE("B", "拒付金额"),;
        public final String code;
        public final String comment;

        AcctQuotaRechargeFundType(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }

        public static String getCommentByCode(String code) {
            for (AcctQuotaRechargeFundType enumV : AcctQuotaRechargeFundType.values()) {
                if (enumV.code.equals(code)) {
                    return enumV.comment;
                }
            }
            return null;
        }
    }

    /**
     * 账户额度-审批状态
     */
    enum AcctQuotaAuditState {
        SUCCESS("00", "审批通过"),
        FAIL("01", "审批拒绝"),
        WAITING("02", "待审批");
        public final String code;
        public final String comment;

        AcctQuotaAuditState(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }

        public static String getCommentByCode(String code) {
            for (AcctQuotaAuditState enumV : AcctQuotaAuditState.values()) {
                if (enumV.code.equals(code)) {
                    return enumV.comment;
                }
            }
            return null;
        }
    }

    /**
     * 账户额度-记账状态
     */
    enum AcctQuotaAcctState {
        SUCCESS("00", "成功"),
        FAIL("01", "失败"),
        INIT("02", "未记账");
        public final String code;
        public final String comment;

        AcctQuotaAcctState(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }

        public static String getCommentByCode(String code) {
            for (AcctQuotaAcctState enumV : AcctQuotaAcctState.values()) {
                if (enumV.code.equals(code)) {
                    return enumV.comment;
                }
            }
            return null;
        }
    }

    /**
     * 附件业务类型
     */
    enum FileBusinessType {
        ACCT_QUOTA("acct_quota", "账户调账附件");
        public final String type;
        public final String comment;

        FileBusinessType(String type, String comment) {
            this.type = type;
            this.comment = comment;
        }

        public static String getCommentByCode(String type) {
            for (FileBusinessType typeEnum : FileBusinessType.values()) {
                if (typeEnum.type.equals(type)) {
                    return typeEnum.comment;
                }
            }
            return null;
        }
    }
    
    // 密码修改建议
    enum PwdChangeSuggest {

        NO_NEED_TO_CHANGE(0, "不需修改"), SUGGEST_CHANGE(1, "建议修改"), MUST_CHANGE(2, "必须修改");

        public final int code;
        public final String comment;

        PwdChangeSuggest(int code, String comment) {
            this.code = code;
            this.comment = comment;
        }
    }

    /**
     * 待开调额-调额类型
     */
    enum AdjustmentType {
        INCREASE("1","增额"),
        DERATE("2","减额"),
        FREEZE("3","冻结"),
        THAW("4","解冻")
        ;
        public String code;
        public String comment;
        AdjustmentType(String code,String comment) {
            this.code = code;
            this.comment = comment;
        }
        public static String getCommentByCode(String code) {
            for (AdjustmentType typeEnum : AdjustmentType.values()) {
                if (typeEnum.code.equals(code)) {
                    return typeEnum.comment;
                }
            }
            return null;
        }
    }

    /**
     * 待开调额-调额事由
     */
    enum AdjustSubjectMatter {
        BZFR("BZFR","补增分润差额"),
        KJSD("KJSD","扣减发票税点"),
        KJFR("KJFR","扣减分润差额"),
        KCFR("KCFR","扣除分润"),
        DJFR("DJFR","冻结分润"),
        JDFR("JDFR","解冻分润")
        ;
        public String code;
        public String comment;
        AdjustSubjectMatter(String code,String comment) {
            this.code = code;
            this.comment = comment;
        }
        public static String getCommentByCode(String code) {
            for (AdjustSubjectMatter typeEnum : AdjustSubjectMatter.values()) {
                if (typeEnum.code.equals(code)) {
                    return typeEnum.comment;
                }
            }
            return null;
        }
        public static List<String> getMattersByAdjustType(String adjustType,String type) {
            List<String> matters = new ArrayList<>();
            if (AdjustmentType.INCREASE.code.equals(adjustType)) {
                matters.add("code".equals(type) ? AdjustSubjectMatter.BZFR.code : AdjustSubjectMatter.BZFR.comment);
            } else if (AdjustmentType.DERATE.code.equals(adjustType)) {
                matters.add("code".equals(type) ? AdjustSubjectMatter.KJSD.code : AdjustSubjectMatter.KJSD.comment);
                matters.add("code".equals(type) ? AdjustSubjectMatter.KJFR.code : AdjustSubjectMatter.KJFR.comment);
                matters.add("code".equals(type) ? AdjustSubjectMatter.KCFR.code : AdjustSubjectMatter.KCFR.comment);
            } else if (AdjustmentType.FREEZE.code.equals(adjustType)) {
                matters.add("code".equals(type) ? AdjustSubjectMatter.DJFR.code : AdjustSubjectMatter.DJFR.comment);
            } else if (AdjustmentType.THAW.code.equals(adjustType)) {
                matters.add("code".equals(type) ? AdjustSubjectMatter.JDFR.code : AdjustSubjectMatter.JDFR.comment);
            }
            return matters;
        }
    }

    /**
     * 待开调额审核状态
     */
    enum DkteAuditStatus {
        PASS("1","审核通过"),
        NO_PASS("2","审核不通过"),
        WAIT_AUDIT("3","待审核")
        ;
        public String code;
        public String comment;
        DkteAuditStatus(String code,String comment) {
            this.code = code;
            this.comment = comment;
        }
    }

    enum REQPARAMCHECK {
        REQ_PARAM_CONTAIN_SPEC_CHAR("REQ_PARAM_CONTAIN_SPEC_CHAR","请求参数包含特殊字符")
        ;

        public final String code;
        public final String comment;

        REQPARAMCHECK(String code, String comment) {
            this.code = code;
            this.comment = comment;
        }
    }

    /**
     * 经管数据发送状态
     */
    enum ManagementDataSendStatus {
        NOT_SENT("0","未发送"),
        HAS_BEEN_SENT("1","已发送")
        ;
        public String code;
        public String comment;
        ManagementDataSendStatus(String code,String comment) {
            this.code = code;
            this.comment = comment;
        }
    }

    /**
     * 加载卡Bin来源
     */
    enum CardBinLoadFrom {
        UN("1","银联"),
        NET("2","网联"),
        EXP("3","连通")
        ;
        public String code;
        public String comment;
        CardBinLoadFrom(String code,String comment) {
            this.code = code;
            this.comment = comment;
        }
    }

    /**
     * 角色管理-数据查看权限
     */
    enum RoleDataAuth {
        ALL("0","全量"),
        CUSTOMIZE("1","自定义"),
        THIS_DEPARTMENT("2","本部门"),
        SELF("3","本人")
        ;
        public String code;
        public String comment;

        RoleDataAuth(String code,String comment) {
            this.code = code;
            this.comment = comment;
        }
    }
}
