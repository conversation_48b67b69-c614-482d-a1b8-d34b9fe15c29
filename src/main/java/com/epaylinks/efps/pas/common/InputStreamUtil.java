package com.epaylinks.efps.pas.common;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;

public class InputStreamUtil {
	public  static String getStrFromInputSteam(InputStream in){  
		StringBuilder stringBuilder = null;
		BufferedReader bufferedReader = null;
		try {
			stringBuilder = new StringBuilder();
			bufferedReader = new BufferedReader(new InputStreamReader(in));
			boolean firstLine = true;
			String line = null; ;
			while((line = bufferedReader.readLine()) != null){
			    if(!firstLine){
			        stringBuilder.append("\r\n");
			    }else{
			        firstLine = false;
			    }
			    stringBuilder.append(line);
			}
			bufferedReader.close();
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} finally {
            if (bufferedReader != null) {
                try {
                	bufferedReader.close();
                } catch (IOException e) {
                }
            }
        }
	       
	    return stringBuilder.toString();  
	}
}
