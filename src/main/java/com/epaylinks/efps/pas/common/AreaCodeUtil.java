package com.epaylinks.efps.pas.common;


import org.apache.commons.lang3.StringUtils;

/**
 * 行政区划代码工具类
 * <p>
 * 参考：http://www.stats.gov.cn/tjsj/tjbz/xzqhdm/201703/t20170310_1471429.html
 *
 * <AUTHOR>
 */
public interface AreaCodeUtil {

    /**
     * 获取行政区划代码的前缀 for前缀匹配查询
     *
     * @param province
     * @param city
     * @return 行政区划代码的前缀
     */
    static String getAreaCodeQueryStr(String province, String city, String district) {
        if (StringUtils.isNotBlank(district)) {
            return district;
        }
        if (StringUtils.isNotBlank(city)) {
            return city.substring(0, 4);
        }
        if (StringUtils.isNotBlank(province)) {
            return province.substring(0, 2);
        }
        return null;
    }
}
