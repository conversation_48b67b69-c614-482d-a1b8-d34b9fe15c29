package com.epaylinks.efps.pas.common;

import org.apache.log4j.Logger;

import java.util.*;

public class SmsSignUtil {
    private static final Logger logger = Logger.getLogger(SmsSignUtil.class);
    //请求的参数
    private SortedMap<String, Object> parameters = new TreeMap<String, Object>();
    /**
     * 设置参数值
     * @param parameter 参数名称
     * @param parameterValue 参数值
     */
    public void setParameter(String parameter, Object parameterValue) {
        if(null!=parameterValue){
            this.parameters.put(parameter, parameterValue);
        }
    }
    public Map<String, Object> buildRequest(){
        //拼装短信发送需要的参数
        Map<String, Object> request = new HashMap<String, Object>();
        request.put("user_no",this.parameters.get("user_no"));
        request.put("mobile_no",this.parameters.get("mobile_no"));
        request.put("model_id",this.parameters.get("model_id"));
        request.put("timestamp",this.parameters.get("timestamp"));
        request.put("data",this.parameters.get("data"));
        request.put("sign",buildRequestSign());
        return request;
    }
    /**
     * 使用SHA256算法生成签名结果,规则是:按参数名称a-z排序,遇到空值的参数不参加签名
     * @return 远程主机响应
     */
    private String buildRequestSign() {
        //用于生成签名的有序map
        SortedMap<String, Object> signparam = new TreeMap<String, Object>();
        //用于拼接参数的字符串
        StringBuffer sb = new StringBuffer();
        //当前所有要签名的参数集合
        Set<Map.Entry<String, Object>> es = this.parameters.entrySet();
        //要签名参数的迭代器
        Iterator<Map.Entry<String, Object>> it = es.iterator();
        //遍历请求参数，提取要签名的参数加入有序MAP
        while(it.hasNext()) {
            Map.Entry<String, Object> entry = it.next();
            String k = entry.getKey();
            Object v = entry.getValue();
            if(null != v && !"".equals(v) && !"sign".equals(k)) {
                if(!"data".equals(k)){	//非map参数加入签名
                    signparam.put(k, v);
                }else{	//map参数加入签名
                    Map<String,String> data = (Map<String,String>)v;
                    Iterator<Map.Entry<String, String>> datait = data.entrySet().iterator();
                    while(datait.hasNext()) {
                        Map.Entry<String, String> dataentry = datait.next();
                        signparam.put(dataentry.getKey(),dataentry.getValue());
                    }
                }
            }
        }
        //遍历有序MAP，生成要签名的字符串
        Iterator<Map.Entry<String, Object>> paramit = signparam.entrySet().iterator();
        while(paramit.hasNext()) {
            Map.Entry<String, Object> paramentry = paramit.next();
            sb.append(paramentry.getKey() + "=" + paramentry.getValue() + "&");
        }
        //去掉最后一个&
        String reqPars = sb.substring(0, sb.lastIndexOf("&"));
        logger.info("sortParam:"+reqPars);
        //生成签名信息
        String sign = SmsSHA256Util.SHA256Encode(reqPars, "UTF-8").toLowerCase();
        return sign;
    }
}
