package com.epaylinks.efps.pas.common.sm3;

import org.bouncycastle.math.ec.ECPoint;

import java.math.BigInteger;

/**
 * SM2算法运算结果
 * 
 * <AUTHOR> 杨阳
 * 
 */
public class SM2_Result {
	// 签名、验签
	/**
	 * 签名结果r
	 */
	public BigInteger r;
	/**
	 * 签名结果s
	 */
	public BigInteger s;

	// 加解密
	/**
	 * 密文C
	 */
	public byte[] C;
	/**
	 * 解密后得到的明文M
	 */
	public byte[] M_sq;
	/**
	 * 解密后得到的明文的校验结果
	 */
	public Boolean check;

	// 密钥交换
	/**
	 * 密钥协商时用户B发送给用户A的中间结果R<sub>B</sub>
	 */
	public ECPoint RB;
	/**
	 * 密钥协商时用户A发送给用户B的中间结果S<sub>A</sub>
	 */
	public byte[] SA;
	/**
	 * 密钥协商时用户B发送给用户A的中间结果S<sub>B</sub>
	 */
	public byte[] SB;
	/**
     * 密钥协商是否成功 
     */
	public Boolean isSuccess;

}
