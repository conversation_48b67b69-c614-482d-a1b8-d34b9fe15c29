package com.epaylinks.efps.pas.common;

/**
 * Created by adm on 2018/2/6.
 */


import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.*;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

@Component
   @WebFilter(urlPatterns = "/*",filterName = "commonFilter")
public class CommonFilter extends HttpServlet  implements Filter {
//public class CommonFilter extends OncePerRequestFilter {

        @Override
        public void init(FilterConfig filterConfig) throws ServletException {

        }



        @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, Fi<PERSON><PERSON>hain filterChain) throws IOException, ServletException {
        // 将请求转换成HttpServletRequest 请求
        HttpServletRequest req = (HttpServletRequest) servletRequest;
        HttpServletResponse resp = (HttpServletResponse) servletResponse;
//        String path = req.getContextPath();
          Cookie c =   getCookieByName(req,"userId") ;
            HttpServletRequest wrapper = null;
            if(c!=null) {
                req.setAttribute("x-userid",c.getValue());
            }
       /* String basePath = req.getScheme()+"://"+req.getServerName()+":"+req.getServerPort()+path;
        HttpSession session = req.getSession(true);
        String username = (String) session.getAttribute("username");
        if (username == null || "".equals(username)) {
            resp.setHeader("Cache-Control", "no-store");
            resp.setDateHeader("Expires", 0);
            resp.setHeader("Prama", "no-cache");
            //此处设置了访问静态资源类
            resp.sendRedirect(basePath+"/index.html");*/
//        } else {
            // Filter 只是链式处理，请求依然转发到目的地址。
                filterChain.doFilter(req, resp);

//        }
    }




    @Override
    public void destroy() {

    }

    /**
        * 根据名字获取cookie
        *
        * @param request
        * @param name
        *            cookie名字
        * @return
        */
       public static Cookie getCookieByName(HttpServletRequest request, String name) {
           Map<String, Cookie> cookieMap = ReadCookieMap(request);
           if (cookieMap.containsKey(name)) {
               Cookie cookie = (Cookie) cookieMap.get(name);
               return cookie;
           } else {
               return null;
           }
       }

       /**
        * 将cookie封装到Map里面
        *
        * @param request
        * @return
        */
       private static Map<String, Cookie> ReadCookieMap(HttpServletRequest request) {
           Map<String, Cookie> cookieMap = new HashMap<String, Cookie>();
           Cookie[] cookies = request.getCookies();
           if (null != cookies) {
               for (Cookie cookie : cookies) {
                   cookieMap.put(cookie.getName(), cookie);
               }
           }
           return cookieMap;
       }

       /**
        * 保存Cookies
        *
        * @param response
        *            servlet请求
        * @param value
        *            保存值
        * <AUTHOR>
        */
       public static HttpServletResponse setCookie(HttpServletResponse response, String name, String value,int time) {
           // new一个Cookie对象,键值对为参数
           Cookie cookie = new Cookie(name, value);
           // tomcat下多应用共享
           cookie.setPath("/");
           // 如果cookie的值中含有中文时，需要对cookie进行编码，不然会产生乱码
           try {
               URLEncoder.encode(value, "utf-8");
           } catch (UnsupportedEncodingException e) {
               e.printStackTrace();
           }
           cookie.setMaxAge(time);
           // 将Cookie添加到Response中,使之生效
           response.addCookie(cookie); // addCookie后，如果已经存在相同名字的cookie，则最新的覆盖旧的cookie
           return response;
       }
}