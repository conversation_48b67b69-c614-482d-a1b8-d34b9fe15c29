package com.epaylinks.efps.pas.common;

/**
 * <AUTHOR>
 *rule:
 *1.登录密码方面：长度8-16位，至少包含数字、字母、特殊字符三种。
 *2.支付密码方面：6位支付密码不能连续数字、一样数字、等差数字、一样字母，不能与出生日期、证件号码、手机号码、姓名拼音出现6位连续一样的字符。
 */
public class PasswordCheck {
	public static final int PASS = 0;
	public static final int NO_LETTER = 1;				//缺少大小写字母
	public static final int NO_SYMBOL = 2;				//缺少符号
	public static final int NO_NUMERAL = 3;				//缺少数字
	public static final int LENGTH_OUT = 4;				//长度不对
	public static final int NO_MACTH = 5;				//模式不匹配
	public static final int SIMPLE_RULE = 6;			//规则太简单（等差数列）
	public static final int HIT_NUMBER = 7;				//命中（重叠）传入的号码
	public static final int EMPTY_INPUT = 8;			//输入为空
	public static final int UNSPOURT_CHECK_TYPE = 9;	//不支持的密码检查类型
	
	public static final String LONGIN_CHECK_TYPE = "1";	//登录密码类型检查
	public static final String PAY_CHECK_TYPE = "2";	//登录密码类型检查
	
	
    /**
     * 指定密码类型按照规则检查
     * @param password 明文密码
     * @param checkType 密码类型
     * @param nums 各种号码
     * @return boolean
     */
	public static boolean assignType(String password, String checkType, String...nums) {
		if(password == null || checkType == null) {
			return false;
		}
		switch(checkType) {
		case LONGIN_CHECK_TYPE:
			return PasswordCheck.loginPasswordCheck(password);
		case PAY_CHECK_TYPE :
			return PasswordCheck.payPasswordCheck(password, nums);
		default : return false;
		}
	}
    /**
     * 指定密码类型按照规则检查
     * @param password 明文密码
     * @param checkType 密码类型
     * @param nums 各种号码
     * @return int 校验结果详情:PasswordCheck.XXXXXX
     */
	public static int assignTypeCheckDetail(String password, String checkType, String...nums) {
		if(password == null || checkType == null) {
			return EMPTY_INPUT;
		}
		switch(checkType) {
		case LONGIN_CHECK_TYPE:
			return PasswordCheck.loginPasswordCheckDetail(password);
		case PAY_CHECK_TYPE :
			return PasswordCheck.payPasswordCheckDetail(password, nums);
		default : return UNSPOURT_CHECK_TYPE;
		}
	}
    /**
     * 按登录密码规则检查
     * @param password 明文密码
     * @return boolean
     */
	public static boolean loginPasswordCheck(String passWord) {
		if (passWord == null || passWord.length() < 8 || passWord.length() > 16) {
			return false;
		}
		return passWord.matches(".*[A-Za-z].*") && passWord.matches(".*[0-9].*") && passWord.matches(".*\\p{Punct}.*");
	}
	
    /**
     * 按登录密码规则检查
     * @param password 明文密码
     * @return int 校验结果详情:PasswordCheck.XXXXXX
     */
	public static int loginPasswordCheckDetail(String passWord) {
		if (passWord == null) {
			return EMPTY_INPUT;
		}
		if(passWord.length() < 8 || passWord.length() > 16)
			return LENGTH_OUT;
		if(!passWord.matches(".*[A-Za-z].*")) {
			return NO_LETTER;
		}
		if(!passWord.matches(".*[0-9].*")) {
			return NO_NUMERAL;
		}
		if(!passWord.matches(".*\\p{Punct}.*")) {
			return NO_SYMBOL;
		}
		return PASS;
	}
    /**
     * 按支付密码规则检查
     * @param password 明文密码
     * @param nums 各种号码
     * @return boolean
     */
	public static boolean payPasswordCheck(String password, String... nums) {
		
		// 不能为空且必须是六位数字
		if(password == null || !password.matches("\\d{6}")) {
			return false;
		}
		
		/*
		//1.等差数列验证，预演法，单纯数字的等差数列，无非全等，连续递增、递减自然数三种，如果后面放开带字母，那么先算个差就好
		char first = password.charAt(0);
		String equalsStr = first + "";
		String dijianStr = first + "";
		String dizengStr = first + "";
		for(int i = 1; i < password.length(); i++) {
			equalsStr += first;
			dijianStr += (char)(first + i);
			dizengStr += (char)(first - i);
		}
		if(password.equals(equalsStr) || password.equals(dizengStr) || password.equals(dijianStr)) {
			return false;
		}
		*/
		
		
		//2.等差数列验证，冒泡法（效率更高，兼容性更好）
		int index = 0;
		//取第一个减去第二个做差额，如果等差，则每个差额都一样（由于前文已经肯定了password是六位，所以下面不用做数组下标越界的处理，否则至少数组要长度为3才不会抛异常）
		int margin = password.charAt(index++) - password.charAt(index);
		while(true) {
			if(margin != password.charAt(index++) - password.charAt(index)) {//如果差额有不相等的，那就不是等差数列了
				break;
			}else if(index == password.length() - 1) {//遍历到最后一个还没有退出循环，也就是一直等差，那就是等差数列了
				return false;
			}
		}
		
		
		//密码不能和证件号码或者手机号码有6位连续一致，也就是密码取自其中连续六位
		for(String num : nums) {
			if(num != null && num.contains(password)) {
				return false;
			}
		}
		return true;
	}
	
    /**
     * 按支付密码规则检查
     * @param password 明文密码
     * @param nums 各种号码
     * @return int 校验结果详情:PasswordCheck.XXXXXX
     */
	public static int payPasswordCheckDetail(String password, String... nums) {
		
		// 不能为空且必须是六位数字
		if(password == null) {
			return EMPTY_INPUT;
		}
		if(!password.matches("\\d{6}")) {
			return NO_MACTH;
		}
		
		//2.等差数列验证，冒泡法（效率更高，兼容性更好）
		int index = 0;
		//取第一个减去第二个做差额，如果等差，则每个差额都一样（由于前文已经肯定了password是六位，所以下面不用做数组下标越界的处理，否则至少数组要长度为3才不会抛异常）
		int margin = password.charAt(index++) - password.charAt(index);
		while(true) {
			if(margin != password.charAt(index++) - password.charAt(index)) {//如果差额有不相等的，那就不是等差数列了
				break;
			}else if(index == password.length() - 1) {//遍历到最后一个还没有退出循环，也就是一直等差，那就是等差数列了
				return SIMPLE_RULE;
			}
		}
		
		//密码不能和证件号码或者手机号码有6位连续一致，也就是密码取自其中连续六位
		for(String num : nums) {
			if(num != null && num.contains(password)) {
				return HIT_NUMBER;
			}
		}
		return PASS;
	}
}
