package com.epaylinks.efps.pas.common.sm3;

import org.bouncycastle.math.ec.ECPoint;

import java.math.BigInteger;

/**
 * SM2算法运算中间值，用于核查算法运算过程
 * <AUTHOR>
 *
 */
public class SM2_IntermediateResult {
	// 签名中间值
	public byte[] _MLine;
	public BigInteger _e;
	public ECPoint _p1;
	public BigInteger _OneAddda_1;

	// 验签中间值
	public byte[] _MLine_sq;
	public BigInteger _e_sq;
	public BigInteger _Sign_t;
	public ECPoint _p0_sq;
	public ECPoint _p00_sq;
	public ECPoint _p1_sq;

	// 加解密中间值
	public ECPoint _Point_C1;
	public byte[] _C1;
	public int _klen;
	public ECPoint _p2;
	public byte[] _Crypt_t;
	public byte[] _C2;
	public byte[] _C3;
	public byte[] _u;

	// 密钥交换中间值
	public BigInteger _x2_;
	public BigInteger _tA;
	public BigInteger _tB;
	public BigInteger _x1_;
	public ECPoint _V;
	public ECPoint _U;
}
