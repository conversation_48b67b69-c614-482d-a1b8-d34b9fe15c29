package com.epaylinks.efps.pas.common;

import com.epaylinks.efps.common.log.Logable;
import com.epaylinks.efps.common.sequence.SequenceService;
import org.apache.commons.lang.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class TransactionNoUtil {

    @Autowired
    private SequenceService sequenceService;

    /**
     * EPSP订单号生成器
     * @param sequence
     * @param prefix
     * @return
     */
    @Logable(businessTag = "generateTransactionNo")
    public String generateTransactionNo(String sequence, String prefix) {
        String random = String.format("%06d", sequenceService.nextValue(sequence));
        String dateString = DateFormatUtils.format(new Date(), "yyyyMMddHHmmss");
        return prefix + dateString + random;
    }
}