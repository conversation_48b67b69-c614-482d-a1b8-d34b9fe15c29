package com.epaylinks.efps.pas.common.sm3;

import org.bouncycastle.crypto.digests.SM3Digest;

import java.io.UnsupportedEncodingException;

/**
 * Created by puke on 2019/11/27
 */
public class SM3Util {

    private SM3Util(){}

    private static final SM3 SM3 = new SM3();
    
    /**
     * 计算SM3摘要值
     *
     * @param srcData 原文
     * @return 摘要值，对于SM3算法来说是32字节
     */
    public static byte[] hash(byte[] srcData) {
        SM3Digest digest = new SM3Digest();
        digest.update(srcData, 0, srcData.length);
        byte[] hash = new byte[digest.getDigestSize()];
        digest.doFinal(hash, 0);
        return hash;
    }

    /**
     * SM3杂凑算法(编码使用UTF-8)
     * @param rawDataStr
     * @param usrId
     * @param pubKeyHex
     * @return
     * @throws UnsupportedEncodingException
     */
    public static byte[] digest(String rawDataStr, String usrId, String pubKeyHex) throws UnsupportedEncodingException {
        return SM3.digest(rawDataStr, usrId, pubKeyHex);
    }

    public static byte[] digest(byte[] rawData, byte[] usrId, String pubKeyHex) {
        return SM3.digest(rawData, usrId, pubKeyHex);
    }

    /**
     * sm3摘要
     * @param raw
     * @return
     */
    public static byte[] sm3(byte[] raw){
        SM3Digest sm3 = new SM3Digest();
        sm3.update(raw, 0, raw.length);
        byte[] result = new byte[sm3.getDigestSize()];
        sm3.doFinal(result, 0);
        return result;
    }
}
