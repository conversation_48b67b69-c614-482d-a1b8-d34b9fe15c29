package com.epaylinks.efps.pas.common;

import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSONObject;

public interface ExceptionUtil {
	
	public static String getErrorCode(Exception e) {
		String errorCode = null;
		String subStr = StringUtils.substringBetween(e.getMessage(), "<span>", "</span>");
        if (StringUtils.isNotBlank(subStr)) {
            JSONObject jsonObject = JSONObject.parseObject(subStr.replaceAll("&quot;", "\""), JSONObject.class);
            if (jsonObject != null && jsonObject.get("code") != null) {
            	errorCode = jsonObject.get("code").toString();
            }
        }
		return errorCode;
	}
}
