alter table pas_dept
   drop constraint FK_PAS_DEPT_REFERENCE_PAS_ROLE;

alter table pas_menu
   drop constraint FK_PAS_MENU_REFERENCE_PAS_PERM;

alter table pas_role_perm
   drop constraint FK_PAS_ROLE_PERM;

alter table pas_role_perm
   drop constraint FK_PAS_ROLE_REFERENCE_PAS_PERM;

alter table pas_role_user
   drop constraint FK_PAS_ROLE_REFERENCE_PAS_USER;

alter table pas_role_user
   drop constraint FK_PAS_ROLE_USER;

alter table pas_user
   drop constraint FK_PAS_USER_REFERENCE_PAS_DEPT;

drop table pas_dept cascade constraints;

drop table pas_menu cascade constraints;

drop index idxu_perm;

drop table pas_perm cascade constraints;

drop table pas_role cascade constraints;

drop table pas_role_perm cascade constraints;

drop table pas_role_user cascade constraints;

drop table pas_user cascade constraints;

/*==============================================================*/
/* Table: pas_dept                                            */
/*==============================================================*/
create table pas_dept 
(
   dept_id            NUMBER               not null,
   role_id            NUMBER,
   parent_id          NUMBER,
   name               VARCHAR2(50)         not null,
   remark             VARCHAR2(200),
   constraint PK_PAS_DEPT primary key (dept_id)
);

/*==============================================================*/
/* Table: pas_menu                                            */
/*==============================================================*/
create table pas_menu 
(
   menu_id            NUMBER               not null,
   perm_id            NUMBER,
   parent_id          NUMBER,
   name               VARCHAR2(50)         not null,
   remark             VARCHAR2(200),
   url                VARCHAR2(200),
   module             VARCHAR2(50),
   constraint PK_PAS_MENU primary key (menu_id)
);

/*==============================================================*/
/* Table: pas_perm                                            */
/*==============================================================*/
create table pas_perm 
(
   perm_id            NUMBER               not null,
   name               VARCHAR2(50)         not null,
   code               VARCHAR2(10)         not null,
   remark             VARCHAR2(200),
   constraint PK_PAS_PERM primary key (perm_id)
);

/*==============================================================*/
/* Index: idxu_perm                                           */
/*==============================================================*/
create unique index idxu_perm on pas_perm (
   code ASC
);

/*==============================================================*/
/* Table: pas_role                                            */
/*==============================================================*/
create table pas_role 
(
   role_id            NUMBER               not null,
   name               VARCHAR2(50)         not null,
   remark             VARCHAR2(200),
   constraint PK_PAS_ROLE primary key (role_id)
);

/*==============================================================*/
/* Table: pas_role_perm                                       */
/*==============================================================*/
create table pas_role_perm 
(
   role_id            NUMBER,
   perm_id            NUMBER
);

/*==============================================================*/
/* Table: pas_role_user                                       */
/*==============================================================*/
create table pas_role_user 
(
   user_id            NUMBER,
   role_id            NUMBER
);

/*==============================================================*/
/* Table: pas_user                                            */
/*==============================================================*/
create table pas_user 
(
   user_id            NUMBER               not null,
   dept_id            NUMBER,
   name               VARCHAR2(50)         not null,
   real_name          VARCHAR2(50),
   remark             VARCHAR2(200),
   dept               VARCHAR2(50),
   password           VARCHAR2(50)         not null,
   creator            VARCHAR2(50),
   create_time        TIMESTAMP,
   updator            VARCHAR2(50),
   update_time        TIMESTAMP,
   constraint PK_PAS_USER primary key (user_id)
);

alter table pas_dept
   add constraint FK_PAS_DEPT_REFERENCE_PAS_ROLE foreign key (role_id)
      references pas_role (role_id);

alter table pas_menu
   add constraint FK_PAS_MENU_REFERENCE_PAS_PERM foreign key (perm_id)
      references pas_perm (perm_id);

alter table pas_role_perm
   add constraint FK_PAS_ROLE_PERM foreign key (role_id)
      references pas_role (role_id);

alter table pas_role_perm
   add constraint FK_PAS_ROLE_REFERENCE_PAS_PERM foreign key (perm_id)
      references pas_perm (perm_id);

alter table pas_role_user
   add constraint FK_PAS_ROLE_REFERENCE_PAS_USER foreign key (user_id)
      references pas_user (user_id);

alter table pas_role_user
   add constraint FK_PAS_ROLE_USER foreign key (role_id)
      references pas_role (role_id);

alter table pas_user
   add constraint FK_PAS_USER_REFERENCE_PAS_DEPT foreign key (dept_id)
      references pas_dept (dept_id);
