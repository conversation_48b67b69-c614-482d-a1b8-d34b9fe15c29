



-- Create table
create table PAS_CUSTOMER
(
  customer_id                      NUMBER(12) not null,
  customer_code               VARCHAR2(20),
  creator_id              NUMBER(12) not null,
  create_time             TIMESTAMP(6),
  newest_customer_info_id NUMBER(12),
  newest_state_id         NUMBER(12),
  state                   NUMBER(1) not null
);
-- Add comments to the table 
comment on table PAS_CUSTOMER
  is '�ͻ���';
-- Add comments to the columns 
comment on column PAS_CUSTOMER.customer_id
  is 'ID';
comment on column PAS_CUSTOMER.customer_code
  is '�ͻ�����';
comment on column PAS_CUSTOMER.creator_id
  is '¼����';
comment on column PAS_CUSTOMER.create_time
  is '����ʱ��';
comment on column PAS_CUSTOMER.newest_customer_info_id
  is '���¿ͻ�����ID';
comment on column PAS_CUSTOMER.newest_state_id
  is '����״̬�޸�ID';
comment on column PAS_CUSTOMER.state
  is '״̬��0����ʼ����1��������2�����ᣬ3��ע����';
-- Create/Recreate primary, unique and foreign key constraints 
alter table PAS_CUSTOMER
  add constraint PK_PAS_CUSTOMER primary key (customer_id);


















-- Create table
create table PAS_CUSTOMER_INFO
(
  info_id                        NUMBER(12) not null,
  customer_id                    NUMBER(12) not null,
  customer_code                  VARCHAR2(20),
  name                           VARCHAR2(100),
  short_name                     VARCHAR2(100),
  mobile                         VARCHAR2(20) not null,
  telephone                      VARCHAR2(20) not null,
  business_address               VARCHAR2(100) not null,
  province_code                  CHAR(20) not null,
  city_code                      CHAR(20) not null,
  use_uscc                       NUMBER(1) not null,
  customer_type                  NUMBER(2) not null,
  business_license_no            VARCHAR2(30) not null,
  business_license_exp_date      TIMESTAMP(6) not null,
  registered_address             VARCHAR2(100) not null,
  registered_capital             NUMBER(20) not null,
  nat_tax_reg_cer_no             VARCHAR2(30),
  nat_tax_reg_exp_date           TIMESTAMP(6),
  loc_tax_reg_cer_no             VARCHAR2(30),
  loc_tax_reg_cer_exp_date       TIMESTAMP(6),
  org_structure_code             VARCHAR2(30),
  lea_person_name                VARCHAR2(20) not null,
  lea_personi_dentification_type NUMBER(1) not null,
  lea_personi_dentification_no   VARCHAR2(20) not null,
  lea_per_den_exp_date           TIMESTAMP(6),
  parent_customer_code           VARCHAR2(20),
  web_address                    VARCHAR2(100),
  icp_license_no                 VARCHAR2(100),
  business_scope                 VARCHAR2(100),
  industry_description           VARCHAR2(100),
  employee_count                 NUMBER(8)
);
-- Add comments to the table 
comment on table PAS_CUSTOMER_INFO
  is '��ҵ�ͻ�������Ϣ��';
-- Add comments to the columns 
comment on column PAS_CUSTOMER_INFO.info_id
  is '�ͻ���ϢID';
comment on column PAS_CUSTOMER_INFO.customer_id
  is '�ͻ�ID';
comment on column PAS_CUSTOMER_INFO.customer_code
  is '�ͻ�����';
comment on column PAS_CUSTOMER_INFO.name
  is '�̻�����';
comment on column PAS_CUSTOMER_INFO.short_name
  is '�̻����';
comment on column PAS_CUSTOMER_INFO.mobile
  is '�̻��ֻ���';
comment on column PAS_CUSTOMER_INFO.telephone
  is '�̻��绰����';
comment on column PAS_CUSTOMER_INFO.business_address
  is 'Ӫҵ��ַ';
comment on column PAS_CUSTOMER_INFO.province_code
  is '����ʡ����';
comment on column PAS_CUSTOMER_INFO.city_code
  is '�����б���';
comment on column PAS_CUSTOMER_INFO.use_uscc
  is '�Ƿ�ʹ��ͳһ������ô��루���Ƿ���֤��һ����0��ʾ��1��ʾ��';
comment on column PAS_CUSTOMER_INFO.customer_type
  is '�ͻ�����(1�����˿ͻ�2����ҵ�ͻ�3�������ͻ�4���ڲ��̻�)
';
comment on column PAS_CUSTOMER_INFO.business_license_no
  is 'Ӫҵִ�պ�';
comment on column PAS_CUSTOMER_INFO.business_license_exp_date
  is 'Ӫҵִ�յ�������';
comment on column PAS_CUSTOMER_INFO.registered_address
  is 'ע���ַ';
comment on column PAS_CUSTOMER_INFO.registered_capital
  is 'ע���ʽ𣬵�λԪ�����ɰ���С��';
comment on column PAS_CUSTOMER_INFO.nat_tax_reg_cer_no
  is '��˰˰��Ǽ�֤��';
comment on column PAS_CUSTOMER_INFO.nat_tax_reg_exp_date
  is '��˰˰��Ǽ�֤��������';
comment on column PAS_CUSTOMER_INFO.loc_tax_reg_cer_no
  is '��˰˰��Ǽ�֤��';
comment on column PAS_CUSTOMER_INFO.loc_tax_reg_cer_exp_date
  is '��˰˰��Ǽ�֤��������';
comment on column PAS_CUSTOMER_INFO.org_structure_code
  is '��֯�ṹ����';
comment on column PAS_CUSTOMER_INFO.lea_person_name
  is '���˻�Ӫ������';
comment on column PAS_CUSTOMER_INFO.lea_personi_dentification_type
  is '���˻�Ӫ��֤������';
comment on column PAS_CUSTOMER_INFO.lea_personi_dentification_no
  is '���˻�Ӫ��֤������';
comment on column PAS_CUSTOMER_INFO.lea_per_den_exp_date
  is '���˻�Ӫ��֤����Ч�ڽ�ֹ����';
comment on column PAS_CUSTOMER_INFO.parent_customer_code
  is '�ϼ��ͻ�����';
comment on column PAS_CUSTOMER_INFO.web_address
  is '��վ��ַ';
comment on column PAS_CUSTOMER_INFO.icp_license_no
  is 'ICP���ʱ��';
comment on column PAS_CUSTOMER_INFO.business_scope
  is '��Ӫ��Χ';
comment on column PAS_CUSTOMER_INFO.industry_description
  is '�̻���ҵ����';
comment on column PAS_CUSTOMER_INFO.employee_count
  is 'Ա������';
-- Create/Recreate primary, unique and foreign key constraints 
alter table PAS_CUSTOMER_INFO
  add constraint PK_PAS_CUSTOMER_INFO primary key (INFO_ID);








-- Create table
create table PAS_CUSTOMER_SETTLE_INFO
(
  settle_id              NUMBER(12) not null,
  customer_info_id       NUMBER(12) not null,
  customer_id            NUMBER(12),
  customer_code          VARCHAR2(20),
  circle                 NUMBER(5) not null,
  transfer_money_day     NUMBER(5) not null,
  risk_prematurity       NUMBER(5) not null,
  target                 NUMBER(1) not null,
  currency               VARCHAR2(4) not null,
  pause_sett             NUMBER(1) not null,
  bank_account_type      NUMBER(1),
  bank_code              VARCHAR2(4) not null,
  province_code          NUMBER not null,
  city_code              NUMBER not null,
  bank_line_number       VARCHAR2(12),
  open_account_bank_name VARCHAR2(40) not null,
  bank_account_no        VARCHAR2(20),
  customer_name_in_bank  VARCHAR2(20)
);
-- Add comments to the table 
comment on table PAS_CUSTOMER_SETTLE_INFO
  is '�ͻ�������Ϣ��';
-- Add comments to the columns 
comment on column PAS_CUSTOMER_SETTLE_INFO.settle_id
  is '����ID';
comment on column PAS_CUSTOMER_SETTLE_INFO.customer_info_id
  is '�ͻ���ϢID����CUM��ģ�';
comment on column PAS_CUSTOMER_SETTLE_INFO.customer_id
  is '�ͻ�ID';
comment on column PAS_CUSTOMER_SETTLE_INFO.customer_code
  is '�ͻ�����';
comment on column PAS_CUSTOMER_SETTLE_INFO.circle
  is '��������(��λΪ��)';
comment on column PAS_CUSTOMER_SETTLE_INFO.transfer_money_day
  is '��������';
comment on column PAS_CUSTOMER_SETTLE_INFO.risk_prematurity
  is '����Ԥ����(��λ��)';
comment on column PAS_CUSTOMER_SETTLE_INFO.target
  is '����Ŀ��(1�����㵽���п���2�����㵽��Ʊ���˻�)';
comment on column PAS_CUSTOMER_SETTLE_INFO.currency
  is '�������(CNY������ң�Ĭ��ֵ)';
comment on column PAS_CUSTOMER_SETTLE_INFO.pause_sett
  is '��ͣ����(0���������㣻1����ͣ����)';
comment on column PAS_CUSTOMER_SETTLE_INFO.bank_account_type
  is '���������˺�����(1���Թ���2����˽ TargetΪ���㵽���п�ʱ����)';
comment on column PAS_CUSTOMER_SETTLE_INFO.bank_code
  is '���б���';
comment on column PAS_CUSTOMER_SETTLE_INFO.province_code
  is '����������ʡID';
comment on column PAS_CUSTOMER_SETTLE_INFO.city_code
  is '������������ID';
comment on column PAS_CUSTOMER_SETTLE_INFO.bank_line_number
  is '�����е����к�';
comment on column PAS_CUSTOMER_SETTLE_INFO.open_account_bank_name
  is '����������';
comment on column PAS_CUSTOMER_SETTLE_INFO.bank_account_no
  is '��������п��˻���(TargetΪ���㵽���п�ʱ����)';
comment on column PAS_CUSTOMER_SETTLE_INFO.customer_name_in_bank
  is '���������˻��Ŀ�������';
-- Create/Recreate primary, unique and foreign key constraints 
alter table PAS_CUSTOMER_SETTLE_INFO
  add constraint PK_PAS_CUSTOMER_SETTLE_INFO primary key (SETTLE_ID);













-- Create table
create table PAS_CUSTOMER_BUSINESS_INFO
(
  business_id   NUMBER(12) not null,
  customer_info_id       NUMBER(12) not null,
  customer_id   NUMBER(12),
  customer_code VARCHAR2(20),
  business_code VARCHAR2(20) not null,
  rate_name     VARCHAR2(20) not null,
  rate_mode     NUMBER(2) not null,
  rate_param    VARCHAR2(20) not null,
  status        NUMBER(1) not null
);
-- Add comments to the table 
comment on table PAS_CUSTOMER_BUSINESS_INFO
  is '�ͻ�ҵ����Ϣ��';
-- Add comments to the columns 
comment on column PAS_CUSTOMER_BUSINESS_INFO.business_id
  is 'ҵ����ϢID';
comment on column PAS_CUSTOMER_BUSINESS_INFO.customer_info_id
  is '�ͻ���ϢID����CUM��ģ�';
comment on column PAS_CUSTOMER_BUSINESS_INFO.customer_id
  is '�ͻ�ID';
comment on column PAS_CUSTOMER_BUSINESS_INFO.customer_code
  is '�ͻ�����';
comment on column PAS_CUSTOMER_BUSINESS_INFO.business_code
  is 'ҵ�����';
comment on column PAS_CUSTOMER_BUSINESS_INFO.rate_name
  is '��������';
comment on column PAS_CUSTOMER_BUSINESS_INFO.rate_mode
  is '����ģʽ(1���̶�����)';
comment on column PAS_CUSTOMER_BUSINESS_INFO.rate_param
  is '������ʽ(��ͬ�ķ����в�ͬ�Ĳ�����ʽ�����ڹ̶��ٷֱȷ��ʣ���ֵΪһ��1-99��������������30��ʾ����Ϊ30%)';
comment on column PAS_CUSTOMER_BUSINESS_INFO.status
  is '״̬(0����Ч��1����Ч)';
-- Create/Recreate primary, unique and foreign key constraints 
alter table PAS_CUSTOMER_BUSINESS_INFO
  add constraint PK_PAS_CUSTOMER_BUSINESS_INFO primary key (BUSINESS_ID);











-- Create table
create table PAS_CUSTOMER_AUDIT_INFO
(
  audit_info_id                    NUMBER(12) not null,
  customer_info_id      NUMBER(12) not null,
  create_time           TIMESTAMP(6) not null,
  operation_type        VARCHAR2(4) not null,
  creator_id            NUMBER(12),
  audit_status                VARCHAR2(10),
  first_auditor        NUMBER(12),
  first_audit_time     TIMESTAMP(6),
  first_audit_comment  VARCHAR2(200),
  second_auditor       NUMBER(12),
  second_audit_time    TIMESTAMP(6),
  second_audit_comment VARCHAR2(200)
);
-- Add comments to the table 
comment on table PAS_CUSTOMER_AUDIT_INFO
  is '�ͻ������Ϣ';
-- Add comments to the columns 
comment on column PAS_CUSTOMER_AUDIT_INFO.audit_info_id
  is '�����ϢID';
comment on column PAS_CUSTOMER_AUDIT_INFO.customer_info_id
  is '�ͻ���ϢID';
comment on column PAS_CUSTOMER_AUDIT_INFO.create_time
  is '�ύ����';
comment on column PAS_CUSTOMER_AUDIT_INFO.operation_type
  is '��Ϊ���ͣ�00����Ӫ�Ż�-�ͻ�������01����Ӫ�Ż�-�ͻ��޸ġ�02���ͻ��Ż�-�޸Ľ�����Ϣ��';
comment on column PAS_CUSTOMER_AUDIT_INFO.creator_id
  is '¼��Ա';
comment on column PAS_CUSTOMER_AUDIT_INFO.audit_status
  is '���״̬��00��������01������δͨ����02��������03������δͨ����04����˳ɹ���';
comment on column PAS_CUSTOMER_AUDIT_INFO.first_auditor
  is '������';
comment on column PAS_CUSTOMER_AUDIT_INFO.first_audit_time
  is '����ʱ��';
comment on column PAS_CUSTOMER_AUDIT_INFO.first_audit_comment
  is '����˵��';
comment on column PAS_CUSTOMER_AUDIT_INFO.second_auditor
  is '������';
comment on column PAS_CUSTOMER_AUDIT_INFO.second_audit_time
  is '����ʱ��';
comment on column PAS_CUSTOMER_AUDIT_INFO.second_audit_comment
  is '����˵��';
-- Create/Recreate primary, unique and foreign key constraints 
alter table PAS_CUSTOMER_AUDIT_INFO
  add constraint PK_PAS_CUSTOMER_AUDIT_INFO primary key (audit_info_id);









-- Create table
create table PAS_CUSTOMER_STATE_UPDATE
(
  update_id             NUMBER(12) not null,
  customer_id    NUMBER(12) not null,
  new_state      NUMBER(1),
  old_state      NUMBER(1),
  operator_id    NUMBER(12),
  operate_time   TIMESTAMP(6),
  update_comment VARCHAR2(100)
);
-- Add comments to the table 
comment on table PAS_CUSTOMER_STATE_UPDATE
  is '�̻�״̬�޸ļ�¼';
-- Add comments to the columns 
comment on column PAS_CUSTOMER_STATE_UPDATE.update_id
  is 'ID';
comment on column PAS_CUSTOMER_STATE_UPDATE.customer_id
  is '�̻�ID';
comment on column PAS_CUSTOMER_STATE_UPDATE.new_state
  is '��״̬';
comment on column PAS_CUSTOMER_STATE_UPDATE.old_state
  is '��״̬';
comment on column PAS_CUSTOMER_STATE_UPDATE.operator_id
  is '������ID';
comment on column PAS_CUSTOMER_STATE_UPDATE.operate_time
  is '����ʱ��';
comment on column PAS_CUSTOMER_STATE_UPDATE.update_comment
  is '���˵��';
-- Create/Recreate primary, unique and foreign key constraints 
alter table PAS_CUSTOMER_STATE_UPDATE
  add constraint PK_PAS_CUM_STATE_UPDATE primary key (update_id);










/*==============================================================*/
/* Table: pas_customer_sync_info                              */
/*==============================================================*/
create table pas_customer_sync_info 
(
   sync_id            NUMBER(12)           not null,
   create_time        TIMESTAMP            not null,
   type               VARCHAR(4)           not null,
   record_id          NUMBER(12)           not null,
   customer_code      VARCHAR(50)          not null,
   retry_count        NUMBER(12),
   last_retry_time    TIMESTAMP,
   constraint PK_PAS_CUSTOMER_SYNC_INFO primary key (sync_id)
);

comment on table pas_customer_sync_info is
'��ͬ���̻�֪ͨ�б��ɹ�ͬ��֮��ɾ��';

comment on column pas_customer_sync_info.sync_id is
'ID';

comment on column pas_customer_sync_info.create_time is
'����ʱ��';

comment on column pas_customer_sync_info.type is
'���ͣ�1���ͻ�״̬�����2���ͻ������޸ģ�3���ͻ�����������4���ͻ������˻��޸ģ�';

comment on column pas_customer_sync_info.record_id is
'��ȡ�����ݿ��¼ID��״̬�����¼ID���̻�����ID��';

comment on column pas_customer_sync_info.customer_code is
'�ͻ�����';

comment on column pas_customer_sync_info.retry_count is
'֪ͨ���Դ���';

comment on column pas_customer_sync_info.last_retry_time is
'���һ��֪ͨʱ��';









/*==============================================================*/
/* Table: pas_customer_attachment_info                        */
/*==============================================================*/
create table pas_customer_attachment_info 
(
   attachment_id      NUMBER(12)           not null,
   customer_info_id   NUMBER(12)           not null,
   attachment_code    VARCHAR(10)          not null,
   attachment_name    VARCHAR(50)          not null,
   attachment_url     VARCHAR(100)         not null,
   attachmen_size     NUMBER(12)           not null,
   constraint PK_PAS_CUSTOMER_ATTACHMENT primary key (attachment_id)
);

comment on table pas_customer_attachment_info is
'�ͻ�������Ϣ��';

comment on column pas_customer_attachment_info.attachment_id is
'������ϢID';

comment on column pas_customer_attachment_info.customer_info_id is
'�ͻ�ID';

comment on column pas_customer_attachment_info.attachment_code is
'��������';

comment on column pas_customer_attachment_info.attachment_name is
'��������';

comment on column pas_customer_attachment_info.attachment_url is
'��������URL';

comment on column pas_customer_attachment_info.attachmen_size is
'�ļ���С����λ�ֽ�';









