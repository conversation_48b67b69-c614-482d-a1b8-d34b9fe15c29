-------------------------------------------
-- Export file for user EFPS             --
-- Created by adm on 2018/1/29, 15:15:59 --
-------------------------------------------

set define off
spool ora_dev_pas_20180129.log

prompt
prompt Creating table PAS_BANK_BRANCH
prompt ==============================
prompt
create table EFPS.PAS_BANK_BRANCH
(
  lbnk_no  VARCHAR2(12) not null,
  lbnk_nm  VARCHAR2(256) default ' ',
  lbnk_cd  VARCHAR2(3) default ' ',
  corp_org VARCHAR2(12) default ' ',
  adm_city VARCHAR2(20) default ' ',
  adm_prov VARCHAR2(20) default ' ',
  adm_rgn  VARCHAR2(20) default ' ',
  prov_cd  VARCHAR2(4) default ' ',
  city_cd  VARCHAR2(4) default ' ',
  tm_smp   VARCHAR2(26) default ' ',
  nod_id   VARCHAR2(20) default ' ',
  upd_dt   VARCHAR2(24) default ' '
)
tablespace EFPS
  pctfree 10
  initrans 1
  maxtrans 255
  storage
  (
    initial 64K
    next 1M
    minextents 1
    maxextents unlimited
  );
comment on column EFPS.PAS_BANK_BRANCH.lbnk_no
  is '�����к�';
comment on column EFPS.PAS_BANK_BRANCH.lbnk_nm
  is '��������';
comment on column EFPS.PAS_BANK_BRANCH.lbnk_cd
  is '�б����';
comment on column EFPS.PAS_BANK_BRANCH.corp_org
  is '�����������';
comment on column EFPS.PAS_BANK_BRANCH.adm_city
  is '����������';
comment on column EFPS.PAS_BANK_BRANCH.adm_prov
  is '��������ʡ';
comment on column EFPS.PAS_BANK_BRANCH.adm_rgn
  is '������������';
comment on column EFPS.PAS_BANK_BRANCH.prov_cd
  is 'ʡ��˾���';
comment on column EFPS.PAS_BANK_BRANCH.city_cd
  is '�ƶ��й�˾';
comment on column EFPS.PAS_BANK_BRANCH.tm_smp
  is 'ʱ���';
comment on column EFPS.PAS_BANK_BRANCH.nod_id
  is '���׷����ڵ���';
alter table EFPS.PAS_BANK_BRANCH
  add constraint PK_BKIN primary key (LBNK_NO)
  using index 
  tablespace EFPS
  pctfree 10
  initrans 2
  maxtrans 255
  storage
  (
    initial 64K
    next 1M
    minextents 1
    maxextents unlimited
  );

prompt
prompt Creating table PAS_BKCD
prompt =======================
prompt
create table EFPS.PAS_BKCD
(
  lbnk_cd  VARCHAR2(3) not null,
  bnk_nm   VARCHAR2(60) not null,
  corp_org VARCHAR2(12) default 'xxx' not null,
  flg      VARCHAR2(2) default '0' not null,
  tm_smp   VARCHAR2(26) default ' ' not null,
  nod_id   VARCHAR2(20) default ' ' not null
)
tablespace EFPS
  pctfree 10
  initrans 1
  maxtrans 255
  storage
  (
    initial 64K
    next 1M
    minextents 1
    maxextents unlimited
  );
comment on table EFPS.PAS_BKCD
  is '���б��������Ŷ��ձ�';
comment on column EFPS.PAS_BKCD.lbnk_cd
  is '���б��';
comment on column EFPS.PAS_BKCD.bnk_nm
  is '��������';
comment on column EFPS.PAS_BKCD.corp_org
  is '������';
comment on column EFPS.PAS_BKCD.flg
  is '��־λ';
comment on column EFPS.PAS_BKCD.tm_smp
  is 'ʱ���';
comment on column EFPS.PAS_BKCD.nod_id
  is '���׷����ڵ���';
alter table EFPS.PAS_BKCD
  add primary key (LBNK_CD)
  using index 
  tablespace EFPS
  pctfree 10
  initrans 2
  maxtrans 255
  storage
  (
    initial 64K
    next 1M
    minextents 1
    maxextents unlimited
  );

prompt
prompt Creating table PAS_CCOP
prompt =======================
prompt
create table EFPS.PAS_CCOP
(
  city_cd   VARCHAR2(4) not null,
  prov_cd   VARCHAR2(4) default ' ' not null,
  city_lnm  VARCHAR2(60) default ' ' not null,
  city_nm   VARCHAR2(24) not null,
  boss_city VARCHAR2(4) default ' ' not null,
  vgop_city VARCHAR2(5) default ' ' not null,
  upd_dt    VARCHAR2(8) default ' ' not null,
  tm_smp    VARCHAR2(26) default ' ' not null,
  nod_id    VARCHAR2(20) default ' ' not null
)
tablespace EFPS
  pctfree 10
  initrans 1
  maxtrans 255
  storage
  (
    initial 64K
    next 1M
    minextents 1
    maxextents unlimited
  );
comment on column EFPS.PAS_CCOP.city_cd
  is '�й�˾���';
comment on column EFPS.PAS_CCOP.prov_cd
  is 'ʡ��˾���';
comment on column EFPS.PAS_CCOP.city_nm
  is '��������';
comment on column EFPS.PAS_CCOP.upd_dt
  is '��������';
comment on column EFPS.PAS_CCOP.tm_smp
  is 'ʱ���';
comment on column EFPS.PAS_CCOP.nod_id
  is '�ն���ˮ��';
create index EFPS.PAS_CCOP_NI1 on EFPS.PAS_CCOP (PROV_CD, CITY_CD)
  tablespace EFPS
  pctfree 10
  initrans 2
  maxtrans 255
  storage
  (
    initial 64K
    next 1M
    minextents 1
    maxextents unlimited
  );
create index EFPS.PAS_CCOP_NI2 on EFPS.PAS_CCOP (BOSS_CITY)
  tablespace EFPS
  pctfree 10
  initrans 2
  maxtrans 255
  storage
  (
    initial 64K
    next 1M
    minextents 1
    maxextents unlimited
  );
create index EFPS.PAS_CCOP_NI3 on EFPS.PAS_CCOP (VGOP_CITY)
  tablespace EFPS
  pctfree 10
  initrans 2
  maxtrans 255
  storage
  (
    initial 64K
    next 1M
    minextents 1
    maxextents unlimited
  );
create unique index EFPS.PAS_CCOP_UI1 on EFPS.PAS_CCOP (CITY_NM)
  tablespace EFPS
  pctfree 10
  initrans 2
  maxtrans 255
  storage
  (
    initial 64K
    next 1M
    minextents 1
    maxextents unlimited
  );
alter table EFPS.PAS_CCOP
  add primary key (CITY_CD, CITY_NM)
  using index 
  tablespace EFPS
  pctfree 10
  initrans 2
  maxtrans 255
  storage
  (
    initial 64K
    next 1M
    minextents 1
    maxextents unlimited
  );

prompt
prompt Creating table PAS_PCOP
prompt =======================
prompt
create table EFPS.PAS_PCOP
(
  prov_cd       VARCHAR2(4) not null,
  prov_lvl      VARCHAR2(1) default ' ' not null,
  prov_acs_sts  VARCHAR2(1) default ' ' not null,
  cnct_boss_sts VARCHAR2(1) default ' ' not null,
  boss_prov     VARCHAR2(4) default ' ' not null,
  lps_cd        VARCHAR2(8) default ' ' not null,
  vgop_prov     VARCHAR2(4) default ' ' not null,
  ppd_opn_flg   VARCHAR2(1) default ' ' not null,
  upd_dt        VARCHAR2(8) default ' ' not null,
  tm_smp        VARCHAR2(26) default ' ' not null,
  prov_lnm      VARCHAR2(60) default ' ' not null,
  prov_nm       VARCHAR2(16) default ' ' not null,
  nod_id        VARCHAR2(20) default ' ' not null
)
tablespace EFPS
  pctfree 10
  initrans 1
  maxtrans 255
  storage
  (
    initial 64K
    next 1M
    minextents 1
    maxextents unlimited
  );
comment on table EFPS.PAS_PCOP
  is 'ʡ��˾��Ϣ��';
comment on column EFPS.PAS_PCOP.prov_cd
  is 'ʡ��˾���';
comment on column EFPS.PAS_PCOP.prov_lvl
  is 'ʡ��˾����';
comment on column EFPS.PAS_PCOP.prov_acs_sts
  is 'ʡ��˾����״̬';
comment on column EFPS.PAS_PCOP.cnct_boss_sts
  is '����1��BSS��Ŧ';
comment on column EFPS.PAS_PCOP.boss_prov
  is 'BSSʡ���';
comment on column EFPS.PAS_PCOP.lps_cd
  is 'ʡƽ̨���';
comment on column EFPS.PAS_PCOP.vgop_prov
  is 'VGOPʡ���';
comment on column EFPS.PAS_PCOP.upd_dt
  is '��������';
comment on column EFPS.PAS_PCOP.tm_smp
  is 'ʱ���';
comment on column EFPS.PAS_PCOP.prov_lnm
  is 'ʡ��˾ȫ��';
comment on column EFPS.PAS_PCOP.prov_nm
  is 'ʡ��˾��';
comment on column EFPS.PAS_PCOP.nod_id
  is '���׷����ڵ���';
alter table EFPS.PAS_PCOP
  add primary key (PROV_CD)
  using index 
  tablespace EFPS
  pctfree 10
  initrans 2
  maxtrans 255
  storage
  (
    initial 64K
    next 1M
    minextents 1
    maxextents unlimited
  );


spool off
