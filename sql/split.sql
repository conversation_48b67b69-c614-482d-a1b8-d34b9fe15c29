create table  pas_business_param(id  NUMBER(12),
name  varchar2(50),
code  varchar2(50),
must  char(1),
type  varchar2(10),
business_id  Number,
create_Time  Timestamp,
creator  Number(12),
update_time	Timestamp,
updator	Number(12)) ;
comment on column PAS_BUSINESS_PARAM.id
  is '数据库主键';
comment on column PAS_BUSINESS_PARAM.name
  is '参数名称，用于界面展示';
comment on column PAS_BUSINESS_PARAM.code
  is '参数编码，作为业务主键，格式为“业务的Code.参数Code”，建立唯一索引';
comment on column PAS_BUSINESS_PARAM.must
  is '0：可选
1：必填
';
comment on column PAS_BUSINESS_PARAM.type
  is '类型，用于帮助确认展现形式，取值范围：
Rate：费率类型，界面需按照费率的方式展现
String：字符串，展现为文本输入框
';
comment on column PAS_BUSINESS_PARAM.business_id
  is '从属的业务表记录主键';
comment on column PAS_BUSINESS_PARAM.create_time
  is '创建时间';
comment on column PAS_BUSINESS_PARAM.creator
  is '创建者userId';
comment on column PAS_BUSINESS_PARAM.update_time
  is '修改时间';
comment on column PAS_BUSINESS_PARAM.updator
  is '修改者userId';


  create table pas_business_param_value(ID  NUMBER(12),
  business_param_id  Number,
  value  varchar2(50),
  business_ID  Number,
  create_time	Timestamp,
  creator	Number(12),
  update_time	Timestamp,
  updator	Number(12));

  comment on column PAS_BUSINESS_PARAM_VALUE.id
    is '数据库主键';
  comment on column PAS_BUSINESS_PARAM_VALUE.business_param_id
    is '业务参数表记录主键';
  comment on column PAS_BUSINESS_PARAM_VALUE.value
    is '业务参数值';
  comment on column PAS_BUSINESS_PARAM_VALUE.business_id
    is '从属的业务表记录主键';
  comment on column PAS_BUSINESS_PARAM_VALUE.create_time
    is '创建时间';
  comment on column PAS_BUSINESS_PARAM_VALUE.creator
    is '创建者userId';
  comment on column PAS_BUSINESS_PARAM_VALUE.update_time
    is '修改时间';
  comment on column PAS_BUSINESS_PARAM_VALUE.updator
    is '修改者userId';

 alter table PAS_CUSTOMER_INFO add notify_url varchar2(500);

alter table PAS_CUSTOMER modify creator_id NUMBER(20);

alter table PAS_CUSTOMER_AUDIT_INFO modify creator_id NUMBER(20);

alter table PAS_CUSTOMER_INFO modify business_address null;
alter table PAS_CUSTOMER_INFO modify registered_address null;

alter table PAS_CUSTOMER_ATTACHMENT_INFO modify attachment_name null;
alter table PAS_CUSTOMER_ATTACHMENT_INFO modify attachmen_size null;

--   20180130增加商户管理人信息QQ信息改为可空，
alter table PAS_CUSTOMER_CONTACTS_INFO modify qq null;

    select t.*, t.rowid from pas_business_param_value t;
    select t.*, t.rowid from pas_business_param t;
    select p.id,p.name,p.code,p.must,p.type,p.business_id,p.create_time,p.creator,p.update_time,p.updator,
    v.value
     from pas_business_param p,pas_business_param_value v
     where p.id =v.business_param_id
     and p.business_id =v.business_id
    create table pas_business_param_value(ID  NUMBER(12),
    business_param_id  Number,
    value  varchar2(50),
    business_ID  Number,
    create_time	Timestamp,
    creator	Number(12),
    update_time	Timestamp,
    updator	Number(12))
    